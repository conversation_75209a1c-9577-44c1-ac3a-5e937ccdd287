{
    /*
    options
    =======
    */

    /*
    日期时间格式。

        0: "%Y-%m-%d %H:%M:%S"
        1: "%Y-%m-%d"
        2: "%H:%M:%S"
    */
    "time_format": 0,
    /*
    自定义时间格式。如果您设置了它，将使用此格式格式化 `create_time` 和 `last_modified_time`，
    而不是 `time_format`。时间格式请参考
    https://docs.python.org/2/library/datetime.html#strftime-and-strptime-behavior`。
    */
    "custom_time_format": "",
    /*
    保存时是否添加模板。可以是布尔值，也可以是正则表达式，将针对文件名进行搜索
    */
    "enable_add_template_on_save": true,
    /*
    是否向空文件添加模板。

    当您通过其他命令创建新文件时非常有用，例如默认的 Sublime Text 的 **新建文件...** 或其他插件。
    */
    "enable_add_template_to_empty_file": true,
    /*
    在这里设置您的自定义模板头部路径，这是一个您编写自己头部文件的目录。
    文件名应该是语言名，例如 "Python.tmpl"。
    */
    "custom_template_header_path": "",
    /*
    在这里设置您的自定义模板正文路径，这是一个您编写自己正文文件的目录。
    文件名应该是语言名，例如 "Python.tmpl"。

    模板结构是：

        我是文件
        -----------
        头部
        正文

    */
    "custom_template_body_path": "",
    /*
    当您添加头部时是否显示输入面板。默认要添加头部的文件是您当前编辑的文件。
    */
    "show_input_panel_when_add_header": true,
    /*
    当您向指定目录中的文件添加头部时是否打开文件。
    */
    "open_file_when_add_header_to_directory": true,
    /*
    是否启用向隐藏目录添加头部。如果为 false，则 FileHeader 不会向其下的文件添加头部。
    */
    "enable_add_header_to_hidden_dir": true,
    /*
    是否启用向隐藏文件添加头部。如果为 false，则 FileHeader 不会向其添加头部。
    */
    "enable_add_header_to_hidden_file": false,
    /*
    FileHeader 根据文件后缀判断编程语言。

    当您创建新文件时，如果 FileHeader 判断失败，将使用默认编程语言。
    */
    "syntax_when_not_match": "Text",
    /*
    FileHeader 将根据文件后缀判断编程语言。
    您可以在这里添加更多的文件后缀。注意：语言应该是 **默认** 下的一个。
    如果 FileHeader 没有找到后缀，将把语言设置为上面的 **syntax_when_not_match**。
    */
    "file_suffix_mapping": {

    },
    /*
    FileHeader 将根据编程语言处理头部前缀。
    您可以在这里添加更多的头部前缀。注意：语言应该是 **默认** 下的一个。
    */
    "header_prefix_mapping": {

    },
    /*
    设置特殊语言语法映射，在语法映射文件名与编程语言名称文件名不同的情况下
    */
    "language_syntax_mapping": {

    },
    /*
    设置特殊文件后缀等价。以 `blade.php` 为例，FileHeader 将使用 `html` 的初始文件后缀 `blade.php`。
    */
    "extension_equivalence": {

    },

    /*
    Variables
    =========
    */

    /*
    以下是您渲染模板的变量。
    */
    "Default": {
        "author":"小九呀丶 - QQ：5268416",
        "last_modified_by":"交流QQ群：*********",
        /*
        内置变量
        ================

        - create_time

            文件创建时间。如果您使用它，当您创建新文件时，它将自动设置。

            不能自定义设置。

        - author

            文件创建者。

            FileHeader 将自动设置它。如果在 git 仓库中并且已设置 `user.name`，则 `autor` 将设置为 `user.name`，
            否则将设置为当前系统用户。

            可以自定义设置。

        - last_modified_by

            文件最后由谁修改？它在合作编程时特别有用。

            FileHeader 将自动设置它。如果在 git 仓库中并且已设置 `user.name`，则 `autor` 将设置为 `user.name`，
            否则将设置为当前系统登录用户。

            可以自定义设置。

        - last_modified_time

            文件最后修改时间。

            当您保存文件时，FileHeader 将自动设置它。

            不能自定义设置。

        - file_path

            当前文件的绝对路径。

            当您改变它时，FileHeader 将自动更新它。

            不能自定义设置。

        - file_name

            当前文件的名称，包含扩展名。

            当您改变它时，FileHeader 将自动更新它。

            不能自定义设置。

        - file_name_without_extension

            当前文件的名称，不包含扩展名。

            当您改变它时，FileHeader 将自动更新它。

            不能自定义设置。

        - project_name

            项目名称。

            注意：`project_name` 仅在 ST3 中工作。

            不能自定义设置。
        */

        /*
        电子邮件
        */
        "email": "<EMAIL>"

        // 您可以在这里添加更多......
    },
    /*
    您可以在不同语言中设置不同的变量。它将覆盖 "Default" 中的设置。
    */
    "ASP": {},
    "ActionScript": {},
    "AppleScript": {},
    "Batch File": {},
    "C#": {},
    "C++": {},
    "CSS": {},
    "Clojure": {},
    "D": {},
    "Diff": {},
    "Erlang": {},
    "Go": {},
    "Graphviz": {},
    "Groovy": {},
    "HTML": {},
    "Haskell": {},
    "Java": {},
    "JavaScript": {},
    "LaTeX": {},
    "Lisp": {},
    "Lua": {},
    "Makefile": {},
    "Markdown": {},
    "Matlab": {},
    "OCaml": {},
    "Objective-C": {},
    "PHP": {},
    "Pascal": {},
    "Perl": {},
    "Python": {},
    "R": {},
    "RestructuredText": {},
    "Ruby": {},
    "SQL": {},
    "Scala": {},
    "ShellScript": {},
    "TCL": {},
    "Text": {},
    "Textile": {},
    "XML": {},
    "YAML": {}
}