-- @Author: baidwwy
-- @Date:   2024-06-25 23:13:41
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-07-09 00:05:16
local function 更新活动(活动名称)
	if 活动名称 and type(活动名称) == "string" then
		_G[活动名称]:活动定时器()
	end
end

function 限时活动刷新()
	local 活动列表 = {"降妖伏魔", "彩虹争霸", "天降辰星", "帮派迷宫", "文韵墨香", "门派闯关", "游泳活动", "长安保卫战", "古董商人"}
	for _, 活动 in ipairs(活动列表) do
		更新活动(活动)
	end
end

function 重置每日任务()
	for k, v in pairs(每日任务) do
		每日任务[k] = { 日常任务 = {}, 副本任务 = {}, 活跃度 = { 当前 = 0, 总活跃 = 0 }, 签到数据 = {} }
	end
end

-- ==================== 单元检查：副本创建标记 =====================
local function 检查副本任务标记()
    local ok, lfs = pcall(require, "lfs")
    if not ok then
        __S服务:输出("[检查副本] 未找到 lfs 库，跳过静态检查")
        return
    end
    local base_path = "Script/任务处理类"
    for file in lfs.dir(base_path) do
        if string.match(file, "^副本_.+%.lua$") then
            local full = base_path.."/"..file
            local content = 读入文件(full)
            if content then
                for line in string.gmatch(content, "[^\r\n]+") do
                    if line:find("创建任务%(") and (not line:find("副本重置")) then
                        __S服务:输出("[警告] "..file.." 的创建任务缺少 `副本重置=true` 标记")
                    end
                end
            end
        end
    end
end

function 处理副本重置()
	副本数据={
        乌鸡国={进行={},完成={}},
        车迟斗法={进行={},完成={}},
        水陆大会={进行={},完成={}},
        七绝山={进行={},完成={}},
        通天河={进行={},完成={}},
        黑风山={进行={},完成={}},
        齐天大圣={进行={},完成={}},
        大闹天宫={进行={},完成={}},
        秘境降妖={进行={},完成={}},
        泾河龙王={进行={},完成={}},
        轮回境={进行={},完成=轮回境完成记录,上次重置时间=轮回境重置时间},
        四季={进行={},完成={}},
        红孩儿={进行={},完成={}},
        无底洞={进行={},完成={}},
        天火之殇上={进行={},完成={}},
        如梦奇谭之五更寒={进行={},完成={}},
        如梦奇谭之一斛珠={进行={},完成={}},
        如梦奇谭之双城记={进行={},完成={}},
        锻刀村之战={进行={},完成={},上次重置时间=os.time()},
        五庄观={进行={},完成={}},
    }
	for n, v in pairs(任务数据) do
		if v.副本重置 then
			if 任务数据[n].地图编号 and 任务数据[n].单位编号 then
				地图处理类:删除单位(任务数据[n].地图编号, 任务数据[n].单位编号)
			end
			任务数据[n] = nil
		end
	end

	-- 清理玩家身上已经失效的副本任务，并安全传送回主城
	for pid,pdata in pairs(玩家数据) do
		if pdata and pdata.角色 and pdata.角色.数据 and pdata.角色.数据.任务列表 then
			for idx,tid in pairs(pdata.角色.数据.任务列表) do
				if 任务数据[tid]==nil then
					pdata.角色:取消任务(tid)
				end
			end
		end
		-- 如果玩家当前地图属于副本编号（6000-7999 或 7100-7103 等自定义范围），则传送回长安
		if pdata and pdata.角色 and pdata.角色.地图数据 then
			local map=pdata.角色.地图数据.编号
			if map>=6000 and map<=7999 or map>=7100 and map<=7200 then
				地图处理类:跳转地图(pid,1501,89,114) -- 长安城安全点
				常规提示(pid,"#Y副本已重置，你被传送回安全区")
			end
		end
	end

	-- 重置脚本级别进度变量(命名以"进度"结尾的全局数字变量)
	for k,v in pairs(_G) do
		if type(v)=="number" and tostring(k):match("进度$") then
			_G[k] = 0
		end
	end

	-- 进行一次静态检查，提示遗漏的副本重置标记
	检查副本任务标记()
end

function 处理服务器关闭()
	if 服务器关闭 ~= nil and 服务器关闭.开关 then
		服务器关闭.计时 = 服务器关闭.计时 - 1
		__S服务:输出("服务器关闭倒计时：" .. 服务器关闭.计时)
		广播消息({ 内容 = "#R/服务器关闭倒计时：" .. 服务器关闭.计时, 频道 = "xt" })
		if 服务器关闭.计时 <= 60 and 服务器关闭.计时 > 0 then
			广播消息({ 内容 = "#Y/服务器将在#R/" .. 服务器关闭.计时 .. "#Y/秒后关闭,请所有玩家立即下线。", 频道 = "xt" })
		elseif 服务器关闭.计时 <= 1 then
			自动关闭服务器()
			return
		end
	end
end

function 处理三界书院()
	-- 安全检查：确保三界书院变量已初始化
	if not 三界书院 then
		三界书院 = {
			答案 = "",
			开关 = false,
			结束 = 60,
			起始 = os.time(),
			间隔 = 取随机数(30, 90) * 60,
			名单 = {}
		}
		return
	end

	if 三界书院.开关 and 三界书院.结束 <= os.time() - 三界书院.起始 then
		三界书院.开关 = false
		for n = 1, #三界书院.名单 do
			if 玩家数据[三界书院.名单[n].id] ~= nil then
				玩家数据[三界书院.名单[n].id].角色:添加银子(100000, "三界书院", 1)
			end
		end
		广播消息({ 内容 = "#Y/正确答案：#R/" .. 三界书院.答案, 频道 = "xt" })
		if #三界书院.名单 == 0 then
			广播消息({ 内容 = "#Y/真是遗憾，竟然无人可以回答正确。", 频道 = "xt" })
		else
			local 链接1 = { 提示 = format("#Y/知识就是金钱，每一位作答正确的玩家均获得10万银子金以作奖励。#G" ..
			三界书院.名单[1].名称 .. "#Y/以#R/" .. 三界书院.名单[1].用时 .. "#Y/秒惊人的飞速抢先作答正确，获得了额外的#G/20#Y/万银子金和一张"), 频道 = "xt", 结尾 =
			"#Y/的奖励。" }
			if 玩家数据[三界书院.名单[1].id] ~= nil then
				玩家数据[三界书院.名单[1].id].角色:添加银子(200000, "三界书院", 1)
				玩家数据[三界书院.名单[1].id].道具:给予超链接道具(三界书院.名单[1].id, "怪物卡片", 取随机数(4, 8), nil, 链接1, "成功")
			end
		end
	end
end

function 处理宝藏山数据()
	if 宝藏山数据.开关 then
		宝藏山数据.间隔 = 宝藏山数据.间隔 - 1
		if 宝藏山数据.间隔 == 180 then
			地图处理类:当前消息广播1(5001, "#Y各位玩家请注意，宝藏山将在#R3#Y分钟后刷出宝箱。")
		elseif 宝藏山数据.间隔 == 60 then
			地图处理类:当前消息广播1(5001, "#Y各位玩家请注意，宝藏山将在#R1#Y分钟后刷出宝箱。")
		elseif 宝藏山数据.间隔 <= 0 then
			任务处理类:宝藏山刷出宝箱()
			宝藏山数据.间隔 = 300
		end
		if os.time() - 宝藏山数据.起始 >= 3600 then
			宝藏山数据 = { 开关 = false, 起始 = os.time(), 刷新 = 0, 间隔 = 600 }
			广播消息({ 内容 = "#G/宝藏山活动已经结束，处于场景内的玩家将被自动传送出场景。", 频道 = "xt" })
			地图处理类:清除地图玩家(5001, 1226, 115, 15)
			完成任务_203()
		end
	end
end

function 处理小龟快跑()
	if 小龟快跑.次数3 == 3 and 小龟快跑.开关 == true then
		local 结算目标 = nil
		if 小龟快跑.第一名 == 1 then
			结算目标 = "小龟村一郎"
		elseif 小龟快跑.第一名 == 2 then
			结算目标 = "小龟蹦二郎"
		elseif 小龟快跑.第一名 == 3 then
			结算目标 = "小龟驰三郎"
		end
		广播消息({ 内容 = "#G/本次小龟快跑第一名的小龟是#R/" .. 结算目标 .. "#G/希望其他小龟不要气馁继续加油", 频道 = "xt" })
		for k, v in pairs(小龟快跑[结算目标]) do
			玩家数据[k].角色:添加银子(20000 * 小龟快跑[结算目标][k].下注, "小龟快跑", 1)
		end
		小龟快跑 = { 开关 = false, 开始比赛 = false, 起始 = os.time(), 次数 = 0, 次数2 = 0, 次数3 = 0, 小龟村一郎 = {}, 小龟蹦二郎 = {}, 小龟驰三郎 = {}, 第一名 = 0, 随机事件 = { "休息", "雷劈" }, 结束 = 20 }
	end
end

function 重置双倍数据()
	-- 重置双倍数据
	for id, data in pairs(双倍数据) do
		data.可领 = 14
		data.冻结 = 0
		data.本周已领 = 0
	end

	-- 重置三倍数据
	for id, data in pairs(三倍数据) do
		data.可领 = 3
		data.冻结 = 0
		data.本周已领 = 0
	end

	-- 保存重置后的数据
	写出文件("游戏数据/双倍数据.txt", table.tostring(双倍数据))
	写出文件("游戏数据/三倍数据.txt", table.tostring(三倍数据))
end

function 整秒处理(时间)
	if 时间 == "59" and 服务端参数.小时 == "02" and 服务端参数.分钟 == "59" then
		-- 每日重置
		押镖:重置数据()
		系统处理类:藏宝阁更新() -- 调用藏宝阁处理类中的藏宝阁更新函数
		重置每日任务()
		物品掉落控制.重置数据()
		
		-- 重置昆仑仙境等活动的个人数据
		if 初始活动 and 初始活动.昆仑仙境 then
			-- 清空所有玩家的昆仑仙境个人数据
			for 玩家id, 数据 in pairs(初始活动.昆仑仙境) do
				if type(玩家id) == "number" and 数据.次数 then
					初始活动.昆仑仙境[玩家id] = nil
				end
			end
		end
		
		重置数据()
		发送公告("#G美好的一天从这一秒开始，游戏对应的活动任务数据已经刷新，合理安排时间，注意休息。")
		处理副本重置()
		雁塔地宫:每日下调()
		神器任务:刷新神器任务类型()

		-- 周一凌晨的额外重置
		if tonumber(os.date("%w", os.time())) == 1 then
			雁塔地宫:每周归零()
			重置双倍数据()  -- 调用重置双倍数据函数
			-- 检查轮回境副本重置
			if 副本数据.轮回境 and 检查轮回境重置() then
				副本数据.轮回境.完成 = {}  -- 只在周一才清空完成记录
			end
			广播消息({内容="#G本周双倍和精修时间已重置,每位玩家可重新领取双倍时间14小时和精修时间3小时。",频道="xt"})
		end

		神器任务:刷新神器任务类型()

		-- 新增捉鬼每日次数重置
		for id, data in pairs(玩家数据) do
			if data and data.角色 and data.角色.捉鬼数据 then
				data.角色.捉鬼数据.每日次数 = 0
			end
			-- 重置休息次数
			if data and data.休息数据 then
				data.休息数据.每日次数 = 0
				data.休息数据.上次重置日期 = os.date("%Y-%m-%d")
			end
		end

		-- 新增翰墨丹青每日次数重置
		for id, data in pairs(玩家数据) do
			if data and data.翰墨丹青数据 then
				data.翰墨丹青数据.每日轮数 = 0
				data.翰墨丹青数据.上次重置日期 = os.date("%Y-%m-%d")
			end
		end

		-- 重置免资材修炼次数
		if 免资材修炼数据 == nil then
			免资材修炼数据 = {}
		end
		for id, data in pairs(玩家数据) do
			if data and data.角色 then
				免资材修炼数据[id] = 300
			end
		end

		-- 游泳活动重置已移至重置数据()函数中统一处理

	end

	-- 安全检查三界书院变量
	if 三界书院 and 服务端参数.小时 + 0 >= 12 and 服务端参数.小时 + 0 <= 17 and 三界书院.间隔 <= os.time() - 三界书院.起始 then
		三界书院.起始 = os.time()
		任务处理类:开启三界书院()
	end

	处理服务器关闭()
	处理三界书院()
	处理宝藏山数据()
	处理小龟快跑()
	游戏活动类:炼丹更新()
end

function 整点处理(时刻)
	if 服务端参数.小时 == 时刻 then
		return 0
	else
		服务端参数.小时 = 时刻
		服务端参数.分钟 = "0"
		帮派处理:维护处理()
		二八星宿:刷新资源()
	end
	if 时刻 == "00" or 时刻 == "0" then
		迷宫数据.开关 = false
		广播消息({ 内容 = "#G/幻域迷宫活动已经结束，所有处于战斗中的玩家将被强制退出战斗。", 频道 = "xt" })
	elseif 时刻 == "10" then
		任务处理类:开启宝藏山()
	elseif 时刻 == "14" then
		任务处理类.开启科举大赛()
	elseif 时刻 == "17" then
		任务处理类.结束科举大赛()
	elseif 时刻 == "19" then
		任务处理类:开启首席争霸赛()
	elseif 时刻 == "20" then
		-- 修复：使用正确的参数调用强制结束首席争霸赛
		-- 参数说明：门派名, 类型3(时间结束), 玩家id, 门派编号
		-- 由于是时间结束，传入空字符串作为门派名，类型3表示时间结束
		任务处理类:结束首席争霸门派("", 3, 0, 0)

	elseif 时刻 == "21" then
		任务处理类:开启镖王活动()
	elseif 时刻 == "22" then
		镖王活动.开关 = false
		广播消息({ 内容 = "#G/镖王大赛活动已经结束，所有处于战斗中的玩家将被强制退出战斗。", 频道 = "xt" })
		for n, v in pairs(战斗准备类.战斗盒子) do
			if v.战斗类型 == 100025 then
				v:结束战斗(0, 0, 1)
			end
		end
		for n, v in pairs(玩家数据) do
			if v.角色:取任务(208) ~= 0 then
				v.角色:取消任务(v.角色:取任务(208))
				常规提示(n, "#Y你的镖王任务已经被自动取消")
			end
		end
		for n, v in pairs(任务数据) do
			if v.类型 == 208 then
				任务数据[n] = nil
			end
		end
	end
end

function 整分处理(时间)
	服务端参数.分钟 = 时间
	if 时间 == "00" or 时间 == "10" or 时间 == "20" or 时间 == "30" or 时间 == "40" or 时间 == "50" then
		-- 跑商价格刷新已移至时辰函数中，按时辰变化刷新
		-- 商店处理类:刷新跑商商品买入价格()
		-- for i, v in pairs(跑商) do
		-- 	跑商[i] = 取商品卖出价格(i)
		-- end
	end

	-- 处理任务
	if 时间 == "05" then
		任务处理类:宝图妖王(id)
	elseif 时间 == "10" then
	elseif 时间 == "13" then
		--天罡星:刷新资源()
	elseif 时间 == "15" then
		任务处理类:刷出师门守卫()
	elseif 时间 == "20" then
		--梦魇夜叉:开启(id)
	elseif 时间 == "25" then
		任务处理类:宝图封妖(id)
	elseif 时间 == "32" then
		任务处理类:调皮的泡泡(id)
		任务处理类:雪人小动物()
		任务处理类:天降雪人物品()
		任务处理类:开启王婆西瓜()
	elseif 时间 == "35" then
		地煞星:刷新资源()
	elseif 时间 == "40" then
		--任务处理类:年兽活动()
		-- 18:40开启首席争霸赛进场，给玩家20分钟进场时间
		if 服务端参数.小时 + 0 == 18 then
			广播消息({ 内容 = "#Y/首席争霸赛活动已经开启入场，20分钟后将开始战斗。请各位玩家找本门派首席弟子进入比赛地图。", 频道 = "xt" })
			首席争霸赛进场开关 = true
			首席争霸赛战斗开关 = false
		end
	elseif 时间 == "45" then
		任务处理类:宝图封妖(id)
	elseif 时间 == "50" then
		任务处理类:东海湾小活动(id)
		-- 首席争霸赛进场通知移至18:40，给玩家更多进场时间
		if 服务端参数.小时 + 0 == 18 then
			广播消息({ 内容 = "#Y/首席争霸赛活动将在20分钟后开始战斗，请各位玩家提前找本门派首席弟子进入比赛地图。", 频道 = "xt" })
		end
	elseif 时间 == "55" then
		星辰挑战:刷新资源()
	end

	-- -- 刷新珍品和存档
	 if  时间 == "30" then
	-- 	商店处理类:刷新珍品()
		任务处理类:开启小龟快跑()
		-- 20点30分开启迷宫
		if 服务端参数.小时 == "20" then
			任务处理类:开启迷宫()
		end
	-- 	if not 服务端参数.玩家存档 or 服务端参数.玩家存档 < os.time() then
	-- 		服务端参数.玩家存档 = os.time()
	-- 		保存系统数据()
	-- 	end
	end
end

function 自动关闭服务器()
	-- 先保存所有玩家数据，确保数据不丢失
	__S服务:输出("开始保存所有玩家数据...")
	for n, v in pairs(玩家数据) do
		if v ~= nil and v.角色 then
			v.角色:存档()
		end
	end
	__S服务:输出("玩家数据保存完成")

	-- 保存任务数据，确保任务存档不丢失
	__S服务:输出("保存任务数据...")
	保存任务数据()
	__S服务:输出("任务数据保存完成")

	-- 等待1秒确保数据写入完成
	local 开始时间 = os.time()
	while os.time() - 开始时间 < 1 do
		-- 等待1秒
	end

	-- 强制断开所有玩家连接
	for n, v in pairs(玩家数据) do
		if v ~= nil then
			发送数据(v.连接id, 998, "服务器已经关闭，你被强制断开连接")
		end
	end

	-- 再次保存系统数据
	保存系统数据()
	__S服务:输出("服务器关闭：所有数据保存完成")

	-- 等待2秒确保所有数据写入磁盘
	local 结束等待时间 = os.time()
	while os.time() - 结束等待时间 < 2 do
		-- 等待2秒
	end

	os.exit()
end

function 全部玩家下线()
	for n, v in pairs(玩家数据) do
		if v ~= nil and v.连接id then
			发送数据(v.连接id, 998, "服务器维护中，你被强制断开连接")
		end
	end
	保存系统数据()
	-- 确保任务数据也被保存
	保存任务数据()
	for n, v in pairs(__C客户信息) do
		if v.网关 == nil then
			__S服务:断开连接(n)
			__C客户信息[n] = nil
		end
	end
	print("全部玩家下线成功！")
end

function 输入函数(t)
	local 服务器关闭配置 = 调试模式 and { 开关 = true, 计时 = 10, 起始 = os.time() } or { 开关 = true, 计时 = 60, 起始 = os.time() }

	-- 命令处理函数表
	local 命令处理 = {
		-- 系统管理命令
		["@cq"] = function()
			保存系统数据()
			-- 收集所有在线玩家ID并保存数据
			local 在线玩家列表 = {}
			for n, v in pairs(玩家数据) do
				if v and v.角色 then
					table.insert(在线玩家列表, n)
				end
			end
			保存玩家数据(在线玩家列表)
			服务器关闭 = 服务器关闭配置
			发送公告("#R各位玩家请注意，服务器将在2分钟后进行更新,届时服务器将临时关闭，请所有玩家注意提前下线。")
			广播消息({ 内容 = format("#R各位玩家请注意，服务器将在2分钟后进行更新,届时服务器将临时关闭,，请所有玩家提前下线。"), 频道 = "xt" })
		end,
		["@bcsj"] = function() 保存系统数据() end,
		["@bcwjsj"] = function() 
			-- 收集所有在线玩家ID并保存数据
			local 在线玩家列表 = {}
			for n, v in pairs(玩家数据) do
				if v and v.角色 then
					table.insert(在线玩家列表, n)
				end
			end
			保存玩家数据(在线玩家列表)
		end,
		["@bcqtsj"] = function() 保存帮派数据() end,
		["@qbxx"] = function() 全部玩家下线() end,
		["@kqmj"] = function() 副本_秘境降妖:开启秘境降妖() end,
		["@bcbpsj"] = function()
			for k, v in pairs(取所有帮派) do
				local 文件路径 = "游戏数据/帮派/帮派数据" .. k .. ".txt"
				if v.已解散 then
					写出文件(文件路径, table.tostring({}))
				else
					写出文件(文件路径, table.tostring(帮派数据[k]))
				end
			end
			__S服务:输出("保存帮派数据成功……")
		end,
		["@bcwj"] = function()
			for n, v in pairs(玩家数据) do
				if v ~= nil then
					v.角色:存档()
				end
			end
			__S服务:输出("保存玩家数据成功……")
		end,
		["@bcrw"] = function()
			local llsj = {}
			local 数量 = 0
			for n, v in pairs(任务数据) do
				if v.是存档 then
					数量 = 数量 + 1
					llsj[数量] = table.loadstring(table.tostring(任务数据[n]))
					llsj[数量].存储id = n
				end
			end
			写出文件([[游戏数据/任务数据.txt]], table.tostring(llsj))
			llsj = nil
			__S服务:输出("保存任务数据成功……")
		end,
		["@cklb"] = function() 查看在线列表() end,
		["@qzxx"] = function() 强制下线() end,
		["@csrz"] = function()
			local 结果 = 测试错误日志()
			__S服务:输出("错误日志测试完成")
			__S服务:输出("错误类型: " .. 结果.错误日志类型 .. ", 错误数: " .. 结果.错误数目)
		end,
		["@ckrz"] = function()
			__S服务:输出("错误日志类型: " .. type(错误日志))
			__S服务:输出("错误计数: " .. tostring(错误数目))
			if type(错误日志) == "table" then
				__S服务:输出("错误日志条数: " .. #错误日志)
				for i, v in ipairs(错误日志) do
					if i <= 5 then -- 只显示前5条避免刷屏
						__S服务:输出("日志" .. i .. ": " .. tostring(v):sub(1, 100))
					end
				end
				if #错误日志 > 5 then
					__S服务:输出("... 共" .. #错误日志 .. "条错误日志")
				end
			else
				__S服务:输出("错误日志内容长度: " .. tostring(错误日志):len())
			end
		end,

		-- 排行榜管理
		["清空排行数据"] = function()
			排行数据 = {}
			local 数组 = { "属性排行", "积分排行", "数据排行" }
			local 排行属性 = {
				{ "气血排行", "防御排行", "伤害排行", "速度排行", "命中排行", "灵力排行", "魔法排行", "躲避排行" },
				{ "门贡排行", "知了积分", "长安保卫", "活动积分", "比武积分", "副本积分", "成就积分", "单人积分" },
				{ "银子排行", "储备排行", "存银排行", "人气排行", "帮贡排行", "在线时间", "人物等级", "活跃排行" }
			}
			for i = 1, #数组 do
				排行数据[数组[i]] = {}
				for o = 1, 8 do
					排行数据[数组[i]][排行属性[i][o]] = {}
					for io = 1, 20 do
						排行数据[数组[i]][排行属性[i][o]][io] = { 名称 = "无", 数值 = 0 }
					end
				end
			end
		end,

		-- 活动管理命令
		["kqzq"] = function()
			Q_中秋开关 = true
			__S服务:输出("中秋活动已经开启")
			中秋任务:开启中秋任务()
		end,
		["gbzq"] = function()
			Q_中秋开关 = false
			__S服务:输出("中秋活动已经关闭")
		end,
		["11"] = function() 天降辰星:开启活动() end,
		["12"] = function() 设置任务138() end,
		["13"] = function() 任务处理类:开启三界书院() end,
		["14"] = function() 长安保卫战:开启活动() end,
		["14.1"] = function() 长安保卫战:添加NPC() end,
		["14.2"] = function() 长安保卫战:添加巢穴NPC() end,
		["14.3"] = function() 长安保卫战:刷出怪物() end,
		["15"] = function() 帮派迷宫:开启活动() end,
		["16"] = function() 任务处理类:开启皇宫飞贼() end,
		["16.1"] = function()
			皇宫飞贼 = { 开关 = false, 贼王 = {} }
			广播消息({ 内容 = "#G/皇宫飞贼活动已经结束，已处战斗中的玩家在战斗结束后依然可以获得奖励。", 频道 = "xt" })
		end,
		["17"] = function() 任务处理类.开启科举大赛() end,
		["17.1"] = function() 任务处理类.结束科举大赛() end,
		
		-- 科举大赛管理命令
		["@清空科举"] = function() 
			local 结果 = 任务处理类.清空科举大赛信息()
			__S服务:输出(结果)
		end,
		["@清空科举排行"] = function() 
			local 结果 = 任务处理类.清空科举排行()
			__S服务:输出(结果)
		end,
		["@清空科举雕像"] = function() 
			local 结果 = 任务处理类.清空科举雕像信息()
			__S服务:输出(结果)
		end,
		["@重载科举雕像"] = function() 
			local 结果 = 任务处理类.重载科举雕像()
			__S服务:输出(结果)
		end,
		["@修复科举染色"] = function() 
			local 结果 = 任务处理类.修复科举雕像染色格式()
			__S服务:输出(结果)
		end,
		["@测试科举染色"] = function() 
			local 结果 = 任务处理类.测试科举雕像染色()
			__S服务:输出("科举雕像染色测试完成，详情请查看控制台输出")
		end,
		["@测试科举雕像"] = function() 
			local 结果 = 任务处理类.手动更新科举雕像("测试状元", "测试榜眼", "测试探花")
			__S服务:输出(结果)
		end,
		["@测试真实雕像"] = function()
			local 结果 = 任务处理类.测试真实玩家雕像()
			__S服务:输出(结果)
		end,
		["@清除测试称谓"] = function()
			local 结果 = 任务处理类.清除测试称谓()
			__S服务:输出(结果)
		end,
		["小龟快跑开启"] = function() 任务处理类:开启小龟快跑() end,
		["18"] = function() 门派闯关:开启活动() end,
		["19"] = function() 任务处理类:年兽活动() end,
		["20"] = function() 任务处理类:知了王活动() end,
		["21"] = function() 任务处理类:开启迷宫() end,
		["21.1"] = function() 迷宫数据.开关 = false end,
		["22"] = function() 雁塔地宫:每周归零() end,
		["23"] = function() 任务处理类:开启宝藏山() end,
		["24"] = function()
			if 游泳活动 and type(游泳活动.开启活动) == "function" then
				游泳活动:开启活动()
			else
				__S服务:输出("警告：游泳活动对象未正确初始化，无法开启活动")
			end
		end,
		["24.1"] = function()
			-- 手动重置游泳活动数据
			if 游泳活动 then
				游泳活动.数据 = {}
				游泳活动.退出战斗记录 = {}
				__S服务:输出("游泳活动数据已手动重置")
				广播消息({内容="#G/管理员已重置游泳活动数据，所有玩家现在可以重新参加游泳比赛。", 频道="xt"})
			else
				__S服务:输出("警告：游泳活动对象未正确初始化，无法重置数据")
			end
		end,
		["25"] = function() 彩虹争霸:开启报名() end,
		["26"] = function() 彩虹争霸:开启彩虹进场() end,
		["27"] = function() 彩虹争霸:正式开始比赛() end,
		["28"] = function() 文韵墨香:开启活动() end,
		["29"] = function() 文韵墨香:关闭活动() end,
		["30"] = function() 任务处理类:开启镖王活动() end,
		["30.1"] = function()
			镖王活动.开关 = false
			广播消息({ 内容 = "#G/镖王大赛活动已经结束，所有处于战斗中的玩家将被强制退出战斗。", 频道 = "xt" })
			for n, v in pairs(战斗准备类.战斗盒子) do
				if v.战斗类型 == 100025 then
					v:结束战斗(0, 0, 1)
				end
			end
			for n, v in pairs(玩家数据) do
				if v.角色:取任务(208) ~= 0 then
					v.角色:取消任务(v.角色:取任务(208))
					常规提示(n, "#Y你的镖王任务已经被自动取消")
				end
			end
			for n, v in pairs(任务数据) do
				if v.类型 == 208 then
					任务数据[n] = nil
				end
			end
		end,
		["31"] = function() 帮派迷宫:开启活动() end,
		["32"] = function()
			地煞星:刷新资源()
			天罡星:刷新资源()
			星辰挑战:刷新资源()
			星官:刷新资源()
		end,
		-- 古董商人相关命令
		["古董商人"] = function()
			if 古董商人 then
				古董商人:手动刷新()
				__S服务:输出("古董商人已手动刷新")
			else
				__S服务:输出("古董商人系统未加载")
			end
		end,
		["古董商人状态"] = function()
			if 古董商人 then
				local 状态 = 古董商人:获取状态()
				if 状态.存在 then
					__S服务:输出(string.format("古董商人当前在地图1110坐标(%d,%d)，剩余时间%d秒，商品库存%s",
						状态.位置.x, 状态.位置.y, 状态.剩余时间, 状态.商品状态))
				else
					__S服务:输出(string.format("古董商人当前不在线，下次刷新时间还有%d秒", 状态.下次刷新时间))
				end
			else
				__S服务:输出("古董商人系统未加载")
			end
		end,
		-- 状态污染检测命令
		["检测状态污染"] = function()
			local 状态污染检测工具 = require("Script/工具/状态污染检测工具")
			状态污染检测工具:输出检测报告()
		end,
		["修复状态污染"] = function()
			local 状态污染检测工具 = require("Script/工具/状态污染检测工具")
			local 修复结果 = 状态污染检测工具:自动修复状态污染()
			if 修复结果.修复数量 > 0 then
				__S服务:输出(string.format("状态污染修复完成，修复了%d个玩家的状态", 修复结果.修复数量))
				for _, 详情 in ipairs(修复结果.修复详情) do
					__S服务:输出(string.format("  - %s (ID:%d) 原因:%s", 详情.名称, 详情.id, 详情.原因))
				end
			else
				__S服务:输出("未发现需要修复的状态污染")
			end
		end,
		["完整状态检测"] = function()
			local 状态污染检测工具 = require("Script/工具/状态污染检测工具")
			状态污染检测工具:执行完整检测()
		end,
		["33"] = function() 任务处理类:帮派强盗() end,
		-- 帮派强盗相关命令
		["帮派强盗"] = function(帮派编号)
			if 帮派编号 and tonumber(帮派编号) then
				任务处理类:帮派强盗_指定帮派(tonumber(帮派编号))
			else
				__S服务:输出("用法: 帮派强盗 [帮派编号]")
				__S服务:输出("例如: 帮派强盗 1")
			end
		end,
		["刷出强盗"] = function(帮派编号)
			if 帮派编号 and tonumber(帮派编号) then
				local 结果 = 任务处理类:帮派强盗_指定帮派(tonumber(帮派编号))
				if 结果 then
					__S服务:输出("成功为帮派编号 " .. 帮派编号 .. " 刷出强盗")
				else
					__S服务:输出("刷出强盗失败，请检查帮派编号是否正确")
				end
			else
				__S服务:输出("用法: 刷出强盗 [帮派编号]")
				__S服务:输出("例如: 刷出强盗 1")
			end
		end,
		["全服刷强盗"] = function()
			任务处理类:帮派强盗()
			__S服务:输出("已为所有帮派刷出强盗")
		end,
		["清理强盗"] = function(帮派编号)
			local 清理数量 = 0
			if 帮派编号 and tonumber(帮派编号) then
				-- 清理指定帮派的强盗
				local 目标帮派 = tonumber(帮派编号)
				for 任务id, 任务信息 in pairs(任务数据) do
					if 任务信息.类型 == 35 and 任务信息.帮派编号 == 目标帮派 then
						地图处理类:删除单位(任务信息.地图编号, 任务信息.单位编号)
						任务数据[任务id] = nil
						清理数量 = 清理数量 + 1
					end
				end
				__S服务:输出("已清理帮派编号 " .. 帮派编号 .. " 的 " .. 清理数量 .. " 个强盗")
			else
				-- 清理所有强盗
				for 任务id, 任务信息 in pairs(任务数据) do
					if 任务信息.类型 == 35 then
						地图处理类:删除单位(任务信息.地图编号, 任务信息.单位编号)
						任务数据[任务id] = nil
						清理数量 = 清理数量 + 1
					end
				end
				__S服务:输出("已清理所有 " .. 清理数量 .. " 个强盗")
			end
		end,
		["强盗状态"] = function()
			local 帮派强盗统计 = {}
			local 总数量 = 0

			-- 统计各帮派的强盗数量
			for 任务id, 任务信息 in pairs(任务数据) do
				if 任务信息.类型 == 35 then
					local 帮派编号 = 任务信息.帮派编号 or "未知"
					if not 帮派强盗统计[帮派编号] then
						帮派强盗统计[帮派编号] = 0
					end
					帮派强盗统计[帮派编号] = 帮派强盗统计[帮派编号] + 1
					总数量 = 总数量 + 1
				end
			end

			__S服务:输出("=== 帮派强盗状态统计 ===")
			__S服务:输出("总强盗数量: " .. 总数量)

			if 总数量 > 0 then
				for 帮派编号, 数量 in pairs(帮派强盗统计) do
					local 帮派名称 = "未知帮派"
					if 帮派编号 ~= "未知" and 帮派数据[帮派编号] then
						帮派名称 = 帮派数据[帮派编号].帮派名称
					end
					__S服务:输出("帮派 " .. 帮派名称 .. " (编号:" .. 帮派编号 .. "): " .. 数量 .. " 个强盗")
				end
			else
				__S服务:输出("当前没有强盗")
			end
		end,
		["强盗帮助"] = function()
			__S服务:输出("=== 帮派强盗管理命令 ===")
			__S服务:输出("全服刷强盗        - 为所有帮派刷出强盗")
			__S服务:输出("刷出强盗 [帮派编号] - 为指定帮派刷出强盗")
			__S服务:输出("帮派强盗 [帮派编号] - 同上（别名）")
			__S服务:输出("清理强盗 [帮派编号] - 清理指定帮派的强盗（不填编号则清理所有）")
			__S服务:输出("强盗状态          - 查看当前强盗分布状态")
			__S服务:输出("强盗刷新状态      - 查看各帮派随机刷新时间状态")
			__S服务:输出("帮派列表          - 查看所有帮派编号和名称")
			__S服务:输出("强盗帮助          - 显示此帮助信息")
			__S服务:输出("")
			__S服务:输出("自动刷新机制:")
			__S服务:输出("  系统会为每个帮派在1-3小时内随机刷新强盗")
			__S服务:输出("  每次刷新都会刷出一波新的强盗（15-30个）")
			__S服务:输出("")
			__S服务:输出("示例:")
			__S服务:输出("  刷出强盗 1      - 为编号1的帮派刷出强盗")
			__S服务:输出("  清理强盗 1      - 清理编号1帮派的强盗")
			__S服务:输出("  强盗刷新状态    - 查看各帮派下次刷新时间")
		end,
		["帮派列表"] = function()
			__S服务:输出("=== 服务器帮派列表 ===")
			local 帮派数量 = 0
			for 编号, 帮派信息 in pairs(帮派数据) do
				if 帮派信息 and 帮派信息.帮派名称 then
					__S服务:输出("编号: " .. 编号 .. " | 名称: " .. 帮派信息.帮派名称 .. " | 成员: " .. (帮派信息.成员数量.当前 or 0) .. "/" .. (帮派信息.成员数量.上限 or 0))
					帮派数量 = 帮派数量 + 1
				end
			end
			__S服务:输出("总计: " .. 帮派数量 .. " 个帮派")
		end,
		["强盗刷新状态"] = function()
			__S服务:输出("=== 帮派强盗随机刷新状态 ===")
			local 当前时间 = os.time()

			if not 帮派强盗随机刷新 then
				__S服务:输出("随机刷新系统未初始化")
				return
			end

			for 帮派编号, 帮派信息 in pairs(帮派数据 or {}) do
				if 帮派信息 and 帮派信息.帮派名称 and 帮派信息.成员数量.当前 > 0 then
					local 刷新数据 = 帮派强盗随机刷新[帮派编号]
					if 刷新数据 then
						local 距离下次刷新 = 刷新数据.下次刷新时间 - 当前时间
						local 小时 = math.floor(距离下次刷新 / 3600)
						local 分钟 = math.floor((距离下次刷新 % 3600) / 60)

						-- 统计当前强盗数量
						local 当前强盗数量 = 0
						for 任务id, 任务信息 in pairs(任务数据 or {}) do
							if 任务信息.类型 == 35 and 任务信息.帮派编号 == 帮派编号 then
								当前强盗数量 = 当前强盗数量 + 1
							end
						end

						if 距离下次刷新 > 0 then
							__S服务:输出("帮派 " .. 帮派信息.帮派名称 .. " (编号:" .. 帮派编号 .. ") | 强盗数量: " .. 当前强盗数量 .. " | 下次刷新: " .. 小时 .. "小时" .. 分钟 .. "分钟后")
						else
							__S服务:输出("帮派 " .. 帮派信息.帮派名称 .. " (编号:" .. 帮派编号 .. ") | 强盗数量: " .. 当前强盗数量 .. " | 状态: 等待刷新")
						end
					else
						__S服务:输出("帮派 " .. 帮派信息.帮派名称 .. " (编号:" .. 帮派编号 .. ") | 状态: 未初始化")
					end
				end
			end
		end,
		["34"] = function()
			任务处理类:调皮的泡泡(id)
			任务处理类:雪人小动物()
			任务处理类:天降雪人物品()
			任务处理类:开启王婆西瓜()
		end,
		["35"] = function() 降妖伏魔:开启活动() end,
		["36"] = function() 降妖伏魔:刷出怪物() end,
		-- 障碍管理命令
		["设置墙障碍"] = function()
			__S服务:输出("开始设置墙障碍...")

			local 成功, 错误 = pcall(function()
				local 运行时障碍管理器 = require("Script/工具/运行时障碍管理器")
				local 障碍管理器 = 运行时障碍管理器()

				-- 设置墙障碍参数
				local 地图ID = 1001
				local 起始x = 226
				local 起始y = 115
				local 墙长度 = 10
				local 墙厚度 = 2

				__S服务:输出("创建障碍管理器成功，开始添加障碍...")

				-- 创建墙障碍
				障碍管理器:添加矩形障碍(地图ID, 起始x, 起始y, 起始x + 墙长度 - 1, 起始y + 墙厚度 - 1, "墙障碍")

				__S服务:输出("障碍添加完成，开始保存配置...")

				-- 保存配置
				local 保存成功 = 障碍管理器:保存配置("Script/工具/地图障碍设置.lua")
				if 保存成功 ~= false then
					__S服务:输出("配置保存成功")
				else
					__S服务:输出("配置保存失败")
				end

				__S服务:输出("墙障碍设置完成！地图1001坐标(226,115)到(235,116)")
				障碍管理器:打印统计信息()
			end)

			if not 成功 then
				__S服务:输出("设置墙障碍失败: " .. tostring(错误))
			end
		end,
		["测试障碍"] = function()
			local 运行时障碍管理器 = require("Script/工具/运行时障碍管理器")
			local 障碍管理器 = 运行时障碍管理器()

			-- 加载配置
			障碍管理器:加载配置("Script/工具/地图障碍设置.lua")

			-- 测试特定坐标
			local 测试结果 = 障碍管理器:检查动态障碍(1001, 226, 115)
			if 测试结果 then
				__S服务:输出("坐标(226,115)有障碍: " .. (测试结果.是否障碍 and "是" or "否"))
			else
				__S服务:输出("坐标(226,115)没有动态障碍设置")
			end

			-- 显示统计
			障碍管理器:打印统计信息()
		end,
		["清除障碍"] = function()
			local 运行时障碍管理器 = require("Script/工具/运行时障碍管理器")
			local 障碍管理器 = 运行时障碍管理器()

			-- 清空所有障碍
			障碍管理器:清空所有修改()

			-- 保存空配置
			障碍管理器:保存配置("Script/工具/地图障碍设置.lua")

			__S服务:输出("所有障碍已清除")
		end,
		["37"] = function()
			print("当前服务器时间 " .. "[周" .. tonumber(os.date("%w", os.time())) .. " " ..
				服务端参数.小时 .. "/" .. 服务端参数.分钟 .. "/" .. 服务端参数.秒 .. "]")
		end,
		["38"] = function() 梦魇夜叉:开启() end,
		-- 天气设置
		["40"] = function() 时辰信息.天气 = 0 end,
		["41"] = function() 时辰信息.天气 = 1 end,
		["42"] = function() 时辰信息.天气 = 2 end,
		["43"] = function() 时辰信息.天气 = 3 end,
		["44"] = function() 二八星宿:刷新资源() end,
		["45"] = function()
			任务处理类:宝图妖王(id)
			任务处理类:宝图封妖(id)
		end,
		-- 新区重置命令
		["新区重置"] = function()
			local 新区重置工具 = require("Script/工具/新区重置工具")
			新区重置工具.执行完整重置()
		end,
		["重置游戏数据"] = function()
			local 新区重置工具 = require("Script/工具/新区重置工具")
			新区重置工具.重置游戏数据()
		end,
		["清空玩家数据"] = function()
			local 新区重置工具 = require("Script/工具/新区重置工具")
			新区重置工具.清空玩家数据()
		end,
		["重置内存数据"] = function()
			local 新区重置工具 = require("Script/工具/新区重置工具")
			新区重置工具.重置内存数据()
		end,
		-- 手动重置双倍经验
		["重置双倍经验"] = function()
			重置双倍数据()
			__S服务:输出("已手动重置所有玩家的双倍经验数据")
		end,
		-- 手动重置所有副本（任务、数据、进度）
		["重置副本"] = function()
			处理副本重置()
			__S服务:输出("已手动重置所有副本数据，并执行静态检查")
		end,
		-- 数据文件修复命令
		["修复数据文件"] = function()
			local 数据文件修复工具 = require("Script/工具/数据文件修复工具")
			数据文件修复工具.修复所有文件()
		end,
		["检查数据文件"] = function()
			local 数据文件修复工具 = require("Script/工具/数据文件修复工具")
			数据文件修复工具.检查所有文件()
		end,
		-- 全局数据初始化命令
		["初始化全局数据"] = function()
			local 全局数据初始化工具 = require("Script/工具/全局数据初始化工具")
			全局数据初始化工具.执行完整初始化()
		end,
		["检查数据完整性"] = function()
			local 全局数据初始化工具 = require("Script/工具/全局数据初始化工具")
			全局数据初始化工具.检查数据完整性()
		end,

		["强制重置全局数据"] = function()
			local 全局数据初始化工具 = require("Script/工具/全局数据初始化工具")
			全局数据初始化工具.强制重置所有数据()
		end,
		["重置科举雕像"] = function()
			if 任务处理类 and 任务处理类.清空科举雕像信息 then
				local 结果 = 任务处理类.清空科举雕像信息()
				__S服务:输出("=== 科举雕像重置 ===")
				__S服务:输出(结果)
			else
				__S服务:输出("科举雕像重置功能未加载")
			end
		end,

		["清除旧版科举称谓"] = function()
			if 任务处理类 and 任务处理类.清除旧版科举称谓 then
				local 结果 = 任务处理类.清除旧版科举称谓()
				__S服务:输出("=== 清除旧版科举称谓 ===")
				__S服务:输出(结果)
				__S服务:输出("现在玩家需要前往金銮殿找唐王手动领取称谓")
			else
				__S服务:输出("清除旧版科举称谓功能未加载")
			end
		end,
		-- 帮派战相关
		["50"] = function() 帮派PK:开启帮战报名() end,
		["51"] = function() 帮派PK:开启帮战进场() end,
		["52"] = function() 帮派PK:正式开始帮战() end,
		["53"] = function() 帮派PK:刷出场景怪() end,
		["54"] = function() 帮派PK:强制计算胜利方() end,
		["55"] = function() 帮派PK:发放帮战宝箱() end,
		["56"] = function() 重置数据() end,
		["57"] = function()
			for n, v in pairs(玩家数据) do
				if v ~= nil then
					v.角色:检查临时属性()
					print("检测临时符属性")
					if v.召唤兽 then
						v.召唤兽:检查临时属性()
					end
				end
			end
		end,
		["58"] = function() 系统处理类:藏宝阁更新() end,

		-- 首席争霸相关
		["@首席争霸"] = function()
			广播消息({ 内容 = "#Y/首席争霸赛活动已经开启入场，10分钟后将无法进入比赛地图。请各位玩家提前找本门派首席弟子进入比赛地图。", 频道 = "xt" })
			首席争霸赛进场开关 = true
			首席争霸赛战斗开关 = false
		end,
		["@kqsxzb"] = function() 任务处理类:开启首席争霸赛() end,
		["@kqsxbw"] = function() 任务处理类:结束首席争霸门派(1, 3, 0, 0) end,
		["@scpssp"] = function() 商店处理类:刷新跑商商品买入价格() end,
		["@testps"] = function() 商店处理类:测试跑商价格() end,
		["@psly"] = function() 商店处理类:分析跑商利润() end,
		["@刷新跑商价格"] = function()
			商店处理类:刷新跑商商品卖出价格()
			__S服务:输出("跑商卖出价格已刷新")
		end,
		["@51"] = function() 神兵异兽榜:初始化数据() end,
		["@52"] = function() 神兵异兽榜:初始化计算() end,
		["@53"] = function() 神兵异兽榜:初始化统计() end,
		["@54"] = function() 神兵异兽榜:存档() end,
		["@1111"] = function()
			任务处理类:刷出师门守卫()
		end,
		["102"] = function()
			-- 获取在线玩家列表
			local 在线玩家列表 = {}
			for id, _ in pairs(玩家数据) do
				if type(id) == "number" and 玩家数据[id].连接id and 玩家数据[id].连接id ~= "假人" then
					table.insert(在线玩家列表, id)
				end
			end

			if #在线玩家列表 > 0 then
				local 随机玩家ID = 在线玩家列表[取随机数(1, #在线玩家列表)]
				设置任务102(随机玩家ID)
				__S服务:输出("已触发宝图宝宝任务，随机选择玩家ID: " .. 随机玩家ID)
			else
				__S服务:输出("当前没有在线玩家，无法触发宝图宝宝任务")
			end
		end,
		["102.1"] = function()
			-- 获取第一个在线玩家ID
			local 在线玩家列表 = {}
			for id, _ in pairs(玩家数据) do
				if type(id) == "number" and 玩家数据[id].连接id and 玩家数据[id].连接id ~= "假人" then
					table.insert(在线玩家列表, id)
				end
			end

			if #在线玩家列表 > 0 then
				local 玩家ID = 在线玩家列表[1]
				设置任务102(玩家ID)
				__S服务:输出("已为第一个在线玩家ID " .. 玩家ID .. " 触发宝图宝宝任务")
			else
				__S服务:输出("当前没有在线玩家，无法触发宝图宝宝任务")
			end
		end,
		-- ========== 动态障碍控制命令 ==========
		-- 障碍方案设置命令
		["障碍1"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.设置障碍方案(1, 1001)
			__S服务:输出("已设置障碍方案1：副本入口障碍")
		end,
		["障碍2"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.设置障碍方案(2, 1001)
			__S服务:输出("已设置障碍方案2：BOSS房间障碍")
		end,
		["障碍3"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.设置障碍方案(3, 1001)
			__S服务:输出("已设置障碍方案3：传送点障碍")
		end,
		["障碍4"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.设置障碍方案(4, 1001)
			__S服务:输出("已设置障碍方案4：活动区域障碍")
		end,
		["障碍5"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.设置障碍方案(5, 1001)
			__S服务:输出("已设置障碍方案5：PVP竞技场障碍")
		end,

		-- 清除障碍命令
		["清除障碍1001"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.清除地图障碍(1001)
			__S服务:输出("已清除地图1001的所有障碍")
		end,
		["清除全部障碍"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.清除所有障碍()
			__S服务:输出("已清除所有地图的障碍")
		end,

		-- 查询障碍状态命令
		["查询障碍"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			local 方案序号, 方案名称 = 游戏障碍集成.查询激活方案(1001)
			if 方案序号 then
				__S服务:输出("地图1001当前激活方案：" .. 方案序号 .. " - " .. 方案名称)
			else
				__S服务:输出("地图1001当前无激活的障碍方案")
			end
		end,
		["列出障碍方案"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.列出所有方案()
		end,

		-- ========== 锻刀村副本专用障碍命令 ==========
		["锻刀村障碍6"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.设置障碍方案(6, 6100)
			__S服务:输出("已设置锻刀村障碍方案6：村庄入口封锁")
		end,
		["锻刀村障碍7"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.设置障碍方案(7, 6100)
			__S服务:输出("已设置锻刀村障碍方案7：战斗区域限制")
		end,
		["锻刀村障碍8"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.设置障碍方案(8, 6100)
			__S服务:输出("已设置锻刀村障碍方案8：BOSS房间封锁")
		end,
		["锻刀村障碍9"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.设置障碍方案(9, 6100)
			__S服务:输出("已设置锻刀村障碍方案9：迷宫路径")
		end,
		["清除锻刀村障碍"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.清除地图障碍(6100)
			__S服务:输出("已清除锻刀村地图(6100)的所有障碍")
		end,

		-- ========== 自定义障碍命令 ==========
		["自定义障碍"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.设置障碍方案(10, 6100)
			__S服务:输出("已在锻刀村坐标(21,128)设置障碍")
		end,
		["锻刀村21128障碍"] = function()
			local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
			游戏障碍集成.设置障碍方案(10, 6100)
			__S服务:输出("已在锻刀村坐标(21,128)设置障碍")
		end,

		-- ========== ID特效管理命令 ==========
		["@ID特效状态"] = function()
			if ID特效管理器 then
				local 统计 = ID特效管理器:获取特效统计()
				__S服务:输出("ID特效使用统计:")
				for 特效类型, 数量 in pairs(统计) do
					__S服务:输出(string.format("  %s: %d人", 特效类型, 数量))
				end
				local 可用特效 = ID特效管理器:获取可用特效类型()
				__S服务:输出("可用特效类型: " .. table.concat(可用特效, ", "))
			else
				__S服务:输出("ID特效管理器未启动")
			end
		end,

		-- ========== 热更新系统命令 ==========
		["@热更新"] = function()
			if 热更新系统 then
				local 状态 = 热更新系统:获取状态()
				__S服务:输出(string.format("热更新状态 - 监控模块:%d 待更新:%d 安全模式:%s 运行时间:%d秒",
					状态.监控模块数, 状态.待更新数, 状态.安全模式 and "开" or "关", 状态.运行时间))
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@手动更新"] = function()
			if 热更新系统 then
				-- 示例：手动更新网络处理类
				热更新系统:手动更新模块("Script/系统处理类/网络处理类")
				__S服务:输出("已触发手动更新网络处理类")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@安全模式开"] = function()
			if 热更新系统 then
				热更新系统:设置安全模式(true)
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@安全模式关"] = function()
			if 热更新系统 then
				热更新系统:设置安全模式(false)
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新掉落控制"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/系统/物品掉落控制")
				__S服务:输出("已触发物品掉落控制系统热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@热更新调试"] = function()
			if 热更新系统 then
				-- 强制检查所有文件
				热更新系统:强制检查所有文件()
				__S服务:输出("已强制检查所有监控文件")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新摆摊假人数据"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/假人处理类/摆摊假人数据")
				__S服务:输出("已触发摆摊假人数据热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新摆摊假人"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/假人处理类/摆摊假人")
				__S服务:输出("已触发摆摊假人处理类热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新假人玩家"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/假人处理类/假人玩家")
				__S服务:输出("已触发假人玩家处理类热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新假人事件"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/假人处理类/假人事件类")
				__S服务:输出("已触发假人事件类热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		-- 副本任务热更新命令
		["@更新大闹天宫"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/副本任务/大闹天宫")
				__S服务:输出("已触发大闹天宫副本热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新齐天大圣"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/副本任务/齐天大圣")
				__S服务:输出("已触发齐天大圣副本热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新红孩儿"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/副本任务/红孩儿")
				__S服务:输出("已触发红孩儿副本热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新秘境降妖"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/副本任务/秘境降妖")
				__S服务:输出("已触发秘境降妖副本热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新五庄观"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/副本任务/五庄观")
				__S服务:输出("已触发五庄观副本热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新七绝山"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/副本任务/七绝山")
				__S服务:输出("已触发七绝山副本热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新双城记"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/副本任务/双城记")
				__S服务:输出("已触发双城记副本热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新轮回境"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/副本任务/轮回境副本")
				__S服务:输出("已触发轮回境副本热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新团队副本"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/系统处理类/团队副本类")
				__S服务:输出("已触发团队副本类热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新任务处理"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务处理类/任务处理类")
				__S服务:输出("已触发任务处理类热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		-- 批量热更新命令
		["@批量更新任务_小九"] = function()
			if 热更新系统 then
				热更新系统:批量更新任务_小九()
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新中秋任务"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/中秋任务")
				__S服务:输出("已触发中秋任务热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新地煞星"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/地煞星")
				__S服务:输出("已触发地煞星任务热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新天罡星"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/天罡星")
				__S服务:输出("已触发天罡星任务热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		["@更新星辰任务"] = function()
			if 热更新系统 then
				-- 批量更新所有星辰相关任务
				local 星辰任务列表 = {
					"Script/任务_小九/二八星宿",
					"Script/任务_小九/天降辰星",
					"Script/任务_小九/星辰挑战",
					"Script/任务_小九/星官"
				}
				for _, 模块名 in ipairs(星辰任务列表) do
					热更新系统:手动更新模块(模块名)
				end
				__S服务:输出("已触发所有星辰任务热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		-- 临时任务热更新命令
		["@更新知了王"] = function()
			if 热更新系统 then
				热更新系统:手动更新模块("Script/任务_小九/临时任务/知了王")
				__S服务:输出("已触发知了王任务热更新")
			else
				__S服务:输出("热更新系统未启动")
			end
		end,
		-- 调试模式控制命令
		["@调试模式"] = function()
			local 状态 = 获取调试模式状态()
			__S服务:输出(string.format("调试模式状态 - 引擎调试:%s 本地开关:%s 当前状态:%s",
				tostring(状态.引擎调试), tostring(状态.本地开关), tostring(状态.当前状态)))
		end,
		["@开启调试"] = function()
			切换调试模式(true)
		end,
		["@关闭调试"] = function()
			切换调试模式(false)
		end,
		["@切换调试"] = function()
			切换调试模式()
		end,
		-- 剧情测试命令
		["@主线6"] = function()
			-- 设置主线6选择界面
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=6,编号=1,地图=1174,进度=1,分支=0,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 80)
					__S服务:输出(string.format("玩家[%s]已设置为主线6选择界面", v.角色.名称))
				end
			end
		end,
		["@无名鬼城"] = function()
			-- 直接传送到千年怨鬼处开始无名鬼城支线任务
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.等级 = math.max(v.角色.等级, 80)
					地图处理类:npc传送(k, 1130, 50, 50)
					__S服务:输出(string.format("玩家[%s]已传送到千年怨鬼处，可开始无名鬼城支线", v.角色.名称))
				end
			end
		end,
		["@女娲神迹"] = function()
			-- 设置女娲神迹剧情
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=6,编号=2,地图=1174,进度=1,分支=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 80)
					__S服务:输出(string.format("玩家[%s]已设置为女娲神迹剧情", v.角色.名称))
				end
			end
		end,
		["@剧情"] = function()
			-- 查看当前剧情状态
			for k, v in pairs(玩家数据) do
				if v and v.角色 and v.角色.剧情 then
					local 剧情 = v.角色.剧情
					__S服务:输出(string.format("玩家[%s] 剧情状态：", v.角色.名称))
					__S服务:输出(string.format("  主线：%s", tostring(剧情.主线)))
					__S服务:输出(string.format("  进度：%s", tostring(剧情.进度)))
					__S服务:输出(string.format("  分支：%s", tostring(剧情.分支)))
					__S服务:输出(string.format("  编号：%s", tostring(剧情.编号)))
					__S服务:输出(string.format("  地图：%s", tostring(剧情.地图)))
					if 剧情.附加 then
						__S服务:输出(string.format("  附加：%s", table.tostring(剧情.附加)))
					end
				else
					__S服务:输出(string.format("玩家[%s] 无剧情数据", v.角色.名称 or "未知"))
				end
			end
		end,
		["@春十三娘"] = function()
			-- 设置春十三娘战斗剧情
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=6,编号=2,地图=1144,进度=9,分支=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 80)
					__S服务:输出(string.format("玩家[%s]已设置为春十三娘战斗剧情，请去盘丝洞找春十三娘", v.角色.名称))
				end
			end
		end,
		["@飞升牛魔王"] = function()
			-- 设置飞升进度48（牛魔王战斗）
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					-- 设置飞升剧情进度48（牛魔王战斗）
					v.角色.剧情 = {主线=8,编号=1,地图=1145,进度=48,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 135)

					-- 确保有飞升任务
					local 任务id = v.角色:取任务(9209)
					if 任务id == 0 then
						剧情_飞升:添加飞升任务(k)
					end

					-- 设置飞升任务进度为不死壤阶段
					任务id = v.角色:取任务(9209)
					if 任务id ~= 0 then
						任务数据[任务id].分类 = "不死壤"
						任务数据[任务id].子类进程 = 4
					end

					-- 添加魔族任务
					剧情_飞升:添加魔族任务(k)

					-- 刷新任务跟踪
					v.角色:刷新任务跟踪()

					__S服务:输出(string.format("玩家[%s]已设置为飞升进度48（牛魔王战斗），请去火焰山找牛魔王", v.角色.名称))
				end
			end
		end,
		["@飞升大大王"] = function()
			-- 设置飞升进度45（大大王）
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					-- 设置飞升剧情进度45（大大王）
					v.角色.剧情 = {主线=8,编号=1,地图=1134,进度=45,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 135)

					-- 确保有飞升任务
					local 任务id = v.角色:取任务(9209)
					if 任务id == 0 then
						剧情_飞升:添加飞升任务(k)
					end

					-- 设置飞升任务进度为不死壤阶段
					任务id = v.角色:取任务(9209)
					if 任务id ~= 0 then
						任务数据[任务id].分类 = "不死壤"
						任务数据[任务id].子类进程 = 4
					end

					-- 添加魔族任务
					剧情_飞升:添加魔族任务(k)

					-- 刷新任务跟踪
					v.角色:刷新任务跟踪()

					__S服务:输出(string.format("玩家[%s]已设置为飞升进度45（大大王），请去魔王寨找大大王", v.角色.名称))
				end
			end
		end,
		["@飞升鬼将"] = function()
			-- 设置飞升进度52（鬼将战斗）
			-- 【修复】只对第一个在线玩家生效，避免重复任务
			local 目标玩家 = nil
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					目标玩家 = {id = k, 数据 = v}
					break  -- 只处理第一个找到的玩家
				end
			end

			if 目标玩家 then
				local k = 目标玩家.id
				local v = 目标玩家.数据

				-- 先清理现有的飞升任务，避免重复
				for i = 9209, 9222 do
					local 任务id = v.角色:取任务(i)
					if 任务id ~= 0 then
						v.角色:取消任务(任务id)
					end
				end

				-- 【修复】使用宝箱触发，编号设置为3（宝箱编号）
				v.角色.剧情 = {主线=8,编号=3,地图=1202,进度=52,附加={野外战斗=110049}}
				v.角色.等级 = math.max(v.角色.等级, 135)

				-- 添加飞升任务
				剧情_飞升:添加飞升任务(k)

				-- 设置飞升任务进度为不死壤阶段
				local 任务id = v.角色:取任务(9209)
				if 任务id ~= 0 then
					任务数据[任务id].分类 = "不死壤"
					任务数据[任务id].子类进程 = 4
				end

				-- 添加魔族任务
				剧情_飞升:添加魔族任务(k)

				-- 刷新任务跟踪
				v.角色:刷新任务跟踪()

				__S服务:输出(string.format("玩家[%s]已设置为飞升进度52（鬼将战斗），请去无名鬼城找鬼将", v.角色.名称))
			else
				__S服务:输出("没有找到在线玩家")
			end
		end,
		["@魔族飞升"] = function()
			-- 设置魔族飞升分支开始（地藏王不死壤阶段）
			-- 【修复】只对第一个在线玩家生效，避免重复任务
			local 目标玩家 = nil
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					目标玩家 = {id = k, 数据 = v}
					break  -- 只处理第一个找到的玩家
				end
			end

			if 目标玩家 then
				local k = 目标玩家.id
				local v = 目标玩家.数据

				-- 先清理现有的飞升任务，避免重复
				for i = 9209, 9222 do
					local 任务id = v.角色:取任务(i)
					if 任务id ~= 0 then
						v.角色:取消任务(任务id)
					end
				end

				-- 设置飞升剧情进度42（地藏王不死壤阶段）
				v.角色.剧情 = {主线=8,编号=1,地图=1124,进度=42,附加={}}
				v.角色.等级 = math.max(v.角色.等级, 135)

				-- 添加飞升任务
				剧情_飞升:添加飞升任务(k)

				-- 设置飞升任务进度为不死壤阶段
				local 任务id = v.角色:取任务(9209)
				if 任务id ~= 0 then
					任务数据[任务id].分类 = "不死壤"
					任务数据[任务id].子类进程 = 1
				end

				-- 添加魔族任务（牛魔王和春十三娘）
				剧情_飞升:添加魔族任务(k)

				-- 刷新任务跟踪
				v.角色:刷新任务跟踪()

				__S服务:输出(string.format("玩家[%s]已设置为魔族飞升分支开始，请去地府找地藏王", v.角色.名称))
			else
				__S服务:输出("没有找到在线玩家")
			end
		end,
		["@春十三娘飞升"] = function()
			-- 设置春十三娘飞升剧情进度49
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					-- 设置飞升剧情进度49（征求春十三娘同意）
					v.角色.剧情 = {主线=8,编号=2,地图=1144,进度=49,分支=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 135)

					-- 确保有飞升任务
					local 任务id = v.角色:取任务(9209)
					if 任务id == 0 then
						剧情_飞升:添加飞升任务(k)
					end

					-- 设置飞升任务进度为不死壤阶段
					任务id = v.角色:取任务(9209)
					if 任务id ~= 0 then
						任务数据[任务id].分类 = "不死壤"
						任务数据[任务id].子类进程 = 4
					end

					-- 添加魔族任务
					剧情_飞升:添加魔族任务(k)

					-- 刷新任务跟踪
					v.角色:刷新任务跟踪()

					__S服务:输出(string.format("玩家[%s]已设置为春十三娘飞升剧情进度49，请去盘丝洞找春十三娘", v.角色.名称))
				end
			end
		end,
		["@退回不死壤前"] = function()
			-- 退回到不死壤任务之前（进度51）
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					-- 设置飞升剧情进度51（地藏王说要去取不死壤）
					v.角色.剧情 = {主线=8,编号=1,地图=1124,进度=51,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 135)

					-- 确保有飞升任务
					local 任务id = v.角色:取任务(9209)
					if 任务id == 0 then
						剧情_飞升:添加飞升任务(k)
					end

					-- 设置飞升任务进度为不死壤阶段
					任务id = v.角色:取任务(9209)
					if 任务id ~= 0 then
						任务数据[任务id].分类 = "不死壤"
						任务数据[任务id].子类进程 = 1
					end

					-- 添加魔族任务
					剧情_飞升:添加魔族任务(k)

					-- 刷新任务跟踪
					v.角色:刷新任务跟踪()

					__S服务:输出(string.format("玩家[%s]已退回到不死壤任务之前（进度51），请去地府找地藏王", v.角色.名称))
				end
			end
		end,
		["@单人退回不死壤前"] = function()
			-- 只对第一个玩家退回到不死壤任务之前（进度51）
			local 目标玩家 = nil
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					目标玩家 = {id = k, 数据 = v}
					break
				end
			end

			if 目标玩家 then
				local k = 目标玩家.id
				local v = 目标玩家.数据

				-- 设置飞升剧情进度51（地藏王说要去取不死壤）
				v.角色.剧情 = {主线=8,编号=1,地图=1124,进度=51,附加={}}
				v.角色.等级 = math.max(v.角色.等级, 135)

				-- 确保有飞升任务
				local 任务id = v.角色:取任务(9209)
				if 任务id == 0 then
					剧情_飞升:添加飞升任务(k)
				end

				-- 设置飞升任务进度为不死壤阶段
				任务id = v.角色:取任务(9209)
				if 任务id ~= 0 then
					任务数据[任务id].分类 = "不死壤"
					任务数据[任务id].子类进程 = 1
				end

				-- 添加魔族任务
				剧情_飞升:添加魔族任务(k)

				-- 刷新任务跟踪
				v.角色:刷新任务跟踪()

				__S服务:输出(string.format("玩家[%s]已退回到不死壤任务之前（进度51），请去地府找地藏王", v.角色.名称))
			else
				__S服务:输出("没有找到在线玩家")
			end
		end,
		["@女娲进度8"] = function()
			-- 设置女娲神迹剧情进度8，用于测试修复
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=6,编号=1,地图=1103,进度=8,分支=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 80)
					__S服务:输出(string.format("玩家[%s]已设置为女娲神迹进度8，请去女娲神殿找孙悟空触发进度9", v.角色.名称))
				end
			end
		end,
		-- ========== 通用剧情跳转系统 ==========
		-- 主线1剧情跳转
		["@主线1进度1"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=1,编号=1,地图=1003,进度=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 10)
					__S服务:输出(string.format("玩家[%s]已设置为主线1进度1", v.角色.名称))
				end
			end
		end,
		["@主线1进度5"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=1,编号=1,地图=1003,进度=5,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 15)
					__S服务:输出(string.format("玩家[%s]已设置为主线1进度5", v.角色.名称))
				end
			end
		end,
		["@主线1进度10"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=1,编号=1,地图=1003,进度=10,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 20)
					__S服务:输出(string.format("玩家[%s]已设置为主线1进度10", v.角色.名称))
				end
			end
		end,
		-- 主线2剧情跳转
		["@主线2进度1"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=2,编号=1,地图=1003,进度=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 25)
					__S服务:输出(string.format("玩家[%s]已设置为主线2进度1", v.角色.名称))
				end
			end
		end,
		["@主线2进度5"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=2,编号=1,地图=1003,进度=5,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 35)
					__S服务:输出(string.format("玩家[%s]已设置为主线2进度5", v.角色.名称))
				end
			end
		end,
		["@主线2进度10"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=2,编号=1,地图=1003,进度=10,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 45)
					__S服务:输出(string.format("玩家[%s]已设置为主线2进度10", v.角色.名称))
				end
			end
		end,
		-- 主线3剧情跳转
		["@主线3进度1"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=3,编号=1,地图=1173,进度=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 55)
					__S服务:输出(string.format("玩家[%s]已设置为主线3进度1", v.角色.名称))
				end
			end
		end,
		["@主线3进度10"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=3,编号=1,地图=1167,进度=10,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 65)
					__S服务:输出(string.format("玩家[%s]已设置为主线3进度10", v.角色.名称))
				end
			end
		end,
		["@主线3进度婆婆眼睛"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=3,编号=28,地图=1110,进度=38,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 65)
					__S服务:输出(string.format("玩家[%s]已设置为主线3进度10", v.角色.名称))
				end
			end
		end,		
		-- 主线4剧情跳转
		["@主线4进度1"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=4,编号=1,地图=1173,进度=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 75)
					__S服务:输出(string.format("玩家[%s]已设置为主线4进度1", v.角色.名称))
				end
			end
		end,
		-- 主线5剧情跳转
		["@主线5进度1"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=5,编号=1,地图=1173,进度=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 80)
					__S服务:输出(string.format("玩家[%s]已设置为主线5进度1", v.角色.名称))
				end
			end
		end,
		-- 主线6剧情跳转（女娲神迹/无名鬼城）
		["@主线6进度1"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=6,编号=1,地图=1174,进度=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 80)
					__S服务:输出(string.format("玩家[%s]已设置为主线6进度1，可选择女娲神迹", v.角色.名称))
				end
			end
		end,
		["@女娲进度1"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=6,编号=1,地图=1174,进度=1,分支=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 80)
					__S服务:输出(string.format("玩家[%s]已设置为女娲神迹进度1", v.角色.名称))
				end
			end
		end,
		["@女娲进度5"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=6,编号=1,地图=1100,进度=5,分支=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 80)
					__S服务:输出(string.format("玩家[%s]已设置为女娲神迹进度5", v.角色.名称))
				end
			end
		end,
		["@无名鬼城支线"] = function()
			-- 传送到千年怨鬼处，开始支线任务
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.等级 = math.max(v.角色.等级, 80)
					地图处理类:npc传送(k,1130,92,32)
					__S服务:输出(string.format("玩家[%s]已传送到千年怨鬼处，可开始无名鬼城支线", v.角色.名称))
				end
			end
		end,


		["@无名鬼城战斗"] = function()
			-- 直接创建无名鬼城支线战斗
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.等级 = math.max(v.角色.等级, 80)
					__S服务:输出(string.format("玩家[%s]开始无名鬼城支线战斗", v.角色.名称))
					-- 直接创建战斗
					战斗准备类:创建战斗(k+0,110050)
				end
			end
		end,
		-- 主线7剧情跳转
		["@主线7进度1"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=7,编号=2,地图=1114,进度=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 85)
					__S服务:输出(string.format("玩家[%s]已设置为主线7进度1", v.角色.名称))
				end
			end
		end,
		["@主线7进度10"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=7,编号=30,地图=1173,进度=10,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 85)
					__S服务:输出(string.format("玩家[%s]已设置为主线7进度10", v.角色.名称))
				end
			end
		end,
		-- 主线8剧情跳转
		["@主线8进度1"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					v.角色.剧情 = {主线=8,编号=1,地图=1114,进度=1,附加={}}
					v.角色.等级 = math.max(v.角色.等级, 135)
					__S服务:输出(string.format("玩家[%s]已设置为主线8进度1", v.角色.名称))
				end
			end
		end,
		["@完成无名鬼城"] = function()
			-- 直接设置无名鬼城完成状态，用于测试传送功能
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					if not v.角色.历劫 then
						v.角色.历劫 = {}
					end
					v.角色.历劫.无名鬼城 = true
					__S服务:输出(string.format("玩家[%s]已设置为无名鬼城任务完成状态", v.角色.名称))
				end
			end
		end,
		["@重置无名鬼城"] = function()
			-- 重置所有玩家的无名鬼城状态
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					if not v.角色.历劫 then
						v.角色.历劫 = {}
					end
					v.角色.历劫.无名鬼城 = false
					__S服务:输出(string.format("玩家[%s]已重置无名鬼城任务状态", v.角色.名称))
				end
			end
		end,
		-- 【修改】添加飞升测试指令 - GM控制台命令
		["@飞升测试"] = function()
			-- 为所有在线玩家开启飞升测试状态
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					-- 设置等级和技能满足飞升条件
					v.角色.等级 = math.max(v.角色.等级, 135)
					-- 设置师门技能
					for i = 1, #v.角色.师门技能 do
						if v.角色.师门技能[i] then
							v.角色.师门技能[i].等级 = math.max(v.角色.师门技能[i].等级, 130)
						end
					end
					__S服务:输出(string.format("玩家[%s]已设置为飞升测试状态（等级135，技能130）", v.角色.名称))
				end
			end
		end,
		["@飞升进度"] = function()
			-- 显示所有在线玩家的飞升进度
			for k, v in pairs(玩家数据) do
				if v and v.角色 and v.角色.剧情 and v.角色.剧情.主线 == 8 then
					__S服务:输出(string.format("玩家[%s] 飞升进度:%d 地图:%d 编号:%d",
						v.角色.名称, v.角色.剧情.进度, v.角色.剧情.地图, v.角色.剧情.编号))
				end
			end
		end,
		["@设置飞升进度"] = function()
			-- 用法示例：@设置飞升进度 玩家名 进度
			-- 需要手动输入参数，这里提供说明
			__S服务:输出("用法：在控制台输入 设置飞升进度 玩家名 进度")
			__S服务:输出("常用进度：1-开始 3-太上老君 7-玉皇大帝 25-李世民 33-药物问答 42-地藏王")
			__S服务:输出("魔族进度：45-大大王 48-牛魔王战斗 49-春十三娘 50-找回龙镊魂镖 51-地藏王最终")
			__S服务:输出("最终进度：53-最终考验 54-飞升选择")
		end,
		["设置飞升进度"] = function()
			-- 实际的设置飞升进度命令（需要在控制台手动输入参数）
			local 输入 = io.read()
			local 参数 = {}
			for 词 in string.gmatch(输入, "%S+") do
				table.insert(参数, 词)
			end

			if #参数 >= 2 then
				local 玩家名 = 参数[1]
				local 进度 = tonumber(参数[2])

				if 进度 then
					for k, v in pairs(玩家数据) do
						if v and v.角色 and v.角色.名称 == 玩家名 then
							-- 地图和编号映射
							local 地图表 = {
								[1] = {地图=1114, 编号=1},  -- 吴刚开始
								[3] = {地图=1113, 编号=1},  -- 太上老君
								[7] = {地图=1112, 编号=3},  -- 玉皇大帝
								[25] = {地图=1044, 编号=5}, -- 李世民
								[33] = {地图=1154, 编号=1}, -- 神木林药物问答
								[42] = {地图=1124, 编号=1}, -- 地藏王
								[45] = {地图=1134, 编号=1}, -- 大大王
								[46] = {地图=1134, 编号=1}, -- 大大王找宠物
								[47] = {地图=1134, 编号=1}, -- 大大王找宠物
								[48] = {地图=1145, 编号=1}, -- 牛魔王战斗
								[49] = {地图=1144, 编号=2}, -- 春十三娘
								[50] = {地图=1144, 编号=2}, -- 春十三娘找回龙镊魂镖
								[51] = {地图=1124, 编号=1}, -- 地藏王最终
								[52] = {地图=1202, 编号=1}, -- 无名鬼城鬼将军
								[53] = {地图=1112, 编号=3}, -- 最终考验
								[54] = {地图=1114, 编号=1}  -- 飞升选择
							}

							local 设置 = 地图表[进度] or {地图=1114, 编号=1}

							-- 设置剧情进度
							v.角色.剧情 = {
								主线 = 8,
								编号 = 设置.编号,
								地图 = 设置.地图,
								进度 = 进度,
								附加 = {}
							}

							-- 确保有飞升任务
							local 任务id = v.角色:取任务(9209)
							if 任务id == 0 then
								剧情_飞升:添加飞升任务(k)
							end

							-- 根据进度设置任务状态
							任务id = v.角色:取任务(9209)
							if 任务id ~= 0 then
								if 进度 >= 45 then
									任务数据[任务id].分类 = "不死壤"
									任务数据[任务id].子类进程 = 4
									-- 添加魔族任务
									剧情_飞升:添加魔族任务(k)
								elseif 进度 >= 25 then
									任务数据[任务id].分类 = "李世民"
									任务数据[任务id].子类进程 = 1
								elseif 进度 >= 7 then
									任务数据[任务id].分类 = "玉皇大帝找东西"
									任务数据[任务id].子类进程 = 1
								end
							end

							-- 刷新任务跟踪
							v.角色:刷新任务跟踪()

							__S服务:输出(string.format("玩家[%s]飞升进度已设置为:%d", 玩家名, 进度))
							return
						end
					end
					__S服务:输出(string.format("未找到玩家[%s]", 玩家名))
				else
					__S服务:输出("进度必须是数字")
				end
			else
				__S服务:输出("参数不足，用法：玩家名 进度")
			end
		end,
		["@清理飞升任务"] = function()
			-- 清理所有玩家的飞升相关任务
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					-- 清理飞升任务(9209-9222)
					for i = 9209, 9222 do
						local 任务id = v.角色:取任务(i)
						if 任务id ~= 0 then
							v.角色:取消任务(任务id)
							__S服务:输出(string.format("玩家[%s]清理任务类型:%d", v.角色.名称, i))
						end
					end
					-- 刷新任务跟踪
					v.角色:刷新任务跟踪()
				end
			end
			__S服务:输出("所有玩家的飞升任务已清理完成")
		end,
		["@查看玩家任务"] = function()
			-- 查看指定玩家的所有任务
			local 输入 = io.read()
			local 玩家名 = string.match(输入, "%S+")

			if 玩家名 then
				for k, v in pairs(玩家数据) do
					if v and v.角色 and v.角色.名称 == 玩家名 then
						__S服务:输出(string.format("玩家[%s]的任务列表:", 玩家名))
						for i, 任务id in pairs(v.角色.任务) do
							if 任务数据[任务id] then
								__S服务:输出(string.format("  任务ID:%s, 类型:%d, 分类:%s",
									任务id, 任务数据[任务id].类型 or "nil", 任务数据[任务id].分类 or "nil"))
							else
								__S服务:输出(string.format("  任务ID:%s (数据缺失)", 任务id))
							end
						end
						return
					end
				end
				__S服务:输出(string.format("未找到玩家[%s]", 玩家名))
			else
				__S服务:输出("用法：玩家名")
			end
		end,
		-- 五行法阵测试命令
		["@五行法阵测试"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 then
					-- 设置剧情进度到五行法阵战斗之前（进度53）
					v.角色.剧情 = {主线=8,编号=3,地图=1112,进度=53,附加={}}
					-- 确保等级满足飞升要求
					v.角色.等级 = math.max(v.角色.等级, 135)
					-- 设置历劫状态（飞升是渡劫之前的剧情）
					v.角色.历劫.女娲 = true
					v.角色.历劫.飞升 = false  -- 飞升尚未完成
					v.角色.历劫.渡劫 = false  -- 渡劫尚未完成
					-- 传送到玉皇大帝处
					v.角色.地图数据 = {编号=1112, x=400, y=300, 方向=0}
					__S服务:输出(string.format("玩家[%s]已设置为五行法阵测试状态（进度53），请使用@直接五行法阵命令开始战斗", v.角色.名称))
				end
			end
		end,
		-- 直接触发五行法阵战斗（测试专用）
		["@直接五行法阵"] = function()
			for k, v in pairs(玩家数据) do
				if v and v.角色 and v.角色.剧情 and v.角色.剧情.主线 == 8 and v.角色.剧情.进度 == 53 then
					-- 直接创建五行法阵第一场战斗
					战斗准备类:创建战斗(k, 110052, "1112" .. "8" .. "53")
					__S服务:输出(string.format("玩家[%s]开始五行法阵连续战斗测试", v.角色.名称))
				end
			end
		end
	}

	-- 执行命令
	if 命令处理[t] then
		命令处理[t]()
	end
end


-- 帮派强盗随机刷新处理函数
function 处理帮派强盗随机刷新()
	-- 初始化随机刷新数据
	if not 帮派强盗随机刷新 then
		帮派强盗随机刷新 = {}
	end

	-- 为每个帮派设置独立的随机刷新
	for 帮派编号, 帮派信息 in pairs(帮派数据 or {}) do
		if 帮派信息 and 帮派信息.帮派名称 and 帮派信息.成员数量.当前 > 0 then
			-- 初始化该帮派的刷新数据
			if not 帮派强盗随机刷新[帮派编号] then
				帮派强盗随机刷新[帮派编号] = {
					下次刷新时间 = os.time() + 取随机数(3600, 10800), -- 1-3小时
					上次刷新时间 = os.time()
				}
			end

			-- 检查是否到了刷新时间
			local 刷新数据 = 帮派强盗随机刷新[帮派编号]
			if os.time() >= 刷新数据.下次刷新时间 then
				-- 直接刷新一波强盗，不检查数量
				if 任务处理类 and 任务处理类.帮派强盗_指定帮派 then
					任务处理类:帮派强盗_指定帮派(帮派编号)
				end

				-- 设置下次随机刷新时间（1-3小时）
				刷新数据.下次刷新时间 = os.time() + 取随机数(3600, 10800)
				刷新数据.上次刷新时间 = os.time()
			end
		end
	end
end

function 循环函数()
	服务端参数.运行时间 = 服务端参数.运行时间 + 1
	时辰函数()
	if 任务处理类 ~= nil then
		任务处理类:更新(1)  -- 添加dt参数
	end
	-- 热更新系统检查
	if 热更新系统 then
		热更新系统:定期检查()
	end
	
	
	-- 古董商人启动检查机制
	if 古董商人启动检查 and not 古董商人启动检查.已检查 then
		if os.time() >= 古董商人启动检查.检查时间 then
			if 古董商人 then
				print("执行古董商人启动后检查...")
				local 状态 = 古董商人:获取状态()
				if not 状态.存在 then
					print("古董商人启动时刷新失败，执行备份刷新...")
					pcall(function()
						古董商人:手动刷新()
						print("古董商人备份刷新完成")
					end)
				else
					print("古董商人启动时刷新成功，状态正常")
				end
			end
			古董商人启动检查.已检查 = true
		end
	end
	
	if 小龟快跑.次数3 == 3 and 小龟快跑.开关 == true then
		小龟快跑.开关 = false
		系统处理类:小龟快跑结算()
	end
	if os.time() - 服务端参数.启动时间 >= 1 then
		整秒处理(os.date("%S", os.time()))
		服务端参数.启动时间 = os.time()
	end
	local 当前时间 = os.time()
	local 当前秒 = os.date("%S", 当前时间)
	local 当前分 = os.date("%M", 当前时间)
	local 当前时 = os.date("%H", 当前时间)

	if 当前秒 == "00" then
		if os.date("%X", 当前时间) == 当前时 .. ":00:00" then
			整点处理(当前时)
		elseif 服务端参数.分钟 ~= 当前分 then
			整分处理(当前分)
		end
	end
	if os.time() - 刷新时间 >= 1 then
		刷新时间 = os.time()
		限时活动刷新()
	elseif os.time() - 塔怪刷新 >= 600 then
		任务处理类:设置大雁塔怪()
		塔怪刷新 = os.time()
	elseif os.time() - 水果刷新 >= 1200 then
		任务处理类:调皮的泡泡(id)
		水果刷新 = os.time()		
	elseif os.time() - 夏日泡泡刷新 >= 3600 then
		任务处理类:夏日泡泡()
		夏日泡泡刷新 = os.time()
	elseif os.time() - 天罡星刷新 >= 7200 then
		天罡星:刷新资源()
		天罡星刷新=os.time()
	end

	-- 处理帮派强盗随机刷新
	处理帮派强盗随机刷新()
	if os.time() % 300 == 0 then -- 每5分钟自动保存
		写出文件("游戏数据/双倍数据.txt", table.tostring(双倍数据))
		写出文件("游戏数据/三倍数据.txt", table.tostring(三倍数据))
	end
end

function 退出函数()
	保存系统数据()
	-- 确保任务数据也被保存
	保存任务数据()
	os.execute("pause")
end

