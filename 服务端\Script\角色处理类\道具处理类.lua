-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2025-03-16 22:31:00
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-07-06 01:38:58

local 道具处理类 = class()
local 书铁范围 = {"枪矛","斧钺","剑","双短剑","飘带","爪刺","扇","魔棒","锤","鞭","环圈","刀","法杖","弓弩","宝珠","巨剑","伞","灯笼","头盔","发钗","项链","女衣","男衣","腰带","鞋子"}
local 图策范围={"项圈","护腕","铠甲"}
local qz=math.floor
local floor=math.floor
local random = 取随机数
local remove = table.remove
local 右键合成宝石={
	红玛瑙=1,
	太阳石=1,
	舍利子=1,
	黑宝石=1,
	月亮石=1,
	神秘石=1,
	光芒石=1,
	星辉石=1,
}
local 右键合成五宝={
	金刚石=1,
	定魂珠=1,
	避水珠=1,
	夜光珠=1,
	龙鳞=1,
}
function 道具处理类:初始化(id,数字id)
	self.玩家id=数字id
	self.数据={}
	self.阵法名称={
	[3]="普通",
	[4]="风扬阵",
	[5]="虎翼阵",
	[6]="天覆阵",
	[7]="云垂阵",
	[8]="鸟翔阵",
	[9]="地载阵",
	[10]="龙飞阵",
	[11]="蛇蟠阵",
	[12]="鹰啸阵",
	[13]="雷绝阵",
	}
	self.飞行传送点={
	[1]={1001,336,217},
	[2]={1001,358,35},
	[3]={1501,65,112},
	[4]={1092,122,54},
	[5]={1070,106,158},
	[6]={1040,108,98},
	[7]={1226,117,48},
	[8]={1208,128,36},
	}
	self.kediejia={}
	self.kediejia["易经丹"]=99
	self.kediejia["修炼果"]=99
	self.kediejia["五宝盒"]=99
	self.kediejia["超级净瓶玉露"]=99
	self.kediejia["净瓶玉露"]=99
	self.kediejia["超级金柳露"]=99
	self.kediejia["金柳露"]=99
	self.kediejia["金钥匙"]=99
	self.kediejia["银钥匙"]=99
	self.kediejia["铜钥匙"]=99
	self.kediejia["特殊兽决·碎片"]=99
	self.kediejia["60灵饰礼包"]=99
	self.kediejia["80灵饰礼包"]=99
	self.kediejia["100灵饰礼包"]=99
	self.kediejia["120灵饰礼包"]=99
	self.kediejia["140灵饰礼包"]=99
	self.kediejia["60自选灵饰礼包"]=99
	self.kediejia["80自选灵饰礼包"]=99
	self.kediejia["100自选灵饰礼包"]=99
	self.kediejia["120自选灵饰礼包"]=99
	self.kediejia["140自选灵饰礼包"]=99
	self.kediejia["五宝"]=99
	self.kediejia["特技书"]=99
	self.kediejia["九转金丹礼包"]=99
	self.kediejia["160附魔宝珠礼包"]=99
	self.kediejia["特效宝珠礼包"]=99
	self.kediejia["灵犀玉礼包"]=99
	self.kediejia["灵犀之屑"]=99
	self.kediejia["福禄丹"]=99
	self.kediejia["灵犀玉礼包"]=99
	--self.kediejia["神兜兜"]=99
	self.kediejia["授业经验书"]=99
	self.kediejia["普通木材"]=99
	self.kediejia["高级木材"]=99
	self.kediejia["金金子"]=99 -- 新增金金子可叠加
	self.js=os.time()
	self.jingzhi=0
	self.xiaxian=0
end
function 道具处理类:加载数据(账号,数字id)
	self.数字id=数字id
	self.数据=table.loadstring(读入文件(程序目录..[[data/]]..账号..[[/]]..数字id..[[/道具.txt]]))
	if not self.数据 then
		常规提示(数字id,"#Y账号道具异常请与GM联系")
		return
	end
	for n, v in pairs(self.数据) do
		if self.数据[n] == 0 then
			self.数据[n] = nil
		end
	end
end
function 道具处理类:数据处理(连接id,序号,数字id,数据)
	if 玩家数据[数字id].摊位数据~=nil then
		if 序号~=3699 and 序号~=3700 and 序号~=3720 and 序号~=3721 and 序号~=3722 and 序号~=3723 and 序号~=3724 and 序号~=3724.1 and 序号~=3725 then
			常规提示(数字id,"#Y/摆摊状态下禁止此种行为")
			return
		end
	elseif 体验状态开关 and 玩家数据[数字id].角色.体验状态 then
		if 序号==3715 or 序号==3716 or 序号==3717 or 序号==3718 or 序号==3720 or 序号==3722 or 序号==3725 or 序号==3726 then
			常规提示(数字id,"体验状态下无法进行此操作。")
			return
		end
	end
	if 序号==3699 then
		self:索要道具(连接id,数字id)
	elseif 序号==3700 then
		self:索要行囊(连接id,数字id)
	elseif 序号==3701 then
		self:道具格子互换(连接id,数字id,数据)
	elseif 序号==3702 then
		self:丢弃道具(连接id,数字id,数据)
	elseif 序号==3703 then
		self:佩戴装备(连接id,数字id,数据)
	elseif 序号==3704 then
		self:卸下装备(连接id,数字id,数据)
	elseif 序号==3705 then
		self:使用道具(连接id,数字id,数据)
	elseif 序号==3706 then
		self:飞行符传送(连接id,数字id,数据)
	elseif 序号==3740 then
		self:新春飞行符传送(连接id,数字id,数据)
	elseif 序号==3707 then
		发送数据(连接id,14,玩家数据[数字id].道具:索要道具1(数字id))
	elseif 序号==3708 then
		self:佩戴bb装备(连接id,数字id,数据)
	elseif 序号==3708.5 then
		self:佩戴孩子装备(连接id,数字id,数据)
	elseif 序号==3709 then
		self:卸下bb装备(连接id,数字id,数据)
	elseif 序号==3709.5 then
		self:卸下孩子装备(连接id,数字id,数据)
	elseif 序号==3710 then
		self:染色处理(连接id,数字id,数据)
	elseif 序号==3711 then
		玩家数据[数字id].角色:学习门派技能(连接id,数字id,数据.序列)
	elseif 序号==3712 then
		玩家数据[数字id].角色:学习生活技能(连接id,数字id,数据.序列)
	elseif 序号==3713 then
		self:烹饪处理(连接id,数字id,1)
	elseif 序号==3714 then
		self:炼药处理(连接id,数字id,1)
    elseif 序号==3714.1 then
        self:成药处理(连接id,数字id,数据)
	elseif 序号==3715 then
		self:给予处理(连接id,数字id,数据)
	elseif 序号==3716 then
    	玩家数据[数字id].给予数据={类型=2,id=数据.id+0}
    	if 玩家数据[数据.id+0]~=nil then
      	发送数据(连接id,3507,{道具=self:索要道具1(数字id),名称=玩家数据[数据.id+0].角色.名称,类型="玩家",等级=玩家数据[数据.id+0].角色.等级})
    	end
	elseif 序号==3717 then

		self:锁定交易数据(连接id,数字id,数据)
	elseif 序号==3741.5 then
		self:确定交易(连接id,数字id)
	elseif 序号==3718 then
		if 数字id==数据.id+0 then
			return
		end
		self:发起交易处理(连接id,数字id,数据.id)
	elseif 序号==3719 then
		self:取消交易(数字id)
	elseif 序号==3720 then
		self:索要摊位数据(连接id,数字id,3515)
	elseif 序号==3721 then
		self:更改摊位招牌(连接id,数字id,数据.名称)
	elseif 序号==3722 then
		self:摊位上架商品(连接id,数字id,数据)
	elseif 序号==3723 then
		self:摊位下架商品(连接id,数字id,数据)
	elseif 序号==3724 then
		self:收摊处理(连接id,数字id)
	elseif 序号==3724.1 then
		self:查看摊位日志(数字id)
	elseif 序号==3725 then
		self:索要其他玩家摊位(连接id,数字id,数据.id,3521)
	elseif 序号==3726 then
		self:购买摊位商品(连接id,数字id,数据)
	elseif 序号==3726.1 then
		self:购买摊位制造商品(连接id,数字id,数据)
	elseif 序号==3727 then
		self:快捷加血(连接id,数字id,数据.类型)
	elseif 序号==3728 then
		self:快捷加蓝(连接id,数字id,数据.类型)
	elseif 序号==3732 then
		self:索要法宝(连接id,数字id)
	elseif 序号==3733 then
		self:修炼法宝(连接id,数字id,数据.序列)
	elseif 序号==3734 then
		self:卸下法宝(连接id,数字id,数据.序列,数据.神器)
	elseif 序号==3735 then
		self:替换法宝(连接id,数字id,数据.序列,数据.序列1)
	elseif 序号==3736 then
		self:使用法宝(连接id,数字id,数据.序列)
	elseif 序号==3737 then
		self:使用合成旗(连接id,数字id,数据.序列)
	elseif 序号==3739 then
		self:开运(连接id,数字id,数据.格子)
	elseif 序号==3741 then
		self:拆分处理(连接id,数字id,数据)
	elseif 序号==3746 then
		self:道具转移(连接id,数字id,数据)
	elseif 序号==3747 then
		self:坐骑装饰卸下(连接id,数字id,数据)
	elseif 序号==3748 then
		-- 检查是否是迎客僧
		if 数据.名称 == "迎客僧" then
			玩家数据[数字id].给予数据={类型=1,id=0,事件="迎客僧给与银子",地图=数据.地图,编号=数据.编号,序号=数据.序号,标识=数据.标识,名称=数据.名称,模型=数据.模型}
			发送数据(连接id,3530,{道具=self:索要道具1(数字id),名称=数据.名称,类型="NPC",等级="无"})
		else
			玩家数据[数字id].给予数据={类型=1,地图=数据.地图,编号=数据.编号,序号=数据.序号,标识=数据.标识,名称=数据.名称,模型=数据.模型}
			发送数据(连接id,3530,{道具=self:索要道具1(数字id),名称=数据.名称,类型="NPC"})
		end
	elseif 序号==3749 then
		self:临时背包处理(连接id,数字id,数据)
	elseif 序号==3750 then
		self:索要任务(连接id,数字id)
	elseif 序号==3751 then
		self:索要道具更新(数字id,数据.类型)
	elseif 序号==3752 then
		self:修炼灵宝(连接id,数字id,数据.序列)
	elseif 序号==3753 then
		self:卸下灵宝(连接id,数字id,数据.序列)
	elseif 序号==3754 then
		self:替换灵宝(连接id,数字id,数据.序列,数据.序列1)
	elseif 序号==3755 then
		self:使用灵宝(连接id,数字id,数据.序列)
	elseif 序号==3738 then
		self:符纸使用(连接id,数字id,数据)
	elseif 序号==3758 then
		self:鉴定专用装备(连接id,数字id,数据)
	elseif 序号==3759 then
		self:吸附兽诀(连接id,数字id,数据)
	elseif 序号==3760 then
		降妖伏魔:铃铛抽奖(连接id,数字id,数据)
	elseif 序号==3761 then
		降妖伏魔:铃铛处理(连接id,数字id,数据)
	elseif 序号==3762 then
		self:宝宝进阶(连接id,数字id,数据)
	elseif 序号==3763 then
		self:装备点化套装(连接id,数字id,数据)
	elseif 序号==3799 then
		队伍处理类:玩家心情处理(连接id,数字id,数据)
	elseif 序号==3800 then
		if 数据.类型=="道具" then
			玩家数据[数字id].符石镶嵌=玩家数据[数字id].角色.道具[数据.装备]
		else
			玩家数据[数字id].符石镶嵌=玩家数据[数字id].角色.行囊[数据.装备]
		end
		发送数据(玩家数据[数字id].连接id,3550,{装备=self.数据[玩家数据[数字id].符石镶嵌]})
	elseif 序号==3801 then
		self:装备镶嵌符石(数字id,数据.内容)
	elseif 序号==3802 then
		self:翻转星石对话(数字id)
	elseif 序号==3803 then
		self:合成符石(连接id,数字id,数据)
	elseif 序号==3770 then
		self:装备开启星位(连接id,数字id,数据)
	elseif 序号==3780 then
		if 数据.道具类型 == "道具" then
			发送数据(玩家数据[数字id].连接id,3535,{类型="道具",数据=self:索要道具2(数字id)})
		elseif 数据.道具类型 == "行囊" then
			发送数据(玩家数据[数字id].连接id,3535,{类型="行囊",数据=self:索要行囊2(数字id)})
		end
	elseif 序号==3781 then
		self:获取光武拓印(连接id,数字id,数据)
	elseif 序号==3782 then
		发送数据(连接id,233,{nil,nil,nil,nil,"光武拓印"})
	elseif 序号==3783 then
		self:光武拓印转化(连接id,数字id,数据)
	elseif 序号==3784 then
		self:宝宝坐骑喂养(连接id,数字id,数据)
	elseif 序号==3785 then
		self:一键出售道具(连接id,数字id)
	elseif 序号==3751.1 then
		self:加锁物品(连接id,数字id,数据)
	elseif 序号==3751.2 then
		self:解锁物品(连接id,数字id,数据)
	elseif 序号==3751.3 then
		玩家数据[数字id].角色:物品密码设置(连接id,数字id,数据)
	elseif 序号==3751.4 then
		玩家数据[数字id].角色:物品密码解锁(连接id,数字id,数据)
	elseif 序号==3751.5 then
		玩家数据[数字id].角色:删除角色(数字id,数据)
	elseif 序号==3811 then
		self:灵犀之屑合成(连接id,数字id,数据)
	elseif 序号==3813 then
		self:镶嵌灵犀玉(数字id,数据)
	elseif 序号==3814 then
		self:激活天赋符(连接id,数字id,数据)
	elseif 序号==3815 then
		self:整理道具(连接id,数字id)
	end
	数据=nil
end
function 道具处理类:整理道具(连接id, id, 数据)
    local data = {}
    local function 简易排序(a, b)
        return 排序整理(a.序号) < 排序整理(b.序号)
    end
    for k, v in pairs(玩家数据[id].角色.道具) do
        -- 确保 self.数据[v] 不为 nil
        if self.数据[v] then
            -- 将道具内容和序号添加到临时表中
            data[#data + 1] = {内容 = v, 序号 = self.数据[v].名称}
            -- 检查物品是否可叠加并小于最大叠加数量
            if self.数据[v].数量 ~= nil and self.数据[v].可叠加 then
                local maxStack = self.kediejia[self.数据[v].名称] or 999 -- 动态获取最大叠加数量，默认为 999
                if self.数据[v].数量 < maxStack then
                    for i, n in pairs(玩家数据[id].角色.道具) do
                        -- 判断是否是相同物品，并且叠加后不会超出最大叠加数量
                        if k ~= i and self.数据[n] ~= nil and self.数据[v] ~= nil and self.数据[n].名称 == self.数据[v].名称 and self.数据[n].数量 ~= nil and self.数据[v].数量 + self.数据[n].数量 <= maxStack then
                            -- 特殊处理：判断是否有额外限制（如灵气、级别限制）
                            if (self.数据[n].名称 == "初级清灵仙露" and self.数据[v].名称 == "初级清灵仙露") or
                               (self.数据[n].名称 == "中级清灵仙露" and self.数据[v].名称 == "中级清灵仙露") or
                               (self.数据[n].名称 == "高级清灵仙露" and self.数据[v].名称 == "高级清灵仙露") or
                               (self.数据[n].名称 == "高级摄灵珠" and self.数据[v].名称 == "高级摄灵珠") then
                                if self.数据[v].灵气 == self.数据[n].灵气 then
                                    self.数据[v].数量 = self.数据[v].数量 + self.数据[n].数量
                                    self.数据[n] = nil
                                    玩家数据[id].角色.道具[i] = nil
                                end
                            elseif self.数据[n].名称 == "钨金" and self.数据[v].名称 == "钨金" then
                                if self.数据[v].级别限制 == self.数据[n].级别限制 then
                                    self.数据[v].数量 = self.数据[v].数量 + self.数据[n].数量
                                    self.数据[n] = nil
                                    玩家数据[id].角色.道具[i] = nil
                                end
                            else
                                -- 普通物品叠加逻辑
                                self.数据[v].数量 = self.数据[v].数量 + self.数据[n].数量
                                self.数据[n] = nil
                                玩家数据[id].角色.道具[i] = nil
                            end
                        end
                    end
                end
            end
        else
            -- 物品数据不存在时，做个提示或者跳过
            print("警告: 物品数据不存在，物品索引为: " .. v)
        end
    end
    -- 对道具进行排序并重新赋值
    table.sort(data, 简易排序)
    local tabdata = {}
    for k, v in pairs(data) do
        tabdata[#tabdata + 1] = v.内容
    end
    玩家数据[id].角色.道具 = tabdata
    -- 刷新道具
    道具刷新(id)
end

function 道具处理类:激活天赋符(连接id,id,数据)
	local 包裹类型=数据.类型
	local 道具格子=数据.编号
	local 属性=数据.事件
	if 包裹类型~="道具" then
		常规提示(id,"只有道具栏的物品才可以使用")
		return
	elseif 道具格子==nil then
		return
	end
	local 道具id=玩家数据[id].角色[包裹类型][道具格子]
	if 道具id==nil or self.数据[道具id]==nil then
		玩家数据[id].道具[道具格子]=nil
		发送数据(连接id,3699)
		return
	end
	if self.数据[道具id].属性 then
		常规提示(id,"这个物品已被激活过了。")
		return
	end
	if 属性 and 属性~="我再想想" then
		self.数据[道具id].属性=属性
		常规提示(id,"激活成功。")
		发送数据(连接id,3699)
	end
end
function 道具处理类:镶嵌灵犀玉(数字id,数据)
	local id = 数字id + 0
	local 镶嵌部件 = 数据.部件
	local 灵犀玉数据=数据.灵犀玉
	local 客户端属性=数据.客户端属性
	local sqsj = 玩家数据[id].神器.数据.神器解锁[镶嵌部件]
	for i=1,20 do
		local 道具id=玩家数据[id].角色.道具[i]
		if 道具id~=nil and 玩家数据[id].道具.数据[道具id]~=nil and self.数据[道具id].名称 == "灵犀玉" then
			for n=1,4 do
				if 灵犀玉数据[n]~=nil and self.数据[道具id].识别码==灵犀玉数据[n].识别码 then
					sqsj.镶嵌灵犀玉[n].子类 = self.数据[道具id].子类
					sqsj.镶嵌灵犀玉[n].特性 = self.数据[道具id].特性
					self.数据[道具id] = nil
					玩家数据[id].角色.道具[i]=nil
					break
				end
			end
		end
	end
	local 灵犀玉 = 玩家数据[id].道具:索要灵犀玉(id)
	道具刷新(id)
	玩家数据[id].神器:计算灵犀玉属性(玩家数据[id].连接id,id,镶嵌部件,灵犀玉)
end
function 道具处理类:灵犀之屑合成(连接id,id,内容)
	local 数量=内容.数量
	if 数量>self:取灵犀之屑数量(id) then
		常规提示(id,"#Y/灵犀之屑数量不足！")
		return
	end
	if 玩家数据[id].道具:消耗背包道具(连接id,id,"灵犀之屑",数量) then
		local go = self:取灵犀之屑成功率(数量)
		if go then
			玩家数据[id].道具:给予道具(id,"未鉴定的灵犀玉")
		end
		发送数据(连接id,6219,{是否成功=go,剩余灵犀玉=self:取灵犀之屑数量(id)})
	end
	道具刷新(id)
end
function 道具处理类:取灵犀之屑数量(id)
	local shuliang = 0
	for i=1,20 do
		local 道具id=玩家数据[id].角色.道具[i]
		if 道具id~=nil and 玩家数据[id].道具.数据[道具id]~=nil then
			if self.数据[道具id].名称 == "灵犀之屑" then
				shuliang = shuliang + self.数据[道具id].数量
			end
		end
	end
	return shuliang
end
function 道具处理类:取灵犀之屑成功率(数量)
	数量 = 数量 + 0
	local 成功率 =  数量*10
	local go = false
	if 取随机数(1,100) <= 成功率 then
		go = true
	end
	return go
end
function 道具处理类:取新编号()
	for n = 1,1200 do
		if self.数据[n]==nil then
			return n
		end
	end
	return #self.数据+1
end
function 道具处理类:宝宝坐骑喂养(连接id,id,数据)
	local 道具格子=数据.道具格子
	local 道具id=玩家数据[id].角色["道具"][道具格子]
	if self.数据[道具id] == nil then
		常规提示(id,"#Y/你没有这个道具")
		return
	end
	if self.数据[道具id].总类~=2 then
		常规提示(id,"炼化需要#Y/60至80级#W/的人物武器装备或#Y/0至125级#W/的召唤兽装备")
		return
	elseif self.数据[道具id].灵饰 then
		常规提示(id,"炼化需要#Y/60至80级#W/的人物武器装备或#Y/0至125级#W/的召唤兽装备")
		return
	end
	local lv=self.数据[道具id].级别限制
	local 认证码 = 数据.rzm
	local 类型=数据.类型
	if self.数据[道具id].召唤兽装备 then
		if lv>125 then
			常规提示(id,"炼化需要#Y/60至80级#W/的人物武器装备或#Y/0至125级#W/的召唤兽装备")
			return
		end
	else
		if lv>80 or lv<60 then
			常规提示(id,"炼化需要#Y/60至80级#W/的人物武器装备或#Y/0至125级#W/的召唤兽装备")
			return
		end
	end
	if 类型=="宝宝忠诚" then
		local 编号=玩家数据[id].角色:取参战宝宝编号()
		if 编号~=0 and 玩家数据[id].召唤兽.数据[编号] and 玩家数据[id].角色.参战信息~=nil then
			if 玩家数据[id].召唤兽.数据[编号].种类=="神兽" then
				添加最后对话(id,"神兽无需进行此操作。")
				return
			end
			if 玩家数据[id].召唤兽.数据[编号].忠诚>=100 then
				添加最后对话(id,"你的召唤兽忠诚度已经满了")
				return
			end
			self.数据[道具id]=nil
			玩家数据[id].角色["道具"][道具格子]=nil
			lv=qz(lv/5)
			玩家数据[id].召唤兽.数据[编号].忠诚=玩家数据[id].召唤兽.数据[编号].忠诚+lv
			常规提示(id,"你的召唤兽 "..玩家数据[id].召唤兽.数据[编号].名称.." 的忠诚提升了#G"..lv.."#Y点。")
			if 玩家数据[id].召唤兽.数据[编号].忠诚>100 then
				玩家数据[id].召唤兽.数据[编号].忠诚=100
			end
			发送数据(连接id,6568,{zc=玩家数据[id].召唤兽.数据[编号].忠诚,rzm=玩家数据[id].召唤兽.数据[编号].认证码})
			发送数据(连接id,3513,玩家数据[id].道具:索要道具2(id))
		end
	elseif 类型=="修炼" then
		local 坐骑编号=玩家数据[id].角色:取坐骑编号(认证码)
		if 坐骑编号~=0 then
			if 玩家数据[id].角色.坐骑列表[坐骑编号].好感度>=200 then
				添加最后对话(id,"好感度最高只能提升到200")
				return
			end
			self.数据[道具id]=nil
			玩家数据[id].角色["道具"][道具格子]=nil
			lv=qz(lv/10)
			玩家数据[id].角色.坐骑列表[坐骑编号].好感度 = 玩家数据[id].角色.坐骑列表[坐骑编号].好感度+lv
			常规提示(id,"你的坐骑 "..玩家数据[id].角色.坐骑列表[坐骑编号].名称.." 的好感度提升了#G"..lv.."#Y点。")
			if 玩家数据[id].角色.坐骑列表[坐骑编号].好感度>200 then
				玩家数据[id].角色.坐骑列表[坐骑编号].好感度=200
			end
			发送数据(连接id,6569,{hgd=玩家数据[id].角色.坐骑列表[坐骑编号].好感度,rzm= 玩家数据[id].角色.坐骑列表[坐骑编号].认证码})
			发送数据(连接id,3513,玩家数据[id].道具:索要道具2(id))
		end
	end
end
function 道具处理类:获取光武拓印(连接id,id,数据)
	local 道具格子=数据.道具格子
	local 道具id=玩家数据[id].角色["道具"][道具格子]
	if self.数据[道具id] == nil then
		常规提示(id,"#Y/数据异常！")
		return
	end
	if self.数据[道具id].总类~=2 or self.数据[道具id].分类 ~= 3  then
		常规提示(id,"#Y/选择武器有误！")
		return
	end
	if not self.数据[道具id].鉴定 then
		常规提示(id,"#Y/请鉴定后再来！")
		return
	end
	if self.数据[道具id].专用 and self.数据[道具id].专用 ~= id then
		常规提示(id,"#Y/无法对该武器进行操作！")
		return
	end
	发送数据(连接id,308,{道具格子})
end
function 道具处理类:光武拓印转化(连接id,id,数据)
	local 道具格子=数据.道具格子
	local 道具id=玩家数据[id].角色["道具"][道具格子]
	if self.数据[道具id] == nil or 数据.新造型 == nil then
		常规提示(id,"#Y/数据异常！")
		return
	end
	local 新造型 = 数据.新造型
	if self.数据[道具id].总类~=2 or self.数据[道具id].分类 ~= 3  then
		常规提示(id,"#Y/选择武器有误！")
		return
	end
	if not self.数据[道具id].鉴定 then
		常规提示(id,"#Y/请鉴定后再来！")
		return
	end
	if self.数据[道具id].专用 and self.数据[道具id].专用 ~= id then
		常规提示(id,"#Y/无法对该武器进行操作！")
		return
	end
	local 临时武器数据 = 取物品数据(新造型)
	if 临时武器数据 == nil then
		常规提示(id,"#Y/数据异常，请重试！")
		return
	end
	if self.数据[道具id].子类 ~= 临时武器数据[4]  then
		常规提示(id,"#Y/数据异常，请重试！")
		return
	end
	local 原造型=self.数据[道具id].名称
	if 玩家数据[id].角色:扣除银子(25000000,0,0,"光武拓印",1) then
		if self.数据[道具id].光武拓印 == nil then
			self.数据[道具id].光武拓印 = {
			原名称 = self.数据[道具id].名称,
			id  = id
			}
		end
		self.数据[道具id].名称 = 新造型
		发送数据(连接id,3513,玩家数据[id].道具:索要道具1(id))
		发送数据(连接id,308,{})
		添加最后对话(id,"一阵金光闪过，你手中的#Y"..原造型.."#W变成了#Y"..新造型.."#W的造型#80。")
	end
end
function 道具处理类:装备点化套装(连接id,id,sjj)
	local 强化石 = {"青龙石","朱雀石","玄武石","白虎石"}
	local 套装类型 = sjj.套装
	local 宝珠id = sjj.宝珠数据
	local 装备编号 = 玩家数据[id].角色.道具[sjj.装备]
	local 装备等级 = self.数据[装备编号].级别限制
	local 消耗石头 = self.数据[装备编号].分类
	local 强化石数据 = {青龙石=0,朱雀石=0,玄武石=0,白虎石=0}
	if 消耗石头 == 5 or 消耗石头 == 6 then
		消耗石头 = 3
	end
	强化石数据[强化石[消耗石头]] = math.floor(装备等级/10)
	for k,v in pairs(强化石数据) do
		if v>0 then
			强化石数据={k,v}
			break
		end
	end
	if sjj.装备 == nil or sjj.套装==0 or sjj.套装==nil then
		常规提示(id,"道具数据异常，请重新打开界面进行操作。")
		return
	end
	if 宝珠id==nil or self.数据[宝珠id] == nil and self.数据[宝珠id].名称~="附魔宝珠" then
		常规提示(id,"道具数据异常，请重新打开界面进行操作。")
		return
	end
	if self.数据[宝珠id].级别限制<装备等级 then
		常规提示(id,"宝珠等级小于装备等级，无法进行点化。")
		return
	end
	if 玩家数据[id].角色.当前经验 < 装备等级*3000 then
		常规提示(id,"您的经验不足，无法进行点化。")
		return
	end
	if 玩家数据[id].角色.银子 < 装备等级*5000 then
		常规提示(id,"您的银子不足，无法进行点化。")
		return
	end
	if 玩家数据[id].道具:判定背包道具(id,强化石数据[1],强化石数据[2])==false then
		常规提示(id,"您的"..强化石[消耗石头].."不足，无法进行点化。")
		return
	end
	if self.数据[装备编号].祈福值==nil or self.数据[装备编号].祈福值==0 or self.数据[装备编号].祈福值 < 30 then
		if 玩家数据[id].道具:消耗背包道具(连接id,id,强化石数据[1],强化石数据[2]) then
			self.数据[宝珠id] = nil
			发送数据(连接id,3718)
			if 套装类型 == 1 then
				local 随机动物套 = 取所有动物套[取随机数(1,#取所有动物套)]
				self.数据[装备编号].套装效果={"变身术之",随机动物套}
			elseif 套装类型 == 2 then
				local 套装类型="追加法术"
				local 套装效果={追加法术={"知己知彼","满天花雨","唧唧歪歪","龙卷雨击","尘土刃","荆棘舞","冰川怒","夺命咒","夺魄令","浪涌","裂石","落叶萧萧","龙腾","龙吟","五雷咒","飞砂走石","三昧真火","姐妹同心","阎罗令","判官令","紧箍咒","日光华","靛沧海","巨岩破","苍茫树","地裂火","尸腐毒","勾魂","摄魄","百万神兵","魔音摄魂","镇妖","含情脉脉","似玉生香","后发制人","横扫千军","日月乾坤","威慑","催眠符","失心符","落魄符","定身符","神针撼海","当头一棒","雷击","落岩","水攻","烈火","奔雷咒","泰山压顶","水漫金山","地狱烈火",}}
				self.数据[装备编号].套装效果={套装类型,套装效果[套装类型][取随机数(1,#套装效果[套装类型])]}
			elseif 套装类型 == 4 then
				local 套装类型="附加状态"
				local 套装效果={附加状态={"金刚护法","金刚护体","生命之泉","炼气化神","普渡众生","定心术","碎星诀","变身","极度疯狂","盘丝阵","逆鳞","魔王回首","幽冥鬼眼","楚楚可怜","修罗隐身","杀气诀","一苇渡江"}}
				self.数据[装备编号].套装效果={套装类型,套装效果[套装类型][取随机数(1,#套装效果[套装类型])]}
			end
			if self.数据[装备编号].祈福值 == nil then
				self.数据[装备编号].祈福值 = 0
			end
			self.数据[装备编号].祈福值 = self.数据[装备编号].祈福值 + 2
			常规提示(id,"点化成功")
			玩家数据[id].角色.当前经验=qz(玩家数据[id].角色.当前经验-装备等级*3000)
			玩家数据[id].角色:扣除银子(装备等级*5000,0,0,"点化套装",1)
			道具刷新(id)
		end
	else
		local 套装类型名称 = "变身术"
		local 选项 = {}
		if 套装类型 == 1 then
			for n=1,#取所有动物套 do
				table.insert(选项,取所有动物套[n])
			end
		elseif 套装类型 == 2 then
			套装类型名称 = "追加法术"
			local 追加法术={"知己知彼","满天花雨","唧唧歪歪","龙卷雨击","尘土刃","荆棘舞","冰川怒","夺命咒","夺魄令","浪涌","裂石","落叶萧萧","龙腾","龙吟","五雷咒","飞砂走石","三昧真火","姐妹同心","阎罗令","判官令","紧箍咒","日光华","靛沧海","巨岩破","苍茫树","地裂火","尸腐毒","勾魂","摄魄","百万神兵","魔音摄魂","镇妖","含情脉脉","似玉生香","后发制人","横扫千军","日月乾坤","威慑","催眠符","失心符","落魄符","定身符","神针撼海","当头一棒","雷击","落岩","水攻","烈火","奔雷咒","泰山压顶","水漫金山","地狱烈火",}
			for n=1,#追加法术 do
				table.insert(选项,追加法术[n])
			end
		elseif 套装类型 == 4 then
			套装类型名称 = "附加状态"
			local 附加状态={"金刚护法","金刚护体","生命之泉","炼气化神","普渡众生","定心术","碎星诀","变身","极度疯狂","盘丝阵","逆鳞","魔王回首","幽冥鬼眼","楚楚可怜","修罗隐身","杀气诀","一苇渡江"}
			for n=1,#附加状态 do
				table.insert(选项,附加状态[n])
			end
		end
		table.insert(选项,"我暂时先不点化了")
		local 对话="装备祈福值已满，您可以选择一个自己想要的套装效果进行祈福：(正在点化：#Z"..套装类型名称.."#W)"
		玩家数据[id].点化套装数据 = sjj
		发送数据(连接id,1501,{名称="点化装备套装",模型=玩家数据[id].角色.模型,对话=对话,选项=选项})
	end
end
function 道具处理类:判断强化石(数据)
	local 青龙石数量 = 0
	local 白虎石数量 = 0
	local 朱雀石数量 = 0
	local 玄武石数量 = 0
	local 判断 = nil
	if 数据.青龙石 ~= 0 then
		for n, v in pairs(self.数据) do
			if self.数据[n].名称 == "青龙石" then
				青龙石数量 = 青龙石数量 + self.数据[n].数量
			end
		end
		if 青龙石数量 < 数据.青龙石 then
			判断 = false
		else
			判断 = true
		end
	end
	if 数据.白虎石 ~= 0 then
		for n, v in pairs(self.数据) do
			if self.数据[n].名称 == "白虎石" then
				白虎石数量 = 白虎石数量 + self.数据[n].数量
			end
		end
		if 白虎石数量 < 数据.白虎石 then
			判断 = false
		else
			判断 = true
		end
	end
	if 数据.朱雀石 ~= 0 then
		for n, v in pairs(self.数据) do
			if self.数据[n].名称 == "朱雀石" then
				朱雀石数量 = 朱雀石数量 + self.数据[n].数量
			end
		end
		if 朱雀石数量 < 数据.朱雀石 then
			判断 = false
		else
			判断 = true
		end
	end
	if 数据.玄武石 ~= 0 then
		for n, v in pairs(self.数据) do
			if self.数据[n].名称 == "玄武石" then
				玄武石数量 = 玄武石数量 + self.数据[n].数量
			end
		end
		if 玄武石数量 < 数据.玄武石 then
			判断 = false
		else
			判断 = true
		end
	end
	return 判断
end
function 道具处理类:吸附兽诀(连接id,id,内容)
	local gz=内容.兽诀格子
	local 删除数量=1
	local 道具id=玩家数据[id].角色["道具"][gz]
	if self.数据[道具id] == nil then
		常规提示(id,"#Y/该物品不存在")
		return
	elseif self.数据[道具id].名称~="魔兽要诀" and self.数据[道具id].名称~="高级魔兽要诀" then
		常规提示(id,"#Y/该物品不存在")
		return
	end
	if self.数据[道具id].附带技能 then
		local 吸附名称=self.数据[道具id].附带技能
		if 玩家数据[id].召唤兽:是否特殊技能(吸附名称) then
			常规提示(id,"#Y/我不认识这个技能哦")
			return
		end

		-- 设置吸附概率
		local 吸附概率 = 0
		if self.数据[道具id].名称 == "魔兽要诀" then
			吸附概率 = 50  -- 低级兽决（魔兽要诀）50%概率
		elseif self.数据[道具id].名称 == "高级魔兽要诀" then
			吸附概率 = 30  -- 高级兽决（高级魔兽要诀）30%概率
		end

		-- 先消耗吸附石
		if 玩家数据[id].道具:消耗背包道具(玩家数据[id].连接id,id,"吸附石",1) then
			-- 判断吸附是否成功
			if 取随机数() <= 吸附概率 then
				-- 成功：消耗兽决并给予点化石
				玩家数据[id].道具:给予道具(id,"点化石",吸附名称)
				self:删除道具(连接id,id,"道具",道具id,gz,删除数量)
				--常规提示(id,"#Y/吸附成功！获得了点化石")
			else
				-- 失败：只消耗了吸附石，兽决保留
				常规提示(id,"#R/吸附失败！")
			end
			发送数据(连接id,3699)
		end
	end
end
function 道具处理类:开运(连接id,id,gz)
	local lsgz = gz
	gz=玩家数据[id].角色["道具"][gz]
	if self.数据[gz].开运孔数.当前 < self.数据[gz].开运孔数.上限 then
		if 玩家数据[id].角色.银子 < (self.数据[gz].开运孔数.当前+1) * 200000 then self:更新道具1(连接id,id,gz,lsgz) return  end
		if 玩家数据[id].角色:扣除银子((self.数据[gz].开运孔数.当前+1) * 200000,0,0) then
			if 取随机数() < 60 - (self.数据[gz].开运孔数.当前 * 10) then
				self.数据[gz].开运孔数.当前 = self.数据[gz].开运孔数.当前 + 1
				self:更新道具1(连接id,id,gz,lsgz)
				常规提示(id,"#Y开运#G成功#Y扣除了#R"..((self.数据[gz].开运孔数.当前+1) * 200000).."#Y银两")
			else
				self:更新道具1(连接id,id,gz,lsgz,"装备开运")
				常规提示(id,"#Y开运#R失败#Y失去了#R"..((self.数据[gz].开运孔数.当前+1) * 200000).."#Y银两")
			end
		end
	else
		self:更新道具1(连接id,id,gz,lsgz,"装备开运")
		常规提示(id,"#Y开运孔数到达上限!")
	end
end
function 道具处理类:更新道具(连接id,id,gz,lx)
	道具刷新(id)
end
function 道具处理类:更新道具1(连接id,id,gz,lx)
	发送数据(连接id,201,{self.数据[gz],lx})
	道具刷新(id)
end
function 道具处理类:更新道具2(连接id,id,gz)
	发送数据(连接id,202,self.数据[gz])
end
function 道具处理类:一键出售环装(连接id,数字id)
	local ts=""
	for n=1,80 do
		if 玩家数据[数字id].角色.道具[n] and self.数据[玩家数据[数字id].角色.道具[n]] then
			local 道具id=玩家数据[数字id].角色.道具[n]
			if self.数据[道具id] and self.数据[道具id].总类==2 and self.数据[道具id].级别限制 and self.数据[道具id].级别限制>=50 and self.数据[道具id].级别限制<=80 then
				local 名称=self.数据[道具id].名称
				local sbm=self.数据[道具id].识别码
				if not self.数据[道具id].专用 and not self.数据[道具id].灵饰 and not self.数据[道具id].召唤兽装备 then
					local 价格=self:取装备价格(道具id)
					self.数据[道具id]=nil
					玩家数据[数字id].角色.道具[n]=nil
					玩家数据[数字id].角色:添加银子(价格,"出售环装"..名称..sbm,1)
					ts=ts.."#G"..名称.."="..价格.."#Y，"
				end
			end
		end
	end
	if ts~="" then
		常规提示(数字id,"出售#G"..ts.."成功！")
		道具刷新(数字id)
	end
end
function 道具处理类:一键出售道具(连接id,id)
	local ts=""
	for n=1,80 do
		local 道具id=玩家数据[id].角色.道具[n]
		if 道具id~=nil then
			local 价格=self:取回收价格(道具id)
			if 价格.价格==0 or self.数据[道具id].加锁 then
			else
				if self.数据[道具id].数量==nil then self.数据[道具id].数量=0 end
				if self.数据[道具id].数量~=nil and self.数据[道具id].数量~=0 then
					价格.价格=价格.价格*self.数据[道具id].数量
				end
				玩家数据[id].角色:添加银子(价格.价格,format("回收物品:%s,%s,%s",名称,数量,识别码),1)
				ts=ts.."#G"..self.数据[道具id].名称.."="..价格.价格.."#Y，"
				self.数据[玩家数据[id].角色.道具[n]]=nil
				玩家数据[id].角色.道具[n]=nil
				发送数据(玩家数据[id].连接id,3699)
			end
		end
	end
	if ts~="" then
		常规提示(id,"出售#G"..ts.."成功！")
		道具刷新(id)
	else
		常规提示(id,"你没有可以出售的物品！请勿频繁操作。")
	end
end
function 道具处理类:一键合宝石(id,dj1,lv,名称1)
	if 名称1=="星辉石" and lv>=11 then
		常规提示(id,"星辉石最高只能合到11级！")
		return
	end
	if lv>0 and dj1 then
		for n=1,80 do
			local dj2 = 玩家数据[id].角色.道具[n]
			if dj2 and self.数据[dj2] and dj1~=dj2 and 名称1==self.数据[dj2].名称 then
				local 名称2=self.数据[dj2].名称
				if 右键合成宝石[名称2] and self.数据[dj2].级别限制==lv then
					if 装备处理类:取宝石合成几率(id,lv) then
						玩家数据[id].角色.道具[n] = nil
						self.数据[dj2] = nil
						self.数据[dj1].级别限制 = self.数据[dj1].级别限制 +1
						道具刷新(id)
						常规提示(id,"#Y/你合成了一个更高级的宝石")
						return
					else
						玩家数据[id].角色.道具[n] = nil
						self.数据[dj1] = nil
						道具刷新(id)
						常规提示(id,"#Y/你合成失败你损失了一颗宝石")
						return
					end
				end
			end
		end
	end
	常规提示(id,"#Y/没有找到可以合成的宝石")
end
function 道具处理类:一键合五宝(id)
	local sadwe={金刚石=0,定魂珠=0,夜光珠=0,避水珠=0,龙鳞=0}
	local shanchu={}
	local 满足=0
	for n=1,80 do
		local dj = 玩家数据[id].角色.道具[n]
		if dj and self.数据[dj] then
			local 名称=self.数据[dj].名称
			if 右键合成五宝[名称] and sadwe[名称]==0 then
				sadwe[名称]=123
				shanchu[名称]={道具id=dj,角色寄存=n}
				满足=满足+1
				if 满足>=5 then
					break
				end
			end
		end
	end
	if 满足>=5 then
		for k,v in pairs(shanchu) do
			if v.道具id~=0 and self.数据[v.道具id] and 右键合成五宝[self.数据[v.道具id].名称] then
				玩家数据[id].角色.道具[v.角色寄存] = nil
				self.数据[v.道具id] = nil
			end
		end
		玩家数据[id].道具:给予道具(id,"特赦令牌")
	else
		常规提示(id,"不满足合成五宝条件")
	end
end
function 道具处理类:使用道具(连接id,id,内容)
	if 玩家数据[id].坐牢中 or 玩家数据[id].烤火 then
		常规提示(id,"当前不能使用道具")
		return
	end
	local 包裹类型=内容.类型
	local 道具格子=内容.编号
	local 删除数量=1
	local 加血对象=0
	local 道具提示 = true
	if 内容.窗口=="召唤兽" then
		if 玩家数据[id].召唤兽.数据[内容.序列]==nil then
			常规提示(id,"请选择一只召唤兽")
			return
		else
			加血对象=内容.序列
		end
	elseif 内容.窗口=="坐骑" then
		if 玩家数据[id].角色.坐骑列表[内容.序列]==nil then
			常规提示(id,"请选择一只坐骑")
			return
		else
			加血对象=内容.序列
		end
	end
	if 包裹类型~="道具" then
		常规提示(id,"只有道具栏的物品才可以使用")
		return
	elseif 道具格子==nil then
		return
	end
	local 道具id=玩家数据[id].角色[包裹类型][道具格子]
	if 道具id==nil or self.数据[道具id]==nil then
		玩家数据[id].道具[道具格子]=nil
		发送数据(连接id,3699)
		return
	end
	local 名称=self.数据[道具id].名称
	if self.数据[道具id].加锁~=nil then
		常规提示(id,self.数据[道具id].名称.."已加锁，无法使用。")
		return
	end
	local 道具使用=false
	if self:取加血道具(名称) then
		道具使用=true
		local 加血数值=self:取加血道具1(名称,道具id)
		local 加魔数值=self:取加魔道具1(名称,道具id)
		local 伤势数值=self:取加血道具2(名称,道具id)
		if 名称=="翡翠豆腐" then
			self:加血处理(连接id,id,加血数值,加血对象)
			self:加魔处理(连接id,id,加魔数值,加血对象)
		else
			self:加血处理(连接id,id,加血数值,加血对象,nil,伤势数值)
		end
	elseif self:取加魔道具(名称) then
		道具使用=true
		local 加血数值=self:取加血道具1(名称,道具id)
		local 加魔数值=self:取加魔道具1(名称,道具id)
		if 名称=="翡翠豆腐" then
			self:加血处理(连接id,id,加血数值,加血对象)
			self:加魔处理(连接id,id,加魔数值,加血对象)
		else
			self:加魔处理(连接id,id,加魔数值,加血对象)
		end
	elseif self:取寿命道具(名称) then
		if 加血对象==0 then
			常规提示(id,"请选择一只召唤兽")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].种类 =="神兽" then
			常规提示(id,"神兽无法使用此物品")
			return
		else
			local 加血数值=self:取寿命道具1(名称,道具id)
			玩家数据[id].召唤兽:加寿命处理(加血对象,加血数值.数值,加血数值.中毒,连接id,id)
			道具使用=true
		end
	elseif 右键合成宝石[名称] then
		self:一键合宝石(id,道具id,self.数据[道具id].级别限制,self.数据[道具id].名称)
		return
	elseif 右键合成五宝[名称] then
		self:一键合五宝(id)
		return
	elseif 名称=="人参果" then
		self:人参果使用(连接id,id,内容)
		return
	elseif 名称=="超级人参果" then
		self:超级人参果使用(连接id,id,内容)
		return
	elseif 名称=="月饼" then
		self:月饼使用(连接id,id,内容)
		return
	elseif 名称=="梦幻精品粽" then
		self:梦幻精品粽使用(连接id,id,内容)
		return
	elseif 名称 == "水晶糕" then
		if 加血对象==0 or 玩家数据[id].召唤兽.数据[加血对象]==nil then
			常规提示(id,"请选择一只召唤兽")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].种类=="神兽" then
			常规提示(id,"神兽无法使用此物品")
			return
		end
		if 玩家数据[id].召唤兽.数据[加血对象].元宵.水晶糕<=0 then
			常规提示(id,"#Y你的"..玩家数据[id].召唤兽.数据[加血对象].名称.."#Y可食用的水晶糕已达上限。")
			return
		end
		玩家数据[id].召唤兽.数据[加血对象].元宵.水晶糕=玩家数据[id].召唤兽.数据[加血对象].元宵.水晶糕-1
		玩家数据[id].召唤兽.数据[加血对象].元宵.可用=玩家数据[id].召唤兽.数据[加血对象].元宵.可用+1
		常规提示(id,"#Y你的#G"..玩家数据[id].召唤兽.数据[加血对象].名称.."#Y的本周可食用元宵数量增加到了#G"..玩家数据[id].召唤兽.数据[加血对象].元宵.可用.."#Y个。")
		道具使用=true
	elseif 名称 == "炼兽真经" then
		if 加血对象==0 or 玩家数据[id].召唤兽.数据[加血对象]==nil then
			常规提示(id,"请选择一只召唤兽")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].种类=="神兽" then
			常规提示(id,"神兽无法使用此物品")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].成长>=1.3 then
			常规提示(id,"当成长≥1.3时，不可以使用炼兽珍经")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].元宵.炼兽真经<=0 then
			if 玩家数据[id].召唤兽.数据[加血对象].元宵.真经时间>os.time() then
				常规提示(id,"这个召唤兽已经吃了太多炼兽真经了，留着"..时间转换(玩家数据[id].召唤兽.数据[加血对象].元宵.真经时间).."给它吃吧。")
				return
			else
				玩家数据[id].召唤兽.数据[加血对象].元宵.炼兽真经=10
			end
		end
		local sx = 1
		local xx = 1
		if 玩家数据[id].召唤兽.数据[加血对象].成长<1.2 then
			sx = 7
			xx = 5
		elseif 玩家数据[id].召唤兽.数据[加血对象].成长<1.23 then
			sx = 6
			xx = 4
		elseif 玩家数据[id].召唤兽.数据[加血对象].成长<1.25 then
			sx = 6
			xx = 3
		elseif 玩家数据[id].召唤兽.数据[加血对象].成长<1.27 then
			sx = 5
			xx = 2
		elseif 玩家数据[id].召唤兽.数据[加血对象].成长<1.29 then
			sx = 4
			xx = 1
		else
			sx = 3
			xx = 1
		end
		local num = 取随机数(sx,xx)*0.001
		玩家数据[id].召唤兽.数据[加血对象].元宵.炼兽真经=玩家数据[id].召唤兽.数据[加血对象].元宵.炼兽真经-1
		玩家数据[id].召唤兽.数据[加血对象].成长 = 玩家数据[id].召唤兽.数据[加血对象].成长 + num
		玩家数据[id].召唤兽.数据[加血对象].元宵.真经时间=os.time()+604800
		常规提示(id,"#Y经过一番学习，您的#G"..玩家数据[id].召唤兽.数据[加血对象].名称.."#Y成长提升了#G"..num.."#Y。")
		玩家数据[id].召唤兽.数据[加血对象]:刷新信息()
		发送数据(连接id,115,{宝宝数据=玩家数据[id].召唤兽.数据,认证码=玩家数据[id].召唤兽.数据[加血对象].认证码,成长=玩家数据[id].召唤兽.数据[加血对象].成长})
		道具使用=true
	elseif 名称 == "芝麻沁香元宵" or 名称 == "桂花酒酿元宵" or 名称 == "细磨豆沙元宵" or 名称 == "蜜糖腰果元宵" or 名称 == "山楂拔丝元宵" or 名称 == "滑玉莲蓉元宵" then
		if 加血对象==0 or 玩家数据[id].召唤兽.数据[加血对象]==nil then
			常规提示(id,"请选择一只召唤兽")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].种类=="神兽" then
			常规提示(id,"神兽无法使用此物品")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].元宵.可用<=0 then
			if 玩家数据[id].召唤兽.数据[加血对象].元宵.元宵时间>os.time() then
				常规提示(id,"这个召唤兽已经吃了太多元宵了，留着"..时间转换(玩家数据[id].召唤兽.数据[加血对象].元宵.元宵时间).."给它吃吧。")
				return
			else
				玩家数据[id].召唤兽.数据[加血对象].元宵.可用=10
			end
		end
		local zz = "攻击资质"
		if 名称 == "桂花酒酿元宵" then
			zz = "防御资质"
		elseif 名称 == "细磨豆沙元宵" then
			zz = "速度资质"
		elseif 名称 == "蜜糖腰果元宵" then
			zz = "躲闪资质"
		elseif 名称 == "山楂拔丝元宵" then
			zz = "体力资质"
		elseif 名称 == "滑玉莲蓉元宵" then
			zz = "法力资质"
		end
		local shangxian = {攻击资质=1550,防御资质=1550,体力资质=5500,法力资质=3050,速度资质=1550,躲闪资质=1800}
		if 玩家数据[id].召唤兽.数据[加血对象].参战等级>=155 then
			shangxian = {攻击资质=1650,防御资质=1650,体力资质=7000,法力资质=3600,速度资质=1650,躲闪资质=2000}
		end
		if 玩家数据[id].召唤兽.数据[加血对象][zz]>=shangxian[zz] then
			常规提示(id,"通过元宵提升的"..zz.."最高为"..shangxian[zz])
			return
		end
		local sx = 30
		local xx = 10
		if zz=="攻击资质" or zz=="攻击资质" or zz=="速度资质" or zz=="躲闪资质" then
			if 玩家数据[id].召唤兽.数据[加血对象][zz]<1300 then
				sx = 20
				xx = 10
			elseif 玩家数据[id].召唤兽.数据[加血对象][zz]<1400 then
				sx = 15
				xx = 8
			else
				sx = 10
				xx = 5
			end
		elseif  zz=="法力资质" then
			if 玩家数据[id].召唤兽.数据[加血对象][zz]<1500 then
				sx = 40
				xx = 20
			elseif 玩家数据[id].召唤兽.数据[加血对象][zz]<1700 then
				sx = 30
				xx = 15
			elseif 玩家数据[id].召唤兽.数据[加血对象][zz]<2000 then
				sx = 25
				xx = 10
			else
				sx = 20
				xx = 10
			end
		else
			if 玩家数据[id].召唤兽.数据[加血对象][zz]<3000 then
				sx = 50
				xx = 30
			elseif 玩家数据[id].召唤兽.数据[加血对象][zz]<4000 then
				sx = 40
				xx = 25
			elseif 玩家数据[id].召唤兽.数据[加血对象][zz]<5000 then
				sx = 25
				xx = 10
			else
				sx = 20
				xx = 10
			end
		end
		local num = 取随机数(sx,xx)
		玩家数据[id].召唤兽.数据[加血对象].元宵.可用 = 玩家数据[id].召唤兽.数据[加血对象].元宵.可用-1
		玩家数据[id].召唤兽.数据[加血对象][zz]=玩家数据[id].召唤兽.数据[加血对象][zz]+num
		玩家数据[id].召唤兽.数据[加血对象].元宵.元宵时间=os.time()+604800
		常规提示(id,"#Y恭喜！你的#G"..玩家数据[id].召唤兽.数据[加血对象].名称.."#Y的#G"..zz.."#Y增加了#G"..num.."#Y。")
		玩家数据[id].召唤兽.数据[加血对象]:刷新信息()
		发送数据(连接id,114,{宝宝数据=玩家数据[id].召唤兽.数据,认证码=玩家数据[id].召唤兽.数据[加血对象].认证码,资质=zz,资质数额=玩家数据[id].召唤兽.数据[加血对象][zz]})
		道具使用=true
	elseif 名称 == "易经丹" then
		if 加血对象==0 or 玩家数据[id].召唤兽.数据[加血对象]==nil then
			常规提示(id,"请选择一只召唤兽")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].等级<30 then
			常规提示(id,"需等级≥30级才能进阶！")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].参战等级<45 and 玩家数据[id].召唤兽.数据[加血对象].等级<60 and 玩家数据[id].召唤兽.数据[加血对象].种类~="神兽" then
			常规提示(id,"参战等级<45级召唤兽,需等级≥60级才能进阶！")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].内丹.技能==nil then
			常规提示(id,"要求：内丹已满，所有低内丹5层，高内丹至少1层")
			return
		end
		if 玩家数据[id].召唤兽.数据[加血对象].进阶 == nil then
			local go = false
			local 高级内丹 ={ "神机步","腾挪劲","玄武躯","龙胄铠","玉砥柱","碎甲刃","阴阳护","凛冽气","舍身击","电魂闪","通灵法","双星爆","催心浪","隐匿击","生死决","血债偿"}
			for i=1,玩家数据[id].召唤兽.数据[加血对象].内丹.内丹上限 do
				if 玩家数据[id].召唤兽.数据[加血对象].内丹.技能[i] == nil then
					常规提示(id,"要求：内丹已满，所有低内丹5层，高内丹至少1层")
					return
				end
				if i==1 then
					for k,v in pairs(高级内丹) do
						if 玩家数据[id].召唤兽.数据[加血对象].内丹.技能[1].技能==v then
							go = true
						end
					end
				end
			end
			if go then
				for i=2,玩家数据[id].召唤兽.数据[加血对象].内丹.内丹上限 do
					if 玩家数据[id].召唤兽.数据[加血对象].内丹.技能[i].等级<5 then
						常规提示(id,"要求：内丹已满，所有低内丹5层，高内丹至少1层")
						return
					end
				end
			else
				for i=1,玩家数据[id].召唤兽.数据[加血对象].内丹.内丹上限 do
					if 玩家数据[id].召唤兽.数据[加血对象].内丹.技能[i].等级<5 then
						常规提示(id,"要求：内丹已满，所有低内丹5层，高内丹至少1层")
						return
					end
				end
			end
			玩家数据[id].召唤兽.数据[加血对象].进阶 ={清灵仙露=0,灵性=0,特性="无",特性属性={},开启=false}
		end
		if 玩家数据[id].召唤兽.数据[加血对象].进阶.灵性 <50 then
			玩家数据[id].召唤兽.数据[加血对象].进阶.灵性= 玩家数据[id].召唤兽.数据[加血对象].进阶.灵性+10
			玩家数据[id].召唤兽.数据[加血对象].潜力 = 玩家数据[id].召唤兽.数据[加血对象].潜力 + 20
			常规提示(id,玩家数据[id].召唤兽.数据[加血对象].名称.."服用了一个易经丹后，神清气爽，仙气缭绕，灵性增加了#R10#Y点")
			if 玩家数据[id].召唤兽.数据[加血对象].进阶.灵性==50 then
				if 玩家数据[id].召唤兽.数据[加血对象].参战等级<45 then
					常规提示(id,"#G你的召唤兽已经达到了进阶的条件了")
				else
					常规提示(id,"#G你的召唤兽已经达到了进阶的条件,可以进阶改变造型了")
				end
			end
			玩家数据[id].召唤兽.数据[加血对象]:刷新信息()
			发送数据(连接id,108,{认证码=玩家数据[id].召唤兽.数据[加血对象].认证码,进阶=玩家数据[id].召唤兽.数据[加血对象].进阶})
		elseif 玩家数据[id].召唤兽.数据[加血对象].进阶.灵性 >= 80 then
			local txb={"复仇","自恋","灵刃","灵法","巧劲","预知","灵动","瞬击","瞬法","抗法","抗物","阳护","识物","护佑","洞察","弑神","御风","顺势","怒吼","逆境","乖巧","力破","识药","吮魔","争锋","灵断"}
			local a =txb[取随机数(1,#txb)]
			玩家数据[id].召唤兽.数据[加血对象].进阶.特性 = a
			玩家数据[id].召唤兽.数据[加血对象].进阶.特性属性 = {宝宝特性属性(玩家数据[id].召唤兽.数据[加血对象].等级,a)}
			玩家数据[id].召唤兽.数据[加血对象].进阶.开启=true
			常规提示(id,"#Y"..玩家数据[id].召唤兽.数据[加血对象].名称.."充分吸收了易经丹中的灵气，特性变得和原来不同了！")
			玩家数据[id].召唤兽.数据[加血对象]:刷新信息()
			发送数据(连接id,16,玩家数据[id].召唤兽.数据)
			发送数据(连接id,108,{认证码=玩家数据[id].召唤兽.数据[加血对象].认证码,进阶=玩家数据[id].召唤兽.数据[加血对象].进阶})
		else
			常规提示(id,"你的召唤兽目前无法使用该道具")
			return
		end
		道具使用=true
	elseif 名称 == "玉葫灵髓" then
		if 加血对象==0 or 玩家数据[id].召唤兽.数据[加血对象]==nil then
			常规提示(id,"请选择一只召唤兽")
			return
		end
		if 玩家数据[id].召唤兽.数据[加血对象].进阶 ~= nil and 玩家数据[id].召唤兽.数据[加血对象].进阶.灵性>50 then
			local dj = 玩家数据[id].召唤兽.数据[加血对象].等级
			if 玩家数据[id].召唤兽.数据[加血对象].种类=="神兽" then
				玩家数据[id].召唤兽.数据[加血对象].体质=dj+20
				玩家数据[id].召唤兽.数据[加血对象].魔力=dj+20
				玩家数据[id].召唤兽.数据[加血对象].力量=dj+20
				玩家数据[id].召唤兽.数据[加血对象].耐力=dj+20
				玩家数据[id].召唤兽.数据[加血对象].敏捷=dj+20
				玩家数据[id].召唤兽.数据[加血对象].潜力=dj*5
			elseif 玩家数据[id].召唤兽.数据[加血对象].种类=="变异" then
				玩家数据[id].召唤兽.数据[加血对象].体质=dj+15
				玩家数据[id].召唤兽.数据[加血对象].魔力=dj+15
				玩家数据[id].召唤兽.数据[加血对象].力量=dj+15
				玩家数据[id].召唤兽.数据[加血对象].耐力=dj+15
				玩家数据[id].召唤兽.数据[加血对象].敏捷=dj+15
				玩家数据[id].召唤兽.数据[加血对象].潜力=dj*5
			else
				玩家数据[id].召唤兽.数据[加血对象].体质=dj+10
				玩家数据[id].召唤兽.数据[加血对象].魔力=dj+10
				玩家数据[id].召唤兽.数据[加血对象].力量=dj+10
				玩家数据[id].召唤兽.数据[加血对象].耐力=dj+10
				玩家数据[id].召唤兽.数据[加血对象].敏捷=dj+10
				玩家数据[id].召唤兽.数据[加血对象].潜力=dj*5
			end
			玩家数据[id].召唤兽.数据[加血对象].潜力 = 玩家数据[id].召唤兽.数据[加血对象].潜力+100
			玩家数据[id].召唤兽.数据[加血对象].进阶 = {清灵仙露=0,灵性=50,特性="无",特性属性={},开启=false}
			常规提示(id,"你的召唤兽#R/"..玩家数据[id].召唤兽.数据[加血对象].名称.."#Y/服用一个玉葫灵髓后,灵性已回归原始！")
			玩家数据[id].召唤兽.数据[加血对象]:刷新信息()
			发送数据(连接id,16,玩家数据[id].召唤兽.数据)
			发送数据(连接id,108,{认证码=玩家数据[id].召唤兽.数据[加血对象].认证码,进阶=玩家数据[id].召唤兽.数据[加血对象].进阶})
		else
			常规提示(id,"召唤兽的灵性必须大于50才能使用玉葫灵髓")
			return
		end
		道具使用=true
	elseif 名称 == "初级清灵仙露" or 名称 == "中级清灵仙露" or 名称 == "高级清灵仙露" or 名称=="特殊清灵仙露" then
		if 加血对象==0 or 玩家数据[id].召唤兽.数据[加血对象]==nil then
			常规提示(id,"请选择一只召唤兽")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].进阶 == nil then
			常规提示(id,"召唤兽灵性未达到50！")
			return
		end
		if self:清灵仙露处理(连接id,id,加血对象,道具id) then
			道具使用=true
		else
			return
		end
	elseif 名称 == "蒲扇"  then
		local rwid = 玩家数据[id].角色:取任务(81)
		if rwid ~= 0 then
			if 玩家数据[id].角色.地图数据.编号 ~= 6018 then
				常规提示(id,"#Y/该地图无法使用该物品！")
				return
			elseif math.abs(任务数据[rwid].x-玩家数据[id].角色.地图数据.x/20)>3 and math.abs(任务数据[rwid].y-玩家数据[id].角色.地图数据.y/20)>3 then
				常规提示(id,"#Y/并不是要在这里打扫哟！")
				return
			else
				副本_黑风山:完成黑风山任务({id},rwid,81)
			end
		else
			常规提示(id,"#Y/你没有这样的任务！")
			return
		end
		道具使用=true
	elseif 名称 == "拘魂镜"  then
		local rwid = 玩家数据[id].角色:取任务(956)
		if rwid ~= 0 then
			if 玩家数据[id].角色.地图数据.编号 ~= 6054 then
				常规提示(id,"#Y/该地图无法使用该物品！")
				return
			elseif math.abs(任务数据[rwid].x-玩家数据[id].角色.地图数据.x/20)>3 and math.abs(任务数据[rwid].y-玩家数据[id].角色.地图数据.y/20)>3 then
				常规提示(id,"#Y/坐标不对！")
				return
			else
				完成天火之殇上任务({id},rwid,956)
				常规提示(id,"#Y/很可惜，没能阻拦住这些鬼魂~#108")
			end
		else
			常规提示(id,"#Y/你没有这样的任务！")
			return
		end
		道具使用=true
	elseif 名称 == "大闹小铲子"  then
		local rwid = 玩家数据[id].角色:取任务(71)
		if rwid ~= 0 then
			if 玩家数据[id].角色.地图数据.编号 ~= 6033 then
				常规提示(id,"#Y/该地图无法使用该物品！")
				return
			elseif math.abs(任务数据[rwid].x-玩家数据[id].角色.地图数据.x/20)>3 and math.abs(任务数据[rwid].y-玩家数据[id].角色.地图数据.y/20)>3 then
				常规提示(id,"#Y/大圣并不是要在这里除草哟！")
				return
			else
				local 对话="蟠桃园锄树清草，可得讲究力道,增之一分则太多,减之一分则太少。请选择你要使用的力道"
				local 选项={"一分力道","二分力道","我还是在考虑考虑"}
				玩家数据[id].大闹锄树=选项[取随机数(1,2)]
				发送数据(玩家数据[id].连接id,1501,{名称=玩家数据[id].角色.名称,模型=玩家数据[id].角色.模型,对话=对话,选项=选项})
			end
		else
			常规提示(id,"#Y/你没有这样的任务！")
			return
		end
		道具使用=true
	elseif self.数据[道具id].总类==149 then
		if 加血对象==0 then
			常规提示(id,"请选择一只召唤兽")
			return
   		elseif not 玩家数据[id].召唤兽.数据[加血对象] then
	        常规提示(id, "找不到指定的召唤兽")
	        return
		elseif 玩家数据[id].召唤兽.数据[加血对象].饰品~=nil then
			常规提示(id,"这只召唤兽已经有饰品了")
			return
		end
		if 玩家数据[id].召唤兽.数据[加血对象].模型.."饰品"==self.数据[道具id].名称 then
			玩家数据[id].召唤兽.数据[加血对象].饰品 = 1
			常规提示(id,"佩戴召唤兽饰品成功！")
		else
			常规提示(id,"该饰品不符合这个召唤兽")
			return
		end
		道具使用=true
	elseif 名称 == "宠物口粮" then
		if 加血对象==0 or 玩家数据[id].召唤兽.数据[加血对象]==nil then
			常规提示(id,"请选择一只召唤兽")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].种类=="神兽" then
			常规提示(id,"神兽无法使用此物品")
			return
		end
		if 玩家数据[id].召唤兽.数据[加血对象].忠诚>=100 then
			常规提示(id,"你的召唤兽忠诚度已经满了")
			return
		end
		玩家数据[id].召唤兽.数据[加血对象].忠诚=玩家数据[id].召唤兽.数据[加血对象].忠诚+10
		if 玩家数据[id].召唤兽.数据[加血对象].忠诚>100 then
			玩家数据[id].召唤兽.数据[加血对象].忠诚=100
		end
		常规提示(id,"你的召唤兽 "..玩家数据[id].召唤兽.数据[加血对象].名称.." 的忠诚提升了#G10#Y点。")
		发送数据(连接id,6568,{zc=玩家数据[id].召唤兽.数据[加血对象].忠诚,rzm=玩家数据[id].召唤兽.数据[加血对象].认证码})
		道具使用=true
	elseif 名称 == "高级宠物口粮" then
		if 加血对象==0 or 玩家数据[id].召唤兽.数据[加血对象]==nil then
			常规提示(id,"请选择一只召唤兽")
			return
		elseif 玩家数据[id].召唤兽.数据[加血对象].种类=="神兽" then
			常规提示(id,"神兽无法使用此物品")
			return
		end
		if 玩家数据[id].召唤兽.数据[加血对象].忠诚>=100 then
			常规提示(id,"你的召唤兽忠诚度已经满了")
			return
		end
		玩家数据[id].召唤兽.数据[加血对象].忠诚=玩家数据[id].召唤兽.数据[加血对象].忠诚+100
		if 玩家数据[id].召唤兽.数据[加血对象].忠诚>100 then
			玩家数据[id].召唤兽.数据[加血对象].忠诚=100
		end
		常规提示(id,"你的召唤兽 "..玩家数据[id].召唤兽.数据[加血对象].名称.." 的忠诚提升了#G100#Y点。")
		发送数据(连接id,6568,{zc=玩家数据[id].召唤兽.数据[加血对象].忠诚,rzm=玩家数据[id].召唤兽.数据[加血对象].认证码})
		道具使用=true
	elseif 名称 == "如意丹"  then
		if 加血对象==0 or 玩家数据[id].召唤兽.数据[加血对象]==nil then
			常规提示(id,"请选择一只召唤兽")
			return
		elseif self.数据[道具id].灵气==nil then
			return
		end
		local 五行=self.数据[道具id].灵气
		local sx=""
		if 五行=="金" then
			sx="力量"
		elseif 五行=="水" then
			sx="敏捷"
		elseif 五行=="木" then
			sx="体质"
		elseif 五行=="火" then
			sx="魔力"
		elseif 五行=="土" then
			sx="耐力"
		end
		if 玩家数据[id].召唤兽.数据[加血对象].种类=="神兽" then
			if 玩家数据[id].召唤兽.数据[加血对象][sx]<玩家数据[id].召唤兽.数据[加血对象].等级+20 then
				常规提示(id,"神兽的属性点最低只能调整到等级+20")
				return
			end
		else
			if 玩家数据[id].召唤兽.数据[加血对象][sx]<玩家数据[id].召唤兽.数据[加血对象].等级+10 then
				常规提示(id,"该宝宝属性点最低只能调整到等级+10")
				return
			end
		end
		玩家数据[id].召唤兽.数据[加血对象]:如意丹洗点(sx)
		发送数据(连接id,16,玩家数据[id].召唤兽.数据)
		常规提示(id,"你的召唤兽#R/"..玩家数据[id].召唤兽.数据[加血对象].名称.."#Y/服用一个如意丹后，"..sx.."减少了1点，潜力点增加了1点")
		道具使用=true
	elseif 名称 == "天赋符" then
		if 加血对象==0 or 玩家数据[id].召唤兽.数据[加血对象]==nil then
			常规提示(id,"请选择一只召唤兽")
			return
		end
		if self.数据[道具id].属性 then
			local sx=0
			local b0bo=玩家数据[id].召唤兽.数据[加血对象]
			local leixing=self.数据[道具id].属性
			local fwd=leixing
			local weizhi=1
			if leixing=="攻击" then
				fwd="伤害"
				weizhi=3
				sx=玩家数据[id].召唤兽.数据[加血对象]:伤害公式()
			elseif leixing=="气血" then
				fwd="最大气血"
				weizhi=1
				sx=玩家数据[id].召唤兽.数据[加血对象]:气血公式()
			elseif leixing=="防御" then
				weizhi=4
				sx=玩家数据[id].召唤兽.数据[加血对象]:防御公式()
			elseif leixing=="速度" then
				weizhi=5
				sx=玩家数据[id].召唤兽.数据[加血对象]:速度公式()
			elseif leixing=="灵力" then
				weizhi=6
				sx=玩家数据[id].召唤兽.数据[加血对象]:灵力公式()
			elseif leixing=="躲闪" then
				fwd="躲避"
				sx=玩家数据[id].召唤兽.数据[加血对象]:躲避公式()
			end
			玩家数据[id].召唤兽.数据[加血对象].天赋符={lx=fwd,khdwz=weizhi,zhi=sx,sj=os.time()+5}
			玩家数据[id].召唤兽.数据[加血对象]:刷新信息("1")
			发送数据(连接id,20,玩家数据[id].召唤兽.数据[加血对象])
			道具使用=true
			常规提示(id,"使用天赋符成功。这个天赋符将在24小时后消失。")
		end
	elseif 名称=="月华露" then
		local 满足条件 = true
		local 提示 = ""
		if 内容.窗口~="召唤兽" and 内容.窗口~="子女" then
			提示 = "请先选中一只召唤兽或者子女"
			满足条件 = false
		elseif 内容.窗口=="召唤兽" and 玩家数据[id].召唤兽.数据[内容.序列]==nil then
			提示 = "请先选中一只召唤兽"
			满足条件 = false
		elseif 内容.窗口 =="召唤兽" and 玩家数据[id].召唤兽.数据[内容.序列].等级>=180 then
			提示 = "该召唤兽等级已达到上限！"
			满足条件 = false
		elseif 内容.窗口 =="召唤兽" and 玩家数据[id].召唤兽.数据[内容.序列].等级 >= 玩家数据[id].角色.等级 + 10 then
			提示 = "该召唤兽已超过人物等级10级，无法使用该道具"
			满足条件 = false
		elseif 内容.窗口=="子女" and 玩家数据[id].孩子.数据[内容.序列]==nil then
			提示 = "请先选中一只孩子"
			满足条件 = false
		elseif 内容.窗口 =="子女" and 玩家数据[id].孩子.数据[内容.序列].等级>=180 then
			提示 = "该孩子等级已达到上限！"
			满足条件 = false
		elseif 内容.窗口 =="子女" and 玩家数据[id].孩子.数据[内容.序列].等级 >= 玩家数据[id].角色.等级 + 10 then
			提示 = "该子女已超过人物等级10级，无法使用该道具"
			满足条件 = false
		end
		if 满足条件 then
			local 临时等级 = 1
			if 内容.窗口 =="召唤兽" then
				临时等级=玩家数据[id].召唤兽.数据[内容.序列].等级
				if 临时等级 == 0 then
					临时等级 = 1
				end
				玩家数据[id].召唤兽:添加经验(连接id,id,内容.序列,self.数据[道具id].阶品*2*临时等级)
			elseif 内容.窗口 =="子女" then
				临时等级 = 玩家数据[id].孩子.数据[内容.序列].等级
				if 临时等级 == 0 then
					临时等级 = 1
				end
				玩家数据[id].孩子:添加经验(连接id,id,内容.序列,self.数据[道具id].阶品*2*临时等级)
			end
			道具使用=true
		else
			常规提示(id,提示)
		end
	elseif 内容.窗口 =="坐骑" and self.数据[道具id].总类 == "坐骑饰品" then
		self:坐骑装饰佩戴(id,加血对象,道具格子,包裹类型)
		return
	elseif 名称 == "福禄丹" then
		if 加血对象==0 then
			常规提示(id,"请选择一只坐骑")
			return
		end
		if 内容.窗口 =="坐骑" and 玩家数据[id].角色.坐骑列表[加血对象] then
			if 玩家数据[id].角色.坐骑列表[加血对象].初始成长>=1.3433 then
				常规提示(id,"这只坐骑的初始成长已达先天的成长上限，无法继续服用。")
				return
			end
			玩家数据[id].角色.坐骑列表[加血对象].初始成长=玩家数据[id].角色.坐骑列表[加血对象].初始成长+0.05
			if 玩家数据[id].角色.坐骑列表[加血对象].初始成长>1.3433 then
				玩家数据[id].角色.坐骑列表[加血对象].初始成长=1.3433
			end
			常规提示(id,玩家数据[id].角色.坐骑列表[加血对象].名称.."服用了一个福禄丹后，神清气爽，仙气缭绕，初始成长增加了#R0.05#Y点！")
			发送数据(玩家数据[id].连接id,69,玩家数据[id].角色.坐骑列表[加血对象])
			玩家数据[id].角色:刷新信息("1")
			道具使用=true
		end
	elseif 名称 == "高级摄灵珠" or 名称 == "摄灵珠" then
		if 加血对象==0 then
			常规提示(id,"请选择一只坐骑")
			return
		end
		if 内容.窗口 =="坐骑" and 玩家数据[id].角色.坐骑列表[加血对象] then
			local zuoqi=玩家数据[id].角色.坐骑列表[加血对象]
			if zuoqi.额外成长>=0.9 then
				常规提示(id,"这只坐骑的额外成长最高不能超过1。")
				return
			end
			local lq=self.数据[道具id].灵气
			if 取随机数()<=10 then
				lq=lq*2
			end
			if zuoqi.SLZ==nil then
				zuoqi.SLZ={次数=100,灵气=0,时间=os.time()+86400,成长次数=1}
			else
				if os.time()-zuoqi.SLZ.时间>0 then
					zuoqi.SLZ.次数=100
					zuoqi.SLZ.时间=os.time()+86400
				elseif zuoqi.SLZ.次数<=0 then
					常规提示(id,"#Y/这只坐骑今日剩喂食次数不足，"..时间转换(zuoqi.SLZ.时间).."后才能继续喂食！")
					return
				end
			end
			zuoqi.SLZ.灵气=zuoqi.SLZ.灵气+self.数据[道具id].灵气
			zuoqi.SLZ.次数=zuoqi.SLZ.次数-1
			zuoqi.SLZ.时间=os.time()+86400
			local gs = {80,200,320,520,820,1240,1820,2640,3760,6480}
			if zuoqi.SLZ.灵气>=gs[zuoqi.SLZ.成长次数] then
				zuoqi.SLZ.成长次数=zuoqi.SLZ.成长次数+1
				zuoqi.额外成长=zuoqi.额外成长+0.1
				常规提示(id,"#G"..zuoqi.名称.."服用了一个摄灵珠后，额外成长增加了0.1。")
				if zuoqi.额外成长>=0.9 then
					zuoqi.SLZ=nil
				end
			else
				常规提示(id,zuoqi.名称.."服用了一个摄灵珠后，灵气增加了#R"..lq.."#Y点，当前灵气#R"..zuoqi.SLZ.灵气.."#Y点，今日还可以喂食#R"..zuoqi.SLZ.次数.."#Y次。")
			end
			发送数据(玩家数据[id].连接id,69,zuoqi)
			玩家数据[id].角色:刷新信息("1")
			道具使用=true
		end
	elseif 名称=="特殊兽决·碎片" then
		if self.数据[道具id].数量<50 then
			常规提示(id,"还是先凑齐50个碎片再打开看看吧。")
			return
		end
		self:删除道具(连接id,id,包裹类型,道具id,道具格子,50)
		发送数据(连接id,3699)
		local 链接 = {提示=format("#G/%s#P/集齐了50本特殊兽决·碎片，打开一看发现了一本",玩家数据[id].角色.名称),频道="xt",结尾="#28#P！"}
		玩家数据[id].道具:给予超链接道具(id,"高级魔兽要诀",nil,取碎片特殊要诀(),链接,"成功")
	elseif 名称=="五宝盒" then
		玩家数据[id].道具:给予道具(id,取五宝())
		道具使用=true
	elseif 名称=="墨魂灯" then
		墨魂笔之踪:使用墨魂灯(id)
		return
	elseif 名称=="阿盘的指示图" then
		神归昆仑镜:阿盘的指示图(id)
		return
	elseif 名称=="未激活的符石" or 名称=="未激活的星石" then
		self:激活符石对话(id,道具id)
		return
	elseif 名称=="招魂帖" then
		if self:取队长权限(id)==false then
			常规提示(id,"#Y/你不是队长，无法使用此道具！")
			return
		elseif self.数据[道具id].地图编号~=玩家数据[id].角色.地图数据.编号 then
			常规提示(id,"#Y/地图不对哟！")
			return
		elseif math.abs(self.数据[道具id].x-玩家数据[id].角色.地图数据.x/20)>2 and math.abs(self.数据[道具id].y-玩家数据[id].角色.地图数据.y/20)>2 then
			常规提示(id,"#Y/坐标不对哟！")
			return
		end
		道具使用=true
		降妖伏魔:使用招魂帖(id,self.数据[道具id].地图编号,self.数据[道具id].x,self.数据[道具id].y)
	elseif 名称=="逐妖蛊虫" then
		if self:取队长权限(id)==false then
			常规提示(id,"#Y/你不是队长，无法使用此道具！")
			return
		elseif self.数据[道具id].地图编号~=玩家数据[id].角色.地图数据.编号 then
			常规提示(id,"#Y/地图不对哟！")
			return
		elseif math.abs(self.数据[道具id].x-玩家数据[id].角色.地图数据.x/20)>2 and math.abs(self.数据[道具id].y-玩家数据[id].角色.地图数据.y/20)>2 then
			常规提示(id,"#Y/坐标不对哟！")
			return
		end
		道具使用=true
		降妖伏魔:使用逐妖蛊虫(id)
	elseif 名称=="新手大礼包" then
		发送数据(连接id,3715,{是否领取=玩家数据[id].角色.成长礼包})
		return
	elseif 名称=="灵犀玉礼包" then
		local 道具格子=玩家数据[id].角色:取道具格子2()
		if 道具格子<10 then
			常规提示(id,"您的道具栏物品已经满啦")
			return 0
		end
		for i=1,10 do
			玩家数据[id].道具:给予道具(id,"灵犀玉",nil,true)
		end
		道具使用=true
	elseif 名称=="九转金丹礼包" then
		local 道具格子=玩家数据[id].角色:取道具格子2()
		if 道具格子<10 then
			常规提示(id,"您的道具栏物品已经满啦")
			return 0
		end
		玩家数据[id].道具:给予道具(id,"九转金丹",300)
		玩家数据[id].道具:给予道具(id,"九转金丹",300)
		玩家数据[id].道具:给予道具(id,"九转金丹",300)
		玩家数据[id].道具:给予道具(id,"九转金丹",300)
		玩家数据[id].道具:给予道具(id,"九转金丹",300)
		玩家数据[id].道具:给予道具(id,"九转金丹",300)
		玩家数据[id].道具:给予道具(id,"九转金丹",300)
		玩家数据[id].道具:给予道具(id,"九转金丹",300)
		玩家数据[id].道具:给予道具(id,"九转金丹",300)
		玩家数据[id].道具:给予道具(id,"九转金丹",300)
		道具使用=true
	elseif 名称=="特效宝珠礼包" then
		local 道具格子=玩家数据[id].角色:取道具格子2()
		if 道具格子<10 then
			常规提示(id,"您的道具栏物品已经满啦")
			return 0
		end
		玩家数据[id].道具:给予道具(id,"特效宝珠")
		玩家数据[id].道具:给予道具(id,"特效宝珠")
		玩家数据[id].道具:给予道具(id,"特效宝珠")
		玩家数据[id].道具:给予道具(id,"特效宝珠")
		玩家数据[id].道具:给予道具(id,"特效宝珠")
		玩家数据[id].道具:给予道具(id,"特效宝珠")
		玩家数据[id].道具:给予道具(id,"特效宝珠")
		玩家数据[id].道具:给予道具(id,"特效宝珠")
		玩家数据[id].道具:给予道具(id,"特效宝珠")
		玩家数据[id].道具:给予道具(id,"特效宝珠")
		道具使用=true
	elseif 名称=="160附魔宝珠礼包" then
		local 道具格子=玩家数据[id].角色:取道具格子2()
		if 道具格子<10 then
			常规提示(id,"您的道具栏物品已经满啦")
			return 0
		end
		玩家数据[id].道具:给予道具(id,"附魔宝珠",160)
		玩家数据[id].道具:给予道具(id,"附魔宝珠",160)
		玩家数据[id].道具:给予道具(id,"附魔宝珠",160)
		玩家数据[id].道具:给予道具(id,"附魔宝珠",160)
		玩家数据[id].道具:给予道具(id,"附魔宝珠",160)
		玩家数据[id].道具:给予道具(id,"附魔宝珠",160)
		玩家数据[id].道具:给予道具(id,"附魔宝珠",160)
		玩家数据[id].道具:给予道具(id,"附魔宝珠",160)
		玩家数据[id].道具:给予道具(id,"附魔宝珠",160)
		玩家数据[id].道具:给予道具(id,"附魔宝珠",160)
		道具使用=true
	elseif 名称=="120无级别礼包" then
		礼包奖励类:全套专用装备(id,120,"无级别限制",id)
		道具使用=true
	elseif 名称=="130无级别礼包" then
		礼包奖励类:全套专用装备(id,130,"无级别限制",id)
		道具使用=true
	elseif 名称=="140无级别礼包" then
		礼包奖励类:全套专用装备(id,140,"无级别限制",id)
		道具使用=true
	elseif 名称=="60灵饰礼包" then
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"枫华戒",60,"戒指"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"翠叶环",60,"耳饰"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"香木镯",60,"手镯"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"芝兰佩",60,"佩饰"))
		道具使用=true
	elseif 名称=="80灵饰礼包" then
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"芙蓉戒",80,"戒指"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"明月珰",80,"耳饰"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"翡玉镯",80,"手镯"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"逸云佩",80,"佩饰"))
		道具使用=true
	elseif 名称=="100灵饰礼包" then
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"金麟绕",100,"戒指"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"玉蝶翩",100,"耳饰"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"墨影扣",100,"手镯"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"莲音玦",100,"佩饰"))
		道具使用=true
	elseif 名称=="120灵饰礼包" then
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"悦碧水",120,"戒指"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"点星芒",120,"耳饰"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"花映月",120,"手镯"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"相思染",120,"佩饰"))
		道具使用=true
	elseif 名称=="140灵饰礼包" then
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"九曜光华",140,"戒指"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"凤羽流苏",140,"耳饰"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"金水菩提",140,"手镯"))
		玩家数据[id].道具:给予道具(id,nil,nil,nil,nil,nil,玩家数据[id].道具:dz灵饰处理(id,"玄龙苍珀",140,"佩饰"))
		道具使用=true
	elseif 名称=="机缘宝箱" then
		抽奖处理:设置钥匙神秘宝箱(id,连接id)
		道具使用=true
	elseif 名称=="秘宝宝箱" then
		抽奖处理:转盘抽奖(id)
		道具使用=true
	elseif 名称=="祈愿宝箱" then
		帮派迷宫:开祈愿宝箱(id)
		self.数据[道具id].次数=self.数据[道具id].次数-1
		if self.数据[道具id].次数<=0 then
			道具使用=true
		else
			return
		end
	elseif 名称=="符纸" then
		if 玩家数据[id].角色:取任务(1163)~=0 and 任务数据[玩家数据[id].角色:取任务(1163)].分类==7 and 任务数据[玩家数据[id].角色:取任务(1163)].子类==2 then
			if 任务数据[玩家数据[id].角色:取任务(1163)].SZDT=="大雁塔一层" and 玩家数据[id].角色.地图数据.编号==1004 then
				文韵墨香:大雁塔烧纸(id)
				道具使用=true
			elseif 任务数据[玩家数据[id].角色:取任务(1163)].SZDT=="大雁塔二层" and 玩家数据[id].角色.地图数据.编号==1005 then
				文韵墨香:大雁塔烧纸(id)
				道具使用=true
			else
				常规提示(id,"请到指定地点焚烧！")
				return
			end
		else
			常规提示(id,"请到指定地点焚烧！")
			return
		end
	elseif 名称=="鬼谷子" then
		if 玩家数据[id].角色.阵法[self.数据[道具id].子类]==nil then
			玩家数据[id].角色.阵法[self.数据[道具id].子类]=1
			常规提示(id,"恭喜你学会了如何使用#R/"..self.数据[道具id].子类)
			道具使用=true
		else
			常规提示(id,"你已经学过如何使用这个阵型了，请勿重复学习")
			return
		end
	elseif self.数据[道具id].总类==11 and self.数据[道具id].分类==2 then
		发送数据(玩家数据[id].连接id,3529,{地图=self.数据[道具id].地图,xy=self.数据[道具id].xy})
		玩家数据[id].道具操作={类型=包裹类型,编号=内容.编号,id=道具id}
		玩家数据[id].最后操作="合成旗"
		return
	elseif 名称=="怪物卡片" then
		local 剧情等级=玩家数据[id].角色:取剧情技能等级("变化之术")
		if 剧情等级<6 and self.数据[道具id].等级>剧情等级 then
			常规提示(id,"#Y/你的变化之术等级太低了")
			return
		end
		if 玩家数据[id].角色:取任务(1)~=0 then
			任务数据[玩家数据[id].角色:取任务(1)]=nil
			玩家数据[id].角色:取消任务(1)
		end
		玩家数据[id].角色.变身数据=self.数据[道具id].造型
		道具使用=true
		玩家数据[id].角色:刷新信息()
		发送数据(连接id,37,玩家数据[id].角色.变身数据)
		常规提示(id,"你使用了怪物卡片")
		发送数据(连接id,47,{玩家数据[id].角色:取气血数据()})
		发送数据(玩家数据[id].连接id,12)
		设置任务1(id,剧情等级,玩家数据[id].角色.变身数据)
		地图处理类:更改模型(id,玩家数据[id].角色.变身数据,1)
	elseif 名称=="小象炫卡" or 名称=="腾蛇炫卡" or 名称=="龙马炫卡" or 名称=="雪人炫卡" then
		if 玩家数据[id].角色:取任务(1)~=0 then
			任务数据[玩家数据[id].角色:取任务(1)]=nil
			玩家数据[id].角色:取消任务(1)
		end
		玩家数据[id].角色.变身数据=self.数据[道具id].造型
		道具使用=true
		玩家数据[id].角色:刷新信息()
		发送数据(连接id,37,玩家数据[id].角色.变身数据)
		常规提示(id,"你使用了怪物卡片")
		发送数据(连接id,47,{玩家数据[id].角色:取气血数据()})
		发送数据(玩家数据[id].连接id,12)
		设置任务1a(id,剧情等级,玩家数据[id].角色.变身数据)
		地图处理类:更改模型(id,玩家数据[id].角色.变身数据,1)
	elseif 名称=="摄妖香" then
		if 玩家数据[id].角色:取任务(9)~=0 then
			玩家数据[id].角色:取消任务(玩家数据[id].角色:取任务(9))
		end
		设置任务9(id)
		常规提示(id,"#Y/你使用了摄妖香")
		道具使用=true
	elseif 名称=="法宝任务书" then
		任务处理类:添加法宝任务(id)
		常规提示(id,"#Y/你使用了法宝任务书")
		道具使用=true
	elseif 名称=="未鉴定的灵犀玉" then
		玩家数据[id].道具:给予道具(id,"灵犀玉")
		道具使用=true
	elseif 名称 == "清灵净瓶" then
		local 随机瓶子={"高级清灵仙露","中级清灵仙露","初级清灵仙露"}
		local a =随机瓶子[取随机数(1,#随机瓶子)]
		self:给予道具(id,a)
		道具使用=true
	elseif 名称=="洞冥草" then
		if 玩家数据[id].角色:取任务(9)~=0 then
			玩家数据[id].角色:取消任务(玩家数据[id].角色:取任务(9))
			常规提示(id,"#Y/你解除了摄妖香的效果")
			道具使用=true
		end
	elseif 名称=="无常勾魂索" then
		发送数据(玩家数据[id].连接id,3706)
		return
	elseif 名称=="天书" then
		发送数据(玩家数据[id].连接id,3720)
		return
	elseif 名称=="三界悬赏令" then
		if 玩家数据[id].角色:取任务(209)~=0 then
			常规提示(id,"#Y/你已经有个悬赏任务在进行了")
		else
			任务处理类:添加三界悬赏任务(id)
			常规提示(id,"#Y/你获得了三界悬赏任务")
			道具使用=true
		end
	elseif 名称=="树苗" then
		设置任务505(id)
		常规提示(id,"#Y/你种下了树苗")
		道具使用=true
	elseif 名称=="逍遥镜" then
	    if self.数据[道具id].绑定孩子 == nil then
	      if #玩家数据[id].孩子.数据 == 0 then
	        常规提示(id,"你没有可以带出的孩子！")
	        return
	      else
	        local 选项 = {}
	        for i=1,#玩家数据[id].孩子.数据 do
	          选项[#选项+1] = 玩家数据[id].孩子.数据[i].名称
	        end
	        选项[#选项+1] = "我再想想"
	        发送数据(玩家数据[id].连接id,1501,{选项=选项,对话="请选择你要带出的孩子"})
	        玩家数据[id].携带孩子 = {道具id=道具id}
	      end
	    else
	      常规提示(id,"你要带"..self.数据[道具id].绑定孩子.名称.."我去哪里呀")
	    end
	    return
		elseif 名称=="红罗羹" then
			if 玩家数据[id].角色:取任务(10)~=0 then
				local 任务id=玩家数据[id].角色:取任务(10)
				if 任务数据[任务id].气血 == nil then
					任务数据[任务id].气血 = 0
				end
				任务数据[任务id].气血=任务数据[任务id].气血+(self.数据[道具id].阶品*200+15000)
				常规提示(id,"#Y/你使用了红罗羹")
				道具使用=true
				玩家数据[id].角色:刷新任务跟踪()
			else
				设置任务10(id,self.数据[道具id].阶品*200+15000,0,0)
				常规提示(id,"#Y/你使用了红罗羹")
				道具使用=true
			end
		elseif 名称=="绿芦羹" then
			if 玩家数据[id].角色:取任务(10)~=0 then
				local 任务id=玩家数据[id].角色:取任务(10)
				if 任务数据[任务id].魔法 == nil then
					任务数据[任务id].魔法 = 0
				end
				任务数据[任务id].魔法=任务数据[任务id].魔法+(self.数据[道具id].阶品*70+5000)
				常规提示(id,"#Y/你使用了秘制绿罗羹")
				道具使用=true
				玩家数据[id].角色:刷新任务跟踪()
			else
				设置任务10(id,0,self.数据[道具id].阶品*70+5000,0)
				常规提示(id,"#Y/你使用了秘制绿罗羹")
				道具使用=true
			end
		elseif 名称=="秘制红罗羹" then
			if 玩家数据[id].角色:取任务(10)~=0 then
				local 任务id=玩家数据[id].角色:取任务(10)
				if 任务数据[任务id].气血 == nil then
					任务数据[任务id].气血 = 0
				end
				任务数据[任务id].气血=任务数据[任务id].气血+200000
				常规提示(id,"#Y/你使用了秘制红罗羹")
				道具使用=true
				玩家数据[id].角色:刷新任务跟踪()
			else
				设置任务10(id,200000,0,0)
				常规提示(id,"#Y/你使用了秘制红罗羹")
				道具使用=true
			end
		elseif 名称=="秘制绿芦羹" then
			if 玩家数据[id].角色:取任务(10)~=0 then
				local 任务id=玩家数据[id].角色:取任务(10)
				if 任务数据[任务id].魔法 == nil then
					任务数据[任务id].魔法 = 0
				end
				任务数据[任务id].魔法=任务数据[任务id].魔法+65000
				常规提示(id,"#Y/你使用了秘制绿罗羹")
				道具使用=true
				玩家数据[id].角色:刷新任务跟踪()
			else
				设置任务10(id,0,65000,0)
				常规提示(id,"#Y/你使用了秘制绿罗羹")
				道具使用=true
			end
		elseif 名称=="秘制紫芦羹" then
			if 玩家数据[id].角色:取任务(10)~=0 then
				local 任务id=玩家数据[id].角色:取任务(10)
				if 任务数据[任务id].气血 == nil then
					任务数据[任务id].气血 = 0
				end
				任务数据[任务id].气血=任务数据[任务id].气血+900000
				任务数据[任务id].魔法=任务数据[任务id].魔法+650000
				常规提示(id,"#Y/你使用了秘制紫芦羹")
				道具使用=true
				玩家数据[id].角色:刷新任务跟踪()
			else
				设置任务10(id,900000,650000,0)
				常规提示(id,"#Y/你使用了秘制紫芦羹")
				道具使用=true
			end
		elseif 名称=="秘制回梦饮" then  --
			if 玩家数据[id].角色.储备灵气 then
			  	玩家数据[id].角色.储备灵气 = 玩家数据[id].角色.储备灵气 + 200
			else
			  	玩家数据[id].角色.储备灵气 = 200
			end
			常规提示(id,"#Y/你获得了#R/160#Y/点神仙饮灵气，现有神仙饮灵气#R"..玩家数据[id].角色.储备灵气.."#Y/点")
			道具使用=true
		elseif 名称=="神仙饮" then  --
			if 玩家数据[id].角色.神仙饮灵气 then
			  	玩家数据[id].角色.神仙饮灵气 = 玩家数据[id].角色.神仙饮灵气 + 200
			else
			  	玩家数据[id].角色.神仙饮灵气 = 200
			end
			常规提示(id,"#Y/你获得了#R/200#Y/点神仙饮灵气，现有神仙饮灵气#R"..玩家数据[id].角色.神仙饮灵气.."#Y/点")
			道具使用=true
		elseif 名称=="百岁香" then
			if 玩家数据[id].角色.活力+(self.数据[道具id].阶品*2+150) > 玩家数据[id].角色.最大活力 then 常规提示(id,"使用后活力超过了最大数值无法使用") return end
			if 玩家数据[id].角色.体力+(self.数据[道具id].阶品*2+150) > 玩家数据[id].角色.最大体力 then 常规提示(id,"使用后体力超过了最大数值无法使用") return end
			玩家数据[id].角色.活力 = 玩家数据[id].角色.活力+(self.数据[道具id].阶品*2+150)
			玩家数据[id].角色.体力 = 玩家数据[id].角色.体力+(self.数据[道具id].阶品*2+150)
			道具使用=true
		elseif 名称=="白色导标旗" or 名称=="黄色导标旗" or 名称=="蓝色导标旗" or 名称=="绿色导标旗" or 名称=="红色导标旗"  then
			if self.数据[道具id].地图==nil then
				local 地图=玩家数据[id].角色.地图数据.编号
				if 地图~=1001 and 地图~=1070 and 地图~=1208 and 地图~=1092 and 地图~=1122 then
					常规提示(id,"只有长安城、长寿村、傲来国、朱紫国、地府、这四个城市才可以定标哟！")
					return
				end
				local 等级=玩家数据[id].角色:取剧情技能等级("奇门遁甲")
				if 地图==1070 and 等级<1 then
					常规提示(id,"本场景定标需要您的奇门遁甲技能等级达到1级")
					return
				elseif 地图==1092 and 等级<2 then
					常规提示(id,"本场景定标需要您的奇门遁甲技能等级达到2级")
					return
				elseif 地图==1001 and 等级<1 then
					常规提示(id,"本场景定标需要您的奇门遁甲技能等级达到3级")
					return
				elseif 地图==1208 and 等级<2 then
					常规提示(id,"本场景定标需要您的奇门遁甲技能等级达到4级")
					return
				end
				self.数据[道具id].地图=玩家数据[id].角色.地图数据.编号
				self.数据[道具id].x=math.floor(玩家数据[id].角色.地图数据.x/20)
				self.数据[道具id].y=math.floor(玩家数据[id].角色.地图数据.y/20)
				self.数据[道具id].次数=20
				发送数据(玩家数据[id].连接id,3699)
				常规提示(id,"定标成功！")
				return
			else
				发送数据(玩家数据[id].连接id,1501,{选项={"请送我过去","我再想想"},对话=format("请确认是否要传送至#G/%s(%s,%s)#W/处",取地图名称(self.数据[道具id].地图),self.数据[道具id].x,self.数据[道具id].y)})
				玩家数据[id].道具操作={类型=包裹类型,编号=内容.编号,id=道具id}
				玩家数据[id].最后操作="导标旗"
				return
			end
		elseif 名称=="墨魂灯" then
			return
		elseif 名称=="飞行符" then
			if self:取飞行限制(id)==false then
				玩家数据[id].道具操作={类型=包裹类型,编号=内容.编号,id=道具id}
				玩家数据[id].最后操作="飞行符"
				发送数据(连接id,13)
			end
			return
		elseif 名称=="新春飞行符" then
			if self:取飞行限制(id)==false then
				玩家数据[id].道具操作={类型=包裹类型,编号=内容.编号,id=道具id}
				玩家数据[id].最后操作="新春飞行符"
				发送数据(连接id,26)
			end
			return
		elseif 名称=="鱼竿" then
			if 玩家数据[id].队伍~=0 and 玩家数据[id].队长==false then
				常规提示(id,"#y/请联系队长使用")
				return
			end
			if 玩家数据[id].角色.地图数据.编号~=1092 then
				常规提示(id,"#y/请前往傲来国垂钓")
				return
			end
			if math.floor(玩家数据[id].角色.地图数据.x/20)<140
				or math.floor(玩家数据[id].角色.地图数据.x/20)>160
				or math.floor(玩家数据[id].角色.地图数据.y/20)<140
				or math.floor(玩家数据[id].角色.地图数据.y/20)>150 then
				常规提示(id,"#y/这里不是钓鱼的位置，渔夫的边上垂钓")
				return
			end
			if 玩家数据[id].队伍~=0 then
				local ll
				for n=1,#队伍数据[玩家数据[id].队伍].成员数据 do
					ll=队伍数据[玩家数据[id].队伍].成员数据[n]
				end
				发送数据(玩家数据[ll].连接id,146)
				发送数据(玩家数据[id].连接id,146)
				return
			else
				发送数据(连接id,146)
				return
			end
		elseif 名称=="天眼通符" then
			if 玩家数据[id].角色:取任务(8)~=0 then
				任务数据[玩家数据[id].角色:取任务(8)].显示x= 任务数据[玩家数据[id].角色:取任务(8)].x
				任务数据[玩家数据[id].角色:取任务(8)].显示y= 任务数据[玩家数据[id].角色:取任务(8)].y
				道具使用=true
				常规提示(id,"#Y/你使用了天眼通符")
				玩家数据[id].角色:刷新任务跟踪()
				地图处理类:跳转地图(id,任务数据[玩家数据[id].角色:取任务(8)].地图编号,任务数据[玩家数据[id].角色:取任务(8)].x,任务数据[玩家数据[id].角色:取任务(8)].y)
			elseif 玩家数据[id].角色:取任务(14)~=0 then
				任务数据[玩家数据[id].角色:取任务(14)].显示x= 任务数据[玩家数据[id].角色:取任务(14)].x
				任务数据[玩家数据[id].角色:取任务(14)].显示y= 任务数据[玩家数据[id].角色:取任务(14)].y
				道具使用=true
				常规提示(id,"#Y/你使用了天眼通符")
				玩家数据[id].角色:刷新任务跟踪()
				if 调试模式 then
					地图处理类:跳转地图(id,任务数据[玩家数据[id].角色:取任务(14)].地图编号,任务数据[玩家数据[id].角色:取任务(14)].x,任务数据[玩家数据[id].角色:取任务(14)].y)
				end
			elseif 玩家数据[id].角色:取任务(107)~=0 then
				local 任务id=玩家数据[id].角色:取任务(107)
				门派传送={
				大唐官府={x=139,y=46,z=1198}
				,化生寺={x=34,y=74,z=1002}
				,方寸山={x=46,y=125,z=1135}
				,女儿村={x=81,y=80,z=1142}
				,神木林={x=42,y=161,z=1138}
				,盘丝洞={x=145,y=106,z=1513}
				,魔王寨={x=32,y=43,z=1512}
				,狮驼岭={x=90,y=5,z=1131}
				,阴曹地府={x=62,y=79,z=1122}
				,无底洞={x=58,y=86,z=1139}
				,天宫={x=195,y=132,z=1111}
				,普陀山={x=70,y=50,z=1140}
				,凌波城={x=44,y=79,z=1150}
				,五庄观={x=39,y=32,z=1146}
				,龙宫={x=71,y=78,z=1116}
				}
				道具使用=true
				常规提示(id,"#Y/你使用了天眼通符")
				地图处理类:跳转地图(id,门派传送[Q_门派编号[任务数据[任务id].当前序列]].z,门派传送[Q_门派编号[任务数据[任务id].当前序列]].x,门派传送[Q_门派编号[任务数据[任务id].当前序列]].y)
			end
		elseif 名称=="翡翠琵琶" then
			if 玩家数据[id].角色.炼丹灵气 == nil then
				玩家数据[id].角色.炼丹灵气 = 5000
			else
				玩家数据[id].角色.炼丹灵气 = 玩家数据[id].角色.炼丹灵气 + 5000
			end
			常规提示(id,"你使用了翡翠琵琶,当前可用炼丹灵气为#R".. 玩家数据[id].角色.炼丹灵气 .."#Y点")
			道具使用=true
		elseif 名称=="藏宝图" or 名称=="高级藏宝图" or 名称=="玲珑宝图" or 名称=="考古铁铲" then
			if self:取队长权限(id)==false then
				常规提示(id,"#Y/你不是队长，无法使用此道具！")
				return
			elseif self.数据[道具id].地图编号~=玩家数据[id].角色.地图数据.编号 then
				常规提示(id,"#Y/宝图记载的藏宝地点是在#R/" .. 取地图名称(self.数据[道具id].地图编号) .. "#Y/，你好像走错路了")
				return
			elseif 取两点距离(玩家数据[id].角色.地图数据,{x = self.数据[道具id].x*20,y = self.数据[道具id].y*20}) > 100 then
				常规提示(id,"#Y/宝藏就在(" .. self.数据[道具id].x .. "，" ..self.数据[道具id].y .. ")附近")
				return
			end
			道具使用=true
			if 名称=="高级藏宝图" then
				self:高级藏宝图处理(id)
			elseif 名称=="玲珑宝图" then
				self:玲珑宝图处理(id)
			elseif 名称=="藏宝图" then
				self:低级藏宝图处理(id)
			else
				self:考古铁铲处理(id)
			end
		elseif 名称=="九转金丹" then
			if  玩家数据[id].角色.修炼[玩家数据[id].角色.修炼.当前][1] >= 玩家数据[id].角色.修炼[玩家数据[id].角色.修炼.当前][3]  then 常规提示(id,"#Y/你的此项修炼已经达上限") return end
			玩家数据[id].角色:添加人物修炼经验(id,math.floor((self.数据[道具id].阶品 or 1)*0.5))
			道具使用=true
		elseif 名称=="修炼果" then
			if  玩家数据[id].角色.bb修炼[玩家数据[id].角色.bb修炼.当前][1]>= 玩家数据[id].角色.bb修炼[玩家数据[id].角色.bb修炼.当前][3]  then 常规提示(id,"#Y/你的此项修炼已经达上限") return end
			玩家数据[id].角色:添加bb修炼经验(id,150)
			道具使用=true
		elseif 名称=="授业经验书" then
			玩家数据[id].角色:添加经验(25000000,"五行授业")
			道具使用=true
			发送数据(玩家数据[id].连接id,1501,{名称="修业道童",模型="道童",对话=format("五行相生相克，循环不断，生生不息！恭喜你修为增加了！")})
		elseif 名称=="吸附石" then
			发送数据(玩家数据[id].连接id,245)
			return
		elseif 名称=="帮派银票" then
			道具使用=true
	    	常规提示(id,"#Y你拥有的银票，存有#R"..self.数据[道具id].初始金额.."#Y两银子#G\n#Y你需要赚到#R"..self.数据[道具id].完成金额.."#Y才可以完成任务")
	    	return
		elseif 名称=="储灵袋" then
			for n = 1,4 do
				local 已佩戴=玩家数据[id].角色.法宝佩戴[n]
				if 已佩戴 and self.数据[已佩戴] and self.数据[已佩戴].魔法 then
					local 上限=取灵气上限(self.数据[已佩戴].分类)
					self.数据[已佩戴].魔法=self.数据[已佩戴].魔法+15
					if self.数据[已佩戴].魔法>上限 then
						self.数据[已佩戴].魔法=上限
					end
					消息提示(id, "你的法宝"..self.数据[已佩戴].名称.."灵气增加了15点")
				end
			end
			道具使用=true
		elseif 名称=="回梦丹" then
			if 玩家数据[id].角色:取任务(15)~=0 then
				常规提示(id,"#Y/当前已经使用回梦丹了，无法再使用该物品")
				return
			end
			设置任务15(id)
			道具使用=true
		elseif 名称=="海马" then
			玩家数据[id].角色.活力 = 玩家数据[id].角色.活力+150
			玩家数据[id].角色.体力 = 玩家数据[id].角色.体力+150
			if 玩家数据[id].角色.活力 > 玩家数据[id].角色.最大活力 then
				玩家数据[id].角色.活力=玩家数据[id].角色.最大活力
			end
			if 玩家数据[id].角色.体力 > 玩家数据[id].角色.最大体力 then
				玩家数据[id].角色.体力=玩家数据[id].角色.最大体力
			end
			体活刷新(id)
			道具使用=true
		elseif 名称=="梦幻西瓜" then
			玩家数据[id].角色.气血=玩家数据[id].角色.最大气血
			玩家数据[id].角色.魔法=玩家数据[id].角色.最大魔法
			发送数据(连接id,47,{玩家数据[id].角色:取气血数据()})
			道具使用=true
		elseif self.数据[道具id].名称 == "空白强化符" or self.数据[道具id].名称 == "强化符" or self.数据[道具id].名称 == "神兵图鉴" or self.数据[道具id].名称 == "灵宝图鉴" or self.数据[道具id].名称 == "灵饰图鉴" or self.数据[道具id].名称 == "特技书" or self.数据[道具id].名称 == "特效宝珠" or self.数据[道具id].名称 == "超简易宝珠" or self.数据[道具id].名称 == "愤怒符" then
			发送数据(玩家数据[id].连接id,233,{道具格子,self.数据[道具id].总类,self.数据[道具id].分类,self.数据[道具id].子类,"鉴定"})
			return
		elseif 名称=="附魔宝珠" then
			发送数据(连接id,3717,{道具=self:索要道具1(id),宝珠数据 = 道具id})
			return
		elseif 名称=="炫彩ID" then
			玩家数据[id].角色.id特效 = self.数据[道具id].特效
			道具使用=true
			常规提示(id,"使用成功！ID特效已更换为："..self.数据[道具id].特效.."，重新登录后生效")
			-- 注意：炫彩ID特效需要重新登录才能生效，无需立即同步给其他玩家
		elseif self.数据[道具id].名称 == "神秘宝箱" then
			if self:消耗背包道具(连接id,id,"神秘钥匙",1) then
				道具使用=true
				商店处理类:设置神秘宝箱(id)
				发送数据(连接id,235,{道具=神秘宝箱[id],类型="神秘宝箱"})
			else
				常规提示(id,"你没有神秘钥匙无法打开神秘宝箱")
			end
		elseif self.数据[道具id].名称 == "陨铁" then
			发送数据(连接id,239)
			道具提示 = false
		elseif self.数据[道具id].名称 == "秘制食谱" then
			local lssj = 秘制食谱子类()[self.数据[道具id].子类]
			if 玩家数据[id].角色.秘制食谱.食谱[lssj] == nil then
				玩家数据[id].角色.秘制食谱.食谱[lssj] = {次数=秘制食谱消耗()[lssj].次数}
			else
				玩家数据[id].角色.秘制食谱.食谱[lssj].次数 =  玩家数据[id].角色.秘制食谱.食谱[lssj].次数+秘制食谱消耗()[lssj].次数
			end
			道具使用=true
		elseif self.数据[道具id].名称 == "长安战报" then
			长安保卫战:获取玩家数据(id)
			道具提示 = false
		elseif self.数据[道具id].名称 == "九霄清心丸" then
			设置任务241(id)
			道具使用=true
		elseif self.数据[道具id].名称 == "天机培元丹" then
			if not 初始活动.昆仑仙境[id] then
				初始活动.昆仑仙境[id]={次数=0,最大次数=40,时间=os.time() + 180,天机培元丹使用次数=0}
			end
			
			-- 检查天机培元丹使用次数限制（每日最多使用5次）
			local 使用次数 = 初始活动.昆仑仙境[id].天机培元丹使用次数 or 0
			local 最大使用次数 = 5  -- 每日最多使用5次天机培元丹
			
			if 使用次数 >= 最大使用次数 then
				常规提示(id,"天机培元丹每日最多使用"..最大使用次数.."次，你今日已达到使用上限。")
				return
			end
			
			-- 检查修行次数上限（防止超过合理范围）
			local 当前最大次数 = 初始活动.昆仑仙境[id].最大次数 or 40
			local 绝对上限 = 200  -- 设置绝对上限为200次
			
			if 当前最大次数 >= 绝对上限 then
				常规提示(id,"修行次数已达到绝对上限("..绝对上限.."次)，无法继续增加。")
				return
			end
			
			-- 增加修行次数和使用记录
			初始活动.昆仑仙境[id].最大次数 = math.min(当前最大次数 + 20, 绝对上限)
			初始活动.昆仑仙境[id].天机培元丹使用次数 = 使用次数 + 1
			
			local 实际增加 = 初始活动.昆仑仙境[id].最大次数 - 当前最大次数
			local 剩余次数 = 初始活动.昆仑仙境[id].最大次数 - (初始活动.昆仑仙境[id].次数 or 0)
			local 剩余使用次数 = 最大使用次数 - 初始活动.昆仑仙境[id].天机培元丹使用次数
			
			常规提示(id,"天机培元丹使用成功！修行次数增加了"..实际增加.."次，当前剩余修行次数："..剩余次数.."次。今日还可使用天机培元丹"..剩余使用次数.."次。")
			道具使用=true
	   elseif 名称=="如意符" then
	    if 玩家数据[id].角色.地图数据.编号  == id*10+2 or 玩家数据[id].角色.地图数据.编号== id*10+1  then
	        发送数据(玩家数据[id].连接id,1025)
	        道具使用=true
	        常规提示(id,"请鼠标左键选中你想要销毁的家具进行销毁。")
	    else
	        常规提示(id,"这不是你的房子或者不是室内和庭院")
	        return
	    end
	elseif self.数据[道具id].总类 == "家具" then
	    if 玩家数据[id].角色.地图数据.编号  == id*10+2  then
	      local tx = {x=玩家数据[id].角色.地图数据.x/20,y=玩家数据[id].角色.地图数据.y/20,名称=名称,方向=1}
	      for i=1,#房屋数据 do
	        if 房屋数据[i].ID == id then
	        table.insert(房屋数据[i].室内装饰,tx)
	        发送数据(玩家数据[id].连接id,1027,{tx})
	        end
	      end
	        道具使用=true
	    else
	        常规提示(id,"这不是你的房子或者不是室内")
	        return
	    end
    elseif self.数据[道具id].总类 == "庭院" then
	    if 玩家数据[id].角色.地图数据.编号  == id*10+1  then
	      local tx = {x=玩家数据[id].角色.地图数据.x/20,y=玩家数据[id].角色.地图数据.y/20,名称=名称,方向=1}
	      for i=1,#房屋数据 do
	        if 房屋数据[i].ID == id then
	        table.insert(房屋数据[i].庭院装饰,tx)
	        发送数据(玩家数据[id].连接id,1027,{tx})
	        end
	      end
	        道具使用=true
	    else
	        常规提示(id,"这不是你的房子或者不是庭院")
	        return
	    end
		elseif self.数据[道具id].名称 == "摇钱树苗" then
			if 玩家数据[id].角色:取任务(505)~=0 then
				常规提示(id,"#Y/你之前已经种下一棵摇钱树苗了")
				return
			elseif 地图处理类.遇怪地图[玩家数据[id].角色.地图数据.编号]==nil then
				常规提示(id,"#Y/本场景无法种植树苗")
				return
			elseif 玩家数据[id].队伍~=0 then
				常规提示(id,"队伍中无法进行此任务。")
				return
			else
				设置任务505(id)
				常规提示(id,"#Y/你种下了一颗摇钱树")
				道具使用=true
			end
		end
		if 道具使用 then
			self:删除道具(连接id,id,包裹类型,道具id,道具格子,删除数量)
			发送数据(连接id,3699)
		else
			if 道具提示 then
				常规提示(id,"您无法使用这样的道具")
			end
		end

end
function 道具处理类:清灵仙露处理(连接id,id,加血对象,道具id)
	if 玩家数据[id].召唤兽.数据[加血对象].进阶.灵性 > 100 then
		常规提示(id,"该召唤兽已无法再使用清灵仙露")
		return false
	elseif 玩家数据[id].召唤兽.数据[加血对象].进阶.清灵仙露 >= 8 then
		常规提示(id,"该召唤兽已使用8瓶清灵仙露！无法再继续服用了")
		return false
	end
	local 临时灵性 =1
	if 玩家数据[id].召唤兽.数据[加血对象].进阶.灵性 >= 50 then
		if self.数据[道具id].灵气 == 8 then
			if 玩家数据[id].召唤兽.数据[加血对象].进阶.灵性 >=100 then
				临时灵性 = 取随机数(9,10)
			else
				临时灵性 = self.数据[道具id].灵气
			end
		else
			local jil = 25
			if self.数据[道具id].名称 == "中级清灵仙露" then
				jil = 45
			elseif self.数据[道具id].名称 == "高级清灵仙露" then
				jil = 60
			end
			if 取随机数()<jil then
				临时灵性 = 取随机数(self.数据[道具id].灵气+1,self.数据[道具id].灵气+2)
			else
				临时灵性 = self.数据[道具id].灵气
			end
		end
	else
		常规提示(id,"召唤兽灵性必须达到50以上才能使用清灵仙露！")
		return false
	end
	if self.数据[道具id].名称=="特殊清灵仙露" then
		玩家数据[id].召唤兽.数据[加血对象].潜力 = 玩家数据[id].召唤兽.数据[加血对象].潜力 + (110-玩家数据[id].召唤兽.数据[加血对象].进阶.灵性)*2
		玩家数据[id].召唤兽.数据[加血对象].进阶.灵性 = 110
	else
		玩家数据[id].召唤兽.数据[加血对象].潜力 = 玩家数据[id].召唤兽.数据[加血对象].潜力 + 临时灵性*2
		玩家数据[id].召唤兽.数据[加血对象].进阶.灵性 = 玩家数据[id].召唤兽.数据[加血对象].进阶.灵性 + 临时灵性
		玩家数据[id].召唤兽.数据[加血对象].进阶.清灵仙露 = 玩家数据[id].召唤兽.数据[加血对象].进阶.清灵仙露 + 1
		常规提示(id,玩家数据[id].召唤兽.数据[加血对象].名称.."服用了一个清灵仙露后，神清气爽，仙气缭绕，灵性增加了#R"..临时灵性.."#Y点！（还可以食用#R"..(8-玩家数据[id].召唤兽.数据[加血对象].进阶.清灵仙露).."#Y个清灵仙露）")
	end
	if 玩家数据[id].召唤兽.数据[加血对象].进阶.灵性 == 110 then
		玩家数据[id].召唤兽.数据[加血对象].临时进阶 = {
		力量 = 0,
		敏捷 = 0,
		耐力 = 0,
		魔力 = 0,
		体质 = 0,
		}
		发送数据(连接id,105,{头像="进阶",标题="灵兽现世",说明="获得一只灵性为110的召唤兽！"})
		常规提示(id,"你的召唤兽#R"..玩家数据[id].召唤兽.数据[加血对象].名称.."#Y发生了一些变化，快去找御兽仙（仙缘洞天,151,59）问看看吧？")
	end
	玩家数据[id].召唤兽.数据[加血对象]:刷新信息()
	发送数据(连接id,16,玩家数据[id].召唤兽.数据)
	发送数据(连接id,108,{认证码=玩家数据[id].召唤兽.数据[加血对象].认证码,进阶=玩家数据[id].召唤兽.数据[加血对象].进阶})
	return true
end
function 道具处理类:宝宝进阶(连接id,id,内容)
	if 玩家数据[id].角色.参战信息==nil then
		常规提示(id,"#Y/请将需要进阶的宝宝参战！")
		return
	end
	local 最低灵气 = {}
	if 玩家数据[id].角色.参战宝宝.进阶 and 玩家数据[id].角色.参战宝宝.进阶.灵性==110 then
		local gz = 内容.位置
		for i=1,5 do
			if gz[i]==nil then
				常规提示(id,"#Y/物品数据异常！")
				return
			end
			local 道具id=玩家数据[id].角色["道具"][gz[i]]
			if self.数据[道具id] == nil then
				常规提示(id,"#Y/物品数据异常！")
				return
			elseif self.数据[道具id].名称~="初级清灵仙露" and self.数据[道具id].名称~="中级清灵仙露" and self.数据[道具id].名称~="高级清灵仙露" then
				常规提示(id,"#Y/物品数据异常！")
				return
			end
			最低灵气[i]=self.数据[道具id].灵气
			if self.数据[道具id].数量~=nil and self.数据[道具id].数量>1 then
				self.数据[道具id].数量 = self.数据[道具id].数量 -1
			else
				self.数据[道具id]=nil
				玩家数据[id].角色["道具"][gz[i]]=nil
			end
		end
		table.sort(最低灵气, function (a, b) return a < b end)
		local bbbh=玩家数据[id].召唤兽:取编号(玩家数据[id].角色.参战宝宝.认证码)
		玩家数据[id].召唤兽.数据[bbbh].临时进阶 = {
		力量 = 取随机数(最低灵气[1],最低灵气[5]*3),
		敏捷 = 取随机数(最低灵气[1],最低灵气[5]*3),
		耐力 = 取随机数(最低灵气[1],最低灵气[5]*3),
		魔力 = 取随机数(最低灵气[1],最低灵气[5]*3),
		体质 = 取随机数(最低灵气[1],最低灵气[5]*3),
		}
		常规提示(id,"#Y/清灵仙露的灵气已经环绕在召唤兽周围，请选择希望#R保留哪个属性#Y。")
		玩家数据[id].召唤兽.数据[bbbh]:刷新信息()
		发送数据(连接id,3699)
		发送数据(连接id,3513,玩家数据[id].道具:索要道具2(id))
		发送数据(连接id,110,{宝宝=玩家数据[id].召唤兽.数据[bbbh]})
	else
		常规提示(id,"#Y/提升召唤兽能力需灵性达到110")
		return
	end
end
function 道具处理类:使用合成旗(连接id,id,序列)
	if 玩家数据[id].道具操作==nil then return  end
	local 编号=玩家数据[id].道具操作.编号
	local 道具id=玩家数据[id].角色[玩家数据[id].道具操作.类型][编号]
	if 道具id==nil or self.数据[道具id]==nil or self.数据[道具id].总类~=11 or self.数据[道具id].分类~=2 then
		常规提示(id,"#Y你没有这样的道具")
		return
	end
	if self.数据[道具id].xy[序列]==nil then
		常规提示(id,"#Y错误的坐标选择点")
		return
	end
	if self.数据[道具id].xy[序列].x==nil then
		常规提示(id,"#Y错误的坐标选择点")
		return
	end
	if self.数据[道具id].xy[序列].y==nil then
		常规提示(id,"#Y错误的坐标选择点")
		return
	end
	if self:取飞行限制(id) then
		return
	end
	self.数据[道具id].次数=self.数据[道具id].次数-1
	地图处理类:跳转地图(id,self.数据[道具id].地图,self.数据[道具id].xy[序列].x,self.数据[道具id].xy[序列].y)
	if self.数据[道具id].次数<=0 then
		玩家数据[id].角色[玩家数据[id].道具操作.类型][编号]=nil
		self.数据[道具id]=nil
	else
		常规提示(id,format("#Y你的%s还可以使用%s次",self.数据[道具id].名称,self.数据[道具id].次数))
	end
	玩家数据[id].道具操作=nil
	道具刷新(id)
end
function 道具处理类:使用法宝(连接id,id,编号)
	local 道具id=玩家数据[id].角色.法宝[编号]
	if 道具id==nil or self.数据[道具id]==nil then
		常规提示(id,"#Y你没有这件法宝")
		self:索要法宝(连接id,id)
		return
	end
	local 名称=self.数据[道具id].名称
	if 名称=="五色旗盒" then
		if self.数据[道具id].魔法<=0 then
			常规提示(id,"#Y你的法宝灵气不足")
			return
		elseif 玩家数据[id].角色.等级<60 then
			常规提示(id,"#Y你的等级不足以使用此法宝")
			return
		end
		local aa ="请选择你要进行的操作："
		local xx={"合成导标旗","补充合成旗"}
		发送数据(连接id,1501,{名称="五色旗盒",对话=aa,选项=xx})
		玩家数据[id].最后操作="合成旗1"
		玩家数据[id].法宝id=编号
		return
	elseif 名称=="月光宝盒" then
		if self.数据[道具id].魔法<5 then
			常规提示(id,"#Y你的法宝灵气不足")
			return
		elseif 玩家数据[id].角色.等级<100 then
			常规提示(id,"#Y你的等级不足以使用此法宝")
			return
		end
		local aa ="请选择你要进行的操作："
		local xx={"送我过去","在这里定标"}
		发送数据(连接id,1501,{名称="月光宝盒",对话=aa,选项=xx})
		玩家数据[id].最后操作="月光宝盒"
		玩家数据[id].法宝id=编号
		return
	elseif 名称=="影蛊" then
		if self.数据[道具id].魔法<5 then
			常规提示(id,"#Y你的法宝灵气不足")
			return
		elseif 玩家数据[id].角色.等级<100 then
			常规提示(id,"#Y你的等级不足以使用此法宝")
			return
		end
		发送数据(连接id,63,{名称="影蛊"})
		玩家数据[id].最后操作="影蛊"
		玩家数据[id].法宝id=编号
		return
	end
	常规提示(id,"#Y鼠标左键抓取，方可替换或使用此法宝")
	return
end
function 道具处理类:替换法宝(连接id,id,编号,位置)
	if 编号=="神器" then
		if 玩家数据[id].角色.法宝佩戴[位置] and self.数据[玩家数据[id].角色.法宝佩戴[位置]] then
			if not self:卸下法宝(连接id,id,位置) then
				return
			end
		end
		玩家数据[id].神器:佩戴神器(连接id,id,位置)
		return
	else
		if 玩家数据[id].神器.数据.是否佩戴神器 and 玩家数据[id].神器.数据.格子==位置 then
			玩家数据[id].神器:卸下神器(连接id,id)
		end
	end
	local 道具id=玩家数据[id].角色.法宝[编号]
	if 道具id==nil or self.数据[道具id]==nil then
		常规提示(id,"#Y你没有这件法宝")
		self:索要法宝(连接id,id)
		return
	end
	if 玩家数据[id].角色.等级<self.数据[道具id].特技 then
		常规提示(id,"#Y你当前的等级无法佩戴此类型的法宝")
		return
	end
	if 取法宝门派(self.数据[道具id].名称) ~= "无门派" and 玩家数据[id].角色.门派 ~= 取法宝门派(self.数据[道具id].名称) then
		常规提示(id,"#Y你无法佩戴这个门派的法宝")
		self:索要法宝(连接id,id)
		return
	end
	if 位置 and 位置==4 and  玩家数据[id].角色.等级 <120 then
		常规提示(id,"#Y120级才可以佩戴第4个法宝")
		return
	end
	if self.数据[道具id].分类 == 4 and  玩家数据[id].角色.等级 <120 then return 常规提示(id,"#Y120级才可以佩戴第4个法宝") end
	local 类型判定 = 0
	local 佩戴判定 = 0
	local 同类型=0
	for n = 1,4 do
		local 已佩戴=玩家数据[id].角色.法宝佩戴[n]
		if 玩家数据[id].角色.法宝佩戴[n] and self.数据[已佩戴] then
			-- 检查门派法宝限制
			if 取法宝门派(self.数据[已佩戴].名称) ~= "无门派" and 取法宝门派(self.数据[道具id].名称) ~= "无门派" then
				常规提示(id,"#Y门派法宝只能佩戴一个")
				self:索要法宝(连接id,id)
				return
			end
			if self.数据[已佩戴].子类 == self.数据[道具id].子类 then
				同类型=同类型+1
				if 同类型>=2 and 取法宝门派(self.数据[道具id].名称) ~= "无门派" and 玩家数据[id].角色.门派 ~= 取法宝门派(self.数据[道具id].名称) then
					常规提示(id,"#Y相同类型的法宝佩戴不能超过2个")
					self:索要法宝(连接id,id)
					return
				end
			end
			if self.数据[已佩戴].分类==4 then
				佩戴判定 = 佩戴判定 +1
			end
		end
	end
	if self.数据[道具id].分类 == 4 then
		if 佩戴判定 == 1 then
			if 玩家数据[id].角色.历劫.化圣 == false then
				if 玩家数据[id].角色.法宝佩戴[位置] == nil then
					self:索要法宝(连接id,id)
					return 常规提示(id,"#Y只有化圣后化圣境界3可以佩戴两个四级法宝")
				elseif 玩家数据[id].角色.法宝佩戴[位置] ~= nil and self.数据[玩家数据[id].角色.法宝佩戴[位置]].分类~=4  then
					self:索要法宝(连接id,id)
					return 常规提示(id,"#Y只有化圣后化圣境界3可以佩戴两个四级法宝")
				end
			elseif 玩家数据[id].角色.历劫.化圣 and 玩家数据[id].角色.历劫.化圣境界 ~= nil and 玩家数据[id].角色.历劫.化圣境界 < 3 then
				if 玩家数据[id].角色.法宝佩戴[位置] == nil then
					self:索要法宝(连接id,id)
					return 常规提示(id,"#Y只有化圣后化圣境界3可以佩戴两个四级法宝")
				elseif 玩家数据[id].角色.法宝佩戴[位置] ~= nil and self.数据[玩家数据[id].角色.法宝佩戴[位置]].分类~=4  then
					self:索要法宝(连接id,id)
					return 常规提示(id,"#Y只有化圣后化圣境界3可以佩戴两个四级法宝")
				end
			end
		elseif 佩戴判定 == 2 then
			if 玩家数据[id].角色.历劫.化圣 and 玩家数据[id].角色.历劫.化圣境界 ~= nil and 玩家数据[id].角色.历劫.化圣境界 < 3 then
				self:索要法宝(连接id,id)
				return 常规提示(id,"#Y只有化圣后化圣境界3可以佩戴两个四级法宝")
			elseif 玩家数据[id].角色.历劫.化圣 and 玩家数据[id].角色.历劫.化圣境界 ~= nil and 玩家数据[id].角色.历劫.化圣境界 >= 3 then
				if 玩家数据[id].角色.法宝佩戴[位置] == nil then
					self:索要法宝(连接id,id)
					return 常规提示(id,"#Y只有化圣后化圣境界3可以佩戴两个四级法宝")
				elseif 玩家数据[id].角色.法宝佩戴[位置] ~= nil and self.数据[玩家数据[id].角色.法宝佩戴[位置]].分类~=4  then
					self:索要法宝(连接id,id)
					return 常规提示(id,"#Y只有化圣后化圣境界3可以佩戴两个四级法宝")
				end
			end
		end
	end
	if 玩家数据[id].角色.法宝佩戴[位置]==nil then
		玩家数据[id].角色.法宝佩戴[位置]=玩家数据[id].角色.法宝[编号]
		玩家数据[id].角色.法宝[编号]=nil
	else
		local 临时编号=玩家数据[id].角色.法宝[编号]
		玩家数据[id].角色.法宝[编号]=玩家数据[id].角色.法宝佩戴[位置]
		玩家数据[id].角色.法宝佩戴[位置]=临时编号
	end
	self:索要法宝(连接id,id)
	玩家数据[id].角色:刷新信息()
end
function 道具处理类:卸下法宝(连接id,id,编号,神器)
	if 神器 then
		玩家数据[id].神器:卸下神器(连接id,id)
		return false
	end
	local 格子=玩家数据[id].角色:取法宝格子()
	if 格子==0 then
		常规提示(id,"#Y你的法宝栏已经满了")
		return false
	end
	玩家数据[id].角色.法宝[格子]=玩家数据[id].角色.法宝佩戴[编号]
	玩家数据[id].角色.法宝佩戴[编号]=nil
	self:索要法宝(连接id,id)
	玩家数据[id].角色:刷新信息()
	return true
end
function 道具处理类:修炼法宝(连接id,id,编号)
	local 道具id=玩家数据[id].角色.法宝[编号]
	if 道具id==nil or self.数据[道具id]==nil then
		常规提示(id,"#Y你没有这件法宝")
		self:索要法宝(连接id,id)
		return
	end
	local 上限=9
	if self.数据[道具id].分类==2 then
		上限=12
	elseif self.数据[道具id].分类==3 then
		上限=15
	elseif self.数据[道具id].分类==4 then
		上限=18
	end
	if self.数据[道具id].气血==上限 then
		常规提示(id,"#Y你的这件法宝已经满层了，无法再进行修炼")
		return
	end
	local 消耗经验=math.floor(self.数据[道具id].升级经验*0.5)
	if 消耗经验>10000000 then
		消耗经验=10000000
	end
	if 玩家数据[id].角色.当前经验<消耗经验 then
		常规提示(id,"#Y本次修炼需要消耗#R"..消耗经验.."#Y点人物经验，您似乎没有那么多的经验哟")
		return
	end
	玩家数据[id].角色.当前经验=玩家数据[id].角色.当前经验-消耗经验
	常规提示(id,"#Y修炼成功,你消耗了#R"..消耗经验.."#Y了点人物经验")
	self.数据[道具id].当前经验=self.数据[道具id].当前经验+消耗经验
	if self.数据[道具id].当前经验>=self.数据[道具id].升级经验 then
		self.数据[道具id].气血=self.数据[道具id].气血+1
		self.数据[道具id].当前经验=self.数据[道具id].当前经验-self.数据[道具id].升级经验
		self.数据[道具id].魔法=取灵气上限(self.数据[道具id].分类)
		if self.数据[道具id].气血<上限 then
			self.数据[道具id].升级经验=法宝经验[self.数据[道具id].分类][self.数据[道具id].气血+1]
		end
		常规提示(id,"#Y你的法宝#R"..self.数据[道具id].名称.."#Y境界提升了")
	end
	发送数据(连接id,226,{当前经验=玩家数据[id].角色.当前经验,最大经验=玩家数据[id].角色.最大经验})
	发送数据(连接id,3528,{id=编号,当前经验=self.数据[道具id].当前经验,升级经验=self.数据[道具id].升级经验,境界=self.数据[道具id].气血,灵气=self.数据[道具id].魔法})
end
function 道具处理类:替换灵宝(连接id,id,编号,编号1)
	local 道具id=玩家数据[id].角色.灵宝[编号]
	if 道具id==nil or self.数据[道具id]==nil then
		常规提示(id,"#Y你没有这件法宝")
		self:索要法宝(连接id,id)
		return
	end
	if 玩家数据[id].角色.灵宝佩戴[编号1]==nil then
		玩家数据[id].角色.灵宝佩戴[编号1]=玩家数据[id].角色.灵宝[编号]
		玩家数据[id].角色.灵宝[编号]=nil
	else
		local 临时编号=玩家数据[id].角色.灵宝[编号]
		玩家数据[id].角色.灵宝[编号]=玩家数据[id].角色.灵宝佩戴[编号1]
		玩家数据[id].角色.灵宝佩戴[编号1]=临时编号
	end
	self:索要法宝(连接id,id)
end
function 道具处理类:卸下灵宝(连接id,id,编号)
	local 格子=玩家数据[id].角色:取灵宝格子()
	if 格子==0 then
		常规提示(id,"#Y你的灵宝栏已经满了")
		return
	end
	玩家数据[id].角色.灵宝[格子]=玩家数据[id].角色.灵宝佩戴[编号]
	玩家数据[id].角色.灵宝佩戴[编号]=nil
	self:索要法宝(连接id,id)
end
function 道具处理类:修炼灵宝(连接id,id,编号)
	local 道具id=玩家数据[id].角色.灵宝[编号]
	if 道具id==nil or self.数据[道具id]==nil then
		常规提示(id,"#Y你没有这件灵宝")
		self:索要法宝(连接id,id)
		return
	end
	local 上限=5
	if self.数据[道具id].气血==上限 then
		常规提示(id,"#Y你的这件灵宝已经满层了，无法再进行修炼")
		return
	end
	local 消耗经验=math.floor(self.数据[道具id].升级经验*0.5)
	if 消耗经验>10000000 then
		消耗经验=10000000
	end
	if 玩家数据[id].角色.当前经验<消耗经验 then
		常规提示(id,"#Y本次修炼需要消耗#R"..消耗经验.."#Y点人物经验，您似乎没有那么多的经验哟")
		return
	end
	玩家数据[id].角色.当前经验=玩家数据[id].角色.当前经验-消耗经验
	常规提示(id,"#Y修炼成功,你消耗了#R"..消耗经验.."#Y了点人物经验")
	self.数据[道具id].当前经验=self.数据[道具id].当前经验+消耗经验
	if self.数据[道具id].当前经验>=self.数据[道具id].升级经验 then
		self.数据[道具id].气血=self.数据[道具id].气血+1
		self.数据[道具id].当前经验=self.数据[道具id].当前经验-self.数据[道具id].升级经验
		self.数据[道具id].魔法=取灵气上限(self.数据[道具id].分类)
		if self.数据[道具id].气血<上限 then
			self.数据[道具id].升级经验=灵宝经验[self.数据[道具id].分类][self.数据[道具id].气血+1]
		end
		常规提示(id,"#Y你的灵宝#R"..self.数据[道具id].名称.."#Y境界提升了")
	end
	发送数据(连接id,226,{当前经验=玩家数据[id].角色.当前经验,最大经验=玩家数据[id].角色.最大经验})
	发送数据(连接id,3528,{id=编号,当前经验=self.数据[道具id].当前经验,升级经验=self.数据[道具id].升级经验,境界=self.数据[道具id].气血,灵气=self.数据[道具id].魔法})
end
function 道具处理类:快捷加血(连接id,id,类型)
	if 玩家数据[id].战斗~=0 then return  end
	local 数值=0
	local 编号=0
	if 类型==1 then
		数值=玩家数据[id].角色.最大气血-玩家数据[id].角色.气血
	else
		编号=玩家数据[id].召唤兽:取编号(玩家数据[id].角色.参战宝宝.认证码)
		数值=玩家数据[id].召唤兽.数据[编号].最大气血-玩家数据[id].召唤兽.数据[编号].气血
	end
	if 数值==0 then
		return
	end
	local 恢复=self:快捷加血1(id,数值)
	if 恢复==0 then
		return
	end
	self:加血处理(连接id,id,恢复,编号)
	道具刷新(id)
end
function 道具处理类:快捷加血1(id,数值)
	local 道具={"包子","四叶花","八角莲叶","人参","水黄莲","月见草"}
	local 恢复=0
	local 道具删除={}
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil then
			local 道具id=玩家数据[id].角色.道具[n]
			local 符合=false
			for i=1,#道具 do
				if self.数据[道具id].名称==道具[i] then
					符合=true
				end
			end
			if 符合 then
				if 恢复<数值 and self:取加血道具1(self.数据[道具id].名称,道具id)*self.数据[道具id].数量>=数值 then
					local 扣除数量=0
					for i=1,self.数据[道具id].数量 do
						if 恢复<数值 then
							恢复=恢复+self:取加血道具1(self.数据[道具id].名称,道具id)
							扣除数量=扣除数量+1
						end
					end
					道具删除[#道具删除+1]={格子=n,id=道具id,数量=扣除数量}
				elseif 恢复<数值 then
					恢复= self:取加血道具1(self.数据[道具id].名称,道具id)*self.数据[道具id].数量
					道具删除[#道具删除+1]={格子=n,id=道具id,数量=self.数据[道具id].数量}
				end
			end
		end
	end
	if 恢复~=0 then
		for n=1,#道具删除 do
			self.数据[道具删除[n].id].数量=self.数据[道具删除[n].id].数量-道具删除[n].数量
			if self.数据[道具删除[n].id].数量<=0 then
				玩家数据[id].角色.道具[道具删除[n].格子]=nil
			end
		end
	end
	if 恢复>数值 then 恢复=数值 end
	return 恢复
end
function 道具处理类:快捷加蓝(连接id,id,类型)
	if 玩家数据[id].战斗~=0 then return  end
	local 数值=0
	local 编号=0
	if 类型==1 then
		数值=玩家数据[id].角色.最大魔法-玩家数据[id].角色.魔法
	else
		编号=玩家数据[id].召唤兽:取编号(玩家数据[id].角色.参战宝宝.认证码)
		数值=玩家数据[id].召唤兽.数据[编号].最大魔法-玩家数据[id].召唤兽.数据[编号].魔法
	end
	if 数值==0 then
		return
	end
	local 恢复=self:快捷加蓝1(id,数值)
	if 恢复==0 then
		return
	end
	self:加魔处理(连接id,id,恢复,编号)
	道具刷新(id)
end
function 道具处理类:快捷加蓝1(id,数值)
	local 道具={"鬼切草","佛手","紫丹罗","月见草"}
	local 恢复=0
	local 道具删除={}
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil then
			local 道具id=玩家数据[id].角色.道具[n]
			local 符合=false
			for i=1,#道具 do
				if self.数据[道具id].名称==道具[i] then
					符合=true
				end
			end
			if 符合 then
				if 恢复<数值 and self:取加魔道具1(self.数据[道具id].名称,道具id)*self.数据[道具id].数量>=数值 then
					local 扣除数量=0
					for i=1,self.数据[道具id].数量 do
						if 恢复<数值 then
							恢复=恢复+self:取加魔道具1(self.数据[道具id].名称,道具id)
							扣除数量=扣除数量+1
						end
					end
					道具删除[#道具删除+1]={格子=n,id=道具id,数量=扣除数量}
				elseif 恢复<数值 then
					恢复= self:取加魔道具1(self.数据[道具id].名称,道具id)*self.数据[道具id].数量
					道具删除[#道具删除+1]={格子=n,id=道具id,数量=self.数据[道具id].数量}
				end
			end
		end
	end
	if 恢复~=0 then
		for n=1,#道具删除 do
			self.数据[道具删除[n].id].数量=self.数据[道具删除[n].id].数量-道具删除[n].数量
			if self.数据[道具删除[n].id].数量<=0 then
				玩家数据[id].角色.道具[道具删除[n].格子]=nil
			end
		end
	end
	if 恢复>数值 then 恢复=数值 end
	return 恢复
end
function 道具处理类:摊位上架商品(连接id,id,数据)
	if 玩家数据[id].摊位数据==nil then return end
	if 数据.价格=="" or 数据.价格==nil or 数据.价格+0<=0 then
		数据.价格=1
	end
	local 价格=数据.价格+0
	if 数据.bb==nil and 数据.制造 == nil then
		local 编号=玩家数据[id].角色.道具[数据.道具+0]
		if self.数据[编号].不可交易 or self.数据[编号].专用  then
			常规提示(id,"#Y/该物品不可转移给他人")
			return
		end
		玩家数据[id].摊位数据.道具[数据.道具+0]=table.loadstring(table.tostring(self.数据[编号]))
		玩家数据[id].摊位数据.道具[数据.道具+0].价格=价格
		常规提示(id,"#Y/上架物品成功！")
	elseif 数据.bb and 数据.道具==nil and 数据.制造 == nil then
		local 编号=数据.bb+0
		if 玩家数据[id].召唤兽.数据[编号].不可交易 or 玩家数据[id].召唤兽.数据[编号].专用 then
			常规提示(id,"#Y/该召唤兽不可转移给他人")
			return
		elseif 玩家数据[id].召唤兽.数据[编号].统御 ~= nil then
			常规提示(id,"#Y/已被坐骑统御的召唤兽无法转移给他人")
			return
		elseif 玩家数据[id].角色.参战信息~=nil and 玩家数据[id].角色.参战宝宝 then
			if 玩家数据[id].角色.参战宝宝.认证码==玩家数据[id].召唤兽.数据[编号].认证码 then
				常规提示(id,"#Y/参战中的召唤兽无法转移给他人")
				return
			end
		elseif 玩家数据[id].角色.观看召唤兽 == 玩家数据[id].召唤兽.数据[编号].认证码 then
			常规提示(id,"#Y/正在观看的召唤兽无法转移给他人，请先取消观看")
			return
		end
		玩家数据[id].摊位数据.bb[编号]=table.loadstring(table.tostring(玩家数据[id].召唤兽.数据[编号]))
		玩家数据[id].摊位数据.bb[编号].价格=价格
		玩家数据[id].摊位数据.bb[编号].id=编号
		常规提示(id,"#Y/上架召唤兽成功！")
	elseif 数据.bb==nil and 数据.道具 == nil then
		local 编号1=数据.制造[1]
		local 编号2=数据.制造[2]
		for k, v in pairs(玩家数据[id].摊位数据.制造) do
			if v~=nil and k==编号1 and v[编号2]~=nil then
				常规提示(id,"#Y/该制造技能等级已经上架了")
				return
			end
		end
		if 玩家数据[id].摊位数据.制造[编号1]==nil then
			玩家数据[id].摊位数据.制造[编号1]={}
		end
		玩家数据[id].摊位数据.制造[编号1][编号2]={价格=价格,序号=tonumber(编号1..编号2)}
		玩家数据[id].摊位数据.制造.制造组=数据.制造组
		常规提示(id,"#Y/上架成功！")
	end
	玩家数据[id].摊位数据.更新=os.time()
	self:索要摊位数据(连接id,id,3517)
end
function 道具处理类:摊位下架商品(连接id,id,数据)
	if 玩家数据[id].摊位数据==nil then return end
	if 数据.道具~=nil then
		玩家数据[id].摊位数据.道具[数据.道具+0]=nil
		常规提示(id,"#Y/下架物品成功！")
	elseif 数据.bb~=nil then
		local 编号=数据.bb+0
		玩家数据[id].摊位数据.bb[编号]=nil
		常规提示(id,"#Y/下架召唤兽成功！")
	end
	玩家数据[id].摊位数据.更新=os.time()
	self:索要摊位数据(连接id,id,3517)
end
function 道具处理类:收摊处理(连接id,id)
	玩家数据[id].摊位数据=nil
	玩家数据[id].离线摆摊=nil
	常规提示(id,"#Y/收摊回家玩老婆去咯！")
	发送数据(连接id,3518)
	地图处理类:取消玩家摊位(id)
end
function 道具处理类:更改摊位招牌(连接id,id,名称)
	if 玩家数据[id].摊位数据==nil then return end
	if os.time()-玩家数据[id].摊位数据.更新<=5 then
		常规提示(id,"#Y/请不要频繁更换招牌")
		return
	end
	常规提示(id,"#Y/更新招牌成功")
	玩家数据[id].摊位数据.更新=os.time()
	玩家数据[id].摊位数据.名称=名称
	发送数据(连接id,3516,名称)
	地图处理类:设置玩家摊位(id,名称)
end
function 道具处理类:购买摊位商品(连接id,id,数据)
	local 对方id=玩家数据[id].摊位id
	if 假人玩家处理类在线 and 数据~=nil and 对方id~=nil and math.floor(对方id)<4000000 and 摆摊假人处理类:假人信息(对方id) ~= nil then
		假人玩家处理类:malltop(对方id,连接id,id,数据)
		return
	end
	if 对方id==nil or 玩家数据[对方id]==nil or 玩家数据[对方id].摊位数据==nil then
		常规提示(id,"#Y/这个摊位并不存在")
		return
	end
	if 玩家数据[id].摊位查看<玩家数据[对方id].摊位数据.更新 then
		常规提示(id,"#Y/这个摊位的数据已经发生了变化，请重新打开该摊位")
		return
	end
	if 玩家数据[对方id].角色:检测交易是否正常(对方id) or 玩家数据[id].角色:检测交易是否正常(id) then
		return
	end
	local 名称=玩家数据[对方id].角色.名称
	local 名称1=玩家数据[id].角色.名称
	local 账号=玩家数据[对方id].账号
	local 账号1=玩家数据[id].账号
	if 数据.道具~=nil then
		local 购买数量 = 数据.数量+0
		if 玩家数据[对方id].摊位数据.道具[数据.道具]==nil then
			常规提示(id,"#Y/这个商品并不存在")
			return
		end
		if 购买数量<1 or 购买数量>99 then
			常规提示(id,"#R数据异常！")
			local 封禁原因=玩家数据[id].角色.ip.."“违规行为：购买摊位商品”数量=="..购买数量.."，玩家ID=="..id.."，玩家账号=="..玩家数据[id].账号
			f函数.写配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","已违规9",封禁原因)
			return
		end
		购买数量=math.floor(购买数量)
		local 剩余数量=玩家数据[对方id].摊位数据.道具[数据.道具].数量 or 1
		if 购买数量>剩余数量 then
			购买数量=math.floor(剩余数量)
		end
		local 价格=玩家数据[对方id].摊位数据.道具[数据.道具].价格*购买数量
		价格=math.floor(价格)
		if 玩家数据[id].角色.银子<价格 then
			常规提示(id,"#Y/你没有那么多的银子")
			return
		elseif 价格<0 then
			常规提示(id,"#R数据异常！")
			local 封禁原因=玩家数据[id].角色.ip.."“违规行为：购买摊位商品”价格=="..价格.."，玩家ID=="..id.."，玩家账号=="..玩家数据[id].账号
			f函数.写配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","已违规10",封禁原因)
			return
		end
		local 临时格子=玩家数据[id].角色:取道具格子()
		if 临时格子==0 then
			常规提示(id,"#Y/请先整理下包裹吧！")
			return
		end
		local jiuid=玩家数据[对方id].角色.道具[数据.道具]
		if jiuid==nil or 玩家数据[对方id].道具.数据[jiuid]==nil and not 玩家数据[对方id].角色.假人 then
			print("摊位购买道具为空！")
			return
		end
		local 新道具=self:取新编号()
		local 道具名称=玩家数据[对方id].摊位数据.道具[数据.道具].名称
		local 道具识别码=玩家数据[对方id].摊位数据.道具[数据.道具].识别码
		if 玩家数据[对方id].道具:检查道具是否存在(对方id,道具识别码,购买数量)==false and not 玩家数据[对方id].角色.假人 then
			return
		end
		self:增加摊位记录(对方id,id,道具名称,价格)
		玩家数据[id].角色:扣除银子(价格,0,0,"摊位购买",1)
		常规提示(对方id,"#W/出售#R/"..道具名称.."#W/成功！")
		常规提示(id,"#W/购买#R/"..道具名称.."#W/成功！")
		self.数据[新道具]=table.loadstring(table.tostring(玩家数据[对方id].摊位数据.道具[数据.道具]))
		if not 玩家数据[对方id].角色.假人 then
			if  not 玩家数据[对方id].摊位数据.道具[数据.道具].可叠加 or 玩家数据[对方id].摊位数据.道具[数据.道具].数量 == nil then
				玩家数据[对方id].道具.数据[jiuid]=nil
				玩家数据[对方id].角色.道具[数据.道具]=nil
				玩家数据[对方id].摊位数据.道具[数据.道具]=nil
			else
				self.数据[新道具].数量=购买数量
				玩家数据[对方id].摊位数据.道具[数据.道具].数量=玩家数据[对方id].摊位数据.道具[数据.道具].数量-购买数量
				if 玩家数据[对方id].摊位数据.道具[数据.道具].数量<=0 then
					玩家数据[对方id].道具.数据[jiuid]=nil
					玩家数据[对方id].角色.道具[数据.道具]=nil
					玩家数据[对方id].摊位数据.道具[数据.道具]=nil
				else
					玩家数据[对方id].道具.数据[jiuid].数量=玩家数据[对方id].道具.数据[jiuid].数量-购买数量
				end
			end
		else
			if  not 玩家数据[对方id].摊位数据.道具[数据.道具].可叠加 or 玩家数据[对方id].摊位数据.道具[数据.道具].数量 == nil then
				玩家数据[对方id].摊位数据.道具[数据.道具]=nil
			else
				self.数据[新道具].数量=购买数量
				玩家数据[对方id].摊位数据.道具[数据.道具].数量=玩家数据[对方id].摊位数据.道具[数据.道具].数量-购买数量
				if 玩家数据[对方id].摊位数据.道具[数据.道具].数量<=0 then
					玩家数据[对方id].摊位数据.道具[数据.道具]=nil
				end
			end
		end
		玩家数据[id].角色.道具[临时格子]=新道具
		玩家数据[对方id].角色:添加银子(价格,"摊位出售",1)
		道具刷新(id)
		道具刷新(对方id)
	elseif 数据.bb~=nil then
		if 玩家数据[对方id].摊位数据.bb[数据.bb]==nil then
			常规提示(id,"#Y/这只召唤兽不存在")
			return
		elseif  not 玩家数据[id].角色:取新增宝宝数量()then
			常规提示(id,"#Y/你当前可携带的召唤兽数量已达上限！")
			return
		end
		local 价格=玩家数据[对方id].摊位数据.bb[数据.bb].价格
		价格=math.floor(价格)
		if 玩家数据[id].角色.银子<价格 then
			常规提示(id,"#Y/你没有那么多的银子")
			return
		end
		local 道具名称=玩家数据[对方id].摊位数据.bb[数据.bb].名称
		local 道具等级=玩家数据[对方id].摊位数据.bb[数据.bb].等级
		local 道具模型=玩家数据[对方id].摊位数据.bb[数据.bb].模型
		local 道具技能=#玩家数据[对方id].摊位数据.bb[数据.bb].技能
		local 道具识别码=玩家数据[对方id].摊位数据.bb[数据.bb].认证码
		local 临时宝宝 = table.loadstring(table.tostring(玩家数据[对方id].摊位数据.bb[数据.bb]))
		self:增加摊位记录(对方id,id,道具名称,价格)
		玩家数据[对方id].摊位数据.bb[数据.bb]=nil
		玩家数据[id].角色:扣除银子(价格,0,0,"摊位购买",1)
		玩家数据[对方id].角色:添加银子(价格,"摊位出售",1)
		常规提示(对方id,"#W/出售#R/"..道具名称.."#W/成功！")
		常规提示(id,"#W/购买#R/"..道具名称.."#W/成功！")
		local 宝宝=宝宝类.创建()
		宝宝:加载数据(临时宝宝)
		玩家数据[id].召唤兽.数据[#玩家数据[id].召唤兽.数据+1]=宝宝
		玩家数据[id].召唤兽.数据[#玩家数据[id].召唤兽.数据].认证码 = id.."_"..os.time().."_"..取随机数(111111111111,999999999999)
		local 现有数据 = {}
		local 临时摆摊数据 = {}
		for n = 1, 7 do
			if 玩家数据[对方id].召唤兽.数据[n] ~= nil and 玩家数据[对方id].召唤兽.数据[n] ~= 0 and 玩家数据[对方id].召唤兽.数据[n].认证码 ~= 道具识别码 then
				现有数据[#现有数据 + 1] = {
				bb = 玩家数据[对方id].召唤兽.数据[n],
				编号 = n
				}
			end
		end
		玩家数据[对方id].召唤兽.数据 = {}
		for n = 1, #现有数据 do
			玩家数据[对方id].召唤兽.数据[n] = 现有数据[n].bb
			for i = 1, 7 do
				if 玩家数据[对方id].摊位数据.bb[i] ~= nil and 玩家数据[对方id].摊位数据.bb[i].id == 现有数据[n].编号 then
					临时摆摊数据[n] = {
					id = n,
					bb = 玩家数据[对方id].摊位数据.bb[i],
					价格 = 玩家数据[对方id].摊位数据.bb[i].价格
					}
				end
			end
		end
		玩家数据[对方id].摊位数据.bb = {}
		for n = 1, 7 do
			if 临时摆摊数据[n] ~= nil then
				玩家数据[对方id].摊位数据.bb[n] = 临时摆摊数据[n].bb
				玩家数据[对方id].摊位数据.bb[n].价格 = 临时摆摊数据[n].价格
				玩家数据[对方id].摊位数据.bb[n].id = 临时摆摊数据[n].id
			end
		end
		if 玩家数据[id].召唤兽.数据[#玩家数据[id].召唤兽.数据].参战信息~=nil then
			玩家数据[id].召唤兽.数据[#玩家数据[id].召唤兽.数据].参战信息=nil
			玩家数据[对方id].角色.参战信息=nil
			玩家数据[对方id].角色.参战宝宝={}
			发送数据(玩家数据[对方id].连接id,18,玩家数据[对方id].角色.参战宝宝)
		end
		发送数据(玩家数据[id].连接id,3512,玩家数据[id].召唤兽.数据)
		发送数据(玩家数据[对方id].连接id,3512,玩家数据[对方id].召唤兽.数据)
	end
	玩家数据[对方id].角色:存档()
	玩家数据[id].角色:存档()
	玩家数据[对方id].摊位数据.更新=os.time()
	玩家数据[id].摊位查看=os.time()
	self:索要其他玩家摊位(连接id,id,对方id,3522)
	self:索要摊位数据(玩家数据[对方id].连接id,对方id,3517)
end
function 道具处理类:购买摊位制造商品(连接id,id,数据)
	local 对方id=玩家数据[id].摊位id
	if 数据~=nil and 对方id~=nil and math.floor(对方id)<4000000 and 摆摊假人处理类:假人信息(对方id) ~= nil then
		假人玩家处理类:malltop(对方id,连接id,id,数据)
		return
	end
	if 玩家数据[id].摊位数据~=nil then
		常规提示(id,"#Y/摆摊中无法此操作。")
		return
	end
	if 对方id==nil or 玩家数据[对方id]==nil or 玩家数据[对方id].摊位数据==nil then
		常规提示(id,"#Y/这个摊位并不存在")
		return
	end
	if 玩家数据[id].摊位查看<玩家数据[对方id].摊位数据.更新 then
		常规提示(id,"#Y/摊主正忙，请稍等片刻，或重新打开")
		return
	end
	local 价格 = 0
	local 查找标识 = false
	for k,v in pairs(玩家数据[对方id].摊位数据.制造) do
		for i,v in pairs(v) do
			if 查找标识 then
				break
			end
			if v.序号 == 数据.选中序号 then
				价格 = v.价格
				查找标识 = true
				break
			end
		end
	end
	if 价格 < 1  then
		常规提示(id,"#Y/数据异常！")
		return
	end
	价格=math.floor(价格)
	if 数据.制造类别 == "其他类" then
		if 玩家数据[id].角色.银子<价格 then
			常规提示(id,"#Y/你没有那么多的银子")
			return
		end
		if 数据.技能名称 == "嗜血" or 数据.技能名称 == "轻如鸿毛" or 数据.技能名称 == "拈花妙指" or 数据.技能名称 == "穿云破空"
			or 数据.技能名称 == "盘丝舞" or 数据.技能名称 == "一气化三清" or 数据.技能名称 == "浩然正气" or 数据.技能名称 == "龙附"
			or 数据.技能名称 == "神兵护法" or 数据.技能名称 == "魔王护持" or 数据.技能名称 == "莲华妙法" or 数据.技能名称 == "神力无穷"
			or 数据.技能名称 == "尸气漫天" or 数据.技能名称 == "元阳护体" or 数据.技能名称 == "担山赶月" or 数据.技能名称 == "神木呓语" then
			local 等级 = 玩家数据[对方id].角色:取师门技能等级(数据.技能名称)
			if 玩家数据[对方id].角色:扣除活力(等级) then
				玩家数据[对方id].角色:摊位符制造(数据.技能名称,id)
			else
				常规提示(id, "#Y/摊主的活力不足无法制造!")
				return
			end
			self:增加摊位记录(对方id,id,"强化符",价格)
			玩家数据[id].角色:扣除银子(价格,0,0,"购买强化符",1)
			玩家数据[对方id].角色:添加银子(价格,"玩家购买强化符",1)
			玩家数据[对方id].角色.活力 = 玩家数据[对方id].角色.活力 - qz(数据.等级)
			体活刷新(对方id)
		end
	else
		if 数据.技能名称 == "打造" or 数据.技能名称 == "裁缝" or 数据.技能名称 == "炼金" then
			玩家数据[id].给予数据 = {事件="购买玩家打造",制造数据={名称=数据.技能名称,等级=数据.等级,价格=数据.价格,是否强化=数据.打造模式},对方id=对方id,类型=1}
			发送数据(玩家数据[id].连接id,3507,{道具=玩家数据[id].道具:索要道具1(id),名称="打铁炉",类型="NPC",等级=数据.等级})
		end
	end
end
function 道具处理类:索要其他玩家摊位(连接id,id,对方id,序号)
	if 摆摊假人处理类:假人信息(对方id) ~= nil then
		发送数据(玩家数据[id].连接id, 3520, {银子=玩家数据[id].角色.银子})
		玩家数据[id].摊位查看=os.time()
		玩家数据[id].摊位id=对方id
		摆摊假人处理类:索要摊位(玩家数据[id].连接id, 对方id)
		return
	end
	if 玩家数据[对方id]==nil or 玩家数据[对方id].摊位数据==nil then
		常规提示(id,"#Y/这个摊位并不存在")
		return
	end
	玩家数据[id].摊位查看=os.time()
	玩家数据[id].摊位id=对方id
	发送数据(玩家数据[id].连接id,3520,{银子=玩家数据[id].角色.银子})
	发送数据(玩家数据[id].连接id,序号,{bb=玩家数据[对方id].摊位数据.bb,物品=玩家数据[对方id].摊位数据.道具,制造=玩家数据[对方id].摊位数据.制造,id=对方id,摊主名称=玩家数据[对方id].角色.名称,名称=玩家数据[对方id].摊位数据.名称})
end
function 道具处理类:索要摊位数据(连接id,id,序号)
	if 玩家数据[id].摊位数据==nil then
		if 玩家数据[id].队伍~=0 then
			常规提示(id,"#Y/组队状态下无法摆摊")
			return
		elseif 玩家数据[id].角色.飞行中 then
			常规提示(id,"#Y/飞行状态下无法摆摊")
			return
		else
			local 地图=玩家数据[id].角色.地图数据.编号
			if 地图~=1001 and 地图~=1501 and 地图~=1070 and 地图~=1092 and 地图~=1208 and 地图~=1226 and 地图~=1040 then
				常规提示(id,"#Y/该场景无法摆摊")
				return
			elseif 玩家数据[id].角色.等级<30 and 连接id ~="假人"then
				常规提示(id,"#Y/只有等级达到30级的玩家才可使用摆摊功能")
				return
			end
		end
		玩家数据[id].摊位数据={摊位记录="",道具={},bb={},制造={},id=id,名称="杂货摊位",摊主=玩家数据[id].角色.名称,更新=os.time()}
		地图处理类:设置玩家摊位(id,"杂货摊位")
	end
	发送数据(玩家数据[id].连接id,3512,玩家数据[id].召唤兽.数据)
	发送数据(玩家数据[id].连接id,3513,玩家数据[id].道具:索要道具2(id))
	local bb={}
	for k, v in pairs(玩家数据[id].摊位数据.bb) do
		if v~=nil then
			bb[k]={v.价格,v.认证码,v.模型,v.名称}
		end
	end
	local 道具={}
	for n=1,20 do
		if 玩家数据[id].摊位数据.道具[n]~=nil then
			道具[n]=玩家数据[id].摊位数据.道具[n]
		end
	end
	发送数据(连接id,序号,{bb=bb,物品=道具,制造=玩家数据[id].摊位数据.制造,id=id,摊主名称=玩家数据[id].角色.名称,名称=玩家数据[id].摊位数据.名称})
end

function 道具处理类:发起交易处理(连接id,自己id,对方id)
	if not 玩家数据[对方id] or not 玩家数据[对方id].角色 then
		常规提示(自己id,"#Y/对方并不在线")
		return
	elseif 地图处理类:比较距离(自己id,对方id,500)==false then
		常规提示(自己id,"#Y/你们的距离太远了")
		return
	elseif 玩家数据[对方id].交易信息~=nil or 玩家数据[对方id].摊位数据~=nil then
		常规提示(自己id,"#Y/对方正忙，请稍后再试")
		return
	elseif 玩家数据[自己id].交易信息~=nil then
		常规提示(自己id,"#Y/你上次的交易还没有结束哟~")
		return
	elseif 玩家数据[对方id].禁止交易 then
		常规提示(自己id,"#Y/对方没有打开交易开关")
		return
	elseif 玩家数据[对方id].zhuzhan then
		常规提示(自己id,"#Y/对方处于助战状态，无法进行交易")
		return
	end
	交易数据[自己id]={[自己id]={},[对方id]={}}
	玩家数据[自己id].交易信息={发起id=自己id,对方id=对方id}
	玩家数据[对方id].交易信息={发起id=自己id,对方id=自己id}
	常规提示(自己id,"你正在和"..玩家数据[对方id].角色.名称.."进行交易")
	常规提示(对方id,"你正在和"..玩家数据[自己id].角色.名称.."进行交易")
	发送数据(玩家数据[自己id].连接id,3512,玩家数据[自己id].召唤兽.数据)
	发送数据(玩家数据[对方id].连接id,3512,玩家数据[对方id].召唤兽.数据)
	发送数据(玩家数据[自己id].连接id,3514,{名称=玩家数据[对方id].角色.名称,等级=玩家数据[对方id].角色.等级,道具=玩家数据[自己id].道具:索要道具2(自己id)})
	发送数据(玩家数据[对方id].连接id,3514,{名称=玩家数据[自己id].角色.名称,等级=玩家数据[自己id].角色.等级,道具=玩家数据[对方id].道具:索要道具2(对方id)})
end
function 道具处理类:取消交易(id)
	if 玩家数据[id].交易信息~=nil then
		if 玩家数据[玩家数据[id].交易信息.对方id]~=nil then
			发送数据(玩家数据[玩家数据[id].交易信息.对方id].连接id,3511)
			常规提示(玩家数据[id].交易信息.对方id,"#Y/对方取消了交易")
			玩家数据[玩家数据[id].交易信息.对方id].交易信息=nil
		end
		交易数据[玩家数据[id].交易信息.发起id]=nil
		玩家数据[id].交易信息=nil
	end
end
function 道具处理类:锁定交易数据(连接id,id,数据) --先锁定
	if type(玩家数据[id].交易信息)~= "table" then return end
	local 发起id=玩家数据[id].交易信息.发起id
	local 对方id=玩家数据[id].交易信息.对方id
	if  not 交易数据[发起id] then
		self:交易中取消(发起id,id,"#Y/交易异常！请重试2。","#Y/交易异常！请重试2。")
		return
	end
	local 道具数据={}
	local bb数据={}
	local 银子数据=数据.银子
	交易数据[发起id][id].道具={}
	交易数据[发起id][id].bb={}
	交易数据[发起id][id].银子=银子数据+0
	交易数据[发起id][id].锁定=true
	for n=1,3 do
		local 道具格子=数据.道具[n]
		-- if not 数据.数量[n] then
		-- 	return
		-- end
		if 道具格子~=nil and 玩家数据[id].角色.道具[道具格子] then
			if  (数据.数量[n]<1 or 数据.数量[n]>999) then
				return
			end
			local 道具id=玩家数据[id].角色.道具[道具格子]
			道具数据[#道具数据+1]=self:取指定道具(道具id)
			local maxsl=道具数据[#道具数据].数量 or 1
			if 数据.数量[n]>maxsl then
				数据.数量[n]=maxsl
			end
			交易数据[发起id][id].道具[#交易数据[发起id][id].道具+1]={认证码=道具数据[#道具数据].识别码,格子=道具格子,编号=道具id,数量=数据.数量[n]}
		end
	end
	for n=1,3 do
		local bb编号=数据.bb[n]
		if bb编号~=nil then
			if 玩家数据[id].召唤兽.数据[bb编号] then
				bb数据[#bb数据+1]=table.loadstring(table.tostring(玩家数据[id].召唤兽.数据[bb编号]))
				交易数据[发起id][id].bb[#交易数据[发起id][id].bb+1]={认证码=bb数据[#bb数据].认证码,编号=bb编号}
			end
		end
	end
	发送数据(连接id,3508)
	常规提示(id,"#Y/你已经锁定了交易状态，对方锁定交易状态后点击确定即可完成交易")
	常规提示(对方id,"#Y/对方已经锁定了交易状态，等你锁定交易状态后点击确定即可完成交易")
	发送数据(玩家数据[对方id].连接id,3510,{bb=bb数据,道具=道具数据,银子=银子数据,数量=数据.数量})
end
function 道具处理类:确定交易(连接id,id) --
	if type(玩家数据[id].交易信息)~= "table" then return end
	local 发起id=玩家数据[id].交易信息.发起id
	local 对方id=玩家数据[id].交易信息.对方id--取交易对方id(发起id,id)
	if  not 交易数据[发起id] then
		self:交易中取消(发起id,id,"#Y/交易异常！请重试1。","#Y/交易异常！请重试1。")
		return
	end
	if not 交易数据[发起id][id].锁定 then
		常规提示(id,"#Y/请先锁定交易状态！")
		return
	elseif not 交易数据[发起id][对方id].锁定 then
		常规提示(id,"#Y/请耐心等待对方锁定交易状态！")
		return
	end
	交易数据[发起id][id].确定=true
	if 交易数据[发起id][对方id].确定 then
		self:完成交易处理(发起id,id)
		return
	end
end
function 道具处理类:交易中取消(发起id,自己id,提示1,提示2)
	local 对方id=玩家数据[自己id].交易信息.对方id
	--print("交易中取消",自己id,对方id,提示1,提示2)
	发送数据(玩家数据[自己id].连接id,3511)
	发送数据(玩家数据[对方id].连接id,3511)
	玩家数据[自己id].交易信息=nil
	玩家数据[对方id].交易信息=nil
	交易数据[发起id]=nil
	if 提示1 then
		常规提示(自己id,提示1)
	end
	if 提示2 then
		常规提示(对方id,提示2)
	end
end
function 道具处理类:完成交易处理(发起id,id)
	if  not 交易数据[发起id] then
		self:交易中取消(发起id,id,"#Y/交易异常！请重试1。","#Y/交易异常！请重试1。")
		return
	end
	for k,v in pairs(交易数据[发起id]) do --k为双方id
		local duifangid=玩家数据[k].交易信息.对方id--取交易对方id(发起id,k)
		if 玩家数据[k].战斗~=0 then
			self:交易中取消(发起id,k,"#Y/战斗中无法使用此功能","#Y/对方正在战斗中")
			return
		elseif 玩家数据[k].摊位数据~=nil then
			self:交易中取消(发起id,k,"#Y/摆摊状态下无法使用此功能")
			return
		elseif 地图处理类:比较距离(k,duifangid,500)==false then
			self:交易中取消(发起id,k,"#Y/你们的距离太远了","#Y/你们的距离太远了")
			return
		elseif 玩家数据[k].角色.银子<交易数据[发起id][k].银子+0 then
			self:交易中取消(发起id,k,"#Y/你没有那么多的银子","#Y/对方没有那么多的银子")
			return
		elseif 玩家数据[k].角色:取道具格子2()<#交易数据[发起id][duifangid].道具 then
			local 提示1,提示2="#Y/你身上的空间不够","#Y/对方身上的空间不够"
			self:交易中取消(发起id,k,提示1,提示2)
			return
		elseif #交易数据[发起id][duifangid].bb	>0 then
			if #玩家数据[k].召唤兽.数据+#交易数据[发起id][duifangid].bb>玩家数据[k].角色.召唤兽携带上限 then
				--print(11111111111)
				local 提示1,提示2="#Y/你可携带的召唤兽数量已达上限","#Y/对方可携带的召唤兽数量已达上限"
				self:交易中取消(发起id,k,提示1,提示2)
				return
			end
		-- elseif #交易数据[发起id][duifangid].bb	>0 then
		-- elseif #玩家数据[k].召唤兽.数据+#交易数据[发起id][duifangid].bb>玩家数据[k].角色.召唤兽携带上限 then
		-- 	local 提示1,提示2="#Y/你可携带的召唤兽数量已达上限","#Y/对方可携带的召唤兽数量已达上限"
		-- 	self:交易中取消(发起id,k,提示1,提示2)
		-- 	return
		end
	end

	--检查道具是否存在
	for k,v in pairs(交易数据[发起id]) do --k为双方id
		for n=1,#交易数据[发起id][k].道具 do
			local 道具id=交易数据[发起id][k].道具[n].编号
			local 道具对方id=玩家数据[k].角色.道具[交易数据[发起id][k].道具[n].格子]
			if 道具id~=道具对方id or 玩家数据[k].道具.数据[道具id]==nil or 玩家数据[k].道具.数据[道具对方id]==nil or 玩家数据[k].道具.数据[道具对方id].识别码~=交易数据[发起id][k].道具[n].认证码 then
				local 提示1,提示2="#Y/你此次交易的道具数据与锁定前的数据不匹配，本次交易取消","#Y/对方此次交易的道具数据与锁定前的数据不匹配，本次交易取消"
				self:交易中取消(发起id,k,提示1,提示2)
				return
			elseif 玩家数据[k].道具.数据[道具对方id].不可交易 or 玩家数据[k].道具.数据[道具对方id].专用 then
				local 提示1,提示2="#Y/该道具不可交易给他人，本次交易取消","#Y/对方此次交易存在无法交易的道具，本次交易取消"
				self:交易中取消(发起id,k,提示1,提示2)
				return
			end
		end
		-----------检查bb是否存在
		-- 交易数据[发起id][id].bb[#交易数据[发起id][id].bb+1]={认证码=bb数据[#bb数据].认证码,编号=bb编号}
		--table.print(交易数据[发起id][k].bb )
		--table.print(交易数据[发起id][k])
		for n=1,#交易数据[发起id][k].bb do
			--print(22222222)
			local bb编号= 交易数据[发起id][k].bb[n].编号
			local 认证码=玩家数据[k].召唤兽.数据[bb编号].认证码
			if 认证码~=交易数据[发起id][k].bb[n].认证码 then
				local 提示1,提示2="#Y/你此次交易的召唤兽数据与锁定前的数据不匹配，本次交易取消","#Y/对方此次交易的召唤兽数据与锁定前的数据不匹配，本次交易取消"
				self:交易中取消(发起id,k,提示1,提示2)
				return
			elseif 玩家数据[k].召唤兽.数据[bb编号].不可交易 or 玩家数据[k].召唤兽.数据[bb编号].专用 then
				local 提示1,提示2="#Y/该召唤兽不可交易给他人，本次交易取消","#Y/对方此次交易存在无法交易的召唤兽，本次交易取消"
				self:交易中取消(发起id,k,提示1,提示2)
				return
			elseif 玩家数据[k].召唤兽.数据[bb编号].参战信息~=nil then
				-- print(玩家数据[k].角色.参战信息)
				local 提示1,提示2="#Y/该召唤兽处于参战状态，本次交易取消","#Y/对方尚未取消召唤兽参战状态，本次交易取消"
				self:交易中取消(发起id,k,提示1,提示2)
				return
			elseif 玩家数据[k].召唤兽.数据[bb编号].统御~=nil then
				local 提示1,提示2="#Y/该召唤兽处于统御状态，请解除统御后再进行此操作","#Y/对方召唤兽处于统御状态，本次交易取消"
				self:交易中取消(发起id,k,提示1,提示2)
				return
			elseif 玩家数据[k].角色.观看召唤兽 == 玩家数据[k].召唤兽.数据[bb编号].认证码 then
				local 提示1,提示2="#Y/该召唤兽正在观看中，请先取消观看状态","#Y/对方召唤兽正在观看中，本次交易取消"
				self:交易中取消(发起id,k,提示1,提示2)
				return
			end
		end
	end
	-- local tishi1="对方正准备与你交易，他提供了：\n"
	-- local tishi2="对方正准备与你交易，他提供了：\n"
	--银子转移
	for k,v in pairs(交易数据[发起id]) do
		local 交易的银子=交易数据[发起id][k].银子
		if 交易的银子>=1 then
			local duifangid=玩家数据[k].交易信息.对方id--取交易对方id(发起id,k)
			local 账号1=玩家数据[k].账号
			local 名称1=玩家数据[k].角色.名称
			local 账号2=玩家数据[duifangid].账号
			local 名称2=玩家数据[duifangid].角色.名称
			local 之前银子=玩家数据[k].角色.银子
			玩家数据[k].角色.银子 = 玩家数据[k].角色.银子 -  交易的银子
			--玩家数据[k].角色:日志记录(format("[交易系统-扣除银子]给了[%s][%s][%s]银子,银子数额%s,之前数额%s,余额%s",账号1,duifangid,名称1,交易的银子,之前银子,玩家数据[k].角色.银子))
			之前银子=玩家数据[duifangid].角色.银子
			玩家数据[duifangid].角色.银子 =玩家数据[duifangid].角色.银子 +  交易的银子
			--玩家数据[k].角色:日志记录(format("[交易系统-获得银子]获得[%s][%s][%s]银子,银子数额%s,之前数额%s,余额%s",账号,k,名称,交易的银子,之前银子,玩家数据[duifangid].角色.银子))
			常规提示(k,format("#Y/你给了%s%s两银子",名称2,交易的银子))
			常规提示(duifangid,format("#Y/%s给了你%s两银子",名称1,交易的银子))
		end
	end
	--道具转移
	for k,v in pairs(交易数据[发起id]) do
		local duifangid=玩家数据[k].交易信息.对方id--取交易对方id(发起id,k)
		local 账号1=玩家数据[k].账号
		local 名称1=玩家数据[k].角色.名称
		local 账号2=玩家数据[duifangid].账号
		local 名称2=玩家数据[duifangid].角色.名称
		for n=1,#交易数据[发起id][k].道具 do
			local 道具id=交易数据[发起id][k].道具[n].编号
			local 数量 = 交易数据[发起id][k].道具[n].数量 or 1
			local 交易前格子=交易数据[发起id][k].道具[n].格子
			数量=math.floor(数量)
			if 数量 < 1 then
				return
			end
			local 道具识别码=玩家数据[k].道具.数据[道具id].识别码
			local 道具名称=玩家数据[k].道具.数据[道具id].名称
			--给予对方，取新的编号
			local 新格子=玩家数据[duifangid].角色:取道具格子()
			local 新id=玩家数据[duifangid].道具:取新编号()
			if 玩家数据[k].道具:检查道具是否存在(k,道具识别码,数量) then
				if 玩家数据[k].道具.数据[道具id].可叠加 and 玩家数据[k].道具.数据[道具id].数量 then
					if 数量<玩家数据[k].道具.数据[道具id].数量 then
						玩家数据[duifangid].道具.数据[新id]=玩家数据[k].道具:取指定道具(道具id)
						玩家数据[duifangid].道具.数据[新id].数量 = 数量
						玩家数据[duifangid].角色.道具[新格子]=新id
						玩家数据[k].道具.数据[道具id].数量 = 玩家数据[k].道具.数据[道具id].数量 - 数量
					elseif 数量==玩家数据[k].道具.数据[道具id].数量 then
						玩家数据[duifangid].道具.数据[新id]=玩家数据[k].道具:取指定道具(道具id)
						玩家数据[duifangid].角色.道具[新格子]=新id
						玩家数据[k].道具.数据[道具id]=nil
						玩家数据[k].角色.道具[交易前格子]=nil
					end
				else
					玩家数据[duifangid].道具.数据[新id]=玩家数据[k].道具:取指定道具(道具id)
					玩家数据[duifangid].角色.道具[新格子]=新id
					玩家数据[k].道具.数据[道具id]=nil
					玩家数据[k].角色.道具[交易前格子]=nil
				end
				-- 更改道具归属(道具识别码,账号1,duifangid,道具名称)
			--	玩家数据[k].角色:日志记录(format("[交易系统-扣除物品]给了[%s][%s][%s]物品,物品名称为%s,识别码为%s",账号2,duifangid,名称2,道具名称,道具识别码))
			--	玩家数据[duifangid].角色:日志记录(format("[交易系统-获得物品]获得[%s][%s][%s]物品,物品名称为%s,识别码为%s",账号1,k,名称1,道具名称,道具识别码))
				常规提示(duifangid,format("#Y/%s给了你%s",名称1,道具名称))
				常规提示(k,format("#Y/你给了%s%s",名称2,道具名称))
			end
		end
	end
	--bb转移
	for k,v in pairs(交易数据[发起id]) do
		local duifangid=玩家数据[k].交易信息.对方id--取交易对方id(发起id,k)
		local 账号1=玩家数据[k].账号
		local 名称1=玩家数据[k].角色.名称
		local 账号2=玩家数据[duifangid].账号
		local 名称2=玩家数据[duifangid].角色.名称
		for n=1,#交易数据[发起id][k].bb do
			local bb编号=交易数据[发起id][k].bb[n].编号
			if 玩家数据[k].召唤兽.数据[bb编号] then
				local xinbb=table.loadstring(table.tostring(玩家数据[k].召唤兽.数据[bb编号]))
				table.remove(玩家数据[k].召唤兽.数据,bb编号)
				玩家数据[duifangid].召唤兽.数据[#玩家数据[duifangid].召唤兽.数据+1]=xinbb
				local 宝宝=宝宝类.创建()
				宝宝:加载数据(玩家数据[duifangid].召唤兽.数据[#玩家数据[duifangid].召唤兽.数据])
				玩家数据[duifangid].召唤兽.数据[#玩家数据[duifangid].召唤兽.数据]=宝宝
				local bb名称=玩家数据[duifangid].召唤兽.数据[#玩家数据[duifangid].召唤兽.数据].名称
				local bb模型=玩家数据[duifangid].召唤兽.数据[#玩家数据[duifangid].召唤兽.数据].模型
				local bb种类=玩家数据[duifangid].召唤兽.数据[#玩家数据[duifangid].召唤兽.数据].种类
				local bb等级=玩家数据[duifangid].召唤兽.数据[#玩家数据[duifangid].召唤兽.数据].等级
				玩家数据[duifangid].召唤兽.数据[#玩家数据[duifangid].召唤兽.数据].认证码=duifangid.."_"..os.time().."_"..取随机数(111111111111,999999999999)
				local bb认证码=玩家数据[duifangid].召唤兽.数据[#玩家数据[duifangid].召唤兽.数据].认证码
				local bb技能=""
				--玩家数据[k].角色:日志记录(format("[交易系统-扣除bb]给了[%s][%s][%s]bb,名称为%s、模型为%s、种类为%s、等级为%s、技能为%s、认证码为%s",账号2,duifangid,名称2,bb名称,bb模型,bb种类,bb等级,bb技能,bb认证码))
				--玩家数据[duifangid].角色:日志记录(format("[交易系统-获得bb]获得[%s][%s][%s]bb,名称为%s、模型为%s、种类为%s、等级为%s、技能为%s、认证码为%s",账号1,k,名称1,bb名称,bb模型,bb种类,bb等级,bb技能,bb认证码))
				常规提示(duifangid,format("#Y/%s给了你%s",名称1,bb名称))
				常规提示(k,format("#Y/你给了%s%s",名称2,bb名称))
				发送数据(玩家数据[k].连接id,3512,玩家数据[k].召唤兽.数据)
				发送数据(玩家数据[duifangid].连接id,3512,玩家数据[duifangid].召唤兽.数据)
			end
		end
	end
	for k,v in pairs(交易数据[发起id]) do
		发送数据(玩家数据[k].连接id,3511)
		玩家数据[k].角色:存档()
		道具刷新(k)
		玩家数据[k].交易信息=nil
		-- if tishi1~="对方正准备与你交易，他提供了：\n" then
		--    消息提示(k,tishi1)
		-- end
	end
	交易数据[发起id]=nil
end
function 道具处理类:取指定道具(编号)
	return table.loadstring(table.tostring(self.数据[编号]))
end

function 道具处理类:检查道具是否存在(id,识别码,数量)
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil and self.数据[玩家数据[id].角色.道具[n]]~=nil and self.数据[玩家数据[id].角色.道具[n]].识别码==识别码 then
			if self.数据[玩家数据[id].角色.道具[n]].数量~=nil then
				if self.数据[玩家数据[id].角色.道具[n]].数量>=数量 then
					return true
				end
			else
				return true
			end
		end
	end
	return false
end
function 道具处理类:出售装备(连接id,id)
	if 玩家数据[id].出售装备==nil or 玩家数据[id].角色.道具[玩家数据[id].出售装备]==nil then
		添加最后对话(id,"该装备不存在！")
		return
	end
	local 道具id=玩家数据[id].角色.道具[玩家数据[id].出售装备]
	if self.数据[道具id].总类~=2 or self.数据[道具id].灵饰 or self.数据[道具id].分类==9 then
		添加最后对话(id,"该物品无法被我收购")
		return
	end
	local 银子=self:取装备价格(道具id)
	玩家数据[id].角色:添加银子(银子,format("出售装备:%s,%s",self.数据[道具id].名称,self.数据[道具id].识别码),1)
	self.数据[道具id]=nil
	玩家数据[id].角色.道具[玩家数据[id].出售装备]=nil
	玩家数据[id].出售装备=nil
	添加最后对话(id,"出售装备成功，你获得了"..银子.."两银子")
	道具刷新(id)
	return
end
function 道具处理类:取回收价格(id)
  if id == nil or self.数据[id] == nil or self.数据[id].名称 == nil then
    return {价格=0,类型=1}  -- 初始化返回值
  end

  if self.数据[id].专用 then
    return {价格=0,类型=1}  -- 初始化返回值
  end

  local 名称 = self.数据[id].名称
  local 价格 = 0
  local 类型 = 1

  -- 宝石类
  if 名称 == "红玛瑙" or 名称 == "太阳石" or 名称 == "舍利子" or 名称 == "黑宝石" or
     名称 == "月亮石" or 名称 == "光芒石" or 名称 == "神秘石" or 名称 == "星辉石" then
    价格 = self.数据[id].级别限制*15000

  -- 装备类
  elseif self.数据[id].总类 == 2 and self.数据[id].生产方式 == "系统产出" then
    local 等级 = self.数据[id].级别限制
    if 等级 == 0 then
      价格 = 100
    elseif 等级 == 10 then
      价格 = 500
    elseif 等级 == 20 then
      价格 = 1000
    elseif 等级 == 30 then
      价格 = 2000
    elseif 等级 == 40 then
      价格 = 5000
    elseif 等级 == 50 then
      价格 = 10000
    elseif 等级 == 60 then
      价格 = 18000
    elseif 等级 == 70 then
      价格 = 30000
    elseif 等级 == 80 then
      价格 = 100000
    end

  -- 制造指南书
  elseif 名称 == "制造指南书" then
    if self.数据[id].子类 <= 60 then
      价格 = self.数据[id].子类*200
    elseif self.数据[id].子类 == 70 then
      价格 = 15000
    elseif self.数据[id].子类 == 80 then
      价格 = 30000
    elseif self.数据[id].子类 == 90 then
      价格 = 80000
    elseif self.数据[id].子类 == 100 then
      价格 = 150000
    elseif self.数据[id].子类 == 110 then
      价格 = 250000
    elseif self.数据[id].子类 == 120 then
      价格 = 350000
    end

  -- 百炼精铁
  elseif 名称 == "百炼精铁" then
    if self.数据[id].子类 <= 60 then
      价格 = self.数据[id].子类*240
    elseif self.数据[id].子类 == 70 then
      价格 = 15000
    elseif self.数据[id].子类 == 80 then
      价格 = 30000
    elseif self.数据[id].子类 == 90 then
      价格 = 80000
    elseif self.数据[id].子类 == 100 then
      价格 = 150000
    elseif self.数据[id].子类 == 110 then
      价格 = 250000
    elseif self.数据[id].子类 == 120 then
      价格 = 350000
    end

  -- 古董类
  elseif 名称 == "嵌绿松石象牙杯" or 名称 == "熹平经残石" or 名称 == "羊首勺" or
         名称 == "铁矛" or 名称 == "绿釉陶狗" or 名称 == "四帛书" or 名称 == "史叔编钟" then
    价格 = 20000
  elseif 名称 == "盘古神斧" or 名称 == "四足鬲" then
    价格 = 50000
  elseif 名称 == "鸭形玻璃注" or 名称 == "神面卣" or 名称 == "毛公鼎" or 名称 == "朱雀纹瓦当" then
    价格 = 100000
  elseif 名称 == "刀币" then
    价格 = 500000
  elseif 名称 == "金砂丹" or 名称 == "骨算筹" or 名称 == "十二枝灯" or
         名称 == "素纱禅衣" or 名称 == "凉造新泉" or 名称 == "鎏金铜尺" then
    价格 = 1000000
  elseif 名称 == "楚国计官之玺" or 名称 == "齐国大车之玺" or 名称 == "燕国外司炉印" then
    价格 = 1500000
  elseif 名称 == "韩国武遂大夫玺" or 名称 == "秦国工师之印" or 名称 == "赵国榆平发弩玺" then
    价格 = 1700000
  elseif 名称 == "古玉簪" or 名称 == "雁足灯" then
    价格 = 2000000
  elseif 名称 == "菊月古鉴·饕鬄镜" or 名称 == "铜车马" then
    价格 = 6000000
  elseif 名称 == "错金博山炉" then
    价格 = 10000000
  elseif 名称 == "一代将军令" then
    价格 = 12000000
  elseif 名称 == "和田青白玉戈" then
    价格 = 25000000
  elseif 名称 == "莲鹤方壶" then
    价格 = 50000000

  -- 材料类
  elseif 名称 == "铜砂丹" or 名称 == "考古铁铲" or 名称 == "初级清灵仙露" or
         名称 == "精魄灵石" or 名称 == "怪物卡片" or 名称 == "分解符" or
         名称 == "海马" or 名称 == "符石卷轴" or 名称 == "驱邪扇芝" then
    价格 = 10000
  elseif 名称 == "炼妖石" or 名称 == "上古锻造图策" or 名称 == "九转金丹" then
    价格 = 20000
  elseif 名称 == "金柳露" or 名称 == "净瓶玉露" or 名称 == "珍珠" or 名称 == "中级清灵仙露" then
    价格 = 12000
  elseif 名称 == "龙鳞" or 名称 == "避水珠" or 名称 == "金刚石" or
         名称 == "定魂珠" or 名称 == "夜光珠" then
    价格 = 20000
  elseif 名称 == "魔兽要诀" or 名称 == "召唤兽内丹" or
         名称 == "超级金柳露" or 名称 == "超级净瓶玉露" then
    价格 = 30000
  elseif 名称 == "鬼谷子" or 名称 == "未激活的符石" or 名称 == "红玫瑰" then
    价格 = 48888
  elseif 名称 == "银砂丹" or 名称 == "易经丹" or 名称 == "玉葫灵髓" or
         名称 == "圣兽丹" or 名称 == "高级藏宝图" or 名称 == "附魔宝珠" or
         名称 == "高级清灵仙露" or 名称 == "未激活的星石" then
    价格 = 100000
  elseif 名称 == "彩果" or 名称 == "玲珑宝图" or 名称 == "特赦令牌" or 名称 == "摇钱树苗" or 名称 == "高级魔兽要诀" or 名称 == "高级召唤兽内丹" then
    价格 = 180000
  elseif 名称 == "金金子" then -- 新增金金子回收价格
    价格 = 50000

  -- 花卉乐器类
  elseif 名称 == "萧" or 名称 == "钹" or 名称 == "兰花" or 名称 == "桃花" then
    价格 = 1000
  elseif 名称 == "百合" or 名称 == "康乃馨" or 名称 == "牡丹" or
         名称 == "木鱼" or 名称 == "编钟" or 名称 == "唢呐" or
         名称 == "笛子" or 名称 == "竖琴" or 名称 == "琵琶" then
    价格 = 10000
  end

  if 价格 == 0 then
    return {价格=0,类型=1}  -- 初始化返回值
  end
  return {价格=价格,类型=类型}
end
function 道具处理类:取装备价格(道具id)
	local 等级=self.数据[道具id].级别限制
	if not 等级 then
		return
	end
	local 价格=150
	if 等级==10 then
		价格=30
	elseif  等级==20 then
		价格=50
	elseif 等级==30 then
		价格=100
	elseif 等级==40 then
		价格=150
	elseif 等级==50 then
		价格=200
	elseif 等级==60 then
		价格=300
	elseif 等级==70 then
		价格=500
	elseif 等级==80 then
		价格=700
	else
		价格=1000
	end
	if self.数据[道具id].专用~=nil then
		价格=1
		等级=1
	end
	return 价格*等级
end
function 道具处理类:生成合成旗(连接id,id,名称)
	if 玩家数据[id].合成旗序列==nil or #玩家数据[id].合成旗序列<=0 then
		常规提示(id,"#Y未找到已提交的导标旗，请重新使用法宝进行合成")
		玩家数据[id].合成旗序列=nil
		return
	end
	local 编号=玩家数据[id].法宝id
	if 玩家数据[id].角色.法宝[编号]==nil or self.数据[玩家数据[id].角色.法宝[编号]]==nil or self.数据[玩家数据[id].角色.法宝[编号]].名称~="五色旗盒" then
		常规提示(id,"#Y你没有这样的法宝")
		return
	elseif self.数据[玩家数据[id].角色.法宝[编号]].魔法<=0 then
		常规提示(id,"#Y你的法宝灵气不足")
		return
	end
	self.数据[玩家数据[id].角色.法宝[编号]].魔法=self.数据[玩家数据[id].角色.法宝[编号]].魔法-1
	local 次数=0
	for n=1,#玩家数据[id].合成旗序列 do
		local 临时id=玩家数据[id].角色.道具[玩家数据[id].合成旗序列[n]]
		if 临时id==nil or self.数据[临时id]==nil or self.数据[临时id].总类~=11 or self.数据[临时id].分类~=1 or self.数据[临时id].地图~=玩家数据[id].合成旗序列.地图 then
			常规提示(id,"#Y您的物品数据已经发生变化，请重新使用法宝进行合成")
			玩家数据[id].合成旗序列=nil
			return
		end
		if self.数据[临时id].次数==nil then
			self.数据[临时id].次数=1
		end
		次数=次数+self.数据[临时id].次数
	end
	local 临时id=玩家数据[id].角色.道具[玩家数据[id].合成旗序列[1]]
	self.数据[临时id].名称=名称
	self.数据[临时id].分类=2
	self.数据[临时id].次数=次数
	self.数据[临时id].xy={}
	for n=1,#玩家数据[id].合成旗序列 do
		local 临时id1=玩家数据[id].角色.道具[玩家数据[id].合成旗序列[n]]
		self.数据[临时id].xy[n]={x=self.数据[临时id1].x,y=self.数据[临时id1].y}
		if n~=1 then
			玩家数据[id].角色.道具[玩家数据[id].合成旗序列[n]]=nil
			self.数据[临时id1]=nil
		end
	end
	玩家数据[id].合成旗序列=nil
	玩家数据[id].法宝id=nil
	玩家数据[id].最后操作=nil
	发送数据(连接id,38,{内容="你的法宝#R/五色旗盒#W/灵气减少了1点"})
	常规提示(id,"#Y您获得了#R"..名称)
	道具刷新(id)
end
function 道具处理类:系统购买道具(id,名称,数量,价格,道具数据,消费方式,购买渠道)
	if 价格<1 then
		print("玩家id  "..id.."   商城购买物品存在作弊行为！")
		return false
	end
	价格=qz(价格)
	if not 玩家数据[id].角色:扣除银子(价格,0,0,购买渠道,1) then
		常规提示(id,"你没有那么多的银子")
		return false
	end
	local 识别码=id.."_"..os.time().."_"..取随机数(1000,9999).."_"..随机序列
	随机序列=随机序列+1
	local 临时道具
	local 道具id
	local 重置id=0
	local 提醒 = true
	for n=1,80 do
		if 重置id==0 and 玩家数据[id].角色.道具[n] and 数量~=nil and self.数据[玩家数据[id].角色.道具[n]] and self.数据[玩家数据[id].角色.道具[n]].名称==道具数据.名称 and self.数据[玩家数据[id].角色.道具[n]].数量 then
			if self.数据[玩家数据[id].角色.道具[n]].数量+数量<=99 then
				self.数据[玩家数据[id].角色.道具[n]].数量= self.数据[玩家数据[id].角色.道具[n]].数量 +数量
				重置id=1
			end
		end
	end
	if 重置id==0 then
		local 道具格子=玩家数据[id].角色:取道具格子()
		if 道具格子==0 then
			local 道具格子 =玩家数据[id].角色:取临时格子()
			if 道具格子~=0 then
				道具id=self:取新编号()
				self.数据[道具id]= 道具数据
				self.数据[道具id].识别码=识别码
				if 数量 ~= nil and self.数据[道具id].可叠加 then
					self.数据[道具id].数量 = 数量
				end
				玩家数据[id].角色.临时包裹[道具格子]=道具id
			else
				提醒=false
				常规提示(id,"#Y/你无法再继续获得道具")
			end
			local fhz = self:临时背包索取()
			if fhz[2] then
				发送数据(玩家数据[id].连接id,303,{"底图框","临时背包闪烁",true})
			end
		else
			道具id=self:取新编号()
			self.数据[道具id]= 道具数据
			self.数据[道具id].识别码=识别码
			if 数量 ~= nil and self.数据[道具id].可叠加 then
				self.数据[道具id].数量 = 数量
			end
			玩家数据[id].角色.道具[道具格子]=道具id
		end
	end
	临时道具 = 取物品数据(名称)
	临时道具.总类=临时道具[2]
	临时道具.子类=临时道具[4]
	临时道具.分类=临时道具[3]
	if 提醒 then
		常规提示(id,"#Y/你得到了#G"..(数量 or 1).."#Y个#G"..名称)
	end
	发送数据(玩家数据[id].连接id,3699)
	刷新玩家货币(玩家数据[id].连接id, id)
end
function 道具处理类:给予超链接道具(id,名称,数量,参数,超链接)
	self:给予道具(id,名称,数量,参数,附加,专用,数据,消费,消费方式,消费内容,超链接)
end
function 道具处理类:给予道具(id,名称,数量,参数,附加,专用,数据,消费,消费方式,消费内容,超链接)
	local 识别码=id.."_"..os.time().."_"..取随机数(1000,9999).."_"..随机序列
	随机序列=随机序列+1
	local 临时道具
	local 道具id
    local 原始数量 = 数量 or 1
    local 重置id = 0
    local 道具格子 = 玩家数据[id].角色:取道具格子()
    if 道具格子 == 0 then
        local 临时格子 = 玩家数据[id].角色:取临时格子()
        if 临时格子 == 0 then
            常规提示(id, "#Y/你的临时背包已满，无法继续获得道具")
            return false
        end
    end
	if 数据 == nil then
		if 消费方式 ~= nil then
			if 消费<1 then
				print("玩家id  "..id.."   商城购买物品存在作弊行为！")
				return false
			end
			消费=qz(消费)
			if 消费方式 == "银子" then
				if not 玩家数据[id].角色:扣除银子(qz(消费*原始数量),0,0,消费内容,1) then
					常规提示(id,"你没有那么多的银子")
					return false
				end
			else
				常规提示(id,"你没有那么多的积分")
				return
			end
		end
		if 名称 == "超级净瓶玉露" or 名称 == "净瓶玉露"or 名称 == "超级金柳露" or 名称 == "金柳露" or 名称=="易经丹" or 名称=="金钥匙" or 名称=="银钥匙" or 名称=="铜钥匙" then
			if 数量 == nil then
				数量 = 1
			end
		end
		local 灵性
		if 名称=="初级清灵仙露" then
			灵性 = 取随机数(1,4)
		elseif 名称=="中级清灵仙露" then
			灵性 = 取随机数(2,6)
		elseif 名称=="高级清灵仙露" then
			灵性 = 取随机数(4,8)
		elseif 名称=="高级摄灵珠" then
			if 参数 then
				灵性=参数
			else
				灵性 = 4
			end
		elseif 名称=="如意丹" then
			灵性=取五行()
		end
		local 重置id=0
		for n=1,80 do
			if 重置id==0 and 玩家数据[id].角色.道具[n] and 数量~=nil and self.数据[玩家数据[id].角色.道具[n]] and self.数据[玩家数据[id].角色.道具[n]].名称==名称 and self.数据[玩家数据[id].角色.道具[n]].数量
				and 名称 ~= "钨金" and 名称 ~= "元宵" and 名称 ~= "九转金丹" and 名称 ~= "月华露"
				and self.数据[玩家数据[id].角色.道具[n]].灵气 == 灵性 then
				if self.数据[玩家数据[id].角色.道具[n]].数量+数量<=99 then
					数量=self.数据[玩家数据[id].角色.道具[n]].数量+数量
					道具id=玩家数据[id].角色.道具[n]
					识别码=self.数据[玩家数据[id].角色.道具[n]].识别码
					重置id=1
				end
			end
		end		
		if 重置id==0 then
			local 道具格子=玩家数据[id].角色:取道具格子()
			if 道具格子==0 then
				local 临时格子 =玩家数据[id].角色:取临时格子()
				if 临时格子~=0 then
					道具id=self:取新编号()
					self.数据[道具id]=物品类()
					self.数据[道具id]:置对象(名称)
					玩家数据[id].角色.临时包裹[临时格子]=道具id
				else
					常规提示(id,"#Y/你的临时背包已满无法继续获得道具")
				end
				local fhz = self:临时背包索取()
				if fhz[2] then
					发送数据(玩家数据[id].连接id,303,{"底图框","临时背包闪烁",true})
				end
			else
				道具id=self:取新编号()
				self.数据[道具id]=物品类()
				self.数据[道具id]:置对象(名称)
				玩家数据[id].角色.道具[道具格子]=道具id
			end
		end
		临时道具 = 取物品数据(名称)
		临时道具.总类=临时道具[2]
		临时道具.子类=临时道具[4]
		临时道具.分类=临时道具[3]
		if self.kediejia[名称] then
			if 数量==nil then
				数量=1
			end
			self.数据[道具id].可叠加 = true
		elseif 名称=="鬼谷子" then
			local go=false
			if 参数 then
				for i=4,13 do
					if self.阵法名称[i]==参数 then
						self.数据[道具id].子类=参数
						go=true
						break
					end
				end
			end
			if not go then
				self.数据[道具id].子类 = self.阵法名称[取随机数(4,13)]
			end
		elseif 名称=="初级清灵仙露" or 名称=="中级清灵仙露" or 名称=="高级清灵仙露" or 名称=="高级摄灵珠" then
			self.数据[道具id].灵气=灵性
			self.数据[道具id].可叠加 = true
		elseif 名称=="特殊清灵仙露"	then
			self.数据[道具id].灵气=110
			self.数据[道具id].可叠加 = false
		elseif 名称=="灵兜兜" then
			self.数据[道具id].可叠加 = false
		elseif 名称=="镇妖拘魂铃" then
			self.数据[道具id].可叠加 = true
			self.数据[道具id].不可交易 = true
		elseif  名称=="新手大礼包" or 名称=="秘宝宝箱" or 名称=="机缘宝箱" then
			self.数据[道具id].可叠加 = false
			self.数据[道具id].不可交易 = true
		elseif 名称=="新春飞行符" then
			self.数据[道具id].可叠加 = false
			if 数量 and 数量~=999 then
				self.数据[道具id].次数=数量
			end
		elseif 名称=="炫彩ID" then
			--print(数量)
			local sdsd={"绿色ID","蓝色ID","紫色ID","黄金ID"}
			if  数量 and type(数量)~= "number" then
				 self.数据[道具id].特效=数量
			else
				local sdsd={"绿色ID","蓝色ID","紫色ID","黄金ID"}
				 self.数据[道具id].特效=sdsd[取随机数(1,#sdsd)]
			end			
		elseif 名称=="未激活的符石" then
			local 级别 = 数量 or 1
			self.数据[道具id].子类 = 级别
				if 级别 == 1 then
					self.数据[道具id].符石名称 = 一级符石[取随机数(1,#一级符石)]
				elseif 级别==2 then
					self.数据[道具id].符石名称 = 二级符石[取随机数(1,#二级符石)]
				elseif 级别==3 then
					self.数据[道具id].符石名称 = 三级符石[取随机数(1,#三级符石)]
				else
					self.数据[道具id].子类 = 1
					self.数据[道具id].符石名称 = 一级符石[取随机数(1,#一级符石)]
				end
				local lssj = 取物品数据(self.数据[道具id].符石名称)
				self.数据[道具id].符石属性 = lssj[21]
				self.数据[道具id].颜色 = lssj[20]
		elseif 名称=="未激活的星石" then
			if 参数==nil then
				self.数据[道具id].子类 = 取随机数(1,6)
			else
				if 参数 == "头盔" then
					self.数据[道具id].子类 = 1
				elseif 参数 == "饰物" then
					self.数据[道具id].子类 = 2
				elseif 参数 == "武器" then
					self.数据[道具id].子类 = 3
				elseif 参数 == "衣甲" then
					self.数据[道具id].子类 = 4
				elseif 参数 == "腰带" then
					self.数据[道具id].子类 = 5
				elseif 参数 == "靴子" then
					self.数据[道具id].子类 = 6
				else
					self.数据[道具id].子类 = 取随机数(1,6)
				end
			end
		elseif 名称=="灵犀玉" then
			if 参数==nil then
				if 取随机数() <=80 then
					self.数据[道具id].子类 = 取随机数(1,2)
				else
					self.数据[道具id].子类 = 取随机数(2,3)
				end
				self.数据[道具id].特性 = 取灵犀玉特性()
			else
				self.数据[道具id].子类 = 3
				self.数据[道具id].特性 = 取礼包灵犀玉特性()
			end
		elseif 名称=="神兵图鉴" or 名称=="灵宝图鉴" or 名称=="灵饰图鉴" then
			self.数据[道具id].子类 = 数量
		elseif 名称=="阎罗免死牌" then
			self.数据[道具id].次数 = 3
			self.数据[道具id].限时 = os.time()+86400
		elseif 名称=="制造指南书" then
			if 参数==nil then
				self.数据[道具id].子类=数量[取随机数(1,#数量)]*10
				self.数据[道具id].特效=取随机数(1,#书铁范围)
			else
				self.数据[道具id].子类=数量
				self.数据[道具id].特效=参数
			end
		elseif 名称=="灵饰指南书" then
			if 数量 then
				self.数据[道具id].子类=数量[取随机数(1,#数量)]*10
			else
				local lv = 玩家数据[id].角色.等级
				local fanwei={6,8}
				if lv>=80 and lv<100 then
					fanwei={8,10}
				elseif lv>=100 and lv<120 then
					fanwei={10,12}
				elseif lv>=120 then
					fanwei={10,12,14}
				end
				self.数据[道具id].子类=fanwei[取随机数(1,#fanwei)]*10
			end
			if 参数 then
				self.数据[道具id].特效=参数
			else
				self.数据[道具id].特效=随机灵饰[取随机数(1,#随机灵饰)]
			end
		elseif 名称=="元灵晶石" then
			if 数量 then
				self.数据[道具id].子类=数量[取随机数(1,#数量)]*10
			else
				local lv = 玩家数据[id].角色.等级
				local fanwei={6,8}
				if lv>=80 and lv<100 then
					fanwei={8,10}
				elseif lv>=100 and lv<120 then
					fanwei={10,12}
				elseif lv>=120 then
					fanwei={10,12,14}
				end
				self.数据[道具id].子类=fanwei[取随机数(1,#fanwei)]*10
			end
		elseif 名称=="百炼精铁" then
			self.数据[道具id].子类=数量 or 取随机数(1,#15)*10
		elseif 名称=="精魄灵石" then
			local sj = 取随机数(1,6)
			local 类型={"速度","躲避","伤害","灵力","防御","气血"}
			self.数据[道具id].子类=sj
			self.数据[道具id].级别限制=数量 or 1
			self.数据[道具id].特效=类型[sj]
			self.数据[道具id].可叠加=false
		elseif 名称=="钨金" then
			if 数量==nil then
				数量=1
			end
			self.数据[道具id].可叠加=true
			if 参数~=nil then
				self.数据[道具id].级别限制=参数
			else
				self.数据[道具id].级别限制=self.数据[道具id].级别限制 or 150
			end
		elseif 名称=="珍珠" or 名称=="附魔宝珠"  then
			self.数据[道具id].可叠加=false
			if 数量~=nil then
				self.数据[道具id].级别限制=数量
			else
				self.数据[道具id].级别限制=self.数据[道具id].级别限制 or 150
			end
		elseif 名称=="召唤兽内丹" then
			self.数据[道具id].特效=参数 or 取内丹("低级")
		elseif 名称=="高级召唤兽内丹" then
			self.数据[道具id].特效=参数 or 取内丹("高级")
		elseif 名称=="魔兽要诀" then
			if 参数==nil then
				self.数据[道具id].附带技能=取低级要诀()
			else
				self.数据[道具id].附带技能=参数
			end
		elseif 名称=="高级魔兽要诀" then
			if 参数==nil then
				self.数据[道具id].附带技能=取高级要诀()
			else
				self.数据[道具id].附带技能=参数
			end
		elseif self.数据[道具id].总类==141 then
			self.数据[道具id].耐久=100
		elseif 名称=="特殊魔兽要诀" then
			if 参数==nil then
				self.数据[道具id].附带技能=取特殊要诀()
			else
				self.数据[道具id].附带技能=参数
			end
			self.数据[道具id].名称="特殊魔兽要诀"
		elseif 名称=="招魂帖" then
			local 随机地图={1208,1040,1501,1070,1040,1226,1092}
			local 临时地图=随机地图[取随机数(1,#随机地图)]
			self.数据[道具id].地图名称=取地图名称(临时地图)
			self.数据[道具id].地图编号=临时地图
			local xy=地图处理类.地图坐标[临时地图]:取随机点()
			self.数据[道具id].x=xy.x
			self.数据[道具id].y=xy.y
			降妖伏魔:招魂帖(id,self.数据[道具id].地图编号,self.数据[道具id].x,self.数据[道具id].y)
		elseif 名称=="逐妖蛊虫" then
			local 随机地图={1208,1040,1501,1070,1040,1226,1092}
			local 临时地图=随机地图[取随机数(1,#随机地图)]
			self.数据[道具id].地图名称=取地图名称(临时地图)
			self.数据[道具id].地图编号=临时地图
			local xy=地图处理类.地图坐标[临时地图]:取随机点()
			self.数据[道具id].x=xy.x
			self.数据[道具id].y=xy.y
			降妖伏魔:逐妖蛊虫(id,self.数据[道具id].地图编号,self.数据[道具id].x,self.数据[道具id].y)
		elseif 名称=="点化石" then
			self.数据[道具id].附带技能=数量 or "感知"
		elseif  名称=="九转金丹" then
			self.数据[道具id].阶品=数量
			self.数据[道具id].可叠加 = false
		elseif 名称=="月华露" then
			self.数据[道具id].阶品= 参数 or 500
			self.数据[道具id].可叠加 = true
		elseif 名称=="藏宝图" or 名称=="高级藏宝图" or 名称=="玲珑宝图" or 名称=="考古铁铲" then
			local 随机地图={1501,1506,1092,1091,1110,1142,1514,1174,1173,1146,1208}
			local 临时地图=随机地图[取随机数(1,#随机地图)]
			self.数据[道具id].地图名称=取地图名称(临时地图)
			self.数据[道具id].地图编号=临时地图
			local xy=地图处理类.地图坐标[临时地图]:取随机点()
			self.数据[道具id].x=xy.x
			self.数据[道具id].y=xy.y
		elseif 名称=="上古锻造图策" then
			if 数量 then
				self.数据[道具id].级别限制=数量[取随机数(1,#数量)]*10-5
			else
				local 等级=玩家数据[id].角色.等级
				local lv = math.min(qz(等级/10),13)
				local nm={lv+1,lv+2}
				self.数据[道具id].级别限制=nm[取随机数(1,#nm)]*10-5
			end
			self.数据[道具id].种类=图策范围[取随机数(1,#图策范围)]
		elseif 名称=="炼妖石" then
			if 数量 then
				self.数据[道具id].级别限制=数量[取随机数(1,#数量)]*10-5
			else
				local 等级=玩家数据[id].角色.等级
				local lv = math.min(qz(等级/10),13)
				local nm={lv+1,lv+2}
				self.数据[道具id].级别限制=nm[取随机数(1,#nm)]*10-5
			end
			self.数据[道具id].分类=3
		elseif 名称=="吸附石" then
      		self.数据[道具id].五行=取五行()	
		elseif 名称=="怪物卡片" then
			if 变身卡数据[数量]==nil then
				self.数据[道具id].等级=数量
				self.数据[道具id].造型=变身卡范围[数量][取随机数(1,#变身卡范围[数量])]
			else
				self.数据[道具id].等级=变身卡数据[数量].等级
				self.数据[道具id].造型=数量
			end
			self.数据[道具id].类型=变身卡数据[self.数据[道具id].造型].类型
			self.数据[道具id].单独=变身卡数据[self.数据[道具id].造型].单独
			self.数据[道具id].正负=变身卡数据[self.数据[道具id].造型].正负
			self.数据[道具id].技能=变身卡数据[self.数据[道具id].造型].技能
			self.数据[道具id].属性=变身卡数据[self.数据[道具id].造型].属性
			self.数据[道具id].次数=self.数据[道具id].等级
		elseif 名称=="如意丹" then
			self.数据[道具id].灵气 = 灵性
			self.数据[道具id].可叠加 = false
		elseif 名称=="龙之筋" then
			self.数据[道具id].五行 = 取五行()
		elseif 名称=="天蚕丝" then
			self.数据[道具id].五行 = "金"
		elseif 名称=="阴沉木" then
			self.数据[道具id].五行 = "木"
		elseif 名称=="玄龟板" then
			self.数据[道具id].五行 = 取五行()
		elseif 名称=="麒麟血" then
			self.数据[道具id].五行 = 取五行()
		elseif 名称=="补天石" then
			self.数据[道具id].五行 = 取五行()
		elseif 名称=="金凤羽" then
			self.数据[道具id].五行 =取五行()
		elseif 名称=="内丹" then
			self.数据[道具id].五行 =取五行()
		elseif 名称 == "秘制食谱" then
			self.数据[道具id].子类 =参数
		elseif 名称 == "祈愿宝箱" then
			self.数据[道具id].次数 =数量
		-- elseif 临时道具.总类==3 and  临时道具.分类==11 then
		-- 	self.数据[道具id].灵气=取随机数(10,100)
		-- 	self.数据[道具id].级别限制 = 数量
	    elseif 名称=="九眼天珠" or 名称=="三眼天珠" or 名称=="天眼珠" then
	      	if 数量 then
				self.数据[道具id].级别限制=数量[取随机数(1,#数量)]*10-5
			else
				local 等级=玩家数据[id].角色.等级
				local lv = math.min(qz(等级/10),13)
				local nm={lv+1,lv+2}
				self.数据[道具id].级别限制=nm[取随机数(1,#nm)]*10-5
			end
	     	self.数据[道具id].灵气 = 参数
		elseif 临时道具.总类==5 and  临时道具.分类==4 then
			if 数量 == nil then
				self.数据[道具id].级别限制=1
			else
				self.数据[道具id].级别限制=数量+0
			end
		elseif 临时道具.总类==5 and  临时道具.分类==6 then
			if 数量 == nil then
				self.数据[道具id].级别限制=1
			else
				self.数据[道具id].级别限制=数量+0
			end
		elseif 临时道具.总类==1 and 临时道具.子类==1 and 临时道具.分类==4 then
			self.数据[道具id].阶品=参数
		elseif 名称=="金创药" or 名称=="红雪散" or 名称=="小还丹"  or 名称=="千年保心丹" or 名称=="金香玉" or 名称=="风水混元丹" or 名称=="蛇蝎美人" or 名称=="定神香" or 名称=="佛光舍利子" or 名称=="九转回魂丹" or 名称=="五龙丹" or 名称=="十香返生丸"  then
			self.数据[道具id].阶品=参数 or 取随机数(30,50)
		elseif 名称=="醉仙果" or 名称=="七珍丸" or 名称=="凝气丸"  or 名称=="舒筋活络丸" or 名称=="固本培元丹" or 名称=="九转续命丹" or 名称=="十全大补丸"  then
			self.数据[道具id].阶品=参数 or 取随机数(25,35)
		end
		if self.数据[道具id].名称 then
			if self.数据[道具id].可叠加 then
				if 数量 == nil then
					self.数据[道具id].数量=1
					常规提示(id,"#Y/你获得了"..self.数据[道具id].名称)
				else
					self.数据[道具id].数量=数量 or 1
					常规提示(id,"#Y/你获得了"..原始数量.."个"..self.数据[道具id].名称)
				end
			else
				常规提示(id,"#Y/你获得了"..self.数据[道具id].名称)
			end
		end
		-- 设置识别码
		self.数据[道具id].识别码=识别码
		if  self.数据[道具id].名称=="心魔宝珠" then
			self.数据[道具id].不可交易=true
		end
		if 专用~=nil then
			self.数据[道具id].专用=id
			self.数据[道具id].不可交易=true
		end
	else
		if 消费方式 ~= nil then
			if 消费<1 then
				print("玩家id  "..id.."   商城购买物品存在作弊行为！")
				return false
			end
			消费=qz(消费)
			if 消费方式 == "银子" then
				if not 玩家数据[id].角色:扣除银子(消费*原始数量,0,0,消费内容,1) then
					常规提示(id,"你没有那么多的银子")
					return false
				end
			end
		end
	    -- -- 道具格子存在性检查 - 只对可叠加物品进行叠加处理
	    -- local 是可叠加物品2 = self.kediejia[名称] or
		--     名称=="初级清灵仙露" or 名称=="中级清灵仙露" or 名称=="高级清灵仙露" or 名称=="高级摄灵珠" or
		--     名称=="钨金" or 名称=="月华露"

	    -- if 是可叠加物品2 and type(数量) == "number" then
		--     for n = 1, 80 do
	    -- -- 道具格子为空时，不应访问空格子
		--     if 玩家数据[id].角色.道具[n] ~= nil then
		--         -- 确保 self.数据 中对应道具存在
		--         if self.数据[玩家数据[id].角色.道具[n]] ~= nil then
		--             if self.数据[玩家数据[id].角色.道具[n]].名称 == 名称 then
		--                 -- 处理逻辑...只有当数量是数字时才进行叠加
		--                 local 最大叠加数2 = self.kediejia[名称] or 99
		--                 if self.数据[玩家数据[id].角色.道具[n]].数量 + 数量 <= 最大叠加数2 then
		--                     self.数据[玩家数据[id].角色.道具[n]].数量 = self.数据[玩家数据[id].角色.道具[n]].数量 + 数量
		--                     重置id = 1
		--                     break
		--                 end
		--             end
		--         end
		--     end
		-- end
	    -- end
	    -- 道具格子存在性检查
	    for n = 1, 80 do
    -- 道具格子为空时，不应访问空格子
	    if 玩家数据[id].角色.道具[n] ~= nil then
	        -- 确保 self.数据 中对应道具存在
	        if self.数据[玩家数据[id].角色.道具[n]] ~= nil then
	            if self.数据[玩家数据[id].角色.道具[n]].名称 == 名称 then
	                -- 处理逻辑...
	                if self.数据[玩家数据[id].角色.道具[n]].数量 + 数量 <= 99 then
	                    self.数据[玩家数据[id].角色.道具[n]].数量 = self.数据[玩家数据[id].角色.道具[n]].数量 + 数量
	                    重置id = 1
	                    break
	                end
	            end
	        end
	    end
	end
	   if 重置id==0 then
			local 道具格子=玩家数据[id].角色:取道具格子()
			if 道具格子==0 then
				local 道具格子 =玩家数据[id].角色:取临时格子()
				if 道具格子~=0 then
					道具id=self:取新编号()
					self.数据[道具id]= 数据
					self.数据[道具id].识别码=识别码
					if self.数据[道具id].名称 then
						if 数量 ~= nil then
							self.数据[道具id].数量 = 数量
							常规提示(id,"#Y/你获得了"..原始数量.."#Y个"..self.数据[道具id].名称)
						else
							常规提示(id,"#Y/你获得了"..self.数据[道具id].名称)
						end
					end
					玩家数据[id].角色.临时包裹[道具格子]=道具id
					常规提示(id,"#Y/道具栏已满，物品被放入临时物品栏，请及时处理")
				else
					常规提示(id,"#Y/你的临时背包已满无法继续获得道具")
				end
				local fhz = self:临时背包索取()
				if fhz[2] then
					发送数据(玩家数据[id].连接id,303,{"底图框","临时背包闪烁",true})
				end
			else
				道具id=self:取新编号()
				self.数据[道具id]= 数据
				self.数据[道具id].识别码=识别码
				if self.数据[道具id].名称 then
					if 数量 ~= nil then
						self.数据[道具id].数量 = 数量
						常规提示(id,"#Y/你获得了"..原始数量.."#Y个"..self.数据[道具id].名称)
					else
						常规提示(id,"#Y/你获得了"..self.数据[道具id].名称)
					end
				end
				玩家数据[id].角色.道具[道具格子]=道具id
			end
		end
	end
	if 超链接 and self.数据[道具id] then
		local 文本 = 超链接.提示.."#G/qqq|"..self.数据[道具id].名称.."*"..self.数据[道具id].识别码.."*道具/["..self.数据[道具id].名称.."]#W"..超链接.结尾
		local 返回信息 = {}
		返回信息[#返回信息+1] = self.数据[道具id]
		返回信息[#返回信息].索引类型 = "道具"
		广播消息({内容=文本,频道=超链接.频道,方式=1,超链接=返回信息})
	end
	
	发送数据(玩家数据[id].连接id,3699)
	return true
end
function 道具处理类:系统给予处理(连接id,id,数据)
	local 事件=玩家数据[id].给予数据.事件
	local 临时NPC=玩家数据[id].给予数据.临时NPC
	local 任务编号=玩家数据[id].给予数据.任务编号
	local 类型 = 数据.类型
	if 事件 ~= nil and 事件~="法宝补充灵气" and 事件~="迎客僧给与银子" and  (数据.格子[1]==nil or 玩家数据[id].角色[类型][数据.格子[1]]==nil or self.数据[玩家数据[id].角色[类型][数据.格子[1]]]==nil) then
		常规提示(id,"#Y/你没有这样的道具")
		return
	end
	if 事件=="打造角色装备" then
		local 任务id=玩家数据[id].角色:取任务(5)
		if 任务id==0 then
			常规提示(id,"#Y/你没有这样的任务")
			return
		end
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称~=任务数据[任务id].石头 then
			常规提示(id,"#Y/对方需要的不是这种物品")
			return
		end
		if 任务数据[任务id].数量>1 and self.数据[道具id].数量<任务数据[任务id].数量 then
			常规提示(id,"#Y/该物品的数量无法达到要求")
			return
		end
		if 任务数据[任务id].数量>1 then
			self.数据[道具id].数量=self.数据[道具id].数量-任务数据[任务id].数量
		end
		if self.数据[道具id].数量==nil or self.数据[道具id].数量<=0 then
			self.数据[道具id]=nil
			玩家数据[id].角色[类型][数据.格子[1]]=nil
		end
		if 任务数据[任务id].打造类型=="装备" then
			装备处理类:添加强化打造装备(id,任务id)
		elseif 任务数据[任务id].打造类型=="神兵苏醒" then
			装备处理类:打造160神兵(id,任务id)
		elseif 任务数据[任务id].打造类型=="灵饰" then
			local 临时道具 = self:灵饰处理(id,任务数据[任务id].名称,任务数据[任务id].级别,1,任务数据[任务id].部位)
			临时道具.制造者 = 任务数据[任务id].制造者
			if self:给予道具(id,nil,nil,nil,nil,nil,临时道具) then
				玩家数据[id].角色:取消任务(任务id)
				任务数据[任务id]=nil
			end
		end
		return
	elseif 事件=="购买玩家打造" then
		if 数据.格子[1] == nil or 数据.格子[2] == nil then
			常规提示(id,"#Y/请放入你要打造的书和铁")
			return
		elseif 玩家数据[id].给予数据 == nil or 玩家数据[id].给予数据.制造数据 == nil then
			常规提示(id,"#Y/数据错误请重新购买操作")
			return
		end
		装备处理类:摊位打造装备(id,数据)
		return
	elseif 事件=="购买假人打造" then
		if 数据.格子[1] == nil or 数据.格子[2] == nil then
			常规提示(id,"#Y/请放入你要打造的书和铁")
			return
		elseif 玩家数据[id].给予数据 == nil or 玩家数据[id].给予数据.制造数据 == nil then
			常规提示(id,"#Y/数据错误请重新购买操作")
			return
		end
		假人玩家处理类:假人摊位打造装备(id,数据)
		return
    elseif 事件=="孩子生活" then
        local 编号 = 玩家数据[id].给予数据.id
        local 道具id=玩家数据[id].角色.道具[数据.格子[1]]
        if 玩家数据[id].孩子.数据[编号] == nil then
            常规提示(id,"#Y/该孩子好像不存在")
            return
        end
        local 时间 = math.ceil((os.time() - 玩家数据[id].孩子.数据[编号].出生日期)/86400)
        if 玩家数据[id].孩子.数据[编号].培养次数[时间] == nil then
            常规提示(id,"#Y/该孩子已经达到成年条件无法继续进行生活培养")
            return
        elseif 时间 > 6 then
            常规提示(id,"#Y/该孩子已经达到成年条件,无法再进行生活培养")
            return
        end
        local 名称=self.数据[道具id].名称
        local 加成项目 = ""
        local 加成数值 = 0
        if 名称 == "红木短剑" then
            if 玩家数据[id].孩子.数据[编号].培养.武力 < 30 or 玩家数据[id].孩子.数据[编号].培养.武力 >= 70 then
                常规提示(id,"#Y/该孩子无法使用该物品,要求孩子武力>30或者<=70方可使用")
                return
            end
            加成项目 = "武力"
            加成数值 = 取随机数(3,6)
        elseif 名称 == "竹马" then
            if 玩家数据[id].孩子.数据[编号].培养.武力 > 30 then
                常规提示(id,"#Y/该孩子无法使用该物品,要求孩子武力<30方可使用")
                return
            end
            加成项目 = "武力"
            加成数值 = 取随机数(1,4)
        elseif 名称 == "瑶池蟠桃" then
            if 玩家数据[id].孩子.数据[编号].培养.根骨 < 40 or 玩家数据[id].孩子.数据[编号].培养.根骨 >= 70 then
                常规提示(id,"#Y/该孩子无法使用该物品,要求孩子根骨>40或<=70方可使用")
                return
            end
            加成项目 = "根骨"
            加成数值 = 取随机数(3,6)
        elseif 名称 == "玉灵果" then
            if 玩家数据[id].孩子.数据[编号].培养.根骨 > 40 then
                常规提示(id,"#Y/该孩子无法使用该物品,要求孩子根骨<40方可使用")
                return
            end
            加成项目 = "根骨"
            加成数值 = 取随机数(2,5)
        elseif 名称 == "超级金柳露" then
            if 取随机数() <= 50 then
                加成项目 = "根骨"
            else
                加成项目 = "灵敏"
            end
            if 玩家数据[id].孩子.数据[编号].培养[加成项目] > 70 then
                常规提示(id,"#Y/该孩子无法使用该物品,要求孩子"..加成项目.."<=70方可使用")
                return
            end
            加成数值 = 取随机数(2,5)
        elseif 名称 == "金柳露" then
            if 取随机数() <= 50 then
                加成项目 = "根骨"
            else
                加成项目 = "灵敏"
            end
            if 玩家数据[id].孩子.数据[编号].培养[加成项目] > 50 then
                常规提示(id,"#Y/该孩子无法使用该物品,要求孩子"..加成项目.."<=50方可使用")
                return
            end
            加成数值 = 取随机数(1,4)
        end
        if 加成数值 == 0 then
            常规提示(id,"#Y/孩子生活中无法使用该道具")
            return
        else
            玩家数据[id].孩子.数据[编号].培养[加成项目] = 玩家数据[id].孩子.数据[编号].培养[加成项目] + 加成数值
            if self.数据[道具id].数量 ~= nil then
                self.数据[道具id].数量 = self.数据[道具id].数量 - 1
            end
            if self.数据[道具id].数量 == nil or self.数据[道具id].数量 <= 0 then
                self.数据[道具id]=nil
                玩家数据[id].角色.道具[数据.格子[1]]=nil
            end
            常规提示(id,"#Y/孩子在生活中获得无限乐于,竟然意外的提升了"..加成数值..加成项目.."成为天才儿童指日可待")
            玩家数据[id].孩子.数据[编号].培养次数[时间] = 玩家数据[id].孩子.数据[编号].培养次数[时间] + 1
            道具刷新(id)
            发送数据(玩家数据[id].连接id,96.2,{数据=玩家数据[id].孩子.数据[编号],编号=编号})
        end
    elseif 事件=="孤儿领养" then
        local 道具id=玩家数据[id].角色.道具[数据.格子[1]]
        if self.数据[道具id].名称~="孤儿名册" then
            常规提示(id,"#Y/对方需要的不是这种物品")
            return
        elseif #玩家数据[id].孩子.数据 >= 2 then
            常规提示(id,"#Y/你可领养的孩子已经达到了上限")
            return
        else
            local 模型 = "小毛头"
            if 玩家数据[id].角色.种族 == "人" then
                if 取随机数() <= 50 then
                    模型 = "小毛头"
                else
                    模型 = "小丫丫"
                end
            elseif 玩家数据[id].角色.种族 == "仙" then
                if 取随机数() <= 50 then
                    模型 = "小仙女"
                else
                    模型 = "小仙灵"
                end
            elseif 玩家数据[id].角色.种族 == "魔" then
                if 取随机数() <= 50 then
                    模型 = "小魔头"
                else
                    模型 = "小精灵"
                end
            end
            self.数据[道具id]=nil
            玩家数据[id].角色.道具[数据.格子[1]]=nil
            玩家数据[id].孩子:新增孩子(模型)
            常规提示(id,"#Y/领取孤儿成功")
            return
        end
    elseif 事件=="孩子学习" then
        local 道具id=玩家数据[id].角色.道具[数据.格子[1]]
        local 编号 = 玩家数据[id].给予数据.id
        if 玩家数据[id].孩子.数据[编号] == nil then
            常规提示(id,"#Y/该孩子好像不存在")
            return
        end
        local 名称=self.数据[道具id].名称
        local 时间 = math.ceil((os.time() - 玩家数据[id].孩子.数据[编号].出生日期)/86400)
        if 玩家数据[id].孩子.数据[编号].培养次数[时间] ~= nil and 玩家数据[id].孩子.数据[编号].培养次数[时间] >= 999999 then
            常规提示(id,"#Y/该孩子今日学习次数已经达到上限,每日可学习+生活10次")
            return
        elseif 玩家数据[id].孩子.数据[编号].培养次数[时间] == nil and (名称 == "山海经" or 名称 == "论语" or 名称 == "搜神记") then
            常规提示(id,"#Y/该孩子已经达到成年条件,无法再进行学习山海经、论语、搜神记等书籍")
            return
        elseif 时间 > 6 and (名称 == "山海经" or 名称 == "论语" or 名称 == "搜神记") then
            常规提示(id,"#Y/该孩子已经达到成年条件,无法再进行学习山海经、论语、搜神记等书籍")
            return
        elseif 名称 == "六艺修行" and 玩家数据[id].孩子.数据[编号].等级 < 120 then
            常规提示(id,"#Y/孩子120级以前无法学习六艺修行")
            return
        end
        local 加成项目 = ""
        local 加成数值 = 0
        local 魔兽要诀 = false
        local 特殊武学 = false
        if 取要诀(self.数据[道具id].附带技能) ~= "" or 名称 == "还魂秘术" or 名称 == "蚩尤武诀" or 名称 == "黄帝内经" or 名称 == "奇异果" or 名称 == "六艺修行"  then
            魔兽要诀 = true
        end
        if 魔兽要诀 == false then
            if 名称 == "搜神记" then
                if 玩家数据[id].孩子.数据[编号].培养.智力 < 30 or 玩家数据[id].孩子.数据[编号].培养.智力 >= 70 then
                    常规提示(id,"#Y/该孩子无法使用该物品,要求孩子智力>30或者<=70方可使用")
                    return
                end
                加成项目 = "智力"
                加成数值 = 取随机数(3,6)
            elseif 名称 == "山海经" then
                if 玩家数据[id].孩子.数据[编号].培养.智力 > 30 then
                    常规提示(id,"#Y/该孩子无法使用该物品,要求孩子智力<30方可使用")
                    return
                end
                加成项目 = "智力"
                加成数值 = 取随机数(1,5)
            elseif 名称 == "论语" then
                if 玩家数据[id].孩子.数据[编号].培养.定力 > 70 then
                    常规提示(id,"#Y/该孩子无法使用该物品,要求孩子定力<70方可使用")
                    return
                end
                加成项目 = "定力"
                加成数值 = 取随机数(2,6)
            end
            if 加成数值 == 0 then
                常规提示(id,"#Y/该物品不是儿童学习的道具啊")
                return
            else
                玩家数据[id].孩子.数据[编号].培养[加成项目] = 玩家数据[id].孩子.数据[编号].培养[加成项目] + 加成数值
                self.数据[道具id]=nil
                玩家数据[id].角色.道具[数据.格子[1]]=nil
                常规提示(id,"#Y/孩子在生活中获得无限乐于,竟然意外的提升了"..加成数值..加成项目.."成为天才儿童指日可待")
                玩家数据[id].孩子.数据[编号].培养次数[时间] = 玩家数据[id].孩子.数据[编号].培养次数[时间] + 1
            end
        else
            玩家数据[id].孩子:洗练处理(玩家数据[id].连接id,id,{序列=数据.格子[1],序列1=编号})
        end
        道具刷新(id)
        发送数据(玩家数据[id].连接id,96.2,{数据=玩家数据[id].孩子.数据[编号],编号=编号})
	elseif 临时NPC == "竹罗汉" and 事件 == "副本五更寒给予签文" then
	    -- 检查任务数据是否存在
	    if 任务数据[玩家数据[id].给予数据.任务id] == nil then
	      --  print("Debug: 任务数据不存在，任务ID:", 玩家数据[id].给予数据.任务id)
	        return
	    end
	    local 任务id = 玩家数据[id].给予数据.任务id
	    local 物品名称 = 玩家数据[id].给予数据.物品
	    local NPC名称 = 任务数据[任务id].名称
	    --print(string.format("Debug: 临时NPC: %s, 任务ID: %d, 物品名称: %s", 临时NPC, 任务id, 物品名称))
	    if 临时NPC == NPC名称 then
	        -- 获取道具ID
	        local 道具id = 玩家数据[id].角色[类型][数据.格子[1]]
	      --  print(string.format("Debug: 当前道具ID: %s", 道具id))

	        if self.数据[道具id] and self.数据[道具id].名称 == 物品名称 then
	            local 数量 = self.数据[道具id].数量 or 1  -- 确保数量有值，默认1
	         --  print(string.format("Debug: 找到道具，名称: %s, 当前数量: %d", 物品名称, 数量))

	            -- 处理道具数量
	            if 数量 > 1 then
	                self.数据[道具id].数量 = 数量 - 1
	                --print(string.format("Debug: 减少道具数量后，新的数量: %d", self.数据[道具id].数量))
	            else
	                self.数据[道具id] = nil  -- 移除道具
	                玩家数据[id].角色[类型][数据.格子[1]] = nil  -- 清空格子
	               -- print("Debug: 道具已移除，数量为0")
	            end
	            -- 处理副本状态
	            local 副本id = 任务处理类:取副本id(id, 670)
	           -- print(string.format("Debug: 获取的副本ID: %s", 副本id))

	            if 副本id == 0 or 副本id ~= id then
	                print("Error: 副本ID不匹配，返回")
	                return
	            end
	            -- 更新副本状态
	            副本数据.如梦奇谭之五更寒.进行[id].阶段 = 1
	          --  print("Debug: 副本状态更新，阶段设为1")
	            刷新任务670(id)
	            道具刷新(id)
	        else
	          --  print(string.format("Debug: 道具名称不匹配，期待: %s, 实际: %s", 物品名称, self.数据[道具id] and self.数据[道具id].名称 or "无"))
	            添加最后对话(id, "你要给我什么？")
	        end
	        return
     	end
	elseif 临时NPC~=nil then
		-- 玩家数据[玩家id].给予数据={类型=1,临时NPC="竹罗汉",物品="包子",任务id=任务id}
		if 任务数据[玩家数据[id].给予数据.任务id]==nil then
			return
		end
		local 任务id=玩家数据[id].给予数据.任务id
		local 物品名称=玩家数据[id].给予数据.物品
		local NPC名称=任务数据[任务id].名称
		-- print(临时NPC,物品名称)
		-- if 临时NPC==NPC名称 then
		-- 	local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		-- 	--玩家数据[数字id].给予数据={类型=1,临时NPC="陆云鹤",地图=1002,物品="墨魂灯",任务id=任务id,事件=self.任务标题}
		-- 	if self.数据[道具id].名称==物品名称 then
		-- 		if self.数据[道具id].数量~=nil and self.数据[道具id].数量>1 then  --这里可能会丢失剧情道具，导致队员和队长不一致
		-- 			self.数据[道具id].数量 = self.数据[道具id].数量 -1
		-- 		else
		-- 			self.数据[道具id]=nil
		-- 			玩家数据[id].角色[类型][数据.格子[1]]=nil
		-- 		end
		-- 	else
		-- 		添加最后对话(id,"你要给我什么？")
		-- 	end
		-- 	return
		if 任务数据[任务id].神器NPC and  任务数据[任务id].神器NPC==临时NPC then --314更新
			local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
			--玩家数据[数字id].给予数据={类型=1,临时NPC="陆云鹤",地图=1002,物品="墨魂灯",任务id=任务id,事件=self.任务标题}
			if self.数据[道具id].名称==物品名称 then
				local 事件=玩家数据[id].给予数据.事件
				if 事件=="墨魂笔之踪" then
					if 墨魂笔之踪:更新神器进度(id) then
						if self.数据[道具id].数量~=nil and self.数据[道具id].数量>1 then
							self.数据[道具id].数量 = self.数据[道具id].数量 -1
						else
							self.数据[道具id]=nil
							玩家数据[id].角色[类型][数据.格子[1]]=nil
						end
					end
				end
			else
				添加最后对话(id,"你要给我什么？")
			end
			return
		end
	elseif 事件=="墨魂笔之踪" or 事件=="神归昆仑镜"  then --314更新
		local dj={}
		local num=0
		local 名称=玩家数据[id].给予数据.需求物品.名称
		local 数量=玩家数据[id].给予数据.需求物品.数量
		for i=1,数量 do
			if 数据.格子[i]~=nil then
				dj[i]=玩家数据[id].角色[类型][数据.格子[i]]
				if self.数据[dj[i]]==nil then
					添加最后对话(id,"#45#45#45")
					return
				end
				if self.数据[dj[i]].名称~=名称 then
					添加最后对话(id,"一次性给物品#G"..数量.."颗"..名称.."#W吧")
					return
				end
				num=num+1
			else
				添加最后对话(id,"一次性给物品#G"..数量.."颗"..名称.."#W吧")
				return
			end
		end
		if num==数量 then
			for i=1,数量 do
				self.数据[dj[i]]=nil
				玩家数据[id].角色[类型][数据.格子[i]]=nil
			end
			if 事件=="墨魂笔之踪" then
				墨魂笔之踪:更新神器进度(id)
			elseif 事件=="神归昆仑镜" then
				神归昆仑镜:更新神器进度(id)
			end

		end
		玩家数据[id].给予数据=nil
		self:索要道具更新(id,类型)
	elseif 事件=="迎客僧给与银子" then
		-- 迎客僧拒绝银子，玩家不会失去银子
		local 银子数额 = 0

		-- 尝试从不同字段获取银子数额
		if 数据.银子 then
			银子数额 = 数据.银子 + 0
		elseif 数据.money then
			银子数额 = 数据.money + 0
		elseif 数据.金额 then
			银子数额 = 数据.金额 + 0
		end

		-- 迎客僧拒绝，玩家不失去银子
		if 银子数额 > 0 then
			-- 检查玩家银子是否足够
			if 玩家数据[id].角色.银子 < 银子数额 then
				--常规提示(id,"#Y/你没有那么多的银子")
				玩家数据[id].给予数据=nil
				return
			end
			--常规提示(id,format("#Y/你想给迎客僧%s两银子，但是他拒绝了，银子还在你身上",银子数额))
		end

		-- 检查并给予迎客僧成就奖励（每个角色只能获得一次）
		-- 成就数据由成就处理类统一管理，这里不需要初始化

		-- 检查并触发迎客僧成就（只有未完成的成就才能触发）
		-- 成就完成后会被设置为nil，所以检查是否不为nil且为数字类型
		local 成就值 = 成就数据[id] and 成就数据[id].数据 and 成就数据[id].数据["趣味成就"] and 成就数据[id].数据["趣味成就"]["迎客僧的拒绝"]
		if 成就值 ~= nil and type(成就值) == "number" then
			-- 触发成就奖励
			成就数据[id]:判断进度(id,"趣味成就","迎客僧的拒绝")
		end

		-- 返回迎客僧的对话
		发送数据(玩家数据[id].连接id,1501,{
			名称="迎客僧",
			模型="胖和尚",
			对话="我不要钱钱！#54",
			--选项={"我知道了"}
		})

		玩家数据[id].给予数据=nil
		return		
	elseif 事件=="中秋上交物品" then
	    local 任务id=玩家数据[id].角色:取任务(55)
	    if 任务id==0 or 任务数据[任务id]==nil or 任务数据[任务id].分类~=3 then
	      常规提示(id,"#Y/你没有这样的任务")
	      return
	    end
	    local 道具id=玩家数据[id].角色.道具[数据.格子[1]]
		if self.数据[道具id].名称=="包子" then
			local 数量 = self.数据[道具id].数量 or 0  -- 确保数量有值，默认0
			    if 数量 > 1 then
			        self.数据[道具id].数量 = 数量 - 1
			    else
			        self.数据[道具id] = nil
			        玩家数据[id].角色[类型][数据.格子[1]] = nil
			    end
			中秋任务:完成中秋单人任务(id,任务id)
	    	self:索要道具更新(id,类型)
	    	道具刷新(id)
		else
			添加最后对话(id,"你给我什么玩意儿，忽悠人呢？")
		end
		return
	elseif 事件=="上交心魔宝珠" then
		if 玩家数据[id].角色.等级>=60 then
			常规提示(id,"#Y/你已经脱离了新手阶段，无法获得此种奖励")
			return
		elseif 玩家数据[id].角色.等级<15 then
			常规提示(id,"#Y/只有等级达到15级的玩家才可获得此种奖励")
			return
		elseif 心魔宝珠[id]~=nil and 心魔宝珠[id]>=20 then
			常规提示(id,"#Y/请明天再来上交心魔宝珠")
			return
		end
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		local 名称="心魔宝珠"
		local 数量=20
		if self.数据[道具id].名称~=名称 then
			常规提示(id,"#Y/对方需要的不是这种物品")
			return
		end
		if 数量>1 and self.数据[道具id].数量<数量 then
			常规提示(id,"#Y/该物品的数量无法达到要求")
			return
		end
		if 数量>1 then
			self.数据[道具id].数量=self.数据[道具id].数量-数量
		end
		if self.数据[道具id].数量==nil or self.数据[道具id].数量<=0 then
			self.数据[道具id]=nil
			玩家数据[id].角色[类型][数据.格子[1]]=nil
		end
		local 等级=取等级(id)
		local 经验=等级*等级+3000
		local 储备=等级*1000
		玩家数据[id].角色:添加经验(经验,"心魔宝珠奖励")
		玩家数据[id].角色:添加银子(储备,"心魔宝珠奖励",1)
		if 心魔宝珠[id]==nil then
			心魔宝珠[id]=1
		else
			心魔宝珠[id]=心魔宝珠[id]+1
		end
		常规提示(id,format("#Y/你本日还可领取#R/%s#Y/次奖励",(20-心魔宝珠[id])))
		self:索要道具更新(id,类型)
	elseif 事件=="降妖伏魔给予包子" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称=="包子" then
			if self.数据[道具id].数量~=nil and self.数据[道具id].数量>1 then
				self.数据[道具id].数量 = self.数据[道具id].数量 -1
			else
				self.数据[道具id]=nil
				玩家数据[id].角色[类型][数据.格子[1]]=nil
			end
			任务处理类:完成饿鬼(id,玩家数据[id].给予数据.任务id)
		else
			添加最后对话(id,"你给我什么玩意儿，忽悠鬼呢？")
		end
		return
	elseif 事件=="转换指南书" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称 ~= "制造指南书" then
			添加最后对话(id,"你给我什么玩意儿，忽悠鬼呢？")
			return
		end
		if  self.数据[道具id].特效 < 13 and self.数据[道具id].特效 > 18  then
			添加最后对话(id,"只限法杖、弓弩、宝珠、巨剑、伞、灯笼")
			return
		end
		if  self.数据[道具id].特效 > 12 and self.数据[道具id].特效 < 19  then
			self.数据[道具id].特效=取随机数(1,12)
			self:索要道具更新(id,类型)
			常规提示(id,"#Y/更换成功")
			return
		end
	elseif 事件=="转换武器造型" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].总类==2 and self.数据[道具id].分类 == 3  then
			if 武器转换临时数据[id]~= nil then
				if not 玩家数据[id].角色:扣除银子(20000000,0,0,"转换武器造型",1) then
					return 添加最后对话(id,"需要支付20000000两银子才可以转换此类道具")
				end
				local 序列 = 1
				local 等级 = self.数据[道具id].级别限制
				if 等级 <= 80 then
					序列 = 等级/10 + 1
				elseif 等级<= 110 then
					序列 = 取随机数(10,12)
				elseif 等级<= 140 then
					序列 = 取随机数(13,15)
				elseif 等级<= 150 then
					序列 = 16
				else
					序列 = 17
				end
				self.数据[道具id].名称 = 武器表[武器转换临时数据[id]][序列]
				local 临时武器数据 = 取物品数据(self.数据[道具id].名称)
				self.数据[道具id].子类 = 临时武器数据[4]
				self.数据[道具id].角色限制 = 临时武器数据[7]
				道具刷新(id)
				添加最后对话(id,"转换成功！")
			else
				添加最后对话(id,"转换失败，请重试！")
			end
		else
			添加最后对话(id,"你给我什么玩意儿？")
		end
		return
	elseif 事件=="转换装备" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].总类==2 then
			local 子类=self:取武器类型(玩家数据[id].给予数据.转换造型)
			玩家数据[id].角色:转换装备操作(id,self.数据[道具id],子类)
		else
			添加最后对话(id,"请给我正确的装备")
		end
		return
	elseif 事件=="降妖伏魔给予酒" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称=="女儿红" or self.数据[道具id].名称=="虎骨酒" or self.数据[道具id].名称=="珍露酒"
			or self.数据[道具id].名称=="梅花酒" or self.数据[道具id].名称=="百味酒" or self.数据[道具id].名称=="蛇胆酒" or self.数据[道具id].名称=="醉生梦死" then
			if self.数据[道具id].数量~=nil and self.数据[道具id].数量>1 then
				self.数据[道具id].数量 = self.数据[道具id].数量 -1
			else
				self.数据[道具id]=nil
				玩家数据[id].角色[类型][数据.格子[1]]=nil
			end
			任务处理类:完成酒鬼(id,玩家数据[id].给予数据.任务id)
		else
			添加最后对话(id,"你给我什么玩意儿，忽悠鬼呢？")
		end
		return
	elseif 事件=="上交暑假道具" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称 ~= "知了" and self.数据[道具id].名称 ~= "蟋蟀" and self.数据[道具id].名称 ~= "小螃蟹"  then
			常规提示(id,"#Y/对方需要的不是这种物品")
			return
		end
		local 数量 = 1
		if self.数据[道具id].数量~= nil then 数量 = self.数据[道具id].数量 end
		self.数据[道具id]=nil
		玩家数据[id].角色[类型][数据.格子[1]]=nil
		local 储备=20000*数量
		玩家数据[id].角色:添加银子(储备,"上交暑假道具",1)
		self:索要道具更新(id,类型)
	elseif 事件=="更换指南造型" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称 ~= "制造指南书" or self.数据[道具id].特效 > 18 then
			常规提示(id,"#Y/只有武器类的指南书才可以更换")
			return
		end
		if 玩家数据[id].角色.银子 < self.数据[道具id].子类 *10000 then
			常规提示(id,"#Y/你的银两不足")
			return
		end
		if 玩家数据[id].角色:扣除银子(self.数据[道具id].子类 *10000,0,0,"更换指南造型",1) then
			self.数据[道具id].特效=取随机数(1,18)
			self:索要道具更新(id,类型)
		end
	elseif 事件=="指南2换1" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		local 道具id2=玩家数据[id].角色[类型][数据.格子[2]]
		if 数据.格子[1]==nil or 数据.格子[2]==nil then
			常规提示(id,"#Y/物品数据错误请重新放置")
			return
		end
		if self.数据[道具id].子类 ~= self.数据[道具id2].子类 then
			常规提示(id,"两本书的等级不一样无法更换")
			return
		end
		if self.数据[道具id].名称 ~= "制造指南书" or self.数据[道具id2].名称 ~= "制造指南书" then
			常规提示(id,"#Y/只有制造指南书才能换")
			return
		end
		self.数据[道具id].特效=取随机数(1,25)
		self.数据[道具id2]=nil
		玩家数据[id].角色[类型][数据.格子[2]]=nil
		self:索要道具更新(id,类型)
		常规提示(id,"#Y/您的指南更换成功请查看背包")
	elseif 事件=="裁缝/炼金熟练度" or 事件=="打造/淬灵熟练度" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		local 随机数 = math.random(0.8, 1)
		local 熟练度 = qz(self.数据[道具id].子类 / 10 * 随机数)
		local 级 = self.数据[道具id].子类	
		if self.数据[道具id].名称 ~= "制造指南书" then
		    if self.数据[道具id].名称 ~= "百炼精铁" then
		        if self.数据[道具id].名称 ~= "元灵晶石" then
		            if self.数据[道具id].名称 ~= "灵饰指南书" then
		                常规提示(id, "#Y/我不需要这个东西")
		                return
		            end
		        end
		    end
		end
		if 事件 == "裁缝/炼金熟练度" then
		    if self.数据[道具id].名称 == "制造指南书" then
		        玩家数据[id].角色:增加生活技能熟练("裁缝技巧", 熟练度)
		        消息提示(id, "你上交了#G/"..级.. "级制造指南书#增加了#S/" ..熟练度.."点#裁缝和炼金熟练度")
		    elseif self.数据[道具id].名称 == "元灵晶石" then
		        玩家数据[id].角色:增加生活技能熟练("裁缝技巧", 熟练度)
		        消息提示(id, "你上交了#G/"..级.. "级元灵晶石#增加了#S/" ..熟练度.."点#裁缝和炼金熟练度")
		    elseif self.数据[道具id].名称 == "灵饰指南书" then
		        玩家数据[id].角色:增加生活技能熟练("裁缝技巧", 熟练度)
		        消息提示(id, "你上交了#G/"..级.. "级灵饰指南书#增加了#S/" ..熟练度.."点#裁缝和炼金熟练度")
		    elseif self.数据[道具id].名称 == "百炼精铁" then
		        玩家数据[id].角色:增加生活技能熟练("裁缝技巧", 熟练度)
		        消息提示(id, "你上交了#G/"..级.. "级百炼精铁#增加了#S/" ..熟练度.."点#裁缝和炼金熟练度")	        
		    else
		        消息提示(id, "#Y该物品无法增加裁缝/炼金熟练度")
		    end
		else
		    if self.数据[道具id].名称 == "百炼精铁" then
		        玩家数据[id].角色:增加生活技能熟练("打造技巧", 熟练度)
		        消息提示(id, "你上交了#G/"..级.. "级百炼精铁#增加了#S/" ..熟练度.."点#打造和淬灵熟练度")	
		    elseif self.数据[道具id].名称 == "元灵晶石" then
		        玩家数据[id].角色:增加生活技能熟练("打造技巧", 熟练度)
		        消息提示(id, "你上交了#G/"..级.. "级元灵晶石#增加了#S/" ..熟练度.."点#打造和淬灵熟练度")
		    elseif self.数据[道具id].名称 == "灵饰指南书" then
		        玩家数据[id].角色:增加生活技能熟练("打造技巧", 熟练度)
		        消息提示(id, "你上交了#G/"..级.. "级灵饰指南书#增加了#S/" ..熟练度.."点#打造和淬灵熟练度")
		   	elseif self.数据[道具id].名称 == "制造指南书" then
		        玩家数据[id].角色:增加生活技能熟练("打造技巧", 熟练度)
		        消息提示(id, "你上交了#G/"..级.. "级制造指南书#增加了#S/" ..熟练度.."点#打造和淬灵熟练度")		        
		    else
		        消息提示(id, "#Y该物品无法增加打造熟练度")
		    end
		end

		-- if 事件=="裁缝/炼金熟练度"  then
		-- 	玩家数据[id].角色:增加生活技能熟练("裁缝技巧",self.数据[道具id].子类/10)
		-- 	消息提示(id,"#Y/你上交了#R/制造指南书#Y/熟练度增加了#R"..self.数据[道具id].子类/10)
		-- else
		-- 	玩家数据[id].角色:增加生活技能熟练("打造技巧",self.数据[道具id].子类/10)
		-- 	消息提示(id,"#Y/你上交了#R/百炼精铁#Y/熟练度增加了#R/"..self.数据[道具id].子类/10)
		-- end
		self.数据[道具id]=nil
		玩家数据[id].角色[类型][数据.格子[1]]=nil
		self:索要道具更新(id,类型)
	elseif 事件=="我来归还勾魂索。" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称 ~= "无常勾魂索" then
			常规提示(id,"#Y/你给我的是什么玩意儿？")
			return
		end
		self.数据[道具id]=nil
		玩家数据[id].角色[类型][数据.格子[1]]=nil
		玩家数据[id].已扣除勾魂索=nil
		玩家数据[id].勾魂索中=nil
		self:索要道具更新(id,类型)
		玩家数据[id].角色:添加银子(5000000,"归还勾魂索",1)
	elseif 事件=="官职任务上交物品" then
		local 任务id=玩家数据[id].角色:取任务(110)
		if 任务id==0 or 任务数据[任务id]==nil then
			常规提示(id,"#Y/你没有这样的任务")
			return
		end
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		local 名称="？？？"
		if 任务数据[任务id].分类==3 then
			名称="情报"
		elseif 任务数据[任务id].分类==4 then
			名称=任务数据[任务id].物品
		end
		if self.数据[道具id].名称~=名称 then
			常规提示(id,"#Y/对方需要的不是这种物品")
			return
		end
		if self.数据[道具id].数量 and self.数据[道具id].数量 >=2 then
			self.数据[道具id].数量 = self.数据[道具id].数量-1
		else
			self.数据[道具id]=nil
			玩家数据[id].角色[类型][数据.格子[1]]=nil
		end
		完成任务_110(id,任务数据[任务id].分类)
		self:索要道具更新(id,类型)
	elseif 事件=="门派任务上交物品" then
		local 任务id=玩家数据[id].角色:取任务(111)
		if 任务id==0 or 任务数据[任务id]==nil then
			常规提示(id,"#Y/你没有这样的任务")
			return
		end
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		local 名称=任务数据[任务id].物品
		if self.数据[道具id].名称~=名称 then
			常规提示(id,"#Y/对方需要的不是这种物品")
			return
		end
		local 双倍
		if 任务数据[任务id].品质~=nil and self.数据[道具id].阶品>=任务数据[任务id].品质 then
			双倍=1
		end
		if  self.数据[道具id].数量~=nil and self.数据[道具id].数量 >=2 then
			self.数据[道具id].数量 = self.数据[道具id].数量-1
		else
			self.数据[道具id]=nil
			玩家数据[id].角色[类型][数据.格子[1]]=nil
		end
		完成任务_111(id,4,双倍)
		self:索要道具更新(id,类型)
	elseif 事件=="门派任务上交乾坤袋" then
		local 任务id=玩家数据[id].角色:取任务(111)
		if 任务id==0 or 任务数据[任务id]==nil then
			常规提示(id,"#Y/你没有这样的任务")
			return
		end
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		local 名称="乾坤袋"
		if self.数据[道具id].名称~=名称 then
			常规提示(id,"#Y/对方需要的不是这种物品")
			return
		elseif 任务数据[任务id].乾坤袋==nil then
			常规提示(id,"#Y/你似乎还没有完成这个任务哟~")
			return
		end
		self.数据[道具id]=nil
		玩家数据[id].角色[类型][数据.格子[1]]=nil
		完成任务_111(id,7,双倍)
		self:索要道具更新(id,类型)
	elseif 事件=="低内丹二换一" then
		local dj={}
		local num=0
		for i=1,2 do
			if 数据.格子[i]~=nil then
				dj[i]=玩家数据[id].角色[类型][数据.格子[i]]
				if self.数据[dj[i]]==nil then
					添加最后对话(id,"#45#45#45")
					return
				end
				if self.数据[dj[i]].名称~="召唤兽内丹" then
					添加最后对话(id,"一次性给两颗你想兑换的#Y“召唤兽内丹”#W即可兑换一颗新的召唤兽内丹")
					return
				end
				num=num+1
			else
				添加最后对话(id,"一次性给两颗你想兑换的#Y“召唤兽内丹”#W即可兑换一颗新的召唤兽内丹")
				return
			end
		end
		if num==2 then
			for i=1,2 do
				self.数据[dj[i]]=nil
				玩家数据[id].角色[类型][数据.格子[i]]=nil
			end
			self:给予道具(id,"召唤兽内丹")
			添加最后对话(id,"嗯嗯~不错不错~待我施展法力…@………~&，变#113#Y1颗召唤兽内丹#W已经放进你的兜兜里啦~#109")
		end
		玩家数据[id].给予数据=nil
		self:索要道具更新(id,类型)
	elseif 事件=="高内丹三换一" then
		local dj={}
		local num=0
		for i=1,3 do
			if 数据.格子[i]~=nil then
				dj[i]=玩家数据[id].角色[类型][数据.格子[i]]
				if self.数据[dj[i]]==nil then
					添加最后对话(id,"#45#45#45")
					return
				end
				if self.数据[dj[i]].名称~="高级召唤兽内丹" then
					添加最后对话(id,"一次性给三颗你想兑换的#Y“高级召唤兽内丹”#W即可兑换一颗新的高级召唤兽内丹")
					return
				end
				num=num+1
			else
				添加最后对话(id,"一次性给三颗你想兑换的#Y“高级召唤兽内丹”#W即可兑换一颗新的高级召唤兽内丹")
				return
			end
		end
		if num==3 then
			for i=1,3 do
				self.数据[dj[i]]=nil
				玩家数据[id].角色[类型][数据.格子[i]]=nil
			end
			self:给予道具(id,"高级召唤兽内丹")
			添加最后对话(id,"嗯嗯~不错不错~待我施展法力…@………~&，变#113#Y1颗高级召唤兽内丹#W已经放进你的兜兜里啦~#109")
		end
		玩家数据[id].给予数据=nil
		self:索要道具更新(id,类型)
	elseif 事件=="高兽决三换一" then
		local dj={}
		local num=0
		for i=1,3 do
			if 数据.格子[i]~=nil then
				dj[i]=玩家数据[id].角色[类型][数据.格子[i]]
				if self.数据[dj[i]]==nil then
					添加最后对话(id,"#45#45#45")
					return
				end
				if self.数据[dj[i]].名称~="高级魔兽要诀" then
					添加最后对话(id,"一次性给三本你想兑换的#Y“高级魔兽要诀”#W即可兑换一本新的高级魔兽要诀")
					return
				end
				num=num+1
			else
				添加最后对话(id,"一次性给三本你想兑换的#Y“高级魔兽要诀”#W即可兑换一本新的高级魔兽要诀")
				return
			end
		end
		if num==3 then
			for i=1,3 do
				self.数据[dj[i]]=nil
				玩家数据[id].角色[类型][数据.格子[i]]=nil
			end
			self:给予道具(id,"高级魔兽要诀")
			添加最后对话(id,"嗯嗯~不错不错~待我施展法力…@………~&，变#113#Y1本高级魔兽要诀#W已经放进你的兜兜里啦~#109")
		end
		玩家数据[id].给予数据=nil
		self:索要道具更新(id,类型)
	elseif 事件=="我要还原装备拓印" then
		local 道具1=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具1]==nil then
			玩家数据[id].给予数据=nil
			return
		end
		if self.数据[道具1].总类~=2 or self.数据[道具1].分类 ~= 3  then
			常规提示(id,"#Y/选择武器有误！")
			return
		end
		if self.数据[道具1].光武拓印 == nil then
			常规提示(id,"#Y/这把武器没有光武拓印啊？")
			return
		end
		local 原造型=self.数据[道具1].光武拓印.原名称
		self.数据[道具1].名称 = 原造型
		self.数据[道具1].光武拓印=nil
		玩家数据[id].给予数据=nil
		添加最后对话(id,"一阵金光闪过，你手中的这把武器变成了#Y"..原造型.."#W的造型#80。")
		self:索要道具更新(id,类型)
	elseif 事件=="增加神兽技能格子" then
		local 道具1=玩家数据[id].角色[类型][数据.格子[1]]
		local 道具2=玩家数据[id].角色[类型][数据.格子[2]]
		if self.数据[道具1]==nil or self.数据[道具2]==nil then
			玩家数据[id].给予数据=nil
			添加最后对话(id,"请将“神兜兜”需要附加技能的“兽决”一起给我吧#24")
			return
		end
		local 名称1=self.数据[道具1].名称
		local 名称2=self.数据[道具2].名称
		local 兽决,兜兜
		local sjgz=1
		if 名称1=="魔兽要诀" or 名称1=="高级魔兽要诀" then
			兽决=道具1
		elseif 名称2=="魔兽要诀" or 名称2=="高级魔兽要诀" then
			兽决=道具2
			sjgz=2
		end
		if 名称1=="神兜兜" then
			兜兜=道具1
		elseif 名称2=="神兜兜" then
			兜兜=道具2
		end
		if 兽决~=nil and 兜兜~=nil and 兽决~=兜兜 then
			local jn=self.数据[兽决].附带技能
			if jn then
				local bh=玩家数据[id].角色:取参战宝宝编号()
				local xianzhi=12
				if #玩家数据[id].召唤兽.数据[bh].技能<xianzhi then
					if 玩家数据[id].召唤兽.数据[bh].打书次数==nil then
						玩家数据[id].召唤兽.数据[bh].打书次数=0
					end
					local num=(玩家数据[id].召唤兽.数据[bh].打书次数+1)*10
					if 玩家数据[id].召唤兽.数据[bh].种类~="神兽" then
						num=(玩家数据[id].召唤兽.数据[bh].打书次数+1)*20
					end
					if 玩家数据[id].道具:消耗背包道具(连接id,id,"神兜兜",num) then
						self.数据[兽决]=nil
						玩家数据[id].角色[类型][数据.格子[sjgz]]=nil
						玩家数据[id].召唤兽.数据[bh].技能[#玩家数据[id].召唤兽.数据[bh].技能+1]=jn
						玩家数据[id].召唤兽.数据[bh].打书次数=玩家数据[id].召唤兽.数据[bh].打书次数+1
						常规提示(id,"#G恭喜，您心爱的宝宝领悟了新技能！#Y"..jn.."#G。")
						道具刷新(id)
					else
						添加最后对话(id,"需要"..num.."个“神兜兜”才行哦。")
					end
				else
					添加最后对话(id,"只能对小于等于.."..xianzhi.."..技能的宝宝使用。")
				end
			end
		else
			添加最后对话(id,"请将“神兜兜”和需要附加技能的“兽决”一起给我吧#24")
		end
		玩家数据[id].给予数据=nil
		return
	elseif 事件=="替换神兽技能" then
		local 道具1=玩家数据[id].角色[类型][数据.格子[1]]
		local 道具2=玩家数据[id].角色[类型][数据.格子[2]]
		if self.数据[道具1]==nil or self.数据[道具2]==nil then
			玩家数据[id].给予数据=nil
			添加最后对话(id,"请将“神兜兜”需要附加技能的“兽决”一起给我吧#24")
			return
		end
		local 名称1=self.数据[道具1].名称
		local 名称2=self.数据[道具2].名称
		local 兽决,兜兜
		local sjgz=1
		if 名称1=="魔兽要诀" or 名称1=="高级魔兽要诀" then
			兽决=道具1
		elseif 名称2=="魔兽要诀" or 名称2=="高级魔兽要诀" then
			兽决=道具2
			sjgz=2
		end
		if 名称1=="神兜兜" then
			兜兜=道具1
		elseif 名称2=="神兜兜" then
			兜兜=道具2
		end
		if 兽决~=nil and 兜兜~=nil and 兽决~=兜兜 then
			local zd=玩家数据[id].给予数据.替换技能
			local jn=self.数据[兽决].附带技能
			if jn then
				local bh=玩家数据[id].角色:取参战宝宝编号()
				local jngz=0
				for i=1,#玩家数据[id].召唤兽.数据[bh].技能 do
					if 玩家数据[id].召唤兽.数据[bh].技能[i]==jn then
						常规提示(id,"这只宝宝已经学会了这个技能了。")
						return
					end
					if 玩家数据[id].召唤兽.数据[bh].技能[i]==zd then
						jngz=i
					end
				end
				if jngz~=0 then
					if 玩家数据[id].召唤兽.数据[bh].打书次数==nil then
						玩家数据[id].召唤兽.数据[bh].打书次数=0
					end
					local num=(玩家数据[id].召唤兽.数据[bh].打书次数+1)*10
					if 玩家数据[id].召唤兽.数据[bh].种类~="神兽" then
						num=(玩家数据[id].召唤兽.数据[bh].打书次数+1)*20
					end
					if 玩家数据[id].道具:消耗背包道具(连接id,id,"神兜兜",num) then
						self.数据[兽决]=nil
						玩家数据[id].角色[类型][数据.格子[sjgz]]=nil
						玩家数据[id].召唤兽.数据[bh].技能[jngz]=jn
						玩家数据[id].召唤兽.数据[bh].打书次数=玩家数据[id].召唤兽.数据[bh].打书次数+1
						常规提示(id,"#G恭喜，您心爱的宝宝学会了新技能！")
						道具刷新(id)
					else
						添加最后对话(id,"需要"..num.."个“神兜兜”才行哦。")
					end
				end
			end
		else
			添加最后对话(id,"请将“神兜兜”和需要附加技能的“兽决”一起给我吧#24")
		end
		玩家数据[id].给予数据=nil
		return
	elseif 事件=="偷偷怪上交物品" then
		local 道具id=玩家数据[id].角色.道具[数据.格子[1]]
		local 名称=self.数据[道具id].名称
		if 玩家数据[id].角色.五宝数据[名称]==nil then
			添加最后对话(id,"我要的是金刚石、龙鳞、定魂珠、避水珠、夜光珠，你给我的是个啥玩意？")
			return
		elseif 玩家数据[id].角色.五宝数据[名称]~=0 then
			添加最后对话(id,"您可真是贵人多忘事，您不是已经给了我"..名称.."吗？这么快就忘记了？是到了要喝脑白金的年纪吗？")
			return
		end
		玩家数据[id].角色.五宝数据[名称]=1
		self.数据[道具id]=nil
		玩家数据[id].角色.道具[数据.格子[1]]=nil
		local 满足=true
		for n=1,#五宝名称 do
			if 玩家数据[id].角色.五宝数据[五宝名称[n]]==0 then
				满足=false
			end
		end
		if 满足 then
			玩家数据[id].角色.五宝数据={夜光珠=0,龙鳞=0,定魂珠=0,避水珠=0,金刚石=0}
			self:给予道具(id,"特赦令牌")
			添加最后对话(id,"这块特赦令牌你可收好了，要是给了那些在地狱里无法进入轮回的鬼怪，说不定可以得到什么好东西呢！")
			常规提示(id,"#Y你获得了#R特赦令牌")
		end
		道具刷新(id)
	elseif 事件=="无名野鬼上交物品" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		local 名称="特赦令牌"
		if self.数据[道具id].名称~=名称 then
			添加最后对话(id,"我需要的是特赦令牌，你给我的这个能当饭吃吗？")
			return
		end
		self.数据[道具id]=nil
		玩家数据[id].角色[类型][数据.格子[1]]=nil
		self:给予道具(id,"高级藏宝图")
		添加最后对话(id,"您可真是大好人啊，这块特赦令牌终于让我能离开这地狱了。我这里有一张高级藏宝图你拿去吧，就当你做好事的回报。")
		self:索要道具更新(id,类型)
	elseif 事件=="我帮你带来了驱邪扇芝" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称~="驱邪扇芝" then
			添加最后对话(id,"少侠别拿我开玩笑了……")
			return
		end
		self.数据[道具id]=nil
		玩家数据[id].角色[类型][数据.格子[1]]=nil
		self:索要道具更新(id,类型)
		完成任务_19(id)
	elseif 事件=="文墨门派送信" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称~="密信" then
			添加最后对话(id,"这不是我要的东西#55")
			return
		else
			self.数据[道具id]=nil
			玩家数据[id].角色[类型][数据.格子[1]]=nil
			self:索要道具更新(id,类型)
			战斗准备类:创建战斗(id,130040,玩家数据[id].角色:取任务(1163))
		end
		return
	elseif 事件=="上交雪人道具" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		local 名称=self.数据[道具id].名称
		local 点数=1
		if 名称 == "小雪块" then
			点数=1
		elseif 名称 == "大雪球" then
			点数=2
		elseif 名称 == "雪人鼻子" then
			点数=3
		elseif 名称 == "雪人眼睛" then
			点数=4
		elseif 名称 == "雪人帽子" then
			点数=5
		end
		if 名称 ~= "雪人鼻子" and 名称 ~= "雪人帽子" and 名称 ~= "雪人眼睛" and 名称 ~= "大雪块"  and 名称 ~= "小雪块" then
			添加最后对话(id,"#Y/你交给我的是神马东西！")
			return
		elseif 雪人活动.当前 >= 雪人活动.进度 then
			添加最后对话(id,"#Y/本轮活动该道具已经满了,等待下一轮再前来吧！")
			return
		else
			local 应扣数量 = 雪人活动.进度- 雪人活动.当前
			if self.数据[道具id].数量 < 应扣数量 then
				应扣数量 = self.数据[道具id].数量
			end
			local 经验 =  qz((玩家数据[id].角色.等级*300*应扣数量))
			local 银子 =  qz((玩家数据[id].角色.等级*10*应扣数量))
			self.数据[道具id].数量 = self.数据[道具id].数量 - 应扣数量
			雪人活动.当前=雪人活动.当前+应扣数量*点数
			玩家数据[id].角色:添加银子(银子,"雪人",1)
			玩家数据[id].角色:添加经验(经验,"雪人",1)
			添加最后对话(id,"谢谢你的帮忙，雪人的完成进度增加了"..应扣数量*点数.."，这是给你的"..经验.."经验和"..银子.."金钱奖励")
			if self.数据[道具id].数量 <= 0 then
				self.数据[道具id] = nil
				物品格子 = nil
			end
			道具刷新(id)
			for i,v in pairs(雪人活动) do
				if i~="上限" then
					if v < 雪人活动.进度 then
						return
					end
				end
			end
			for i,v in pairs(玩家数据) do
				if 玩家数据[i] ~= nil and 玩家数据[i].管理 == nil then
					玩家数据[i].道具:发放雪人奖励(i)
				end
			end
			for i,v in pairs(雪人活动) do
				if i~="上限" then
					雪人活动[i]=0
				end
			end
		end
	elseif 事件=="给予银票" then
		local 道具id=玩家数据[id].角色.道具[数据.格子[1]]
		if self.数据[道具id].名称 ~= "帮派银票" then
			常规提示(id,"#Y/对方需要的不是这种物品")
			return
		end
		if self.数据[道具id].初始金额 < self.数据[道具id].完成金额 then
			添加最后对话(id,"#Y/还没赚够足够的银票，你这是糊弄我呢？")
			return
		end
		if os.time() - 玩家数据[id].角色.跑商时间 <= 15 or 玩家数据[id].角色.跑商时间==nil then
			__S服务:输出("玩家"..id.." 疑似重复刷跑商任务!")
			return
		elseif os.time() - 玩家数据[id].角色.跑商时间 <= 30 then
			添加最后对话(id,"#Y/我这里正在忙着整理商品，请你稍后再来！(30秒内仅可上交一次)")
			return
		end

		-- 引入跑商验证系统
		local 跑商验证系统 = require("Script/反作弊系统/跑商验证系统")

		-- 检查是否需要验证
		if 跑商验证系统:需要验证(id) then
			-- 如果玩家还没有通过验证，开始验证流程
			if not 玩家数据[id].验证通过 then
				local 验证id = 跑商验证系统:开始验证(id)
				if 验证id then
					添加最后对话(id,"#Y/系统检测到频繁跑商行为，需要进行安全验证后才能完成任务")
					return -- 暂停完成流程，等待验证
				end
			end
		end

		-- 清除验证标记
		玩家数据[id].验证通过 = nil

		-- 更新跑商统计
		跑商验证系统:更新跑商统计(id)

		local bh = 玩家数据[id].角色.BPBH
		if 帮派数据[bh].成员数据[id].权限==1 then
			玩家数据[id].角色:删除称谓(帮派数据[bh].帮派名称.."商人")
			玩家数据[id].角色:添加称谓(帮派数据[bh].帮派名称.."帮众")
			地图处理类:系统更新称谓(id,帮派数据[bh].帮派名称.."帮众")
		end
		if 玩家数据[id].角色:取任务(24)~=0 then
			local 任务id=玩家数据[id].角色:取任务(24)
			玩家数据[id].角色:取消任务(玩家数据[id].角色:取任务(24))
			任务数据[任务id]=nil
		end
		玩家数据[id].角色.道具[数据.格子[1]]=nil
		玩家数据[id].角色.跑商=nil
		玩家数据[id].角色.跑商时间=nil
		帮派数据[bh].成员数据[id].跑商 = 帮派数据[bh].成员数据[id].跑商 + 1
		if 玩家数据[id].角色.等级>=20 and 玩家数据[id].角色.等级<=39 then
			玩家数据[id].角色:添加经验(75000,"帮派跑商",1)
			玩家数据[id].角色:添加银子(50000,"帮派跑商",1)
			添加帮贡(id,12)
			帮派数据[bh].成员数据[id].帮贡.当前 = 帮派数据[bh].成员数据[id].帮贡.当前 + 12
			帮派数据[bh].成员数据[id].帮贡.上限 = 帮派数据[bh].成员数据[id].帮贡.上限 + 12
			帮派数据[bh].繁荣度 = 帮派数据[bh].繁荣度 + 2
		elseif 玩家数据[id].角色.等级>=40 and 玩家数据[id].角色.等级<=59 then
			玩家数据[id].角色:添加经验(112500,"帮派跑商",1)
			玩家数据[id].角色:添加银子(100000,"帮派跑商",1)
			添加帮贡(id,24)
			帮派数据[bh].成员数据[id].帮贡.当前 = 帮派数据[bh].成员数据[id].帮贡.当前 + 24
			帮派数据[bh].成员数据[id].帮贡.上限 = 帮派数据[bh].成员数据[id].帮贡.上限 + 24
			帮派数据[bh].繁荣度 = 帮派数据[bh].繁荣度 + 3
		elseif 玩家数据[id].角色.等级>=60 and 玩家数据[id].角色.等级<=79 then
			玩家数据[id].角色:添加经验(150000,"帮派跑商",1)
			玩家数据[id].角色:添加银子(150000,"帮派跑商",1)
			添加帮贡(id,30)
			帮派数据[bh].成员数据[id].帮贡.当前 = 帮派数据[bh].成员数据[id].帮贡.当前 + 30
			帮派数据[bh].成员数据[id].帮贡.上限 = 帮派数据[bh].成员数据[id].帮贡.上限 + 30
			帮派数据[bh].繁荣度 = 帮派数据[bh].繁荣度 + 4
		elseif 玩家数据[id].角色.等级>=80 and 玩家数据[id].角色.等级<=99 then
			玩家数据[id].角色:添加经验(212500,"帮派跑商",1)
			玩家数据[id].角色:添加银子(180000,"帮派跑商",1)
			添加帮贡(id,48)
			帮派数据[bh].成员数据[id].帮贡.当前 = 帮派数据[bh].成员数据[id].帮贡.当前 + 48
			帮派数据[bh].成员数据[id].帮贡.上限 = 帮派数据[bh].成员数据[id].帮贡.上限 + 48
			帮派数据[bh].繁荣度 = 帮派数据[bh].繁荣度 + 5
		elseif 玩家数据[id].角色.等级>=100 then
			玩家数据[id].角色:添加经验(287500,"帮派跑商",1)
			玩家数据[id].角色:添加银子(180000,"帮派跑商",1)
			添加帮贡(id,48)
			帮派数据[bh].成员数据[id].帮贡.当前 = 帮派数据[bh].成员数据[id].帮贡.当前 + 48
			帮派数据[bh].成员数据[id].帮贡.上限 = 帮派数据[bh].成员数据[id].帮贡.上限 + 48
			帮派数据[bh].繁荣度 = 帮派数据[bh].繁荣度 + 6
		end
		帮派数据[bh].帮派资金.当前 = 帮派数据[bh].帮派资金.当前 + self.数据[道具id].初始金额
		if 帮派数据[bh].帮派资金.当前 > 帮派数据[bh].帮派资金.上限 then
			帮派数据[bh].帮派资金.当前 = 帮派数据[bh].帮派资金.上限
		end
		广播帮派消息({内容="[帮派总管]#R/"..玩家数据[id].角色.名称.."#Y/完成跑商任务，帮派资金增加了#R/"..self.数据[道具id].初始金额.."#Y/两。",频道="bp"},玩家数据[id].角色.BPBH)

		-- 抽取跑商奖励
		local 抽取结果 = 商店处理类:抽取帮派跑商奖励(bh)
		if 抽取结果 then
			-- 给予抽取到的物品
			self:给予道具(id, 抽取结果.名称, 抽取结果.参数, 抽取结果.品质, nil, "跑商奖励")
			常规提示(id,"#Y/感谢少侠为帮派的发展做出的贡献！你还获得了跑商奖励：#G/"..抽取结果.名称)
		else
			常规提示(id,"#Y/感谢少侠为帮派的发展做出的贡献！")
		end

		道具刷新(id)
	elseif 事件=="设置跑商奖励" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if not 道具id or not self.数据[道具id] then
			常规提示(id,"#Y/请选择要设置为奖励的物品")
			return
		end

		local bh = 玩家数据[id].角色.BPBH
		if not 帮派数据[bh] then
			常规提示(id,"#Y/你没有帮派")
			return
		end

		-- 再次检查权限
		if 帮派数据[bh].成员数据[id].职务 ~= "帮主" and 帮派数据[bh].成员数据[id].职务 ~= "副帮主" then
			常规提示(id,"#Y/只有帮主和副帮主才能设置跑商奖励物品！")
			return
		end

		-- 检查物品是否可以设置为奖励
		local 物品 = self.数据[道具id]
		if 物品.加锁 then
			常规提示(id,"#Y/已加锁的物品无法设置为跑商奖励")
			return
		elseif 物品.专用 then
			常规提示(id,"#Y/专用物品无法设置为跑商奖励")
			return
		elseif 物品.不可交易 then
			常规提示(id,"#Y/不可交易的物品无法设置为跑商奖励")
			return
		elseif 物品.绑定 then
			常规提示(id,"#Y/绑定物品无法设置为跑商奖励")
			return
		elseif 物品.总类 == "跑商商品" then
			常规提示(id,"#Y/跑商商品无法设置为跑商奖励")
			return
		elseif 物品.总类 == "帮派银票" then
			常规提示(id,"#Y/帮派银票无法设置为跑商奖励")
			return
		elseif 物品.名称 == "心魔宝珠" or 物品.名称 == "镇妖拘魂铃" or 物品.名称 == "新手大礼包" or 物品.名称 == "秘宝宝箱" or 物品.名称 == "机缘宝箱" then
			常规提示(id,"#Y/特殊任务道具无法设置为跑商奖励")
			return
		elseif 物品.名称 == "净瓶玉露" or 物品.名称 == "超级净瓶玉露" or 物品.名称 == "特殊清灵仙露" or 物品.名称 == "灵兜兜" or 物品.名称 == "神兜兜" then
			常规提示(id,"#Y/珍贵道具无法设置为跑商奖励")
			return
		elseif 物品.名称 == "炫彩ID" or 物品.名称 == "新春飞行符" or 物品.名称 == "愤怒符" then
			常规提示(id,"#Y/特效道具无法设置为跑商奖励")
			return
		elseif string.find(物品.名称, "商品") == 1 then  -- 以"商品"开头的物品
			常规提示(id,"#Y/跑商商品无法设置为跑商奖励")
			return
		elseif 物品.名称 == "怪物卡片" then  -- 怪物卡片
			常规提示(id,"#Y/怪物卡片无法设置为跑商奖励")
			return
		elseif 物品.总类 == 11 then  -- 功能性物品（导标旗等）
			常规提示(id,"#Y/功能性物品无法设置为跑商奖励")
			return
		elseif 物品.总类 == 183 or 物品.总类 == 1000 or 物品.总类 == 1002 then  -- 特殊总类
			常规提示(id,"#Y/特殊类型物品无法设置为跑商奖励")
			return
		end

		local 物品名称 = self.数据[道具id].名称
		local 物品数量 = self.数据[道具id].数量 or 1
		local 物品品质 = self.数据[道具id].阶品 or 0
		local 物品参数 = self.数据[道具id].级别限制 or self.数据[道具id].等级 or 0

		-- 添加到帮派跑商奖励池
		if 商店处理类:添加帮派跑商奖励(bh, 物品名称, 物品数量, 物品品质, 物品参数) then
			-- 删除玩家的物品
			self.数据[道具id] = nil
			玩家数据[id].角色[类型][数据.格子[1]] = nil

			常规提示(id, "#Y/成功将#G/"..物品名称.."#Y/设置为跑商奖励物品！")
			广播帮派消息({内容="[帮派总管]#R/"..玩家数据[id].角色.名称.."#Y/设置了新的跑商奖励物品：#G/"..物品名称.."#Y/，数量：#G/"..物品数量,频道="bp"}, bh)

			self:索要道具更新(id,类型)
			道具刷新(id)
		else
			常规提示(id,"#Y/设置跑商奖励失败，请稍后再试")
		end
	elseif 事件=="上交锦盒" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		local 名称="金银锦盒"
		if self.数据[道具id].名称~=名称 then
			添加最后对话(id,"我需要的是金银锦盒，你给我的这个能当饭吃吗？")
			return
		end
		if 帮派数据[玩家数据[id].角色.BPBH] == nil then
			添加最后对话(id,"你没有帮派无法上交金银锦盒")
			return
		end
		local 数量 = qz(self.数据[道具id].数量 or 1)
		self.数据[道具id]=nil
		玩家数据[id].角色[类型][数据.格子[1]]=nil
		local 增加帮贡=数量*5
		local 增加帮派资金=数量*50000
		帮派数据[玩家数据[id].角色.BPBH].帮派资金.当前 = 帮派数据[玩家数据[id].角色.BPBH].帮派资金.当前+增加帮派资金
		帮派处理:增加当前内政(玩家数据[id].角色.BPBH,数量*2)
		if 帮派数据[玩家数据[id].角色.BPBH].帮派资金.当前> 50000000 then
			帮派数据[玩家数据[id].角色.BPBH].帮派资金.当前=50000000
		end
		添加帮贡(id,增加帮贡,"上限")
		广播帮派消息({内容="[白虎堂总管]#G/"..玩家数据[id].角色.名称.."#Y/上交金银锦盒为帮派增加了帮派资金#R/"..增加帮派资金.."#Y/两银子。#93",频道="bp"},玩家数据[id].角色.BPBH)
		self:索要道具更新(id,类型)
	elseif 事件=="合成旗4" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].总类~=11 or  self.数据[道具id].分类~=2 then
			常规提示(id,"#Y我只可以为合成旗补充次数哟。")
			return
		elseif self.数据[道具id].次数>=140 then
			常规提示(id,"#Y你的这个合成旗次数已经满了")
			return
		end
		local 编号=玩家数据[id].法宝id
		if 玩家数据[id].角色.法宝[编号]==nil or self.数据[玩家数据[id].角色.法宝[编号]]==nil or self.数据[玩家数据[id].角色.法宝[编号]].名称~="五色旗盒" then
			常规提示(id,"#Y你没有这样的法宝")
			return
		end
		local 灵气=140-self.数据[道具id].次数
		灵气=math.floor(灵气/5)
		if 灵气<1 then 灵气=1 end
		if 灵气>self.数据[玩家数据[id].角色.法宝[编号]].魔法 then
			常规提示(id,"#Y本次补充需要消耗#R"..灵气.."#Y点法宝灵气，你的法宝没有那么多的灵气")
			return
		end
		self.数据[玩家数据[id].角色.法宝[编号]].魔法=self.数据[玩家数据[id].角色.法宝[编号]].魔法-灵气
		self.数据[道具id].次数=140
		发送数据(连接id,38,{内容="你的法宝#R/五色旗盒#W/灵气减少了"..灵气.."点"})
		常规提示(id,"#Y补充成功！你的这个合成旗可使用次数已经恢复到140次了")
		self:索要道具更新(id,类型)
	elseif 事件=="合成旗" then
		local 道具id={}
		for n=1,#数据.格子 do
			if 数据.格子[n]~=nil  then
				道具id[n]=数据.格子[n]
				local 临时id=玩家数据[id].角色[类型][数据.格子[n]]
				if self.数据[临时id].总类~=11 and self.数据[临时id].分类~=1 then
					常规提示(id,"#Y只有导标旗才可以合成哟")
					return
				end
			end
		end
		if 玩家数据[id].合成旗序列==nil then
			玩家数据[id].合成旗序列={}
			for n=1,#道具id do
				local 临时id=玩家数据[id].角色[类型][道具id[n]]
				for i=1,#道具id do
					local 临时id1=玩家数据[id].角色[类型][道具id[i]]
					if i~=n and 临时id1==临时id then
						常规提示(id,"#Y合成的导标旗中存在重复导标旗")
						return
					elseif i~=n and self.数据[临时id].地图~=self.数据[临时id1].地图 then
						常规提示(id,"#Y合成的导标旗定标场景必须为同一个")
						return
					end
				end
			end
		else
			for n=1,#道具id do
				local 临时id=玩家数据[id].角色[类型][道具id[n]]
				if 玩家数据[id].合成旗序列.地图~=nil and 玩家数据[id].合成旗序列.地图~=self.数据[临时id].地图 then
					常规提示(id,"#Y只有#R"..取地图名称(玩家数据[id].合成旗序列.地图).."#Y的导标旗才可合成")
					return
				end
				for i=1,#道具id do
					local 临时id1=玩家数据[id].角色[类型][道具id[i]]
					if i~=n and 临时id1==临时id then
						常规提示(id,"#Y合成的导标旗中存在重复导标旗")
						return
					end
				end
				for i=1,#玩家数据[id].合成旗序列 do
					local 临时id1=玩家数据[id].角色[类型][玩家数据[id].合成旗序列[i]]
					if  临时id1==临时id then
						常规提示(id,"#Y合成的导标旗中存在重复导标旗")
						return
					end
				end
			end
		end
		local 编号=玩家数据[id].法宝id
		if 玩家数据[id].角色.法宝[编号]==nil or self.数据[玩家数据[id].角色.法宝[编号]]==nil or self.数据[玩家数据[id].角色.法宝[编号]].名称~="五色旗盒" then
			常规提示(id,"#Y你没有这样的法宝")
			return
		end
		local 上限=7
		if self.数据[玩家数据[id].角色.法宝[编号]].气血<=0 then
			上限=3
		elseif self.数据[玩家数据[id].角色.法宝[编号]].气血<=2 then
			上限=4
		elseif self.数据[玩家数据[id].角色.法宝[编号]].气血<=5 then
			上限=5
		elseif self.数据[玩家数据[id].角色.法宝[编号]].气血<=8 then
			上限=6
		elseif self.数据[玩家数据[id].角色.法宝[编号]].气血<=9 then
			上限=7
		end
		for n=1,#道具id do
			if #玩家数据[id].合成旗序列<上限 then
				if 玩家数据[id].合成旗序列.地图==nil then
					玩家数据[id].合成旗序列.地图=self.数据[玩家数据[id].角色[类型][道具id[n]]].地图
				end
				玩家数据[id].合成旗序列[#玩家数据[id].合成旗序列+1]=道具id[n]
			end
		end
		if #玩家数据[id].合成旗序列==上限 then
			local aa ="请选择超级合成旗的颜色："
			local xx={"绿色合成旗","蓝色合成旗","红色合成旗","白色合成旗","黄色合成旗",}
			发送数据(连接id,1501,{名称="五色旗盒",对话=aa,选项=xx})
			玩家数据[id].最后操作="合成旗3"
		else
			玩家数据[id].给予数据={类型=1,id=0,事件="合成旗"}
			发送数据(连接id,3530,{道具=玩家数据[id].道具:索要道具1(id),名称="五色旗盒",类型="法宝",等级="无"})
			玩家数据[id].最后操作="合成旗2"
			常规提示(id,format("#Y你已提交%s个导标旗，还需要提交%s个导标旗",#玩家数据[id].合成旗序列,上限-#玩家数据[id].合成旗序列))
		end
	elseif 事件=="装备出售" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].总类~=2 or self.数据[道具id].灵饰 or self.数据[道具id].分类==9 then
			添加最后对话(id,"我这里只收购人物装备哟，其它的破铜烂铁我可是不收滴哟。")
			return
		end
		玩家数据[id].出售装备=数据.格子[1]
		玩家数据[id].最后操作="出售装备"
		添加最后对话(id,format("你的这件#Y%s#W我将以#R%s#W两银子进行收购，请确认是否出售该装备？",self.数据[道具id].名称,self:取装备价格(道具id)),{"确认出售","我不卖了"})
	elseif 事件=="转换召唤兽饰品" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id] == nil then
			添加最后对话(id,"#24")
			return
		end
		if self.数据[道具id].总类==149 then
			if string.find(self.数据[道具id].名称,"进阶")~=nil then
				local mc=string.gsub(self.数据[道具id].名称,"进阶","")
				if 取饰品表1(mc) then
					self.数据[道具id].名称=mc
					发送数据(连接id,3699)
					常规提示(id,"转换成功！")
				else
					添加最后对话(id,"#17少侠，你给我的似乎不是召唤兽饰品哦？（或对应进阶召唤兽还未开放饰品）")
				end
			else
				local mc="进阶"..self.数据[道具id].名称
				if 取饰品表1(mc) then
					self.数据[道具id].名称=mc
					发送数据(连接id,3699)
					常规提示(id,"转换成功！")
				else
					添加最后对话(id,"#17少侠，你给我的似乎不是召唤兽饰品哦？（或对应进阶召唤兽还未开放饰品）")
				end
			end
		end
	elseif 事件=="法宝补充灵气" then
		local 道具id=玩家数据[id].角色.法宝[数据.格子[1]]
		if self.数据[道具id] == nil then
			添加最后对话(id,"只有法宝才可以补充灵气哟，你这个是什么玩意？")
			return
		end
		if self.数据[道具id].总类~=1000 then
			添加最后对话(id,"只有法宝才可以补充灵气哟，你这个是什么玩意？")
			return
		end
		local 价格=2000000
		if self.数据[道具id].分类==2 then
			价格=3500000
		elseif self.数据[道具id].分类==3 then
			价格=6000000
		end
		if 玩家数据[id].角色.银子<价格 then
			添加最后对话(id,"本次补充法宝灵气需要消耗"..价格.."两银子，你身上没有那么多的现金哟。")
			return
		end
		玩家数据[id].角色:扣除银子(价格,0,0,"补充法宝扣除，法宝名称为"..self.数据[道具id].名称,1)
		self.数据[道具id].魔法=取灵气上限(self.数据[道具id].分类)
		添加最后对话(id,"补充法宝灵气成功！")
	elseif 事件 == "青龙任务" then
		local 任务id=玩家数据[id].角色:取任务(301)
		if 任务id==0 then
			添加最后对话(id,"你没有这个任务！")
			return
		end
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称~=任务数据[任务id].物品  then
			添加最后对话(id,"我拿这个玩意用来干啥？")
			return
		end
		if self.数据[道具id].数量~=nil and self.数据[道具id].数量>0 then
			self.数据[道具id].数量=self.数据[道具id].数量-1
			if self.数据[道具id].数量<=0 then
				self.数据[道具id]=nil
				玩家数据[id].角色[类型][数据.格子[1]]=nil
			end
		else
			self.数据[道具id]=nil
			玩家数据[id].角色[类型][数据.格子[1]]=nil
		end
		self:索要道具更新(id,类型)
		任务处理类:完成青龙任务(任务id,id,任务数据[任务id].分类)
	elseif 事件 == "玄武任务" then
		local 任务id=玩家数据[id].角色:取任务(302)
		if 任务id==0 then
			添加最后对话(id,"你没有这个任务！")
			return
		end
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称~=任务数据[任务id].物品  then
			添加最后对话(id,"我拿这个玩意用来干啥？")
			return
		end
		if self.数据[道具id].数量~=nil and self.数据[道具id].数量>0 then
			self.数据[道具id].数量=self.数据[道具id].数量-1
			if self.数据[道具id].数量<=0 then
				self.数据[道具id]=nil
				玩家数据[id].角色[类型][数据.格子[1]]=nil
			end
		else
			self.数据[道具id]=nil
			玩家数据[id].角色[类型][数据.格子[1]]=nil
		end
		self:索要道具更新(id,类型)
		任务处理类:完成玄武任务(id,任务数据[任务id].分类)
	elseif 事件=="上交任务物品" then
		local 任务id=玩家数据[id].角色:取任务(任务编号)
		if 任务id==0 then
			添加最后对话(id,"你没有这个任务！")
			return
		end
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].名称~=任务数据[任务id].物品  then
			添加最后对话(id,"我拿这个玩意用来干啥？")
			return
		end
		if self.数据[道具id].数量~=nil and self.数据[道具id].数量>0 then
			self.数据[道具id].数量=self.数据[道具id].数量-1
			if self.数据[道具id].数量<=0 then
				self.数据[道具id]=nil
				玩家数据[id].角色[类型][数据.格子[1]]=nil
			end
		else
			self.数据[道具id]=nil
			玩家数据[id].角色[类型][数据.格子[1]]=nil
		end
		self:索要道具更新(id,类型)
		local fun = _G["完成任务_"..tostring(任务编号)]
		if fun ~= nil  then
			return fun(id,任务id)
		else
			return
		end
	elseif 事件=="上交副本物品" then
		local 副本id = 任务处理类:取副本id(数字id,数据.副本编号)
		local 需求编号 = 0
		for i=1,#副本数据.车迟斗法.进行[副本id].任务需求 do
			if self.数据[道具id].名称 == 副本数据.车迟斗法.进行[副本id].任务需求[i].物品 then
				需求编号 = i
			end
		end
		if 需求编号 ~= 0 then
			self.数据[道具id]=nil
			玩家数据[id].角色[类型][数据.格子[1]]=nil
			副本数据.车迟斗法.进行[数字id][副本数据.车迟斗法.进行[副本id].任务需求[需求编号].回执]=副本数据.车迟斗法.进行[数字id][副本数据.车迟斗法.进行[副本id].任务需求[需求编号].回执]+副本数据.车迟斗法.进行[副本id].任务需求[需求编号].奖励
		else
			添加最后对话(id,"我拿这个玩意用来干啥？")
		end
		self:索要道具更新(id,类型)
		local fun = _G[""..数据.脚本]
		if fun ~= nil  then
			return fun(id)
		else
			return
		end
	elseif 事件=="法宝锻造" then
		local 内丹,材料1,材料2,法宝,天珠
		if 数据.格子[1] ~= nil then
			local 道具id = 玩家数据[id].角色[类型][数据.格子[1]]
			if  self.数据[道具id].名称 == "内丹" then
				内丹=self.数据[道具id].名称..self.数据[道具id].五行
			end
		end
		if 数据.格子[2] ~= nil then
			local 道具id = 玩家数据[id].角色[类型][数据.格子[2]]
			if self.数据[道具id].总类 == 1003 and self.数据[道具id].分类 ~= 5 then
				材料1=self.数据[道具id].名称..self.数据[道具id].五行
			end
		end
		if 数据.格子[3] ~= nil then
			local 道具id = 玩家数据[id].角色[类型][数据.格子[3]]
			if self.数据[道具id].总类 == 1003 and self.数据[道具id].分类 ~= 5 then
				材料2=self.数据[道具id].名称..self.数据[道具id].五行
			end
		end
		if 数据.格子[4] ~= nil then
			local 道具id = 玩家数据[id].角色[类型][数据.格子[4]]
			if self.数据[道具id].总类 == 1000 then
				法宝=self.数据[道具id].名称..self.数据[道具id].五行
			end
		end
		if 数据.格子[5] ~= nil then
			local 道具id = 玩家数据[id].角色[类型][数据.格子[5]]
			天珠=self.数据[道具id].名称..self.数据[道具id].五行
		end
		local lsb = 法宝公式(内丹,材料1,材料2,法宝,天珠)
		if #lsb == 0 then
			常规提示(id,"#Y/合成失败无法合成法宝")
			return
		elseif #lsb > 1 then
			if lsb[时辰信息.当前] ~= nil then
				self.数据[玩家数据[id].角色[类型][数据.格子[1]]] = nil
				玩家数据[id].角色[类型][数据.格子[1]] = nil
				常规提示(id,"#Y/合成失败无法损失了内丹")
				return
			else
				self:给予法宝(id,lsb[时辰信息.当前])
				self.数据[玩家数据[id].角色[类型][数据.格子[1]]] = nil
				玩家数据[id].角色[类型][数据.格子[1]] = nil
				self.数据[玩家数据[id].角色[类型][数据.格子[2]]] = nil
				玩家数据[id].角色[类型][数据.格子[2]] = nil
				self.数据[玩家数据[id].角色[类型][数据.格子[3]]] = nil
				玩家数据[id].角色[类型][数据.格子[3]] = nil
				if 数据.格子[4] ~= nil then
					self.数据[玩家数据[id].角色[类型][数据.格子[4]]] = nil
					玩家数据[id].角色[类型][数据.格子[4]] = nil
				end
				if 数据.格子[5] ~= nil then
					self.数据[玩家数据[id].角色[类型][数据.格子[5]]] = nil
					玩家数据[id].角色[类型][数据.格子[5]] = nil
				end
				self:索要道具更新(id,类型)
			end
		else
			self:给予法宝(id,lsb[1])
			self.数据[玩家数据[id].角色[类型][数据.格子[1]]] = nil
			玩家数据[id].角色[类型][数据.格子[1]] = nil
			self.数据[玩家数据[id].角色[类型][数据.格子[2]]] = nil
			玩家数据[id].角色[类型][数据.格子[2]] = nil
			self.数据[玩家数据[id].角色[类型][数据.格子[3]]] = nil
			玩家数据[id].角色[类型][数据.格子[3]] = nil
			if 数据.格子[4] ~= nil then
				self.数据[玩家数据[id].角色[类型][数据.格子[4]]] = nil
				玩家数据[id].角色[类型][数据.格子[4]] = nil
			end
			if 数据.格子[5] ~= nil then
				self.数据[玩家数据[id].角色[类型][数据.格子[5]]] = nil
				玩家数据[id].角色[类型][数据.格子[5]] = nil
			end
			self:索要道具更新(id,类型)
		end
	elseif 事件=="更改法宝五行" then
		local 道具id=玩家数据[id].角色.法宝[数据.格子[1]]
		if self.数据[道具id] == nil then
			添加最后对话(id,"只有法宝才可以更换五行哟，你这个是什么玩意？")
			return
		end
		if self.数据[道具id].总类~=1000 then
			添加最后对话(id,"只有法宝才可以更换五行哟，你这个是什么玩意？")
			return
		end
		local 价格=300000
		if 玩家数据[id].角色.银子<价格 then
			添加最后对话(id,"本次更换五行需要消耗"..价格.."两银子，你身上没有那么多的现金哟。")
			return
		end
		玩家数据[id].角色:扣除银子(价格,0,0,"法宝更换五行，法宝名称为"..self.数据[道具id].名称,1)
		self.数据[道具id].五行=取五行()
		添加最后对话(id,self.数据[道具id].名称.."五行更改为#Y/"..self.数据[道具id].五行)
	elseif 事件=="点化装备" then
		local 道具id=玩家数据[id].角色.道具[数据.格子[1]]
		if self.数据[道具id].总类~=2 or self.数据[道具id].灵饰  then
			添加最后对话(id,"我这里目前只能点化人物装备，其它的我可没那么大的能耐。")
			return
		elseif self.数据[道具id].总类 == 2 and self.数据[道具id].分类==3 then
			添加最后对话(id,"武器不能点化。")
			return
		end
		local 银子=self.数据[道具id].级别限制*5000
		if self.数据[道具id].分类==7 or self.数据[道具id].分类==8 or self.数据[道具id].分类==9 then
			银子=self.数据[道具id].级别限制*20000
		end
		if 玩家数据[id].角色.银子<银子 then
			添加最后对话(id,format("本次点化需要消耗#Y%s#W两银子，你似乎手头有点紧哟？",银子))
			return
		end
		玩家数据[id].角色:扣除银子(银子,0,0,"点化装备",1)
		local 套装类型={"附加状态","追加法术"}
		套装类型=套装类型[取随机数(1,#套装类型)]
		local 套装效果={
		附加状态={"逆鳞","金刚护法","金刚护体","生命之泉","炼气化神","普渡众生","定心术","碎星诀","变身"},
		追加法术={"横扫千军","满天花雨","浪涌","唧唧歪歪","五雷咒","龙卷雨击"}
		}
		local 召唤兽套装类型={"追加法术","附加技能"}
		召唤兽套装类型=召唤兽套装类型[取随机数(1,#召唤兽套装类型)]
		if self.数据[道具id].分类==7 or self.数据[道具id].分类==8 or self.数据[道具id].分类==9 then
			套装效果.附加技能={"吸血","夜战","连击","必杀","偷袭","魔之心","法术暴击","法术连击","嗜血追击"}
			套装效果.追加法术={"上古灵符","水攻","火攻","落岩","雷击","水漫金山","泰山压顶","奔雷咒","地狱烈火","死亡召唤"}
			local 随机值 = 取随机数(1, 1000)
	        if 随机值 <= 2 then
	            self.数据[道具id].套装效果 = {"附加技能", "嗜血追击"}
	        else
	            self.数据[道具id].套装效果 = {召唤兽套装类型, 套装效果[召唤兽套装类型][取随机数(1, #套装效果[召唤兽套装类型])]}
	        end
		else
			self.数据[道具id].套装效果={套装类型,套装效果[套装类型][取随机数(1,#套装效果[套装类型])]}
		end
		-- 装备点化成功后，取消任务1600
		local 点化任务id = 玩家数据[id].角色:取任务(1600)
		if 点化任务id ~= 0 then
			玩家数据[id].角色:取消任务(点化任务id)
			任务数据[点化任务id] = nil
		end		
		添加最后对话(id,format("点化装备成功,您本次点化后的套装效果为#Y%s：%s",self.数据[道具id].套装效果[1],self.数据[道具id].套装效果[2]),{"继续点化","告辞"})
	elseif 事件=="更改法宝材料五行" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].总类~=1003 then
			添加最后对话(id,"我这里目前只能更换法宝材料五行，其它的我可没那么大的能耐。")
			return
		end
		local 银子=30000
		if 玩家数据[id].角色.银子<银子 then
			添加最后对话(id,format("本次更换五行需要消耗#Y%s#W两银子，你似乎手头有点紧哟？",银子))
			return
		end
		玩家数据[id].角色:扣除银子(银子,0,0,"点化装备",1)
		self.数据[道具id].五行=取五行()
	elseif 事件=="更改装备五行" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id].总类~=2 or self.数据[道具id].灵饰 then
			添加最后对话(id,"我这里目前只能更换装备五行，其它的我可没那么大的能耐。")
			return
		end
		local 银子=300000
		if 玩家数据[id].角色.银子<银子 then
			添加最后对话(id,format("本次更换五行需要消耗#Y%s#W两银子，你似乎手头有点紧哟？",银子))
			return
		end
		玩家数据[id].角色:扣除银子(银子,0,0,"点化装备",1)
		self.数据[道具id].五行=取五行()
		添加最后对话(id,self.数据[道具id].名称.."五行更改为#Y/"..self.数据[道具id].五行)
	elseif 事件=="炼制灵犀之屑" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		if self.数据[道具id] == nil then
			添加最后对话(id,"炼制灵犀之屑需要，150级-160级的“人物装备”，#W即可兑换到对应数量的灵犀之屑")
			return
		elseif self.数据[道具id].总类~=2 or self.数据[道具id].灵饰 or self.数据[道具id].召唤兽装备 then
			添加最后对话(id,"我这里只接受人物装备兑换！")
			return
		elseif self.数据[道具id].级别限制<150 then
			添加最后对话(id,"装备"..self.数据[道具id].名称.."等级过低！")
			return
		end
		if self.数据[道具id].级别限制==150 then
			玩家数据[id].道具:给予道具(id,"灵犀之屑",20)
		elseif self.数据[道具id].级别限制==160 then
			玩家数据[id].道具:给予道具(id,"灵犀之屑",30)
		end
		玩家数据[id].角色[类型][数据.格子[1]]=nil
		self.数据[道具id]=nil
		self:索要道具更新(id,类型)
	elseif 事件=="上交烹饪换取材料" then
		local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
		local lsb = {女儿红=10,臭豆腐=20,烤肉=20,豆斋果=60,翡翠豆腐=40,佛跳墙=20,桂花丸=50,长寿面=80,珍露酒=10,蛇胆酒=70,梅花酒=40,百味酒=40,醉生梦死=80}
		if 数据.格子[1]==nil then
			添加最后对话(id,format("你什么都没有还不快滚",fhz2,玩家数据[id].角色.秘制食谱.材料),{"继续上交烹饪换取材料","告辞"})
			return
		end
		if lsb[self.数据[道具id].名称] == nil or self.数据[道具id].阶品 == nil then
			添加最后对话(id,format("这个物品无法无法兑换成材料",fhz2,玩家数据[id].角色.秘制食谱.材料),{"继续上交烹饪换取材料","告辞"})
			return
		end
		local fhz = qz(self.数据[道具id].阶品/20)
		local fhz2
		if fhz >= 4 then
			fhz2 = lsb[self.数据[道具id].名称]/10*(fhz-4)+lsb[self.数据[道具id].名称]
		else
			fhz2 = lsb[self.数据[道具id].名称]-(lsb[self.数据[道具id].名称]/10*(fhz+2-4))
		end
		玩家数据[id].角色[类型][数据.格子[1]]=nil
		self.数据[道具id]=nil
		self:索要道具更新(id,类型)
		玩家数据[id].角色.秘制食谱.材料 = 玩家数据[id].角色.秘制食谱.材料 + fhz2
		添加最后对话(id,format("上交成功,您本次上交兑换了#Y%s#W现有材料#Y%s",fhz2,玩家数据[id].角色.秘制食谱.材料),{"继续上交烹饪换取材料","告辞"})
	elseif 事件=="坐骑任务给予烹饪" then
		local 任务id=玩家数据[id].角色:取任务(307)
		if 任务id==0 or 任务数据[任务id]==nil then
			常规提示(id,"#Y/你没有这样的任务")
			return
		end
		local 道具id=玩家数据[id].角色.道具[数据.格子[1]]
		local 名称=任务数据[任务id].烹饪
		if self.数据[道具id].名称~=名称 then
			常规提示(id,"#Y/对方需要的不是这种物品")
			return
		end
		if self.数据[道具id]~=nil and self.数据[道具id].数量~=nil and self.数据[道具id].数量 >1 then
			self.数据[道具id].数量 = self.数据[道具id].数量 - 1
			任务数据[任务id].分类=3
			发送数据(玩家数据[id].连接id,1501,{名称="太白金星",模型="太白金星",对话=format("天宫的千里眼能眼观天下，他也许知道天马的消息！"),选项={"我这就去"}})
			道具刷新(id)
			玩家数据[id].角色:刷新任务跟踪()
		else
			self.数据[道具id]=nil
			玩家数据[id].角色.道具[数据.格子[1]]=nil
			任务数据[任务id].分类=3
			发送数据(玩家数据[id].连接id,1501,{名称="太白金星",模型="太白金星",对话=format("天宫的千里眼能眼观天下，他也许知道天马的消息！"),选项={"我这就去"}})
			道具刷新(id)
			玩家数据[id].角色:刷新任务跟踪()
		end
	elseif 事件=="坐骑任务给予药品" then
		local 任务id=玩家数据[id].角色:取任务(307)
		if 任务id==0 or 任务数据[任务id]==nil then
			常规提示(id,"#Y/你没有这样的任务")
			return
		end
		local 道具id=玩家数据[id].角色.道具[数据.格子[1]]
		local 名称=任务数据[任务id].药品
		if self.数据[道具id].名称~=名称 then
			常规提示(id,"#Y/对方需要的不是这种物品")
			return
		end
		if self.数据[道具id]~=nil and self.数据[道具id].数量~=nil and self.数据[道具id].数量 >1 then
			self.数据[道具id].数量 = self.数据[道具id].数量 - 1
			任务数据[任务id].分类=12
			发送数据(玩家数据[id].连接id,1501,{名称="大大王",模型="大大王",对话=format("听说马儿跑了到建业一带，少侠可以去那里找找"),选项={"我这就去建业看看"}})
			道具刷新(id)
			玩家数据[id].角色:刷新任务跟踪()
		else
			self.数据[道具id]=nil
			玩家数据[id].角色.道具[数据.格子[1]]=nil
			任务数据[任务id].分类=12
			发送数据(玩家数据[id].连接id,1501,{名称="大大王",模型="大大王",对话=format("听说马儿跑了到建业一带，少侠可以去那里找找"),选项={"我这就去建业看看"}})
			道具刷新(id)
			玩家数据[id].角色:刷新任务跟踪()
		end
	elseif 事件 == nil and 玩家数据[id].角色.剧情.附加.物品~= nil then
		if 玩家数据[id].角色.剧情.地图 == 玩家数据[id].给予数据.地图 and 玩家数据[id].角色.剧情.编号 == 玩家数据[id].给予数据.编号 then
			local 道具id=玩家数据[id].角色[类型][数据.格子[1]]
			local 名称=玩家数据[id].角色.剧情.附加.物品
			local 数量=玩家数据[id].角色.剧情.附加.数量
			if 数量 == nil then 数量 = 1 end
			if self.数据[道具id]==nil then
				return
			end
			if self.数据[道具id].名称~=名称 then
				常规提示(id,"#Y/对方需要的不是这种物品")
				return
			end
			if self.数据[道具id].数量 ~= nil and self.数据[道具id].数量<数量 then
				常规提示(id,"#Y/该物品的数量无法达到要求")
				return
			end
			if self.数据[道具id].数量 ~= nil then
				self.数据[道具id].数量=self.数据[道具id].数量-数量
			end
			if self.数据[道具id].数量==nil or self.数据[道具id].数量<=0 then
				self.数据[道具id]=nil
				玩家数据[id].角色[类型][数据.格子[1]]=nil
			end
		end
		玩家数据[id].角色.剧情.给予=true
		对话处理类:数据处理(玩家数据[id].连接id,1501,id,玩家数据[id].给予数据)
	elseif 事件 == nil and 玩家数据[id].角色.剧情.附加.银子~= nil then
		if 玩家数据[id].角色.剧情.地图 == 玩家数据[id].给予数据.地图 and 玩家数据[id].角色.剧情.编号 == 玩家数据[id].给予数据.编号 then
			if 玩家数据[id].角色.剧情.附加.银子 >  数据.银子+0 then
				常规提示(id,"#Y/你给予的银两未达到任务要求")
				return
			end
			if 玩家数据[id].角色:扣除银子(玩家数据[id].角色.剧情.附加.银子,0,0,"剧情任务",1) then
				玩家数据[id].角色.剧情.给予=true
				对话处理类:数据处理(玩家数据[id].连接id,1501,id,玩家数据[id].给予数据)
			end
		end
	end
end
function 道具处理类:给予处理(连接id,id,数据)
	if 玩家数据[id].给予数据==nil then
		return
	elseif 玩家数据[id].给予数据.类型==1 then
		self:系统给予处理(连接id,id,数据)
		return
	end
	local 对方id=玩家数据[id].给予数据.id
	if 玩家数据[对方id]==nil then
		常规提示(id,"#Y/对方并不在线")
		return
	end
	if 地图处理类:比较距离(id,对方id,500)==false then
		常规提示(id,"#Y/你们的距离太远了")
		return
	end
	local 金额=0
	local 银子来源=数据.银子
	local 名称=玩家数据[id].角色.名称
	local 名称1=玩家数据[对方id].角色.名称
	if 银子来源=="" or 银子来源==nil then
		金额=0
	else
		金额=数据.银子+0
	end
	if 金额<0 then
		return
	end
	if 玩家数据[id].角色.银子<金额 then
		常规提示(id,"#Y/你没有那么多的银子")
		return
	end
	if 金额>0 then
		金额=qz(金额)
		local 之前银子=玩家数据[id].角色.银子
		玩家数据[id].角色.银子=玩家数据[id].角色.银子-金额
		local 之前银子=玩家数据[对方id].角色.银子
		玩家数据[对方id].角色.银子=玩家数据[对方id].角色.银子+金额
		常规提示(id,format("#Y/你给了%s%s两银子",名称1,金额))
		常规提示(对方id,format("#Y/%s给了你%s两银子",名称,金额))
	end
	for n=1,3 do
		local 格子=数据.格子[n]
		if 格子~=nil then
			local 数量 = 数据.数量[n] +0
			local go=true
			if 数量<1 or 数量>99 then
				__S服务:输出("玩家"..id.." 存在作弊行为！！！给予处理")
				local 封禁原因=玩家数据[id].角色.ip.."“违规行为：给予处理”数量=="..数量.."，玩家ID=="..id.."，玩家账号=="..玩家数据[id].账号
				f函数.写配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","已违规7",封禁原因)
				go=false
			end
			if go then
				数量=math.floor(数量)
				local 道具id=玩家数据[id].角色.道具[格子]
				if 道具id~=nil and 玩家数据[id].道具.数据[道具id]~=nil then
					if 玩家数据[id].道具.数据[道具id].绑定 or 玩家数据[id].道具.数据[道具id].不可交易 or 玩家数据[id].道具.数据[道具id].专用 then
						常规提示(id,"#Y/该物品无法交易或给予给他人")
					else
						local 对方格子=玩家数据[对方id].角色:取道具格子()
						if 对方格子==0 then
							常规提示(id,"#Y/对方身上没有足够的空间")
						else
							local 对方道具=玩家数据[对方id].道具:取新编号()
							local 道具名称=玩家数据[id].道具.数据[道具id].名称
							local 道具识别码=玩家数据[id].道具.数据[道具id].识别码
							if 玩家数据[id].道具:检查道具是否存在(id,道具识别码,数量) then
								玩家数据[对方id].道具.数据[对方道具]=table.loadstring(table.tostring(玩家数据[id].道具.数据[道具id]))
								玩家数据[对方id].角色.道具[对方格子]=对方道具
								if 玩家数据[id].道具.数据[道具id].可叠加 and   玩家数据[id].道具.数据[道具id].数量 ~= nil and   玩家数据[id].道具.数据[道具id].数量 ~= "假" then
									if 玩家数据[id].道具.数据[道具id].数量 - 数量 > 0 then
										玩家数据[对方id].道具.数据[对方道具].数量 = 数量
										玩家数据[id].道具.数据[道具id].数量 = 玩家数据[id].道具.数据[道具id].数量 - 数量
									else
										玩家数据[id].道具.数据[道具id]=nil
										玩家数据[id].角色.道具[格子]=nil
									end
								else
									玩家数据[id].道具.数据[道具id]=nil
									玩家数据[id].角色.道具[格子]=nil
								end
								常规提示(id,"#Y/你给了"..名称1..玩家数据[对方id].道具.数据[对方道具].名称)
								常规提示(对方id,"#Y/"..名称.."给了你"..玩家数据[对方id].道具.数据[对方道具].名称)
							end
						end
					end
				end
			end
		end
	end
	道具刷新(id)
	道具刷新(对方id)
	玩家数据[id].角色:存档()
	玩家数据[对方id].角色:存档()
	玩家数据[id].给予数据=nil
end
function 道具处理类:取武器类型(子类)
	local n = {"枪矛","斧钺","剑","双短剑","飘带","爪刺","扇","魔棒","锤","鞭","环圈","刀","法杖","弓弩","宝珠","巨剑","伞","灯笼","头盔","发钗","项链","女衣","男衣","腰带","鞋子"}
	for i=1,#n do
		if n[i]==子类 then
			return i
		end
	end
end
function 道具处理类:迷宫奖励(id)
	if 迷宫数据[id]~=nil  then
		添加最后对话(id,"你不是已经领取过奖励了吗？")
		return
	end
	迷宫数据[id]=true
	local 等级=玩家数据[id].角色.等级
	local 经验=等级*等级+30000
	local 银子=等级*等级+20000
	local 链接 = {提示=format("#S(幻域迷宫)#G/%s#Y经过一番激烈的战斗，最终战胜了#R%s#Y，因此获得了其奖励的#G/",玩家数据[id].角色.名称,任务数据[任务id].名称),频道="xt",结尾="#Y！"}
	local 名称,数量,参数="",nil,nil
	local 奖励参数=取随机数(1,80)
	if 奖励参数<=10 then
		名称=取宝石()
	elseif 奖励参数<=20 then
		名称="高级魔兽要诀"
	elseif 奖励参数<=30 then
		名称,数量=取强化石(),1
	elseif 奖励参数<=40 then
		名称,数量="	",1
	elseif 奖励参数<=50 then
		名称="高级召唤兽内丹"
	elseif 奖励参数<=60 then
		名称,数量="修炼果",取随机数(1,2)
	elseif 奖励参数<=80 then
		名称,数量="九转金丹",取随机数(100,200)
	end
	玩家数据[id].道具:给予超链接道具(id,名称,数量,参数,链接)

end
function 道具处理类:玲珑宝图处理(id)
    local 奖励参数=取随机数(1,100)
	local lv = 玩家数据[id].角色.等级
	local fanwei={6,8}
	if lv>=80 and lv<100 then
		fanwei={8,10}
	elseif lv>=100 and lv<120 then
		fanwei={10,12}
	elseif lv>=120 then
		fanwei={10,12}
	end
	local 链接
	local 名称
	if 奖励参数<=10 then
		名称=玩家数据[id].角色.等级*1000
		玩家数据[id].角色:添加经验(qz(玩家数据[id].角色.等级*1000),"玲珑宝图")
		添加最后对话(id,"你的修为又增加了，获得了"..名称.."点经验")
	elseif 奖励参数<=40 then
		名称="灵饰指南书"
		链接 = {提示=format("#Y天降大运！#G/%s#Y拿着玲珑宝图，在野外挖宝意外获得了神仙遗留的",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
		玩家数据[id].道具:给予超链接道具(id,名称,fanwei,nil,链接,"成功")
		添加最后对话(id,"天降大运，你意外获得了神仙遗留的1个"..名称)
	elseif 奖励参数<=55 then
		名称="符石卷轴"
		链接 = {提示=format("#Y天降大运！#G/%s#Y拿着玲珑宝图，在野外挖宝意外获得了神仙遗留的",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
		玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
		添加最后对话(id,"天降大运，你意外获得了神仙遗留的1个"..名称)
	elseif 奖励参数<=80 then
		名称="元灵晶石"
		链接 = {提示=format("#Y天降大运！#G/%s#Y拿着玲珑宝图，在野外挖宝意外获得了神仙遗留的",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
		玩家数据[id].道具:给予超链接道具(id,名称,fanwei,nil,链接,"成功")
		添加最后对话(id,"天降大运，你意外获得了神仙遗留的1个"..名称)

	elseif 奖励参数<=90 then
		任务处理类:宝图封妖(id)
	else
		任务处理类:宝图妖王(id)
	end
end
function 道具处理类:高级藏宝图处理(id)
	local 奖励参数=取随机数(1,105)
	local 链接 = {提示=format("#Y天降福缘！#G/%s#Y拿着获得的高级藏宝图，在野外挖宝惊喜的获得了",玩家数据[id].角色.名称),频道="xt",结尾="#24#Y。"}
	if 奖励参数<=4 then
		玩家数据[id].道具:给予超链接书铁(id,{6,8},nil,链接,"成功")
		常规提示(id,"#Y你获得了一个宝贝")
	elseif 奖励参数<=7 then
		任务处理类:宝图妖王(id)
	elseif 奖励参数<=10 then
		local 名称="高级召唤兽内丹"
		玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
		常规提示(id,"#Y你获得了一个宝贝")
	elseif 奖励参数<=14 then
		常规提示(id,"#Y/你获得了一本妖怪遗留下来的秘籍")
		玩家数据[id].道具:给予超链接道具(id,"高级魔兽要诀",nil,nil,链接,"成功")
	elseif 奖励参数<=50 then
		self:取随机装备(id,取随机数(5,7))
		常规提示(id,"#Y你获得了一个宝贝")
	elseif 奖励参数<=55 then
		local 名称="召唤兽内丹"
		玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接,"成功")
		常规提示(id,"#Y你获得了一个宝贝")
	elseif 奖励参数<=60 then
		任务处理类:宝图妖王(id)
	elseif 奖励参数<=70 then
		玩家数据[id].道具:给予超链接道具(id,"彩果",1,nil,链接,"成功")
		常规提示(id,"#Y你获得了一个宝贝")
	elseif 奖励参数<=82 then
		任务处理类:宝图封妖(id)
	elseif 奖励参数<=90 then
		任务处理类:宝图妖王(id)
	else
		玩家数据[id].道具:给予超链接道具(id,"未激活的符石",取随机数(1,3),nil,链接,"成功")
		常规提示(id,"#Y你获得了一个宝贝")
		设置任务102(id) --宝宝窝
	end
end
function 道具处理类:低级藏宝图处理(id)
	local 奖励参数=取随机数(1,170)
	if 奖励参数<=15 then
		self:给予道具(id,"彩果",1)
	elseif 奖励参数<=18 then
		self:取随机装备(id,取随机数(5,8))
		常规提示(id,"#Y/你得到了妖怪遗留下来的宝物")
	elseif 奖励参数<=20 then
		local 名称=self:取五宝()
		self:给予道具(id,名称)
	elseif 奖励参数<=30 then
		任务处理类:宝图妖王(id)
	elseif 奖励参数<=60 then
		玩家数据[id].道具:给予超链接书铁(id,{3,7},nil,链接,"成功")
		常规提示(id,"#Y你获得了一个宝贝")
	elseif 奖励参数<=80 then
		self:给予道具(id,"魔兽要诀")
	elseif 奖励参数<=90 then
		常规提示(id,"#Y/你一锄头挖下去挖出了一团瘴气，等你醒来的时候已经身受重伤了")
		玩家数据[id].角色.气血=math.floor(玩家数据[id].角色.气血*0.5)
		玩家数据[id].角色.气血上限=math.floor(玩家数据[id].角色.气血上限*0.5)
		发送数据(玩家数据[id].连接id,47,{玩家数据[id].角色:取气血数据()})
	elseif 奖励参数<=110 then
		战斗准备类:创建战斗(id+0,100003)
		常规提示(id,"#Y/你一锄头挖下去，似乎触碰到了一个奇形怪状的物体")
	elseif 奖励参数<=130 then
		玩家数据[id].角色:添加银子(取随机数(3000,8000),"挖宝",1)	
	else
		任务处理类:宝图封妖(id)
	end
end
function 道具处理类:完成宝图遇怪(id)
	local 奖励参数=取随机数()
	if 奖励参数<=100 then
		local 名称=取宝石()
		self:给予道具(id,名称,取随机数(1,1))
		发送数据(玩家数据[id].连接id,38,{内容="你得到了#R/"..名称})
	elseif 奖励参数<=40 then
		self:取随机装备(id,取随机数(2,7))
	end
	local 临时经验=取等级(id)*50+1000
	玩家数据[id].角色:添加经验(临时经验,"挖图遇怪",1)
end
function 道具处理类:取五宝()
	return 五宝名称[取随机数(1,5)]
end
function 道具处理类:灵饰处理(id,名称,等级,强化,类型)
	local 临时道具 =物品类()
	临时道具:置对象(名称)
	临时道具.级别限制 = 等级
	临时道具.幻化等级=0
	临时道具.部位类型=类型
	临时道具.灵饰=true
	临时道具.耐久=500
	临时道具.鉴定=false
	临时道具.幻化属性={附加={},}
	临时道具.识别码=id.."_"..os.time().."_"..取随机数(1000,9999999).."_"..随机序列
	随机序列=随机序列+1
	self.临时属性=灵饰属性[类型].主属性[取随机数(1,#灵饰属性[类型].主属性)]
	self.临时数值=灵饰主属性[self.临时属性][等级].b
	self.临时下限=灵饰主属性[self.临时属性][等级].a
	self.临时数值=取随机数(self.临时下限,self.临时数值)
	临时道具.幻化属性.基础={类型=self.临时属性,数值=self.临时数值,强化=0}
	for n=1,取随机数(1,3) do
		local 属性表 = {}
    local 权重表 = {}
    for i=1,#灵饰属性[类型].副属性 do
        local 当前属性 = 灵饰属性[类型].副属性[i]
        table.insert(属性表, 当前属性)
        -- 对于特定属性，赋予较低的权重
        if 当前属性 == "防御" or 当前属性 == "气血" or 当前属性 == "速度" or 当前属性 == "伤害" then
            table.insert(权重表, 30)  -- 30%的权重
        else
            table.insert(权重表, 100) -- 100%的权重
        end
    end

    local 总权重 = 0
    for i=1,#权重表 do
        总权重 = 总权重 + 权重表[i]
    end

    local 随机值 = 取随机数(1, 总权重)
    local 累计权重 = 0

    for i=1,#权重表 do
        累计权重 = 累计权重 + 权重表[i]
        if 随机值 <= 累计权重 then
            self.临时属性 = 属性表[i]
            break
        end
    end

    self.临时数值=灵饰副属性[self.临时属性][等级].b
    self.临时下限=灵饰副属性[self.临时属性][等级].a

		local 运气值 = 取随机数(1,100)
		if 运气值 <= 20 then
		    -- 20%几率获得完整范围的随机值
		    self.临时数值=取随机数(self.临时下限,self.临时数值)
		else
		    -- 80%几率只能获得低数值范围
		    local 上限调整 = math.floor(self.临时下限 + (self.临时数值 - self.临时下限) * 0.6)
		    self.临时数值=取随机数(self.临时下限,上限调整)
		end
		for i=1,#临时道具.幻化属性.附加 do
			if 临时道具.幻化属性.附加[i].类型==self.临时属性 then
				self.临时数值=临时道具.幻化属性.附加[i].数值
			end
		end
		临时道具.幻化属性.附加[n]={类型=self.临时属性,数值=self.临时数值,强化=0}
	end
	return 临时道具
end
function 道具处理类:dz灵饰处理(id,名称,等级,类型)
	local 临时道具 =物品类()
	临时道具:置对象(名称)
	临时道具.级别限制 = 等级
	临时道具.幻化等级=0
	临时道具.部位类型=类型
	临时道具.灵饰=true
	临时道具.耐久=500
	临时道具.鉴定=false
	临时道具.幻化属性={附加={},}
	临时道具.识别码=id.."_"..os.time().."_"..取随机数(1000,9999999).."_"..随机序列
	随机序列=随机序列+1
	self.临时属性=灵饰属性[类型].主属性[取随机数(1,#灵饰属性[类型].主属性)]
	self.临时数值=灵饰主属性[self.临时属性][等级].b
	self.临时下限=灵饰主属性[self.临时属性][等级].a
	self.临时数值=取随机数(self.临时下限,self.临时数值)
	临时道具.幻化属性.基础={类型=self.临时属性,数值=self.临时数值,强化=0}
	for n=1,3 do
		self.临时属性=灵饰属性[类型].副属性[取随机数(1,#灵饰属性[类型].副属性)]
		self.临时数值=灵饰副属性[self.临时属性][等级].b
		self.临时下限=灵饰副属性[self.临时属性][等级].a
		self.临时数值=取随机数(self.临时下限,self.临时数值)
		for i=1,#临时道具.幻化属性.附加 do
			if 临时道具.幻化属性.附加[i].类型==self.临时属性 then
				self.临时数值=临时道具.幻化属性.附加[i].数值
			end
		end
		临时道具.幻化属性.附加[n]={类型=self.临时属性,数值=self.临时数值,强化=0}
	end
	临时道具.特效="超级简易"
	return 临时道具
end
function 道具处理类:烹饪处理(连接id,数字id,数据)
	local 临时等级=玩家数据[数字id].角色:取生活技能等级("烹饪技巧")
	local 临时消耗=20
	if 玩家数据[数字id].角色.活力<临时消耗 then
		常规提示(数字id,"本次操作需要消耗"..临时消耗.."点活力")
		return
	end
	local 物品表={}
	玩家数据[数字id].角色.活力= 玩家数据[数字id].角色.活力-临时消耗
	体活刷新(数字id)
	if 临时等级<=4 then
		物品表={"包子"}
	elseif 临时等级<=9 then
		物品表={"包子","包子","佛跳墙","包子"}
	elseif 临时等级<=14 then
		物品表={"包子","包子","佛跳墙","包子","烤鸭"}
	elseif 临时等级<=19 then
		物品表={"包子","珍露酒","佛跳墙","烤鸭","佛跳墙","佛跳墙","包子","烤鸭"}
	elseif 临时等级<=24 then
		物品表={"包子","珍露酒","佛跳墙","佛跳墙","醉生梦死","烤鸭","包子","烤鸭","虎骨酒","佛跳墙","佛跳墙","包子","女儿红"}
	elseif 临时等级<=29 then
		物品表={"包子","珍露酒","豆斋果","佛跳墙","烤鸭","包子","佛跳墙","醉生梦死","烤鸭","虎骨酒","烤鸭","包子","女儿红"}
	elseif 临时等级<=34 then
		物品表={"包子","佛跳墙","佛跳墙","醉生梦死","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","包子","烤鸭","虎骨酒","包子","女儿红"}
	elseif 临时等级<=39 then
		物品表={"烤肉","桂花丸","佛跳墙","佛跳墙","醉生梦死","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","包子","烤鸭","虎骨酒","包子","女儿红"}
	elseif 临时等级<=44 then
		物品表={"烤肉","翡翠豆腐","桂花丸","佛跳墙","醉生梦死","佛跳墙","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","包子","烤鸭","虎骨酒","包子","女儿红"}
	elseif 临时等级<=49 then
		物品表={"烤肉","长寿面","翡翠豆腐","桂花丸","佛跳墙","醉生梦死","佛跳墙","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","包子","烤鸭","虎骨酒","包子","女儿红"}
	elseif 临时等级<=54 then
		物品表={"烤肉","梅花酒","长寿面","翡翠豆腐","桂花丸","佛跳墙","醉生梦死","佛跳墙","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","包子","烤鸭","虎骨酒","包子","女儿红"}
	elseif 临时等级<=59 then
		物品表={"烤肉","百味酒","梅花酒","长寿面","翡翠豆腐","桂花丸","佛跳墙","佛跳墙","醉生梦死","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","包子","烤鸭","虎骨酒","包子","女儿红"}
	elseif 临时等级<=64 then
		物品表={"烤肉","蛇胆酒","百味酒","梅花酒","长寿面","翡翠豆腐","桂花丸","佛跳墙","醉生梦死","佛跳墙","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","包子","烤鸭","虎骨酒","包子","女儿红"}
	else
		物品表={"烤肉","醉生梦死","蛇胆酒","百味酒","梅花酒","长寿面","翡翠豆腐","桂花丸","珍露酒","豆斋果","臭豆腐","佛跳墙","烤鸭","虎骨酒","女儿红"}
	end
	local 临时物品=物品表[取随机数(1,#物品表)]
	local 临时品质=0
	if 临时物品~="包子" then
		临时品质=qz(取随机数(math.floor(临时等级*0.5),临时等级)*数据)
	end
	常规提示(数字id,"#W/经过一阵忙碌，你烹制出了#R/"..临时物品)
	self:给予道具(数字id,临时物品,1,临时品质)
	发送数据(连接id,3699)
end
function 道具处理类:消耗背包道具(连接id,id,名称,数量)
	local 扣除数量 = 数量
	local 扣除数据={}
	local 已扣除=0
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil and self.数据[玩家数据[id].角色.道具[n]]~=nil and self.数据[玩家数据[id].角色.道具[n]].名称==名称 and 已扣除<扣除数量 then
			if self.数据[玩家数据[id].角色.道具[n]].数量 == nil then
				已扣除=已扣除+1
				扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.道具[n],数量=1}
			else
				if self.数据[玩家数据[id].角色.道具[n]].数量>=扣除数量-已扣除 then
					扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.道具[n],数量=扣除数量-已扣除}
					已扣除=已扣除+(扣除数量-已扣除)
				else
					已扣除=已扣除+self.数据[玩家数据[id].角色.道具[n]].数量
					扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.道具[n],数量=self.数据[玩家数据[id].角色.道具[n]].数量}
				end
			end
		end
	end
	if 已扣除<扣除数量 then
		if 玩家数据[id].战斗==0 then
			常规提示(id,"你没有那么多的"..名称)
		else
			发送数据(连接id, 38, {内容 = "你没有那么多的"..名称})
		end
		return false
	else
		for n=1,#扣除数据 do
			玩家数据[id].道具:删除道具(连接id,id,"道具",扣除数据[n].id,扣除数据[n].格子,扣除数据[n].数量)
		end
		发送数据(连接id,3699)
		return true
	end
	return false
end
function 道具处理类:消耗任务道具(连接id,id,名称,数量)
	local 扣除数量 = 数量
	local 扣除数据={}
	local 已扣除=0
	for n=1,20 do
		if 玩家数据[id].角色.任务包裹[n]~=nil and self.数据[玩家数据[id].角色.任务包裹[n]]~=nil and self.数据[玩家数据[id].角色.任务包裹[n]].名称==名称 and 已扣除<扣除数量 then
			if self.数据[玩家数据[id].角色.任务包裹[n]].数量 == nil then
				已扣除=已扣除+1
				扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.任务包裹[n],数量=1}
			else
				if self.数据[玩家数据[id].角色.任务包裹[n]].数量>=扣除数量-已扣除 then
					扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.任务包裹[n],数量=扣除数量-已扣除}
					已扣除=已扣除+(扣除数量-已扣除)
				else
					已扣除=已扣除+self.数据[玩家数据[id].角色.任务包裹[n]].数量
					扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.任务包裹[n],数量=self.数据[玩家数据[id].角色.任务包裹[n]].数量}
				end
			end
		end
	end
	if 已扣除<扣除数量 then
		常规提示(id,"你没有那么多的"..名称)
		return false
	else
		for n=1,#扣除数据 do
			玩家数据[id].道具:删除道具(连接id,id,"任务包裹",扣除数据[n].id,扣除数据[n].格子,扣除数据[n].数量)
		end
		发送数据(连接id,3699)
		return true
	end
end
function 道具处理类:删除道具(连接id,id,包裹类型,道具id,道具格子,删除数量)
	if self.数据[道具id]==nil then
		玩家数据[id].角色[包裹类型][道具格子]=nil
		return
	end
	if 删除数量==nil then 删除数量=1 end
	if self.数据[道具id].数量==nil and self.数据[道具id].名称~="怪物卡片" and self.数据[道具id].名称~="小象炫卡" and self.数据[道具id].名称~="腾蛇炫卡"  and self.数据[道具id].名称~="龙马炫卡"  and self.数据[道具id].名称~="雪人炫卡" then
		self.数据[道具id]=nil
		玩家数据[id].角色[包裹类型][道具格子]=nil
	elseif self.数据[道具id].名称=="怪物卡片" or self.数据[道具id].名称=="小象炫卡" or self.数据[道具id].名称=="腾蛇炫卡"  or self.数据[道具id].名称=="龙马炫卡"  or self.数据[道具id].名称=="雪人炫卡"  then
		self.数据[道具id].次数=self.数据[道具id].次数-1
		if  self.数据[道具id].次数<=0 then
			self.数据[道具id]=nil
			玩家数据[id].角色[包裹类型][道具格子]=nil
		end
	else
		if type(self.数据[道具id].数量 ) ~= "number" then
			self.数据[道具id]=nil
			玩家数据[id].角色[包裹类型][道具格子]=nil
		else
			self.数据[道具id].数量=self.数据[道具id].数量-删除数量
			if self.数据[道具id].数量<=0  then
				self.数据[道具id]=nil
				玩家数据[id].角色[包裹类型][道具格子]=nil
			end
		end
	end
end
function 道具处理类:判断道具是否有(id,名称)
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil and self.数据[玩家数据[id].角色.道具[n]]~=nil and self.数据[玩家数据[id].角色.道具[n]].名称==名称 then
			return true
		end
	end
	return false
end
function 道具处理类:取对方道具(id,名称)
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil and self.数据[玩家数据[id].角色.道具[n]]~=nil and self.数据[玩家数据[id].角色.道具[n]].名称==名称 then
			return self.数据[玩家数据[id].角色.道具[n]],玩家数据[id].角色.道具[n]
		end
	end
	return 0
end
function 道具处理类:随机删除道具(连接id,id)
	local t={}
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil and self.数据[玩家数据[id].角色.道具[n]]~=nil then
			t[#t+1]=n
		end
	end
	if #t>0 then
		local bh = t[取随机数(1,#t)]
		local 名称 = self.数据[玩家数据[id].角色.道具[bh]].名称
		self.数据[玩家数据[id].角色.道具[bh]]=nil
		玩家数据[id].角色.道具[bh]=nil
		发送数据(连接id,3699)
		return 名称
	end
end
function 道具处理类:判定背包道具(id,名称,数量)
	local 扣除数量 = 数量+0
	local 扣除数据={}
	local 已扣除=0
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil and self.数据[玩家数据[id].角色.道具[n]]~=nil and self.数据[玩家数据[id].角色.道具[n]].名称==名称 and 已扣除<扣除数量 then
			if self.数据[玩家数据[id].角色.道具[n]].数量 ~= nil then
				if self.数据[玩家数据[id].角色.道具[n]].数量>=扣除数量-已扣除 then
					扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.道具[n],数量=扣除数量-已扣除}
					已扣除=已扣除+(扣除数量-已扣除)
				else
					已扣除=已扣除+self.数据[玩家数据[id].角色.道具[n]].数量
					扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.道具[n],数量=self.数据[玩家数据[id].角色.道具[n]].数量}
				end
			else
				扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.道具[n],数量=1}
				已扣除 = 已扣除 +1
			end
		end
	end
	if 已扣除<扣除数量 then
		return false
	else
		return true
	end
end
function 道具处理类:判定任务道具(连接id,id,名称,数量)
	local 扣除数量 = 数量
	local 扣除数据={}
	local 已扣除=0
	for n=1,20 do
		if 玩家数据[id].角色.任务包裹[n]~=nil and self.数据[玩家数据[id].角色.任务包裹[n]]~=nil and self.数据[玩家数据[id].角色.任务包裹[n]].名称==名称 and 已扣除<扣除数量 then
			if self.数据[玩家数据[id].角色.任务包裹[n]].数量 ~= nil then
				if self.数据[玩家数据[id].角色.任务包裹[n]].数量>=扣除数量-已扣除 then
					扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.任务包裹[n],数量=扣除数量-已扣除}
					已扣除=已扣除+(扣除数量-已扣除)
				else
					已扣除=已扣除+self.数据[玩家数据[id].角色.任务包裹[n]].数量
					扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.任务包裹[n],数量=self.数据[玩家数据[id].角色.任务包裹[n]].数量}
				end
			else
				扣除数据[#扣除数据+1]={格子=n,id=玩家数据[id].角色.任务包裹[n],数量=1}
				已扣除 = 已扣除 +1
			end
		end
	end
	if 已扣除<扣除数量 then
		return false
	else
		return true
	end
end
function 道具处理类:扣除装备耐久(类型)
	local 玩家id = self.玩家id
	if  类型 == "攻击" or 类型 == "施法" then
		local 道具id = 玩家数据[玩家id].角色.装备[3]
		if self.数据[道具id] ~= nil and self.数据[道具id].特效 ~= "永不磨损" then
			if self.数据[道具id].耐久 == nil then
				self.数据[道具id].耐久 = 0
			end
			if 类型 == "攻击" then
				if 玩家数据[玩家id].角色.门派=="狮驼岭" then
					self.数据[道具id].耐久 = self.数据[道具id].耐久- 0.07
				elseif 玩家数据[玩家id].角色.门派=="大唐官府" then
					self.数据[道具id].耐久 = self.数据[道具id].耐久- 0.05
				else
					self.数据[道具id].耐久 = self.数据[道具id].耐久- 0.1
				end
			else
				self.数据[道具id].耐久 =  self.数据[道具id].耐久- 0.05
			end
			if  self.数据[道具id].耐久 < 0 then
				self.数据[道具id].耐久 = 0
			end
		end
	end
	if  类型 == "挨打" then
		for w, v in pairs(玩家数据[玩家id].角色.装备) do
			if w ~= 3 then
				local 道具id = 玩家数据[玩家id].角色.装备[w]
				if self.数据[道具id] ~= nil  then
					if self.数据[道具id].耐久 == nil then
						self.数据[道具id].耐久 = 0
					end
					if self.数据[道具id] ~= nil and self.数据[道具id].特效 ~= "永不磨损" then
						self.数据[道具id].耐久 = self.数据[道具id].耐久- 0.1
					end
					if  self.数据[道具id].耐久 < 0 then
						self.数据[道具id].耐久 = 0
					end
				end
			end
		end
		for w, v in pairs(玩家数据[玩家id].角色.灵饰) do
			local 道具id = 玩家数据[玩家id].角色.灵饰[w]
			if self.数据[道具id] ~= nil  then
				if self.数据[道具id].耐久 == nil then
					self.数据[道具id].耐久 = 0
				end
				if self.数据[道具id] ~= nil then
					self.数据[道具id].耐久 = self.数据[道具id].耐久- 0.1
				end
				if  self.数据[道具id].耐久 < 0 then
					self.数据[道具id].耐久 = 0
				end
			end
		end
	end
end
function 道具处理类:卸下孩子装备(连接id,id,数据)
  local 角色=数据.角色
  local 类型=数据.类型
  local 道具=数据.道具
  local bb=数据.编号
  local 道具格子=玩家数据[id].角色:取道具格子()
  if 道具格子==0 then
    常规提示(id,"您的道具栏物品已经满啦")
    return
  else
    local 临时id=self:取新编号()
    self.数据[临时id]=玩家数据[id].孩子.数据[bb].装备[道具]
    玩家数据[id].孩子:卸下装备(玩家数据[id].孩子.数据[bb].装备[道具],道具,bb)
    玩家数据[id].角色.数据.道具[道具格子]=临时id
    玩家数据[id].孩子.数据[bb].装备[道具]=nil
    发送数据(玩家数据[id].连接id,96.2,{数据=玩家数据[id].孩子.数据[bb],编号=bb})
    发送数据(连接id,3699)
    道具刷新(id)
    发送数据(连接id,28)
  end
end
function 道具处理类:卸下bb装备(连接id,id,数据)
	local 角色=数据.角色
	local 类型="道具"
	local 道具=数据.道具
	local bb=数据.编号
	local 道具格子=玩家数据[id].角色:取道具格子1(类型)
	if 道具格子==0 then
		常规提示(id,"您的道具栏物品已经满啦")
		return
	else
		local 临时id=self:取新编号()
		self.数据[临时id]=玩家数据[id].召唤兽.数据[bb].装备[道具]
		玩家数据[id].召唤兽.数据[bb]:卸下装备(玩家数据[id].召唤兽.数据[bb].装备[道具],道具)
		玩家数据[id].角色.道具[道具格子]=临时id
		玩家数据[id].召唤兽.数据[bb].装备[道具]=nil
		self:刷新道具行囊(id,类型)
		发送数据(连接id,20,玩家数据[id].召唤兽.数据[bb]:取存档数据())
		发送数据(连接id,28)
	end
end
function 道具处理类:佩戴孩子装备(连接id,id,数据)
  local 角色=数据.角色
  local 类型=数据.类型
  local 道具=数据.道具
  local 道具id=玩家数据[id].角色.数据[类型][道具]
  local bb=数据.编号
  if self.数据[道具id].分类>6 and self:召唤兽可装备(self.数据[道具id],self.数据[道具id].分类-6,玩家数据[id].孩子.数据[bb].等级,id) then
    local 装备格子=self.数据[道具id].分类 - 6
    if 玩家数据[id].孩子.数据[bb].装备[装备格子] ~= nil then
     local 临时道具=玩家数据[id].孩子.数据[bb].装备[装备格子]
     玩家数据[id].孩子:卸下装备(玩家数据[id].孩子.数据[bb].装备[装备格子],装备格子,bb)
     玩家数据[id].孩子.数据[bb].装备[装备格子] =nil
     玩家数据[id].孩子:穿戴装备(self.数据[道具id],装备格子,bb)
     self.数据[道具id] = 临时道具
     玩家数据[id].角色.数据[类型][道具]=道具id
    else
     玩家数据[id].孩子:穿戴装备(self.数据[道具id],装备格子,bb)
     self.数据[道具id]=nil
     玩家数据[id].角色.数据[类型][道具]=nil
    end
    发送数据(连接id,3699)
    道具刷新(id)
    发送数据(玩家数据[id].连接id,96.2,{数据=玩家数据[id].孩子.数据[bb],编号=bb})
    发送数据(连接id,28)
  end
end
function 道具处理类:佩戴bb装备(连接id,id,数据)
	local 角色=数据.角色
	local 类型=数据.类型
	local 道具=数据.道具
	local 道具id=玩家数据[id].角色[类型][道具]
	local bb=数据.编号
	if self.数据[道具id].分类>6 and self:召唤兽可装备(self.数据[道具id],self.数据[道具id].分类-6,玩家数据[id].召唤兽.数据[bb].等级,id) then
		local 装备格子=self.数据[道具id].分类 - 6
		if 玩家数据[id].召唤兽.数据[bb].装备[装备格子] ~= nil then
			local 临时道具=玩家数据[id].召唤兽.数据[bb].装备[装备格子]
			玩家数据[id].召唤兽.数据[bb]:卸下装备(玩家数据[id].召唤兽.数据[bb].装备[装备格子],装备格子)
			玩家数据[id].召唤兽.数据[bb].装备[装备格子] =nil
			玩家数据[id].召唤兽.数据[bb]:穿戴装备(self.数据[道具id],装备格子)
			self.数据[道具id]=临时道具
		else
			玩家数据[id].召唤兽.数据[bb]:穿戴装备(self.数据[道具id],装备格子)
			self.数据[道具id]=nil
			玩家数据[id].角色[类型][道具]=nil
		end
		self:刷新道具行囊(id,数据.类型)
		发送数据(连接id,20,玩家数据[id].召唤兽.数据[bb]:取存档数据())
		发送数据(连接id,28)
	end
end
function 道具处理类:召唤兽可装备(i1,i2,等级,id)
	if i1 ~= nil and i1.分类 - 6 == i2 then
		if (i1.级别限制 == 0 or i1.特效 == "无级别限制" or 等级 >= i1.级别限制) then
			return true
		else
			if i1.级别限制 > 等级 then
				常规提示(id,"#Y/你的召唤兽等级不足哦")
			end
		end
	end
	return false
end
function 道具处理类:取随机装备(id,等级,返回)
	local 临时等级=等级
	local 临时参数=取随机数(1,#书铁范围)
	local 临时序列=临时参数
	if 临时序列==25 then
		临时序列=23
	elseif 临时序列==24 then
		临时序列=22
	elseif 临时序列==23 or 临时序列==22 then
		临时序列=21
	elseif 临时序列==21 then
		临时序列=20
	elseif 临时序列==20 or 临时序列==19 then
		临时序列=19
	end
	if 临时序列<=18 and 临时等级>=9 then
		临时等级=取随机数(10,12)
	else
		if 临时等级>=12 then
			临时等级=10
		end
	end
	local 临时类型=装备处理类.打造物品[临时序列][临时等级+1]
	if type(临时类型)=="table" then
		if 临时参数 ==23 then
			临时类型=临时类型[2]
		elseif 临时参数 ==22 then
			临时类型=临时类型[1]
		elseif 临时参数 ==20 then
			临时类型=临时类型[2]
		elseif 临时参数 ==19 then
			临时类型=临时类型[1]
		else
			临时类型=临时类型[取随机数(1,2)]
		end
	end
	装备处理类:生成打造装备(id,临时等级*10,临时序列,临时类型,"系统产出")
	if 返回 then
		return 临时类型
	end
end
function 道具处理类:取随机装备1(id,等级,名称)
	装备处理类:生成打造装备(id,等级*10,取装备序列(名称),名称,"系统产出")
end
function 道具处理类:给予超链接书铁(id,等级,类型,链接)
	if 类型==nil then
		self.临时随机=取随机数()
		if self.临时随机<=50 then
			self.书铁名称="制造指南书"
		else
			self.书铁名称="百炼精铁"
		end
		local 书铁等级=取随机数(等级[1]*10,等级[2]*10)
		local 书铁种类=取随机数(1,#书铁范围)
		书铁等级=math.floor(书铁等级/10)*10
		self:给予超链接道具(id,self.书铁名称,书铁等级,书铁种类,链接,"成功")
		return {self.书铁名称,书铁等级,书铁种类}
	else
		if 类型=="书" then
			self.临时随机=取随机数()
			self.书铁名称="制造指南书"
			local 书铁等级=取随机数(等级[1]*10,等级[2]*10)
			local 书铁种类=取随机数(1,#书铁范围)
			书铁等级=math.floor(书铁等级/10)*10
			self:给予超链接道具(id,self.书铁名称,书铁等级,书铁种类,链接,"成功")
			return {self.书铁名称,书铁等级,书铁种类}
		else
			self.临时随机=取随机数()
			self.书铁名称="百炼精铁"
			local 书铁等级=取随机数(等级[1]*10,等级[2]*10)
			书铁等级=math.floor(书铁等级/10)*10
			self:给予超链接道具(id,self.书铁名称,书铁等级,书铁种类,链接,"成功")
			return {self.书铁名称,书铁等级}
		end
	end
end
function 道具处理类:给予书铁(id,等级,类型)
	if 类型==nil then
		self.临时随机=取随机数()
		if self.临时随机<=50 then
			self.书铁名称="制造指南书"
		else
			self.书铁名称="百炼精铁"
		end
		local 书铁等级=取随机数(等级[1]*10,等级[2]*10)
		local 书铁种类=取随机数(1,#书铁范围)
		书铁等级=math.floor(书铁等级/10)*10
		self:给予道具(id,self.书铁名称,书铁等级,书铁种类)
		return {self.书铁名称,书铁等级,书铁种类}
	else
		if 类型=="书" then
			self.临时随机=取随机数()
			self.书铁名称="制造指南书"
			local 书铁等级=取随机数(等级[1]*10,等级[2]*10)
			local 书铁种类=取随机数(1,#书铁范围)
			书铁等级=math.floor(书铁等级/10)*10
			self:给予道具(id,self.书铁名称,书铁等级,书铁种类)
			return {self.书铁名称,书铁等级,书铁种类}
		else
			self.临时随机=取随机数()
			self.书铁名称="百炼精铁"
			local 书铁等级=取随机数(等级[1]*10,等级[2]*10)
			书铁等级=math.floor(书铁等级/10)*10
			self:给予道具(id,self.书铁名称,书铁等级)
			return {self.书铁名称,书铁等级}
		end
	end
end

function 道具处理类:飞行符传送(连接id,id,内容)
	local 传送序列=内容.序列
	if self:取飞行限制(id)==false then
		local 包裹类型=玩家数据[id].道具操作.类型
		local 道具格子=玩家数据[id].道具操作.编号
		local 道具id=玩家数据[id].角色[包裹类型][道具格子]
		地图处理类:跳转地图(id,self.飞行传送点[传送序列][1],self.飞行传送点[传送序列][2],self.飞行传送点[传送序列][3])
		self:删除道具(连接id,id,包裹类型,道具id,道具格子,删除数量)
		发送数据(连接id,3699)
	end
end
function 道具处理类:新春飞行符传送(连接id,id,内容)
	if 玩家数据[id].最后操作=="新春飞行符" then
		if self:取飞行限制(id)==false then
			local 地图id=内容.地图
			local 坐标=内容.zb
			local 包裹类型=玩家数据[id].道具操作.类型
			local 道具格子=玩家数据[id].道具操作.编号
			local 道具id=玩家数据[id].角色[包裹类型][道具格子]
			if self.数据[道具id].名称=="新春飞行符" then
				if self.数据[道具id].次数 then
					self.数据[道具id].次数 =self.数据[道具id].次数-1
					if self.数据[道具id].次数<=0 then
						self:删除道具(连接id,id,包裹类型,道具id,道具格子,删除数量)
					end
				end
				地图处理类:跳转地图(id+0,地图id,坐标.x,坐标.y)
				道具刷新(id)
			end
		end
		玩家数据[id].最后操作=nil
	end
end
function 道具处理类:取加血道具(名称)
	local 临时名称={"包子","草果","山药","九香虫","八角莲叶","水黄莲","月见草","人参","烤鸭","翡翠豆腐","烤肉","臭豆腐","金创药","小还丹","千年保心丹","金香玉","天不老","紫石英","血色茶花","熊胆","鹿茸","六道轮回","凤凰尾","硫磺草","龙之心屑","火凤之睛","四叶花","天青地白","七叶莲"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then
			return true
		end
	end
	return false
end
function 道具处理类:取寿命道具(名称)
	local 临时名称={"桂花丸","长寿面","豆斋果"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then
			return true
		end
	end
	return false
end
function 道具处理类:取寿命道具1(名称,道具id)
	local 品质=self.数据[道具id].阶品
	local 数值=0
	local 中毒=0
	if 名称=="桂花丸" then
		数值=品质*0.5
	elseif 名称=="豆斋果" then
		数值=品质*3
		中毒=3
	elseif 名称=="长寿面" then
		数值=品质*2+50
		中毒=3
	end
	return {数值=qz(数值),中毒=中毒}
end
function 道具处理类:取加魔道具(名称)
	local 临时名称={"翡翠豆腐","佛跳墙","蛇蝎美人","风水混元丹","月见草","定神香","十香返生丸","丁香水","月星子","仙狐涎","地狱灵芝","麝香","血珊瑚","餐风饮露","白露为霜","天龙水","孔雀红","紫丹罗","佛手","旋复花","龙须草","百色花","香叶","白玉骨头","鬼切草","灵脂","曼陀罗花"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then
			return true
		end
	end
	return false
end
function 道具处理类:取加血道具1(名称,道具id)
	local 品质=self.数据[道具id].阶品
	if 品质 == nil then
		品质 = 1
	end
	local 数值=0
	if 名称=="包子" then
		数值=100
	elseif 名称=="烤鸭" then
		数值=品质*10+100
	elseif 名称=="烤肉" then
		数值=品质*10
	elseif 名称=="臭豆腐" then
		数值=品质*20+200
	elseif 名称=="翡翠豆腐" then
		数值=品质*15+150
	elseif 名称=="草果" then
		数值=0
	elseif 名称=="山药" then
		数值=40
	elseif 名称=="九香虫"  then
		数值=0
	elseif 名称=="水黄莲"  then
		数值=70
	elseif 名称=="月见草" then
		数值=40
	elseif 名称=="八角莲叶" then
		数值=60
	elseif 名称=="人参" then
		数值=80
	elseif 名称=="金创药" then
		数值=400
	elseif 名称=="小还丹" then
		数值=品质*8+100
	elseif 名称=="金香玉" or 名称=="固本培元丹" then
		数值=品质*12+150
	elseif 名称=="千年保心丹" or 名称=="十全大补丸" then
		数值=品质*4+200
	elseif 名称=="五龙丹" or 名称=="舒筋活络丸" then
		数值=品质*3
	elseif 名称=="佛光舍利子" then
		数值=品质*3
	elseif 名称=="九转回魂丹" or 名称=="九转续命丹" then
		数值=品质*5+100
	elseif 名称=="天不老" or 名称=="紫石英" then
		数值=100
	elseif 名称=="血色茶花" or 名称=="鹿茸" then
		数值= 150
	elseif 名称=="六道轮回" or 名称=="熊胆" then
		数值= 200
	elseif 名称=="凤凰尾" or 名称=="硫磺草" then
		数值= 250
	elseif 名称=="龙之心屑" or 名称=="火凤之睛" then
		数值= 300
	elseif 名称=="四叶花" then
		数值= 40
	elseif 名称=="天青地白" then
		数值= 80
	elseif 名称=="七叶莲" then
		数值= 60
	end
	return qz(数值)
end
function 道具处理类:取加血道具2(名称,道具id)
	local 品质=10
	if self.数据[道具id].阶品~=nil then 品质=self.数据[道具id].阶品 end
	local 数值=0
	if 名称=="包子" then
		数值=0
	elseif 名称=="烤鸭" then
		数值=品质*5
	elseif 名称=="烤肉" then
		数值=品质*10+10
	elseif 名称=="臭豆腐" then
		数值=0
	elseif 名称=="翡翠豆腐" then
		数值=0
	elseif 名称=="草果" then
		数值=10
	elseif 名称=="山药" then
		数值=10
	elseif 名称=="九香虫" then
		数值=15
	elseif 名称=="八角莲叶" then
		数值=15
	elseif 名称=="人参" then
		数值=20
	elseif 名称=="水黄莲" then
		数值=20
	elseif 名称=="金创药" then
		数值=400
	elseif 名称=="小还丹" then
		数值=品质+80
	elseif 名称=="金香玉" then
		数值=0
	elseif 名称=="千年保心丹" then
		数值=品质*4+100
	elseif 名称=="五龙丹" then
		数值=0
	elseif 名称=="佛光舍利子" then
		数值=0
	elseif 名称=="九转回魂丹" then
		数值=0
	elseif 名称=="天不老" or 名称=="紫石英" then
		数值=0
	elseif 名称=="血色茶花" or 名称=="鹿茸" then
		数值= 0
	elseif 名称=="六道轮回" or 名称=="熊胆" then
		数值= 0
	elseif 名称=="凤凰尾" or 名称=="硫磺草" then
		数值= 0
	elseif 名称=="龙之心屑" or 名称=="火凤之睛" then
		数值= 0
	elseif 名称=="四叶花" then
		数值= 0
	elseif 名称=="天青地白" then
		数值= 0
	elseif 名称=="七叶莲" or "月见草" then
		数值= 0
	end
	return qz(数值)
end
function 道具处理类:取加魔道具1(名称,道具id)
	local 品质=10
	if self.数据[道具id].阶品~=nil then 品质=self.数据[道具id].阶品 end
	local 数值=0
	if 名称=="佛跳墙" or 名称=="翡翠豆腐" then
		数值=品质*10+100
	elseif 名称=="定神香"  then
		数值=品质*5+50
	elseif 名称=="风水混元丹"  then
		数值=品质*3+50
	elseif 名称=="蛇蝎美人"or 名称=="凝气丸"then
		数值=品质*5+100
	elseif 名称=="十香返生丸" or 名称=="七珍丸"  then
		数值=品质*3+50
	elseif 名称=="女儿红" or 名称=="虎骨酒"  then
		数值=20
	elseif 名称=="珍露酒"  then
		数值=品质*0.4+10
	elseif 名称=="梅花酒"  then
		数值=品质*0.6
	elseif 名称=="百味酒"  then
		数值=品质*0.7
	elseif 名称=="蛇胆酒" or 名称=="醉生梦死"  or 名称=="醉仙果" then
		数值=品质*1
	elseif 名称=="月见草" then
		数值=30
	elseif 名称=="丁香水" or 名称=="月星子"  then
		数值=75
	elseif 名称=="仙狐涎" or 名称=="地狱灵芝" or 名称=="麝香" or 名称=="血珊瑚" or 名称=="餐风饮露" or 名称=="白露为霜"  then
		数值=100
	elseif 名称=="天龙水" or 名称=="孔雀红"  then
		数值=150
	elseif 名称=="紫丹罗" or 名称=="佛手" or 名称=="旋复花" then
		数值=20
	elseif 名称=="龙须草" or 名称=="百色花" or 名称=="香叶"  then
		数值=30
	elseif 名称=="白玉骨头" or 名称=="鬼切草" or 名称=="灵脂"  then
		数值=40
	elseif 名称=="曼陀罗花"  then
		数值=50
	end
	return qz(数值)
end
function 道具处理类:清空包裹(连接id,id)
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil then
			self.数据[玩家数据[id].角色.道具[n]]=nil
			玩家数据[id].角色.道具[n]=nil
		end
	end
	发送数据(连接id,3699)
end
function 道具处理类:取是否有相同内丹(id,加血对象,技能)
	local wz = 0
	for i=1,玩家数据[id].召唤兽.数据[加血对象].内丹[1] do
		if 玩家数据[id].召唤兽.数据[加血对象].内丹.技能[i]~= nil and 玩家数据[id].召唤兽.数据[加血对象].内丹.技能[i].技能 == 技能 then
			wz = i
		end
	end
	return wz
end
function 道具处理类:取是内丹空格子(id,加血对象)
	local wz = 0
	for i=1,玩家数据[id].召唤兽.数据[加血对象].内丹[1] do
		if 玩家数据[id].召唤兽.数据[加血对象].内丹.技能[i] == nil then
			wz = i
			return wz
		end
	end
end
function 道具处理类:符纸使用(连接id,id,内容)
	local 包裹类型=内容.类型
	local 道具格子=内容.道具格子
	local 符纸格子 =内容.符纸格子
	local 删除数量=1
	local 道具id=玩家数据[id].角色["道具"][符纸格子]
	local 装备=玩家数据[id].角色["道具"][道具格子]
	if self.数据[道具id] == nil or self.数据[装备] == nil then
		常规提示(id,"#Y/数据异常！")
		return
	end
	if 包裹类型=="强化符" then
		local 强化效果  = {}
		强化效果 = 取强化符效果(self.数据[道具id].技能,self.数据[道具id].等级,self.数据[装备].分类)
		if 强化效果.类型 == "无" then return 常规提示(id,"#Y/附魔部位不正确") end
		if self.数据[装备].分类==1 or self.数据[装备].分类==2 or self.数据[装备].分类==4 then
			self.数据[装备].临时附魔 = {}
		else
			if self.数据[装备].临时附魔 == nil then
				self.数据[装备].临时附魔 = {}
			end
		end
		self.数据[装备].临时附魔[强化效果.类型] = {}
		self.数据[装备].临时附魔[强化效果.类型].数值 = 强化效果.数值
		self.数据[装备].临时附魔[强化效果.类型].时间 = os.time()+(172800*7)
		常规提示(id,"#Y/装备附魔成功！")
		self:删除道具(连接id,id,"道具",道具id,符纸格子,删除数量)
		发送数据(连接id,3699)
		return
	elseif 包裹类型=="装备鉴定" then
		if self.数据[装备].鉴定 == false then
			if self.数据[道具id].子类 >= self.数据[装备].级别限制 then
				self.数据[装备].鉴定 = true
				self.数据[装备].专用提示=nil
				常规提示(id,"#Y/物品鉴定成功！")
				常规提示(id,"#Y/可以通过梦幻精灵网页版查询装备的属性范围，你有兴趣的话可以去瞧瞧。")
				self:删除道具(连接id,id,"道具",道具id,符纸格子,删除数量)
				装备处理类:装备鉴定处理(连接id,id,self.数据[装备])
				发送数据(连接id,3699)
			else
				常规提示(id,"#Y/此图鉴的等级过低无法鉴定该装备")
				return
			end
		else
			常规提示(id,"#Y/这件装备已经鉴定过了请不要重复鉴定")
			return
		end
	elseif 包裹类型=="灵饰鉴定" then
		if self.数据[装备].鉴定 == false then
			local 物品=取物品数据(self.数据[装备].名称)
			local 级别=物品[5]
			if self.数据[道具id].子类 >= 级别 then
				self.数据[装备].鉴定 = true
				常规提示(id,"#Y/物品鉴定成功")
				self:删除道具(连接id,id,"道具",道具id,符纸格子,删除数量)
				if self.数据[装备].幻化属性.附加 then
					local go = self.数据[装备].幻化属性.附加[1].类型
					for i=1,#self.数据[装备].幻化属性.附加 do
						if go~=self.数据[装备].幻化属性.附加[i].类型 then
							go=false
							break
						end
					end
					if go~=false and #self.数据[装备].幻化属性.附加==3 then
						local xt1 = "一样的加成之耳饰"
						local xt2 = "鉴定出一个三种随机属性皆一样的耳饰"
						if self.数据[装备].子类==2 then
							xt1 = "一样的加成之佩饰"
							xt2 = "鉴定出一个三种随机属性皆一样的佩饰"
						elseif self.数据[装备].子类==3 then
							xt1 = "一样的加成之戒指"
							xt2 = "鉴定出一个三种随机属性皆一样的戒指"
						elseif self.数据[装备].子类==4 then
							xt1 = "一样的加成之手镯"
							xt2 = "鉴定出一个三种随机属性皆一样的手镯"
						end
						发送数据(连接id,105,{头像=xt1,标题=xt1,说明=xt2})
					end
				end
				if 取随机数(1,1000)<=15 then
					self.数据[装备].特效="超级简易"
				end
				发送数据(连接id,3699)
			else
				常规提示(id,"#Y/此图鉴的等级过低无法鉴定该装备")
				return
			end
		else
			常规提示(id,"#Y/这件装备已经鉴定过了请不要重复鉴定")
			return
		end
	elseif 包裹类型=="特技书" then
		if not self.数据[装备].鉴定 then
			常规提示(id,"#Y/这个装备未鉴定无法附加特技")
			return
		end
		if self.数据[装备].总类~=2 or self.数据[装备].灵饰 or self.数据[装备].召唤兽装备 then
			常规提示(id,"该物品只能对人物装备使用")
			return
		end
		if self.数据[道具id].名称~=包裹类型 then
			return
		end
		self:删除道具(连接id,id,"道具",道具id,符纸格子,1)
		local tj ={"弱点击破","破血狂攻","心疗术","破碎无双","诅咒之伤","太极护法","罗汉金钟","慈航普度","放下屠刀","笑里藏刀","碎甲术","金刚怒目","命归术","心如明镜","气归术","凝气诀","凝神诀","命疗术","流云诀","啸风诀","河东狮吼","破甲术","气归术","凝气诀","凝神诀","命疗术","流云诀","啸风诀","河东狮吼","破甲术","凝滞术","吸血特技","金刚不坏","菩提心佑","起死回生","回魂咒","气疗术","野兽之力","魔兽之印","修罗咒","身似菩提","光辉之甲","圣灵之甲","四海升平","水清诀","玉清诀","冰清诀","晶清诀"}
		if self.数据[装备].特技~=nil then
			for k,v in pairs(tj) do
				if v==self.数据[装备].特技 then
					table.remove(tj,k)
					break
				end
			end
		end
		self.数据[装备].特技=tj[取随机数(1,#tj)]
		常规提示(id,"#G添加装备特技成功！")
		发送数据(连接id,3699)
	elseif 包裹类型=="特效宝珠" then
		if not self.数据[装备].鉴定 then
			常规提示(id,"#Y/这个装备未鉴定无法附加特效")
			return
		end
		if self.数据[装备].总类~=2 or self.数据[装备].灵饰 or self.数据[装备].召唤兽装备 then
			常规提示(id,"该物品只能对人物装备使用")
			return
		end
		if self.数据[道具id].名称~=包裹类型 then
			return
		end
		if self.数据[装备].级别限制==160 then
			常规提示(id,"不能对160的装备使用")
			return
		end
		self:删除道具(连接id,id,"道具",道具id,符纸格子,删除数量)
		local tx = {}
		if self.数据[装备].分类 == 1 then
			tx = {"珍宝","坚固","珍宝","简易","再生","坚固","精致","永不磨损","易修理"}
		elseif self.数据[装备].分类 == 2 then
			tx = {"珍宝","坚固","珍宝","简易","珍宝","坚固","珍宝","简易","神农","专注","坚固","精致","永不磨损"}
		elseif self.数据[装备].分类 == 3 then
			tx = {"珍宝","坚固","珍宝","珍宝","坚固","珍宝","简易","神佑","必中","绝杀","坚固","精致","永不磨损"}
		elseif self.数据[装备].分类 == 4 then
			tx = {"珍宝","坚固","珍宝","简易","坚固","精致","永不磨损","易修理","伪装"}
		elseif self.数据[装备].分类 == 5 then
			tx = {"珍宝","坚固","珍宝","简易","坚固","精致","永不磨损","易修理","愤怒","暴怒"}
		elseif self.数据[装备].分类 == 6 then
			tx = {"珍宝","坚固","珍宝","简易","坚固","精致","狩猎","迷踪","永不磨损","易修理"}
		end

		if self.数据[装备].特效~=nil and self.数据[装备].特效[1] then
			for n=1,#tx do
				for i=1,#self.数据[装备].特效 do
					if tx[n]==self.数据[装备].特效[i] then
						table.remove(tx,n)
					end
				end
			end
			local sj = 取随机数(1,#tx)
			local num = 取随机数(1,#self.数据[装备].特效)
			self.数据[装备].特效[num]=tx[sj]
		else
			self.数据[装备].特效={}
			local dwq=tx[取随机数(1,#tx)]
			self.数据[装备].特效[1]=dwq
		end
		常规提示(id,"#G添加装备特效成功！")
		发送数据(连接id,3699)
	elseif 包裹类型=="超简易宝珠" then
		if not self.数据[装备].鉴定 then
			常规提示(id,"#Y/这个装备未鉴定无法附加特效")
			return
		end
		if not self.数据[装备].灵饰 then
			常规提示(id,"该物品只能对灵饰使用")
			return
		end
		if self.数据[装备].特效 == "超级简易" then
			常规提示(id,"该装备已经拥有该特效了，不要浪费哦。")
			return
		end
		if self.数据[道具id].名称~=包裹类型 then
			return
		end
		self:删除道具(连接id,id,"道具",道具id,符纸格子,删除数量)
		local num=21
		if 取随机数()<=num then
			self.数据[装备].特效="超级简易"
			常规提示(id,"#G添加装备特效成功！")
		else
			常规提示(id,"#R很遗憾，附魔失败！")
		end
		发送数据(连接id,3699)
	elseif 包裹类型=="愤怒符" then
		if not self.数据[装备].鉴定 then
			常规提示(id,"#Y/这个装备未鉴定无法附加特效")
			return
		end
		if self.数据[装备].总类~=2 or self.数据[装备].灵饰 or self.数据[装备].召唤兽装备 or self.数据[装备].分类~=5   then
			常规提示(id,"该物品只能腰带使用")
			return
		end
		if self.数据[道具id].名称~=包裹类型 then
			return
		end
		if self.数据[装备].特效~=nil and self.数据[装备].特效[1] then
			local go
			for i=1,#self.数据[装备].特效 do
				if self.数据[装备].特效[i]=="愤怒" then
					常规提示(id,"#Y/该装备已经拥有愤怒特效了，不要浪费哦。")
					return
				end
				if self.数据[装备].特效[i]=="无级别限制" then
					go=i
				end
			end
			if go~=nil then
				self:删除道具(连接id,id,"道具",道具id,符纸格子,删除数量)
				if #self.数据[装备].特效==1 then
					self.数据[装备].特效[#self.数据[装备].特效+1]="愤怒"
				else
					for i=1,100 do
						local sjs = 取随机数(1,#self.数据[装备].特效)
						if sjs~=go then
							self.数据[装备].特效[sjs]="愤怒"
							break
						end
					end
				end
			else
				常规提示(id,"#Y/不能附魔没有无级别限制的腰带。")
				return
			end
		else
			常规提示(id,"#Y/不能附魔没有无级别限制的腰带。")
			return
		end
		常规提示(id,"#G添加装备特效愤怒成功！")
			发送数据(连接id,3699)
	end
end
function 道具处理类:鉴定专用装备(连接id,id,内容)
	local 包裹类型=内容.类型
	local 道具格子=内容.道具格子
	local 符纸格子 =内容.符纸格子
	local 删除数量=1
	local 道具id=玩家数据[id].角色["道具"][符纸格子]
	local 装备=玩家数据[id].角色["道具"][道具格子]
	if self.数据[道具id] == nil or self.数据[装备] == nil or self.数据[装备].专用提示==nil then
		常规提示(id,"#Y/数据错误请重新鉴定")
	end
	if 包裹类型=="装备鉴定" then
		if self.数据[装备].鉴定 == false then
			if self.数据[道具id].子类 >= self.数据[装备].级别限制 then
				self.数据[装备].鉴定 = true
				self.数据[装备].专用提示=nil
				常规提示(id,"#Y/物品鉴定成功！")
				常规提示(id,"#Y/可以通过梦幻精灵网页版查询装备的属性范围，你有兴趣的话可以去瞧瞧。")
				self:删除道具(连接id,id,"道具",道具id,符纸格子,删除数量)
				装备处理类:装备鉴定处理(连接id,id,self.数据[装备],true)
				发送数据(连接id,3699)
			else
				常规提示(id,"#Y/此图鉴的等级过低无法鉴定该装备")
				return
			end
		else
			常规提示(id,"#Y/这件装备已经鉴定过了请不要重复鉴定")
			return
		end
	end
end
function 道具处理类:取队长权限(id)
	if 玩家数据[id].队伍==0 then
		return true
	elseif 玩家数据[id].队伍~=0 and 玩家数据[id].队长 then
		return true
	else
		return false
	end
end
function 道具处理类:加血处理(连接id,id,加血数值,加血对象,动画,伤势数值)

	if 加血对象==0 then
		玩家数据[id].角色.气血=玩家数据[id].角色.气血+加血数值
		if 伤势数值 ~= nil then
			玩家数据[id].角色.气血上限=玩家数据[id].角色.气血上限+伤势数值
			if 玩家数据[id].角色.气血上限>玩家数据[id].角色.最大气血 then
				玩家数据[id].角色.气血上限=玩家数据[id].角色.最大气血
			end
		end
		if 玩家数据[id].角色.气血>玩家数据[id].角色.气血上限 then
			玩家数据[id].角色.气血=玩家数据[id].角色.气血上限
		end
		发送数据(连接id,36,{动画="加血"})
		发送数据(连接id,47,{玩家数据[id].角色:取气血数据()})
		地图处理类:加入动画(id,玩家数据[id].角色.地图数据.编号,玩家数据[id].角色.地图数据.x,玩家数据[id].角色.地图数据.y,"加血")
	else
		玩家数据[id].召唤兽:加血处理(加血对象,加血数值,连接id,id)
		发送数据(连接id,36,{动画="加血"})
	end
end
function 道具处理类:加魔处理(连接id,id,加血数值,加血对象)
	if 加血对象==0 then
		玩家数据[id].角色.魔法=玩家数据[id].角色.魔法+加血数值
		if 玩家数据[id].角色.魔法>玩家数据[id].角色.最大魔法 then
			玩家数据[id].角色.魔法=玩家数据[id].角色.最大魔法
		end
		发送数据(连接id,36,{动画="加蓝"})
		发送数据(连接id,47,{玩家数据[id].角色:取气血数据()})
		地图处理类:加入动画(id,玩家数据[id].角色.地图数据.编号,玩家数据[id].角色.地图数据.x,玩家数据[id].角色.地图数据.y,"加蓝")
	else
		玩家数据[id].召唤兽:加蓝处理(加血对象,加血数值,连接id,id)
		发送数据(连接id,36,{动画="加蓝"})
	end
end

function 道具处理类:取飞行限制(id)
	if 玩家数据[id].队伍~=0 and 玩家数据[id].队长==false then
		常规提示(id,"只有队长才可以使用飞行道具")
		return true
	elseif self:取禁止飞行(id) then
		常规提示(id,"#Y/您当前无法使用飞行道具")
		return true
	elseif 玩家数据[id].角色.等级<10 then
		常规提示(id,"#Y/您当前等级太低了无法使用飞行道具")
		return true
	elseif 玩家数据[id].队伍 ~= 0 then
		local 队伍id=玩家数据[id].队伍
		for n=1,#队伍数据[队伍id].成员数据 do
			local 成员id = 队伍数据[队伍id].成员数据[n]
			if 玩家数据[成员id].角色.等级 <= 10 then
				常规提示(id, "#Y队伍中有≤10级的成员，无法使用飞行功能")
				return true
			end
			if self:取禁止飞行(成员id) then
				常规提示(id,format("#G/%s当前不能使用飞行道具",玩家数据[成员id].角色.名称))
				return true
			end
		end
	end
	return false
end
function 道具处理类:取禁止飞行(id)
    if 玩家数据[id].摊位数据~=nil then return true end
    local 任务id=玩家数据[id].角色:取任务(110)
    if 任务id~=0 and 任务数据[任务id].分类==2 then return true  end
    if 玩家数据[id].角色:取任务(208)~=0 or 玩家数据[id].角色:取任务(300)~=0 or 玩家数据[id].勾魂索中 or 玩家数据[id].坐牢中 or 玩家数据[id].烤火 then return true end
    -- 新增：科举大赛任务期间禁止传送
    if 玩家数据[id].角色:取任务(7) ~= 0 then return true end
    if 玩家数据[id].角色.剧情.附加.禁止飞行 then return true end
    if 玩家数据[id].角色.跑商 then return true end
    if feixingdaoju[玩家数据[id].角色.地图数据.编号] then
        return true
    end
    return false
end
function 道具处理类:神兽兑换(id,事件,名称)
	if 玩家数据[id].道具:判定背包道具(id,"神兜兜",99) and 玩家数据[id].道具:判定背包道具(id,"灵兜兜",1) then
		玩家数据[id].道具:消耗背包道具(玩家数据[id].连接id,id,"神兜兜",99)
		玩家数据[id].道具:消耗背包道具(玩家数据[id].连接id,id,"灵兜兜",1)
		local 链接 = {提示=format("#R%s#W成功集齐了99个神兜兜和1个灵兜兜在#R%s#W处换取了一只",玩家数据[id].角色.名称,名称),频道="xt",结尾="#117"}
		玩家数据[id].召唤兽:添加神兽(名称,事件,nil,链接)
	else
		常规提示(id,"#Y/大侠还没有集齐“神兜兜”或是“灵兜兜”，无法兑换神兽")
	end
end
function 道具处理类:给予随机法宝(id)
	local 参数=取随机数()
	local 名称=""
	local 等级=0
	if 参数<=30 then
		名称={"碧玉葫芦","五色旗盒","飞剑","拭剑石","金甲仙衣","惊魂铃","嗜血幡","风袋","清心咒","九黎战鼓","盘龙壁","神行飞剑","汇灵盏","天师符","织女扇"}
		等级=1
	elseif 参数<=65 then
		名称={"发瘟匣","断线木偶","五彩娃娃","七杀","金刚杵","兽王令","摄魂"}
		等级=2
	else
		名称={"失心钹","五火神焰印","九幽","普渡","鬼泣","月光宝盒","混元伞","无魂傀儡","苍白纸人","聚宝盆","乾坤玄火塔","无尘扇","无字经","干将莫邪","慈悲","救命毫毛","伏魔天书","镇海珠","奇门五行令"}
		等级=3
	end
	名称=名称[取随机数(1,#名称)]
	self:给予法宝(id,名称)
end
function 道具处理类:给予法宝(id,名称,消费,消费方式,消费内容)
	if 消费方式 ~= nil then
		if 消费方式 == "银子" then
			if not 玩家数据[id].角色:扣除银子(消费,0,0,消费内容,1) then
				常规提示(id,"你没有那么多的银子")
				return false
			end
		end
	end
	local 识别码=id.."_"..os.time().."_"..取随机数(1000,9999999).."_"..随机序列
	随机序列=随机序列+1
	local 道具格子=玩家数据[id].角色:取法宝格子()
	if 道具格子==0 then
		常规提示(id,"您的法宝栏已经满啦")
		return
	end
	local 道具id=self:取新编号()
	self.数据[道具id]=物品类()
	self.数据[道具id]:置对象(名称)
	玩家数据[id].角色.法宝[道具格子]=道具id
	self.数据[道具id].识别码=识别码
	local 道具 = 取物品数据(名称)
	self.数据[道具id].总类=1000
	self.数据[道具id].分类=道具[3]
	self.数据[道具id].使用 = 道具[5]
	self.数据[道具id].特技 = 道具[6]
	self.数据[道具id].气血 = 0
	self.数据[道具id].魔法 = 取灵气上限(道具[3])
	self.数据[道具id].角色限制 = 道具[7] or 0
	self.数据[道具id].五行 = 取五行()
	self.数据[道具id].伤害 = 道具[8] or 0
	self.数据[道具id].当前经验=0
	self.数据[道具id].升级经验=法宝经验[道具[3]][1]
	常规提示(id,"#Y你获得了新的法宝#R"..名称)
	return true
end
function 道具处理类:给予灵宝(id,名称,消费,消费方式,消费内容)
	if 消费方式 ~= nil then
		if not 玩家数据[id].角色:扣除银子(消费,0,0,消费内容,1) then
			常规提示(id,"你没有那么多的银子")
			return false
		end
	end
	if 名称 == nil then
		名称 = 取随机灵宝()
	end
	local 识别码=id.."_"..os.time().."_"..取随机数(1000,9999999).."_"..随机序列
	随机序列=随机序列+1
	local 道具格子=玩家数据[id].角色:取灵宝格子()
	if 道具格子==0 then
		常规提示(id,"您的灵宝栏已经满啦")
		return
	end
	local 道具id=self:取新编号()
	self.数据[道具id]=物品类()
	self.数据[道具id]:置对象(名称)
	玩家数据[id].角色.灵宝[道具格子]=道具id
	self.数据[道具id].识别码=识别码
	local 道具 = 取物品数据(名称)
	self.数据[道具id].总类=1002
	self.数据[道具id].分类=道具[3]
	self.数据[道具id].使用 = 道具[5]
	self.数据[道具id].特技 = 道具[6]
	self.数据[道具id].气血 = 0
	self.数据[道具id].魔法 = 取灵气上限(道具[3])
	self.数据[道具id].当前经验=0
	self.数据[道具id].升级经验=灵宝经验[self.数据[道具id].分类][1]
	常规提示(id,"#Y你获得了新的灵宝#R"..名称)
	return true
end
function 道具处理类:给予任务道具(id,名称,数量,参数,附加)
	-- if 玩家数据[id].连接id=="小伙伴" and 玩家数据[id].角色.道具==nil and 玩家数据[id].角色.主人id then
	-- end
	local 识别码=id.."_"..os.time().."_"..取随机数(1000,9999999).."_"..随机序列
	local 道具id
	随机序列=随机序列+1
	local 道具格子=玩家数据[id].角色:取任务格子()
	if 道具格子==0 then
		常规提示(id,"您的任务栏物品已经满啦")
		return false
	else
			local 重置id=0
		for n=1,20 do
			if 重置id==0 and 玩家数据[id].角色.任务包裹[n]~=nil and self.数据[玩家数据[id].角色.任务包裹[n]]~=nil and self.数据[玩家数据[id].角色.任务包裹[n]].名称==名称 and self.数据[玩家数据[id].角色.任务包裹[n]].数量~=nil then
				if 数量 == nil then 数量 =1 end
				if self.数据[玩家数据[id].角色.任务包裹[n]].数量+数量<=99 then
					数量=self.数据[玩家数据[id].角色.任务包裹[n]].数量+数量
					道具id=玩家数据[id].角色.任务包裹[n]
					识别码=self.数据[玩家数据[id].角色.任务包裹[n]].识别码
					重置id=1
				end
			end
		end
		if 重置id==0 then
			道具id=self:取新编号()
			self.数据[道具id]=物品类()
			self.数据[道具id]:置对象(名称)
			玩家数据[id].角色.任务包裹[道具格子]=道具id
		end
		临时道具 = 取物品数据(名称)
		临时道具.总类=临时道具[2]
		临时道具.子类=临时道具[4]
		临时道具.分类=临时道具[3]
		if self.数据[道具id].可叠加 then
			if 数量 == nil then
				self.数据[道具id].数量=1
				常规提示(id,"#Y/你得到了#R1#Y个#R"..self.数据[道具id].名称.."#Y已存入任务栏")
			else
				self.数据[道具id].数量=数量
				常规提示(id,"#Y/你得到了#R"..数量.."#Y个#R"..self.数据[道具id].名称.."#Y已存入任务栏")
			end
		else
			常规提示(id,"#Y/你得到了#R1#Y个#R"..self.数据[道具id].名称.."#Y已存入任务栏")
		end
		-- local rwdj={道具={}}
		-- for n=1,20 do
		-- 	if 玩家数据[id].角色.任务包裹[n]~=nil then
		-- 		if self.数据[玩家数据[id].角色.任务包裹[n]]==nil then
		-- 			玩家数据[id].角色.任务包裹[n]=nil
		-- 		else
		-- 			rwdj.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.任务包裹[n]]))
		-- 		end
		-- 	end
		-- end
		-- if #rwdj.道具~=0 then
		-- 	发送数据(玩家数据[id].连接id,3531,rwdj)
		-- end
	end
end
function 道具处理类:临时背包索取()
    local fhz = {道具 = {}}
    local hasItems = false
    -- 检查临时包裹是否存在且不为空
    if 玩家数据[self.玩家id].角色.临时包裹 then
        for k, v in pairs(玩家数据[self.玩家id].角色.临时包裹) do
            if self.数据[v] then
                fhz.道具[k] = table.copy(self.数据[v])
                hasItems = true
            else
                玩家数据[self.玩家id].角色.临时包裹[k] = nil
            end
        end
    end
    -- 只有当实际有道具时才返回true
    return {fhz, next(fhz.道具) ~= nil}
end


function 道具处理类:更新背包状态(id)
    local fhz = self:临时背包索取()
    if fhz[2] then
        发送数据(玩家数据[id].连接id, 301, {"临时背包", fhz[1]})
    else
        发送数据(玩家数据[id].连接id, 302, {"临时背包"})
        发送数据(玩家数据[id].连接id, 303, {"底图框", "临时背包闪烁", false})
    end
end

function 道具处理类:临时背包处理(连接id, id, sj)

    if sj.方式 == "获取" then
        local 格子 = 玩家数据[id].角色:取空道具格子数量()
        if 格子 == 0 then
            常规提示(id, "#Y/你身上的包裹没有足够的空间")
            return
        end

        local 选中 = 玩家数据[id].角色.临时包裹[sj.选中]
        if 选中 ~= nil and self.数据[选中] ~= nil then
            self:给予道具(id, nil, nil, nil, nil, nil, table.copy(self.数据[选中]))
            玩家数据[id].角色.临时包裹[sj.选中] = nil
            self:更新背包状态(id)
        end

    elseif sj.方式 == "全部获取" then
        local 格子 = 玩家数据[id].角色:取空道具格子数量()
        for i, 道具 in pairs(玩家数据[id].角色.临时包裹) do
            if 格子 <= 0 then
                常规提示(id, "#Y/你身上的包裹没有足够的空间")
                break
            end
            if self.数据[道具] ~= nil then
                self:给予道具(id, nil, nil, nil, nil, nil, table.copy(self.数据[道具]))
                玩家数据[id].角色.临时包裹[i] = nil
                格子 = 格子 - 1
            end
        end
        self:更新背包状态(id)

    elseif sj.方式 == "清空" then
        for k, v in pairs(玩家数据[id].角色.临时包裹) do
            self.数据[v] = nil
        end
        玩家数据[id].角色.临时包裹 = {}
        self:更新背包状态(id)

    elseif sj.方式 == "索取" then
        local 临时背包内容 = self:临时背包索取()
        if 临时背包内容 and #临时背包内容[1] >= 80 then
            常规提示(id, "#Y/临时背包已满，无法索取更多物品")
            return
        end
        发送数据(玩家数据[id].连接id, 300, {"临时背包", 临时背包内容[1]})
    end
end


function 道具处理类:卸下装备(连接id,id,数据)
    if 数据.类型 == "任务包裹" then
        return 常规提示(id,"#Y这件物品不能移动到任务栏")
    end
    local 道具格子 = 玩家数据[id].角色:取道具格子1(数据.类型)
    if 道具格子 == 0 then
        常规提示(id,"您的道具栏物品已经满啦")
        return 0
    end
    if 数据.灵饰 then
        self:卸下灵饰(连接id,id,道具id,道具格子,数据)
        return
    end
    local 道具id = 玩家数据[id].角色.装备[数据.道具]
    -- 只在出错时打印信息
    if not 道具id then
        print("=== 卸下装备错误 ===")
        print("错误原因：装备位置为空")
        print("玩家ID:", id)
        print("装备位置:", 数据.道具)
        print("装备类型:", 数据.类型)
        return
    end
    if not self.数据[道具id] then
        print("=== 卸下装备错误 ===")
        print("错误原因：找不到道具数据")
        print("玩家ID:", id)
        print("道具ID:", 道具id)
        print("装备位置:", 数据.道具)
        print("装备类型:", 数据.类型)
        -- 打印玩家当前装备信息
        print("玩家装备栏状态:")
        for k,v in pairs(玩家数据[id].角色.装备) do
            print("位置:", k, "道具ID:", v)
        end
        return
    end
    -- 尝试执行卸下装备，并捕获可能的错误
    local success, err = pcall(function()
        玩家数据[id].角色:卸下装备(self.数据[道具id], self.数据[道具id].分类)
    end)
    if not success then
        print("=== 卸下装备错误 ===")
        print("错误原因：执行卸下装备时出错")
        print("玩家ID:", id)
        print("道具ID:", 道具id)
        print("道具名称:", self.数据[道具id].名称)
        print("道具分类:", self.数据[道具id].分类)
        print("错误信息:", err)
        return
    end
    玩家数据[id].角色.装备[数据.道具] = nil
    玩家数据[id].角色[数据.类型][道具格子] = 道具id
    玩家数据[id].角色:刷新信息()
    self:刷新道具行囊(id,数据.类型)
    发送数据(玩家数据[id].连接id,3503,玩家数据[id].角色:取装备数据())
    if 数据.道具 == 3 then
        发送数据(玩家数据[id].连接id,3505)
        地图处理类:更新武器(id)
    end
    发送数据(玩家数据[id].连接id,12)
    return 道具格子
end
function 道具处理类:佩戴灵饰(连接id,id,道具id,数据)
	local 物品=取物品数据(self.数据[道具id].名称)
	local 级别=物品[5]
	if 级别>玩家数据[id].角色.等级 then
		if self.数据[道具id].特效 =="超级简易" and 级别<=玩家数据[id].角色.等级+15 then
		else
			常规提示(id,"#Y你当前的等级不足以佩戴这样的灵饰")
			return
		end
	end
	if not self.数据[道具id].鉴定 then
		常规提示(id,"#Y没有鉴定的灵饰无法佩戴")
		return
	end
	if self.数据[道具id].专用 and  self.数据[道具id].专用~=id then
		常规提示(id,"#Y这个灵饰id与你不匹配无法穿戴")
		return
	end
	if 玩家数据[id].角色.灵饰[self.数据[道具id].子类]==nil then
		玩家数据[id].角色.灵饰[self.数据[道具id].子类]=道具id
		玩家数据[id].角色:佩戴灵饰(self.数据[道具id])
		玩家数据[id].角色[数据.类型][数据.道具]=nil
	else
		local 道具id1=玩家数据[id].角色.灵饰[self.数据[道具id].子类]
		玩家数据[id].角色:卸下灵饰(self.数据[道具id1])
		玩家数据[id].角色.灵饰[self.数据[道具id].子类]=道具id
		玩家数据[id].角色:佩戴灵饰(self.数据[道具id])
		玩家数据[id].角色[数据.类型][数据.道具]=道具id1
	end
	self:刷新道具行囊(id,数据.类型)
	发送数据(玩家数据[id].连接id,3503,玩家数据[id].角色:取装备数据())
	发送数据(玩家数据[id].连接id,3506,玩家数据[id].角色:取灵饰数据())
	发送数据(连接id,47,{玩家数据[id].角色:取气血数据()})
	发送数据(玩家数据[id].连接id,12)
end
function 道具处理类:卸下灵饰(连接id,id,道具id,道具格子,数据)
	玩家数据[id].角色:卸下灵饰(self.数据[玩家数据[id].角色.灵饰[数据.道具]])
	玩家数据[id].角色[数据.类型][道具格子]=玩家数据[id].角色.灵饰[数据.道具]
	玩家数据[id].角色.灵饰[数据.道具]=nil
	self:刷新道具行囊(id,数据.类型)
	发送数据(玩家数据[id].连接id,3503,玩家数据[id].角色:取装备数据())
	发送数据(玩家数据[id].连接id,3506,玩家数据[id].角色:取灵饰数据())
	发送数据(连接id,47,{玩家数据[id].角色:取气血数据()})
	发送数据(玩家数据[id].连接id,12)
end
function 道具处理类:佩戴装备(连接id,id,数据)
	local 道具id=玩家数据[id].角色[数据.类型][数据.道具]
	if self.数据[道具id] == nil then
		return
	end
	if self.数据[道具id].总类 == 2 and self.数据[道具id].灵饰~=nil then
		self:佩戴灵饰(连接id,id,道具id,数据)
		return
	end
	local 装备条件=self:可装备(self.数据[道具id],self.数据[道具id].分类,数据.角色,id)
	if 装备条件~=true then
		发送数据(连接id,7,装备条件)
		return 0
	else
		if 玩家数据[id].角色.装备[self.数据[道具id].分类]~=nil then
			local 道具id1=玩家数据[id].角色.装备[self.数据[道具id].分类]
			玩家数据[id].角色:卸下装备(self.数据[道具id1],self.数据[道具id1].分类)
			玩家数据[id].角色.装备[self.数据[道具id].分类]= 道具id
			玩家数据[id].角色:穿戴装备(self.数据[道具id],self.数据[道具id].分类)
			玩家数据[id].角色[数据.类型][数据.道具]=道具id1
		else
			玩家数据[id].角色.装备[self.数据[道具id].分类]= 道具id
			玩家数据[id].角色:穿戴装备(self.数据[道具id],self.数据[道具id].分类)
			玩家数据[id].角色[数据.类型][数据.道具]=nil
		end
		玩家数据[id].角色:检查临时属性()
	end
	self:刷新道具行囊(id,数据.类型)
	发送数据(玩家数据[id].连接id,3503,玩家数据[id].角色:取装备数据())
	if self.数据[道具id].分类==3 then
		发送数据(玩家数据[id].连接id,3504)
		地图处理类:更新武器(id,self.数据[玩家数据[id].角色.装备[3]])
	end
	发送数据(玩家数据[id].连接id,12	)
end
function 道具处理类:坐骑装饰佩戴(id,加血对象,道具格子,包裹类型)
  local 道具id=玩家数据[id].角色[包裹类型][道具格子]
  if 玩家数据[id].角色.坐骑列表[加血对象].模型 ~= self.数据[道具id].装备坐骑 then
    return 常规提示(id,"#Y这个坐骑无法装备这个装饰")
  end
  if 玩家数据[id].角色.坐骑列表[加血对象].饰品 == nil then
    玩家数据[id].角色.坐骑列表[加血对象].饰品 = self.数据[道具id]
    self.数据[道具id] = nil
  else
    local 临时道具 = 玩家数据[id].角色.坐骑列表[加血对象].饰品
    玩家数据[id].角色.坐骑列表[加血对象].饰品 = self.数据[道具id]
    self.数据[道具id] = 临时道具
  end
  发送数据(玩家数据[id].连接id,62,{编号=加血对象,坐骑数据=玩家数据[id].角色.坐骑列表[加血对象]})
  self:刷新道具行囊(id,包裹类型)
  发送数据(玩家数据[id].连接id,28)
  玩家数据[id].角色.坐骑=table.loadstring(table.tostring(玩家数据[id].角色.坐骑列表[加血对象]))
  发送数据(玩家数据[id].连接id,60,玩家数据[id].角色.坐骑)
  地图处理类:更新坐骑(id,玩家数据[id].角色.坐骑)
end
function 道具处理类:坐骑装饰卸下(连接id,id,数据)
	local 角色=数据.角色
	local 类型=数据.类型
	local 道具=数据.道具
	--local 道具id=玩家数据[id].角色[类型][道具]
	local bb=数据.编号
	local 道具格子=玩家数据[id].角色:取道具格子()
	if 道具格子==0 then
		常规提示(id,"您的道具栏物品已经满啦")
		return
	else
		local 临时id=self:取新编号()
		self.数据[临时id]=玩家数据[id].角色.坐骑列表[bb].饰品
		玩家数据[id].角色.道具[道具格子]=临时id
		玩家数据[id].角色.坐骑列表[bb].饰品 = nil
		发送数据(玩家数据[id].连接id,62,{编号=bb,坐骑数据=玩家数据[id].角色.坐骑列表[bb]})
		self:刷新道具行囊(id,数据.类型)
		发送数据(连接id,28)
		玩家数据[id].角色.坐骑=table.loadstring(table.tostring(玩家数据[id].角色.坐骑列表[bb]))
		发送数据(玩家数据[id].连接id,60,玩家数据[id].角色.坐骑)
		地图处理类:更新坐骑(id,玩家数据[id].角色.坐骑)
	end
end
function 道具处理类:可装备(i1,i2,类型,id)
    if not i1 or not i2 then
        return "#Y/装备或装备槽信息无效"
    end
	if i2 > 6 and 类型 == "主角" then
		return "#Y/该装备与你的种族不符"
	elseif i2 < 6 and 类型 == "召唤兽" then
		return "#Y/召唤兽不能穿戴该装备"
	end
	if i1.总类 ~= 2 then
		return "#Y/这个物品不可以装备"
	end
	if not i1.鉴定 then
		return "#Y/该装备未鉴定，无法佩戴"
	end
	if i1.专用~=nil and i1.专用~=id then
		return "#Y/你无法佩戴他人的专用装备"
	end
	if type( i1.级别限制) ~=  "number"  then
		return "#Y/该装备的等级数据错误请联系管理员"
	end
	if i1.耐久~=nil and i1.耐久<=0  then
		return 常规提示(id,"#Y/该装备耐久不足，无法穿戴")
	end
	if i1.修理失败~=nil and i1.修理失败==3 and i1.耐久<=0 then
		return 常规提示(id,"#Y/该装备因修理失败过度，而无法使用！")
	end
	if i1 ~= nil and i1.分类 == i2 then
		if i2 == 1 or i2 == 4 then
			if i1.性别限制 ~= 0 and i1.性别限制 == 玩家数据[id].角色.性别 then
				if i1.级别限制 == 0 or 玩家数据[id].角色.等级 >= i1.级别限制 then
					return true
				end
				if i1.特效 then
					for i=1,#i1.特效 do
						if i1.特效[i]=="无级别限制" then
							return true
						elseif i1.特效[i] == "简易" and 玩家数据[id].角色.等级+5 >= i1.级别限制 then
							return true
						end
					end
				end
				return "#Y/你的等级不够呀"
			else
				return "#Y/该装备您无法使用呀"
			end
		elseif i2 == 2 or i2 == 5 or i2 == 6 then
			if i1.级别限制 == 0 or 玩家数据[id].角色.等级 >= i1.级别限制 then
				return true
			end
			if i1.特效 then
				for i=1,#i1.特效 do
					if i1.特效[i]=="无级别限制" then
						return true
					elseif i1.特效[i] == "简易" and 玩家数据[id].角色.等级+5 >= i1.级别限制 then
						return true
					end
				end
			end
			return "#Y/你的等级不够呀"
		elseif i2 == 3 then
			if i1.角色限制 ~= 0 and (i1.角色限制[1] == 玩家数据[id].角色.模型 or i1.角色限制[2] == 玩家数据[id].角色.模型 or i1.角色限制[3] == 玩家数据[id].角色.模型) then
				if i1.级别限制 == 0 or 玩家数据[id].角色.等级 >= i1.级别限制 then
					return true
				end
				if i1.特效 then
					for i=1,#i1.特效 do
						if i1.特效[i]=="无级别限制" then
							return true
						elseif i1.特效[i] == "简易" and 玩家数据[id].角色.等级+5 >= i1.级别限制 then
							return true
						end
					end
				end
				return "#Y/你的等级不够呀"
			else
				return "#Y/该装备您无法使用呀"
			end
		end
	end
	return false
end
function 道具处理类:取可叠加道具(mc,sl)
	for n, v in pairs(self.数据) do
		if self.数据[n].名称==mc and self.数据[n].可叠加 and self.数据[n].数量 + tonumber(sl) <= 99 then
			return n
		end
	end
	return 0
end

-- 检查跑商商品是否可以叠加（需要单价一致）
function 道具处理类:检查跑商商品可叠加(道具id, 商品名称)
	if not 道具id or not self.数据[道具id] then
		return false
	end

	local 现有道具 = self.数据[道具id]

	-- 如果不是跑商商品，返回true（允许正常叠加逻辑处理）
	if not string.find(商品名称, "商品") then
		return true
	end

	-- 如果是跑商商品，检查单价是否一致
	if 现有道具.单价 and 跑商 and 跑商[商品名称] then
		return 现有道具.单价 == 跑商[商品名称]
	end

	-- 如果没有单价信息，允许叠加（向后兼容）
	return true
end
function 道具处理类:判定装备条件()
	for n, v in pairs(玩家数据[self.玩家id].角色.装备) do
		local 格子 = 玩家数据[self.玩家id].角色.装备[n]
		if self.数据[格子] ~= nil then
			local 装备条件=self:可装备(self.数据[格子],self.数据[格子].分类,"主角",self.玩家id)
			if 装备条件 ~= true then
				常规提示(self.玩家id,"#Y你的等级不够，不能装备"..self.数据[格子].名称)
			end
		end
	end
end
function 道具处理类:更新()
	local 更新属性 = false
	for n, v in pairs(玩家数据[self.玩家id].角色.装备) do
		local 格子 = 玩家数据[self.玩家id].角色.装备[n]
		if self.数据 and self.数据[格子] ~= nil then
			if self.数据[格子].临时附魔 ~= nil  then
				for k,v in pairs(self.数据[格子].临时附魔) do
					if v.数值 and v.时间 and v.时间 < os.time() then
						玩家数据[self.玩家id].角色:清除装备附魔属性(self.数据[格子],self.数据[格子].分类,k,v.数值)
						self.数据[格子].临时附魔[k] = nil
						常规提示(self.玩家id,"#Y你装备上的附魔特效消失了！")
					end
				end
			end
			if self.数据[格子].限时 ~= nil and self.数据[格子].限时 < os.time() then
				玩家数据[self.玩家id].角色:卸下装备(self.数据[格子],self.数据[格子].分类,"0")
				玩家数据[self.玩家id].角色.装备[n] = nil
				self.数据[格子] = nil
				if 格子==3 then
					发送数据(玩家数据[self.玩家id].连接id,3505)
					地图处理类:更新武器(self.玩家id)
				end
				更新属性 = true
			end
			if self.数据[格子].耐久 ~= nil and self.数据[格子].耐久 <= 0 and self.数据[格子].刷新属性 == nil then
				玩家数据[self.玩家id].角色:清除装备属性(self.数据[格子],self.数据[格子].分类,"0")
				更新属性 = true
			end
		end
	end
	for n, v in pairs(玩家数据[self.玩家id].角色.灵饰) do
		local 格子 = 玩家数据[self.玩家id].角色.灵饰[n]
		if self.数据[格子].耐久 ~= nil and self.数据[格子].耐久 <= 0 and self.数据[格子].刷新属性 == nil then
			玩家数据[self.玩家id].角色:清除灵饰属性(self.数据[格子])
			更新属性 = true
		end
	end
	for n, v in pairs(self.数据) do
		if self.数据[n].临时 ~= nil then
			if type(self.数据[n].临时.时间) ~= "number" then
				self.数据[n].临时.时间 = os.time()
			end
			if self.数据[n].临时.时间 < os.time() then
				self.数据[n].临时 = nil
				道具刷新(self.玩家id)
			end
		end
	end
	if 更新属性 then
		发送数据(玩家数据[self.玩家id].连接id,3503,玩家数据[self.玩家id].角色:取装备数据())
		发送数据(玩家数据[self.玩家id].连接id,31,玩家数据[self.玩家id].角色:取总数据1())
	end
end
function 道具处理类:丢弃道具(连接id, id, 数据)
    local 类型 = 数据.类型
    local wpid = 数据.物品
    -- 检查 wpid 和 类型 是否有效
    if not 类型 or not wpid then
        return
    end
    -- 获取该物品的名称
    local 物品 = self.数据[玩家数据[id].角色[类型][wpid]]
    -- 如果物品为空，直接返回
    if not 物品 then
        return
    end
    -- 如果物品是"帮派银票"，提示不能丢弃
    if 物品.名称 == "帮派银票" then
        常规提示(id, "#Y该物品无法丢弃")
        self:刷新道具行囊(id, 类型)
        return
    end
    -- 确保玩家角色中的物品存在并删除
    if 玩家数据[id].角色[类型][wpid] then
        self.数据[玩家数据[id].角色[类型][wpid]] = nil
        玩家数据[id].角色[类型][wpid] = nil
    end
    -- 刷新道具行囊
    self:刷新道具行囊(id, 类型)
end

function 道具处理类:道具转移(连接id,id,数据)
	local 临时格子= 0
	local 允许放置=true
	if not self.数据[玩家数据[id].角色[数据.抓取类型][数据.序列]] then
		return
	end
	if 数据.放置类型 == "道具" then
		临时格子=玩家数据[id].角色:取道具格子()
		if 临时格子 == 0 then  常规提示(id,"#Y道具背包已满") end
	elseif 数据.放置类型 == "法宝" then
		临时格子=玩家数据[id].角色:取法宝格子()
		if 临时格子 == 0 then  常规提示(id,"#Y法宝背包已满") end
	elseif 数据.放置类型 == "行囊" then
		临时格子=玩家数据[id].角色:取行囊格子()
		if 临时格子 == 0 then  常规提示(id,"#Y行囊背包已满") end
		if  self.数据[玩家数据[id].角色[数据.抓取类型][数据.序列]].总类 == 150  then
			允许放置 = false
			常规提示(id,"#Y任务物品不能移动到这里")
		end
		if  self.数据[玩家数据[id].角色[数据.抓取类型][数据.序列]].总类 == "帮派银票" or self.数据[玩家数据[id].角色[数据.抓取类型][数据.序列]].总类 == "跑商商品" then
			允许放置 = false
			常规提示(id,"#Y该物品不能存放到这里")
		end
	elseif 数据.放置类型 == "任务包裹" then
		临时格子=玩家数据[id].角色:取任务格子()
		if 临时格子 == 0 then  常规提示(id,"#Y任务背包已满") end
		if  self.数据[玩家数据[id].角色[数据.抓取类型][数据.序列]].总类 ~= 150 then
			允许放置 = false
			常规提示(id,"#Y这件物品不能移动到任务栏")
		end
	end
	if 临时格子 ~= 0 and 允许放置 then
		玩家数据[id].角色[数据.放置类型][临时格子] = 玩家数据[id].角色[数据.抓取类型][数据.序列]
		玩家数据[id].角色[数据.抓取类型][数据.序列]=nil
	end
	发送数据(连接id,3699)
end
function 道具处理类:道具格子互换(连接id,id,数据)
	-- 添加操作锁，防止并发操作导致的数据竞态
	local 锁定键 = "道具操作锁_" .. tostring(id)
	if 玩家数据[id] and 玩家数据[id][锁定键] then
		-- 操作正在进行中，忽略此次请求
		__S服务:输出("道具格子互换 - 操作被锁定，玩家ID:" .. tostring(id))
		return
	end
	
	-- 基础参数检查
	if not 数据 or not 数据.抓取类型 or not 数据.抓取id or not 数据.放置类型 then
		__S服务:输出("道具格子互换错误 - 参数无效，玩家ID:" .. tostring(id))
		return
	end

	-- 自动分配空格子（容错处理）
	if not 数据.放置id then
		local 空格子 = 0
		if 数据.放置类型 == "道具" then
			空格子 = 玩家数据[id].角色:取道具格子()
		elseif 数据.放置类型 == "法宝" then
			空格子 = 玩家数据[id].角色:取法宝格子()
		elseif 数据.放置类型 == "行囊" then
			空格子 = 玩家数据[id].角色:取行囊格子()
		elseif 数据.放置类型 == "任务包裹" then
			空格子 = 玩家数据[id].角色:取任务格子()
		end

		if 空格子 == 0 then
			__S服务:输出("道具格子互换错误 - " .. 数据.放置类型 .. "已满，玩家ID:" .. tostring(id))
			return
		end

		数据.放置id = 空格子
		__S服务:输出("道具格子互换 - 自动分配空格子，玩家ID:" .. tostring(id) .. " 类型:" .. 数据.放置类型 .. " 格子:" .. 空格子)
	end
	
	if 数据.放置类型==数据.抓取类型 and 数据.放置id==数据.抓取id then
		发送数据(连接id,3699)
		return
	end
	
	-- 设置操作锁
	玩家数据[id][锁定键] = true
	
	-- 安全获取道具ID，添加容器检查
	if not 玩家数据[id].角色[数据.抓取类型] then
		__S服务:输出("道具格子互换错误 - 抓取容器不存在，玩家ID:" .. tostring(id) .. " 类型:" .. tostring(数据.抓取类型))
		玩家数据[id][锁定键] = nil
		return
	end
	
	local 抓取道具id = 玩家数据[id].角色[数据.抓取类型][数据.抓取id]
	-- 检查道具ID有效性
	if 抓取道具id == nil then
		__S服务:输出("道具格子互换错误 - 玩家ID:" .. id .. " 抓取类型:" .. tostring(数据.抓取类型) .. " 抓取位置:" .. tostring(数据.抓取id) .. " 道具ID:nil")
		常规提示(id,"#Y抓取位置没有道具")
		玩家数据[id][锁定键] = nil
		return
	end
	if self.数据[抓取道具id] == nil then
		__S服务:输出("道具格子互换错误 - 玩家ID:" .. id .. " 抓取类型:" .. tostring(数据.抓取类型) .. " 抓取位置:" .. tostring(数据.抓取id) .. " 道具ID:" .. tostring(抓取道具id) .. " 道具数据不存在")
		常规提示(id,"#Y道具数据异常")
		-- 清理无效道具引用
		玩家数据[id].角色[数据.抓取类型][数据.抓取id] = nil
		发送数据(连接id,3699)
		玩家数据[id][锁定键] = nil
		return
	end
	if 数据.放置类型~=数据.抓取类型 and self.数据[抓取道具id].总类==1000 then
		if self.数据[抓取道具id].分类~=1 then
			常规提示(id,"#Y只有一级法宝才可以移动")
			玩家数据[id][锁定键] = nil
			return
		end
	end
	if 数据.放置类型=="法宝" and self.数据[抓取道具id].总类~=1000 then
		常规提示(id,"#Y法宝栏只可以放入法宝哟")
		玩家数据[id][锁定键] = nil
		return
	end
	if 玩家数据[id].角色[数据.放置类型][数据.放置id]==nil then
		玩家数据[id].角色[数据.放置类型][数据.放置id]=玩家数据[id].角色[数据.抓取类型][数据.抓取id]
		玩家数据[id].角色[数据.抓取类型][数据.抓取id]=nil
	else
		local 放置id=玩家数据[id].角色[数据.放置类型][数据.放置id]
		self.允许互换=true
		local 放置id=玩家数据[id].角色[数据.放置类型][数据.放置id]
		local 抓取id=玩家数据[id].角色[数据.抓取类型][数据.抓取id]
		-- 道具叠加逻辑 - 添加数据有效性检查
		if self.数据[放置id] and self.数据[抓取id] and 
		   self.数据[放置id].名称==self.数据[抓取id].名称 and 
		   self.数据[抓取id].数量~=nil and self.数据[放置id].数量~=nil then
			if self.数据[抓取id].数量<99 and self.数据[放置id].数量<99 then
				if self.数据[抓取id].阶品~= nil and self.数据[放置id].阶品~=nil and self.数据[抓取id].阶品~=self.数据[放置id].阶品 then
					常规提示(id,"#Y不同阶品的物品，无法叠加")
					发送数据(连接id,3699)
					道具刷新(id)
					玩家数据[id][锁定键] = nil
					return
				elseif self.数据[抓取id].灵气~= nil and self.数据[放置id].灵气~=nil and self.数据[抓取id].灵气~=self.数据[放置id].灵气 then
					常规提示(id,"#Y不同灵气的物品，无法叠加")
					发送数据(连接id,3699)
					道具刷新(id)
					玩家数据[id][锁定键] = nil
					return
				elseif self.数据[抓取id].级别限制~= nil and self.数据[放置id].级别限制~=nil and self.数据[抓取id].级别限制~=self.数据[放置id].级别限制 and self.数据[抓取id].名称=="钨金" and self.数据[放置id].名称=="钨金" then
					常规提示(id,"#Y不同等级的物品，无法叠加")
					发送数据(连接id,3699)
					道具刷新(id)
					玩家数据[id][锁定键] = nil
					return
				end
				-- 原子性操作：道具叠加
				if self.数据[抓取id] and self.数据[放置id] and self.数据[抓取id].数量+self.数据[放置id].数量<=99 then
					self.数据[放置id].数量=self.数据[放置id].数量+self.数据[抓取id].数量
					self.数据[抓取id]=nil
					玩家数据[id].角色[数据.抓取类型][数据.抓取id]=nil
					self.允许互换=false
				elseif self.数据[抓取id] and self.数据[放置id] then
					local 临时数量=self.数据[抓取id].数量+self.数据[放置id].数量
					local 临时数量1=临时数量-99
					self.数据[放置id].数量=99
					self.数据[抓取id].数量=临时数量1
					self.允许互换=false
				end
			end
		end
		if self.允许互换 then
			玩家数据[id].角色[数据.放置类型][数据.放置id]=玩家数据[id].角色[数据.抓取类型][数据.抓取id]
			玩家数据[id].角色[数据.抓取类型][数据.抓取id]=放置id
		end
	end
	
	-- 释放操作锁
	玩家数据[id][锁定键] = nil
	
	self:刷新道具行囊(id,数据.放置类型)
end
function 道具处理类:刷新道具行囊(id,类型)
	if 类型=="道具" then
		self:索要道具(玩家数据[id].连接id,id)
	elseif 类型=="法宝" then
		self:索要法宝(玩家数据[id].连接id,id)
	elseif 类型=="任务包裹" then
		self:索要任务(玩家数据[id].连接id,id)
	else
		self:索要行囊(玩家数据[id].连接id,id)
	end
	发送数据(玩家数据[id].连接id,3699)
  	道具刷新(id)
end
function 道具处理类:索要灵犀玉(id)
	self.发送数据={道具={}}
	for n=1,20 do
		if 玩家数据[id].角色.道具[n]~=nil then
			if self.数据[玩家数据[id].角色.道具[n]]~=nil and self.数据[玩家数据[id].角色.道具[n]].名称=="灵犀玉" then
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.道具[n]]))
			end
		end
	end
	return self.发送数据
end
function 道具处理类:索要法宝(连接id,id)
	self.发送数据={法宝={},佩戴={},灵宝={},灵宝佩戴={},神器={}}
	for n=1,180 do
		if 玩家数据[id].角色.法宝[n]~=nil then
			if self.数据[玩家数据[id].角色.法宝[n]]==nil then
				玩家数据[id].角色.法宝[n]=nil
			else
				self.发送数据.法宝[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.法宝[n]]))
			end
		end
	end
	for n=1,4 do
		if 玩家数据[id].角色.法宝佩戴[n]~=nil then
			self.发送数据.佩戴[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.法宝佩戴[n]]))
		end
	end
	if 玩家数据[id].神器.数据.是否有 and 玩家数据[id].角色.门派~="无门派" then
		self.发送数据.是否有神器=true
		self.发送数据.是否佩戴神器=玩家数据[id].神器.数据.是否佩戴神器
		self.发送数据.神器格子=玩家数据[id].神器.数据.格子
	end
	发送数据(连接id,3527,self.发送数据)
end
function 道具处理类:索要灵宝佩戴(id)
	self.发送数据={}
	for n=1,2 do
		if 玩家数据[id].角色.灵宝佩戴[n]~=nil then
			self.发送数据[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.灵宝佩戴[n]]))
		end
	end
	return self.发送数据
end
function 道具处理类:索要任务(连接id,id)
	self.发送数据={道具={}}
	for n=1,20 do
		if 玩家数据[id].角色.任务包裹[n]~=nil then
			if self.数据[玩家数据[id].角色.任务包裹[n]]==nil then
				玩家数据[id].角色.任务包裹[n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.任务包裹[n]]))
			end
		end
	end
	发送数据(连接id,3531,self.发送数据)
end
function 道具处理类:获取任务道具(id) --314更新
	self.发送数据={道具={}}
	for n=1,20 do
		if 玩家数据[id].角色.任务包裹[n]~=nil then
			if self.数据[玩家数据[id].角色.任务包裹[n]]==nil then
				玩家数据[id].角色.任务包裹[n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.任务包裹[n]]))
			end
		end
	end
	return  self.发送数据
end
function 道具处理类:索要道具(连接id,id)
	self.发送数据={道具={}}
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil then
			if self.数据[玩家数据[id].角色.道具[n]]==nil then
				玩家数据[id].角色.道具[n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.道具[n]]))
			end
		end
	end
	self.发送数据.银子=玩家数据[id].角色.银子
	self.发送数据.储备=玩家数据[id].角色.储备
	self.发送数据.存银=玩家数据[id].角色.存银
	self.发送数据.仙玉=玩家数据[id].角色.仙玉
	self.发送数据.装备 = 玩家数据[id].角色:取装备数据()
	self.发送数据.灵饰 = 玩家数据[id].角色:取灵饰数据()
	发送数据(连接id,3501,self.发送数据)
end
function 道具处理类:索要道具1(id)
	self.发送数据={道具={}}
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil then
			if self.数据[玩家数据[id].角色.道具[n]]==nil then
				玩家数据[id].角色.道具[n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.道具[n]]))
			end
		end
	end
	self.发送数据.银子=玩家数据[id].角色.银子
	self.发送数据.储备=玩家数据[id].角色.储备
	self.发送数据.存银=玩家数据[id].角色.存银
	self.发送数据.仙玉=玩家数据[id].角色.仙玉
	return self.发送数据
end
function 道具处理类:索要道具2(id)
	self.发送数据={道具={}}
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil then
			if self.数据[玩家数据[id].角色.道具[n]]==nil then
				玩家数据[id].角色.道具[n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.道具[n]]))
			end
		end
	end
	return self.发送数据
end
function 道具处理类:索要行囊2(id)
	self.发送数据={道具={}}
	for n=1,20 do
		if 玩家数据[id].角色.行囊[n]~=nil then
			if self.数据[玩家数据[id].角色.行囊[n]]==nil then
				玩家数据[id].角色.行囊[n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.行囊[n]]))
			end
		end
	end
	return self.发送数据
end
function 道具处理类:索要道具3(连接id,id)
	self.发送数据={道具={}}
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil then
			if self.数据[玩家数据[id].角色.道具[n]]==nil then
				玩家数据[id].角色.道具[n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.道具[n]]))
			end
		end
	end
	self.发送数据.银子=玩家数据[id].角色.银子
	self.发送数据.储备=玩家数据[id].角色.储备
	self.发送数据.存银=玩家数据[id].角色.存银
	self.发送数据.仙玉=玩家数据[id].角色.仙玉
	发送数据(连接id,200,self.发送数据)
end
function 道具处理类:索要道具更新(id,类型)
	self.发送数据={道具={},类型=类型}
	for n=1,20 do
		if 玩家数据[id].角色[类型][n]~=nil then
			if self.数据[玩家数据[id].角色[类型][n]]==nil then
				玩家数据[id].角色[类型][n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色[类型][n]]))
			end
		end
	end
	self.发送数据.银子=玩家数据[id].角色.银子
	self.发送数据.储备=玩家数据[id].角色.储备
	self.发送数据.存银=玩家数据[id].角色.存银
	self.发送数据.仙玉=玩家数据[id].角色.仙玉
	发送数据(玩家数据[id].连接id,3532,self.发送数据)
end
function 道具处理类:重置法宝回合(id)
	for n=1,20 do
		if 玩家数据[id].角色.法宝[n]~=nil then
			if self.数据[玩家数据[id].角色.法宝[n]]==nil then
				玩家数据[id].角色.法宝[n]=nil
			else
				self.数据[玩家数据[id].角色.法宝[n]].回合=nil
			end
		end
	end
end
function 道具处理类:索要法宝2(id,回合)
	self.发送数据={道具={}}
	for n=1,20 do
		if 玩家数据[id].角色.法宝[n]~=nil then
			if self.数据[玩家数据[id].角色.法宝[n]]==nil then
				玩家数据[id].角色.法宝[n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.法宝[n]]))
				if self.发送数据.道具[n].回合~=nil then
					if self.发送数据.道具[n].回合<=回合 then
						self.发送数据.道具[n].回合=nil
					else
						self.发送数据.道具[n].回合=self.发送数据.道具[n].回合-回合
					end
				end
			end
		end
	end
	self.发送数据.银子=玩家数据[id].角色.银子
	self.发送数据.储备=玩家数据[id].角色.储备
	self.发送数据.存银=玩家数据[id].角色.存银
	self.发送数据.仙玉=玩家数据[id].角色.仙玉
	return self.发送数据
end
function 道具处理类:索要法宝1(id,回合)
	self.发送数据={道具={}}
	for n=1,20 do
		if 玩家数据[id].角色.法宝[n]~=nil then
			if self.数据[玩家数据[id].角色.法宝[n]]==nil then
				玩家数据[id].角色.法宝[n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.法宝[n]]))
				if self.发送数据.道具[n].回合~=nil then
					if self.发送数据.道具[n].回合<=回合 then
						self.发送数据.道具[n].回合=nil
					else
						self.发送数据.道具[n].回合=self.发送数据.道具[n].回合-回合
					end
				end
			end
		end
	end
	return self.发送数据
end
function 道具处理类:索要行囊(连接id,id)
	self.发送数据={道具={}}
	for n=1,20 do
		if 玩家数据[id].角色.行囊[n]~=nil then
			if self.数据[玩家数据[id].角色.行囊[n]]==nil then
				玩家数据[id].角色.行囊[n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.行囊[n]]))
			end
		end
	end
	self.发送数据.银子=玩家数据[id].角色.银子
	self.发送数据.储备=玩家数据[id].角色.储备
	self.发送数据.存银=玩家数据[id].角色.存银
	self.发送数据.仙玉=玩家数据[id].角色.仙玉
	发送数据(连接id,3502,self.发送数据)
end
function 道具处理类:激活符石对话(id,道具id)
	if self.数据[道具id]~=nil and  self.数据[道具id].名称=="未激活的符石" then
		玩家数据[id].激活符石 = 道具id
		local 消耗数据 = 激活符石消耗(self.数据[道具id].子类)
		local 对话="#W/需要消耗"..消耗数据.体力.."点体力"..消耗数据.经验.."点经验来激活这块符石么。#Y/(激活后为专用，无法转移给他人)"
		local xx={"确定","取消"}
		玩家数据[id].最后对话={}
		玩家数据[id].最后对话.名称="激活符石"
		玩家数据[id].最后对话.模型=玩家数据[id].角色.模型
		发送数据(玩家数据[id].连接id,1501,{名称="激活符石",模型=玩家数据[id].角色.模型,对话=对话,选项=xx})
	elseif self.数据[道具id]~=nil and self.数据[道具id].名称=="未激活的星石" then
		玩家数据[id].激活符石 = 道具id
		local 消耗数据 = 激活符石消耗(4)
		local 对话="#W/需要消耗"..消耗数据.体力.."点体力"..消耗数据.经验.."点经验来激活这块符石么。#Y/(激活后为专用，无法转移给他人)"
		local xx={"确定","取消"}
		玩家数据[id].最后对话={}
		玩家数据[id].最后对话.名称="激活符石"
		玩家数据[id].最后对话.模型=玩家数据[id].角色.模型
		发送数据(玩家数据[id].连接id,1501,{名称="激活符石",模型=玩家数据[id].角色.模型,对话=对话,选项=xx})
	end
end
function 道具处理类:激活符石(id)
	local 道具id = 玩家数据[id].激活符石
	if self.数据[道具id]~=nil and  self.数据[道具id].名称=="未激活的符石" then
		local 消耗数据 = 激活符石消耗(self.数据[道具id].子类)
		if 玩家数据[id].角色.体力 < 消耗数据.体力 then
			常规提示(id,"体力不足，无法激活符石。")
			return
		end
		if 玩家数据[id].角色.当前经验<消耗数据.经验 then
			常规提示(id,"经验不足，无法激活符石。")
			return
		end
		玩家数据[id].角色.当前经验 = 玩家数据[id].角色.当前经验 - 消耗数据.经验
		玩家数据[id].角色.体力 = 玩家数据[id].角色.体力 - 消耗数据.体力
		self.数据[道具id].名称 = self.数据[道具id].符石名称
		self.数据[道具id].分类 = 88
		self.数据[道具id].不可交易 = true
		添加最后对话(id,"你消耗了"..消耗数据.体力.."体力"..消耗数据.经验.."经验,将"..self.数据[道具id].名称.."成功激活.这块符石可以镶嵌在已经开运过的装备上#50。")
		道具刷新(id)
		return
	elseif self.数据[道具id]~=nil and  self.数据[道具id].名称=="未激活的星石" then
		local 消耗数据 = 激活符石消耗(4)
		if 玩家数据[id].角色.体力 < 消耗数据.体力 then
			常规提示(id,"体力不足，无法激活符石。")
			return
		end
		if 玩家数据[id].角色.当前经验<消耗数据.经验 then
			常规提示(id,"经验不足，无法激活符石。")
			return
		end
		玩家数据[id].角色.当前经验 = 玩家数据[id].角色.当前经验 - 消耗数据.经验
		玩家数据[id].角色.体力 = 玩家数据[id].角色.体力 - 消耗数据.体力
		self.数据[道具id].分类 = 91
		self.数据[道具id].不可交易 = true
		if self.数据[道具id].子类 == 1 then
			self.数据[道具id].名称 = "云荒"
		elseif self.数据[道具id].子类 == 2 then
			self.数据[道具id].名称 = "暮霭"
		elseif self.数据[道具id].子类 == 3 then
			self.数据[道具id].名称 = "落日"
		elseif self.数据[道具id].子类 == 4 then
			self.数据[道具id].名称 = "晓天"
		elseif self.数据[道具id].子类 == 5 then
			self.数据[道具id].名称 = "林海"
		elseif self.数据[道具id].子类 == 6 then
			self.数据[道具id].名称 = "流霞"
		else
			self.数据[道具id].名称 = "云荒"
		end
		添加最后对话(id,"你消耗了"..消耗数据.体力.."体力"..消耗数据.经验.."经验,将"..self.数据[道具id].名称.."成功激活.这块符石可以镶嵌在已经开运过的装备上#50。")
		道具刷新(id)
		return
	end
end
function 道具处理类:装备镶嵌符石(数字id,sj)
	local id = 数字id + 0
	local 装备道具=self.数据[玩家数据[id].符石镶嵌]
	local 符石操作序列 = sj
	local 星石操作 = nil
	if 装备道具==nil then
		常规提示(id,"你没有这样的装备！")
		return
	elseif 装备道具.总类~=2 or 装备道具.灵饰 or 装备道具.召唤兽装备 then
		常规提示(id,"我这里目前只能点化人物装备的星位，其它的我可没那么大的能耐。")
		return
	end
	if not 判断是否为空表(符石操作序列) then
		local 重复 = false
		for k,v in pairs(符石操作序列) do
			if 重复 then
				break
			end
			if v.方式=="镶嵌" then
				local lssj =玩家数据[数字id].角色.道具[v.物品id]
				if v.背包类型=="道具" then
					lssj=玩家数据[数字id].角色.道具[v.物品id]
				else
					lssj=玩家数据[数字id].角色.行囊[v.物品id]
				end
				if self.数据[lssj]==nil then
					常规提示(id,"道具数据异常，请重新打开界面进行操作1。")
					return
				end
				if k<=5 and (self.数据[lssj].总类~=889 or self.数据[lssj].分类~=88) then
					常规提示(id,"镶嵌需要符石，请给予我符石道具。")
					return
				elseif k==6 and (self.数据[lssj].总类~=889 or self.数据[lssj].分类~=91) then
					常规提示(id,"镶嵌需要星石，请给予我星石道具。")
					return
				end
				for n,i in pairs(符石操作序列) do
					if k~=n and i.方式=="镶嵌" and v.背包类型==i.背包类型 and v.物品id==i.物品id then
						重复=true
						break
					end
				end
			end
		end
		if not 重复 then
			if 装备道具.星位==nil then
				装备道具.星位={组合="",部位="无",门派="无"}
			end
			for k,v in pairs(符石操作序列) do
				if v.方式=="扣除" then
					装备道具.星位[k]=nil
				elseif v.方式=="镶嵌" then
					local lssj =玩家数据[数字id].角色.道具[v.物品id]
					if v.背包类型=="道具" then
						lssj=玩家数据[数字id].角色.道具[v.物品id]
					else
						lssj=玩家数据[数字id].角色.行囊[v.物品id]
					end
					if k==6 then
						装备道具.星位[k] = {}
						装备道具.星位[k].名称 = self.数据[lssj].名称
						装备道具.星位[k].颜色 = 取星位颜色(self.数据[lssj].子类)[2]
						装备道具.星位[k].阴阳 = 2
						local 临时属性 = 取星位属性(self.数据[lssj].子类)
						装备道具.星位[k].符石属性={}
						装备道具.星位[k].符石属性[临时属性.名称]=临时属性.属性值
					else
						装备道具.星位[k]={颜色=self.数据[lssj].颜色,名称=self.数据[lssj].名称,符石属性=self.数据[lssj].符石属性,符石等级=self.数据[lssj].子类}
					end
					self.数据[lssj]=nil
				end
			end
			if 装备道具.星位组 and 装备道具.星位~=nil and 装备道具.星位[6]~=nil then
				装备道具.星位[6].相互 = nil
				local 等级计算 = 0
				for n=1,5 do
					if  装备道具.星位~=nil and 装备道具.星位[n]~=nil then
						等级计算 = 等级计算 + (装备道具.星位[n].符石等级 or 0)
					end
				end
				if 等级计算~=0 then
					if 等级计算%2==0 then
						if 装备道具.星位[6].阴阳==2 then
							装备道具.星位[6].相互={}
							装备道具.星位[6].相互[取星位相互(装备道具.分类)]=2
						end
					else
						if 装备道具.星位[6].阴阳==1 then
							装备道具.星位[6].相互={}
							装备道具.星位[6].相互[取星位相互(装备道具.分类)]=2
						end
					end
				end
			end
			常规提示(id,"镶嵌成功。")
			local 星位数据=取星位组合(装备道具.星位)
			if 星位数据 ~=nil then
				装备道具.星位.组合=星位数据.组合
				装备道具.星位.部位=星位数据.部位
				装备道具.星位.门派=星位数据.门派
				装备道具.星位.组合等级=星位数据.等级
				常规提示(id,"#Y你的这件装备似乎开启了神秘的力量")
			else
				装备道具.星位.组合=nil
				装备道具.星位.部位=nil
				装备道具.星位.门派=nil
				装备道具.星位.组合等级=nil
			end
			if 符石操作序列[6]~=nil then
				星石操作=true
			end
			道具刷新(id)
			发送数据(玩家数据[id].连接id,3550,{装备=装备道具,星石操作=星石操作})
		else
			常规提示(id,"道具数据异常，请重新打开界面进行操作2。")
			return
		end
	else
		常规提示(id,"道具数据异常，请重新打开界面进行操作3。")
		return
	end
end
function 道具处理类:装备开启星位(连接id,数字id,数据)
	local 装备编号 = 玩家数据[数字id].角色.道具[数据.装备]
	local id = 数字id
	if self.数据[装备编号].总类~=2 or self.数据[装备编号].灵饰 or self.数据[装备编号].分类>6 then
		常规提示(id,"少侠，只有人物装备才可以开启星位。")
		return
	elseif self.数据[装备编号].级别限制==nil and self.数据[装备编号].级别限制<60 then
		常规提示(id,"少侠，需要装备等级达到60级才可以开启星位。")
		return
	elseif self.数据[装备编号].开运孔数==nil or self.数据[装备编号].开运孔数.当前<self.数据[装备编号].开运孔数.上限 then
		常规提示(id,"少侠，你的装备孔数没满，不能开启星位。")
		return
	elseif self.数据[装备编号].星位组~=nil then
		常规提示(id,"少侠，你这件装备已经开启过星位了别闹了（&……*&")
		return
	end
	local 临时消耗 = 取开启星位消耗(self.数据[装备编号].级别限制)
	if 玩家数据[id].角色.银子<临时消耗.金钱 then
		常规提示(id,format("开启星位需要消耗#Z/%s#Y/两银子，你似乎手头有点紧哟？",临时消耗.金钱))
		return
	elseif 玩家数据[id].角色.当前经验<临时消耗.经验 then
		常规提示(id,format("开启星位需要消耗#Z/%s#Y/点经验，你似乎没有那么多的经验？",临时消耗.经验))
		return
	end
	玩家数据[id].角色:扣除银子(临时消耗.金钱,0,0,"开启星位",1)
	玩家数据[id].角色.当前经验=玩家数据[id].角色.当前经验-临时消耗.经验
	self.数据[装备编号].星位组 = true
	常规提示(id,"#Z/恭喜你开启星位成功！")
	道具刷新(id)
end
function 道具处理类:装备翻转星石(数字id)
	local id = 数字id + 0
	local 装备道具 = self.数据[玩家数据[id].符石镶嵌]
	if 装备道具==nil then
		常规提示(id,"你没有这样的装备！")
		return
	elseif 装备道具.总类~=2 or 装备道具.灵饰 or 装备道具.分类==9 or 装备道具.分类==8 or 装备道具.分类==7 then
		常规提示(id,"我这里目前只能点化人物装备的星位，其它的我可没那么大的能耐。")
		return
	elseif not 装备道具.星位组 or 装备道具.星位==nil or 装备道具.星位[6]==nil then
		常规提示(id,"装备数据错误，请重新打开界面！")
		return
	end
	if 玩家数据[id].角色.体力 >= 100 then
		if 装备道具.星位组 and 装备道具.星位~=nil and 装备道具.星位[6]~=nil then
			if 装备道具.星位[6].阴阳 ==1 then
				装备道具.星位[6].阴阳 = 2
				装备道具.星位[6].颜色 = 取星位颜色(装备道具.分类)[2]
			else
				装备道具.星位[6].阴阳 = 1
				装备道具.星位[6].颜色 = 取星位颜色(装备道具.分类)[1]
			end
			装备道具.星位[6].相互 = nil
			local 等级计算 = 0
			for n=1,5 do
				if  装备道具.星位~=nil and 装备道具.星位[n]~=nil then
					等级计算 = 等级计算 + 装备道具.星位[n].符石等级
				end
			end
			if 等级计算~=0 then
				if 等级计算%2==0 then
					if 装备道具.星位[6].阴阳==2 then
						装备道具.星位[6].相互={}
						装备道具.星位[6].相互[取星位相互(装备道具.分类)]=2
					end
				else
					if 装备道具.星位[6].阴阳==1 then
						装备道具.星位[6].相互={}
						装备道具.星位[6].相互[取星位相互(装备道具.分类)]=2
					end
				end
			end
		end
		玩家数据[id].角色.体力=玩家数据[id].角色.体力-100
		常规提示(id,"翻转星位成功。")
		道具刷新(id)
		玩家数据[id].角色:刷新信息()
		发送数据(玩家数据[id].连接id,3550,{装备=装备道具})
	else
		常规提示(id,"体力不足，无法翻转")
	end
end
function 道具处理类:翻转星石对话(id,道具id)
	local 装备道具 = self.数据[玩家数据[id].符石镶嵌]
	if 装备道具==nil then
		常规提示(id,"你没有这样的装备！")
		return
	elseif 装备道具.总类~=2 or 装备道具.灵饰 or 装备道具.分类==9 or 装备道具.分类==8 or 装备道具.分类==7 then
		常规提示(id,"我这里目前只能点化人物装备的星位，其它的我可没那么大的能耐。")
		return
	elseif not 装备道具.星位组 or 装备道具.星位==nil or 装备道具.星位[6]==nil then
		常规提示(id,"装备数据错误，请重新打开界面！")
		return
	end
	local 对话="#W/确定要将星石翻转吗？翻转星石不会引起星石自身属性变化，请放心操作。"
	local xx={"消耗100体力进行翻转","取消"}
	玩家数据[id].最后对话={}
	玩家数据[id].最后对话.名称="翻转星石"
	玩家数据[id].最后对话.模型=玩家数据[id].角色.模型
	发送数据(玩家数据[id].连接id,1501,{名称="翻转星石",模型=玩家数据[id].角色.模型,对话=对话,选项=xx})
end
function 道具处理类:合成符石(连接id,数字id,数据)
	local id = 数字id
	local 物品 = 数据.材料
	local 卷轴 = 0
	local 卷轴道具 = 0
	local 符石 = {}
	local 符石道具 = {}
	local f1,f2,f3=0,0,0
	local 玩家道具栏 = 玩家数据[id].角色.道具
	local 道具格子 = 玩家数据[id].角色:取道具格子()
	if 道具格子 == 0 then
		常规提示(id,"你的道具栏已经满了,保留至少一格以上的位置进行合成哦")
		return
	end
	if 玩家数据[id].角色.体力<40 then
		常规提示(id,"合成符石至少需要40点体力哦。")
		return
	end
	for k,v in pairs(物品) do
		if self.数据[玩家道具栏[v]]~=nil and self.数据[玩家道具栏[v]].总类 == 889 and self.数据[玩家道具栏[v]].分类 == 89 then
			if self.数据[玩家道具栏[v]].子类==6 then
				卷轴 = 玩家道具栏[v]
				卷轴道具 = v
			else
				符石[#符石+1] = 玩家道具栏[v]
				符石道具[#符石道具+1] = v
				if self.数据[符石[#符石]].子类==1 then
					f1=f1+1
				elseif self.数据[符石[#符石]].子类==2 then
					f2=f2+1
				elseif self.数据[符石[#符石]].子类==3 then
					f3=f3+1
				end
			end
		else
			常规提示(id,"少侠，给的物品不对哦。")
			return
		end
	end
	local 是否合成 = false
	if f1>=1 and f1+f2==3 and f3==0 and 卷轴==0 then
		是否合成 = 1
	elseif f2>=1 and f2+f3==2 and 卷轴~=0 and f1==0 then
		是否合成 = 2
	elseif f3>=2 and 卷轴~=0 and f2==0 and f1==0 then
		是否合成 = 3
	elseif f3>=1 and f1+f2+f3==3 and 卷轴~=0 then
		是否合成 = 4
	else
		常规提示(id,"请仔细查看放入的材料是否正确。")
		return
	end
	玩家数据[id].角色:扣除体力(40,nil,1)
	local 概率范围 = 取随机数(1,1000)
	if 是否合成 == 1 then
		local 成功率 = 800
		if 概率范围 <= 成功率 then
			for i=1,#符石 do
				self.数据[符石[i]] = nil
				玩家道具栏[符石道具[i]] = nil
			end
			玩家数据[id].道具:给予道具(id,"未激活的符石",2)
			常规提示(id,"合成成功")
		else
			for i=1,#符石 do
				if 50 <= 取随机数(1,100) then
					self.数据[符石[i]] = nil
					玩家道具栏[符石道具[i]] = nil
					break
				elseif i==#符石 then
					self.数据[符石[i]] = nil
					玩家道具栏[符石道具[i]] = nil
					break
				end
			end
			道具刷新(id)
			常规提示(id,"合成失败，你因此随机损失了一个符石")
		end
	elseif 是否合成 == 2 then
		local 成功率 = 600
		if  概率范围 <= 成功率 then
			for i=1,#符石 do
				self.数据[符石[i]]=nil
				玩家道具栏[符石道具[i]] = nil
			end
			self.数据[卷轴] = nil
			玩家道具栏[卷轴道具] = nil
			玩家数据[id].道具:给予道具(id,"未激活的符石",3)
			常规提示(id,"合成成功")
		else
			self.数据[卷轴] = nil
			道具刷新(id)
			常规提示(id,"合成失败，你因此损失了一个符石卷轴")
		end
	elseif 是否合成 == 3 then
		local 成功率 = 200
		if 概率范围 <= 成功率 then
			for i=1,#符石 do
				self.数据[符石[i]]=nil
				玩家道具栏[符石道具[i]] = nil
			end
			self.数据[卷轴] = nil
			玩家道具栏[卷轴道具] = nil
			local 获取符石 = 新三级符石[取随机数(1,#新三级符石)]
			玩家数据[id].道具:给予道具(id,"未激活的符石",3,获取符石)
			常规提示(id,"合成成功")
		else
			self.数据[卷轴] = nil
			玩家道具栏[卷轴道具] = nil
			for i=1,#符石 do
				if 50 <= 取随机数(1,100) then
					self.数据[符石[i]] = nil
					玩家道具栏[符石道具[i]] = nil
					break
				else
					self.数据[符石[i]] = nil
					玩家道具栏[符石道具[i]] = nil
					break
				end
			end
			道具刷新(id)
			常规提示(id,"合成失败，你因此损失了一个符石卷轴及一颗符石")
		end
	elseif 是否合成 == 4 then
		local 成功率 = 400
		if 概率范围 <= 成功率 then
			for i=1,#符石 do
				self.数据[符石[i]]=nil
				玩家道具栏[符石道具[i]] = nil
			end
			self.数据[卷轴] = nil
			玩家道具栏[卷轴道具] = nil
			玩家数据[id].道具:给予道具(id,"未激活的星石")
			常规提示(id,"合成成功")
		else
			self.数据[卷轴] = nil
			玩家道具栏[卷轴道具] = nil
			道具刷新(id)
			常规提示(id,"合成失败，你因此损失了一个符石卷轴")
		end
	end
end
function 道具处理类:月饼使用(连接id,id,内容)
	self.临时id = 玩家数据[id].角色.道具[内容.编号]
	if 内容.序列 == nil then
		return
	end
	if 玩家数据[id].角色.附加潜能.月饼>50 then
		发送数据(玩家数据[id].连接id, 7, "#Y/你已经食用过50个月饼了")
		return 0
	else
		local 可吃月饼 = 50-玩家数据[id].角色.附加潜能.月饼
		玩家数据[id].角色.附加潜能.月饼=玩家数据[id].角色.附加潜能.月饼+1
		if 取随机数()<=20 then
			玩家数据[id].角色.体质=玩家数据[id].角色.体质+1
			发送数据(玩家数据[id].连接id, 7, "#Y/吃下一个月饼提升1点体质，您还可以食用"..可吃月饼.."个月饼。")
		elseif 取随机数()<=40 then
			玩家数据[id].角色.魔力=玩家数据[id].角色.魔力+1
			发送数据(玩家数据[id].连接id, 7, "#Y/吃下一个月饼提升1点魔力，您还可以食用"..可吃月饼.."个月饼。")
		elseif 取随机数()<=60 then
			玩家数据[id].角色.力量=玩家数据[id].角色.力量+1
			发送数据(玩家数据[id].连接id, 7, "#Y/吃下一个月饼提升1点力量，您还可以食用"..可吃月饼.."个月饼。")
		elseif 取随机数()<=80 then
			玩家数据[id].角色.耐力=玩家数据[id].角色.耐力+1
			发送数据(玩家数据[id].连接id, 7, "#Y/吃下一个月饼提升1点耐力，您还可以食用"..可吃月饼.."个月饼。")
		elseif 取随机数()<=100 then
			玩家数据[id].角色.敏捷=玩家数据[id].角色.敏捷+1
			发送数据(玩家数据[id].连接id, 7, "#Y/吃下一个月饼提升1点敏捷，您还可以食用"..可吃月饼.."个月饼。")
		end
		if self.数据[self.临时id].数量 == nil then
			self.数据[self.临时id].数量 = 1
		end
		玩家数据[id].道具.数据[self.临时id].数量 = 玩家数据[id].道具.数据[self.临时id].数量 - 1
		if 玩家数据[id].道具.数据[self.临时id].数量 <= 0 then
			玩家数据[id].道具.数据[self.临时id].数量 = nil
			玩家数据[id].角色.道具[内容.编号] = nil
		end
	end
	玩家数据[id].角色:刷新信息()
	道具刷新(id)
end
function 道具处理类:梦幻精品粽使用(连接id,id,内容)
	self.临时id = 玩家数据[id].角色.道具[内容.编号]
	if 内容.序列 == nil then
		return
	end
	if 玩家数据[id].角色.附加潜能.粽子>50 then
		发送数据(玩家数据[id].连接id, 7, "#Y/你已经食用过50个粽子了")
		return 0
	else
		local 可吃粽子 = 50-玩家数据[id].角色.附加潜能.粽子
		玩家数据[id].角色.附加潜能.粽子=玩家数据[id].角色.附加潜能.粽子+1
		if 取随机数()<=20 then
			玩家数据[id].角色.体质=玩家数据[id].角色.体质+1
			发送数据(玩家数据[id].连接id, 7, "#Y/吃下一个梦幻精品粽提升1点体质，您还可以食用"..可吃粽子.."个粽子。")
		elseif 取随机数()<=40 then
			玩家数据[id].角色.魔力=玩家数据[id].角色.魔力+1
			发送数据(玩家数据[id].连接id, 7, "#Y/吃下一个梦幻精品粽提升1点魔力，您还可以食用"..可吃粽子.."个粽子。")
		elseif 取随机数()<=60 then
			玩家数据[id].角色.力量=玩家数据[id].角色.力量+1
			发送数据(玩家数据[id].连接id, 7, "#Y/吃下一个梦幻精品粽提升1点力量，您还可以食用"..可吃粽子.."个粽子。")
		elseif 取随机数()<=80 then
			玩家数据[id].角色.耐力=玩家数据[id].角色.耐力+1
			发送数据(玩家数据[id].连接id, 7, "#Y/吃下一个梦幻精品粽提升1点耐力，您还可以食用"..可吃粽子.."个粽子。")
		elseif 取随机数()<=100 then
			玩家数据[id].角色.敏捷=玩家数据[id].角色.敏捷+1
			发送数据(玩家数据[id].连接id, 7, "#Y/吃下一个梦幻精品粽提升1点敏捷，您还可以食用"..可吃粽子.."个粽子。")
		end
		if self.数据[self.临时id].数量 == nil then
			self.数据[self.临时id1].数量 = 1
		end
		玩家数据[id].道具.数据[self.临时id].数量 = 玩家数据[id].道具.数据[self.临时id].数量 - 1
		if 玩家数据[id].道具.数据[self.临时id].数量 <= 0 then
			玩家数据[id].道具.数据[self.临时id].数量 = nil
			玩家数据[id].角色.道具[内容.编号] = nil
		end
	end
	玩家数据[id].角色:刷新信息()
	道具刷新(id)
end
function 道具处理类:人参果使用(连接id,id,内容)
	self.临时id = 玩家数据[id].角色.道具[内容.编号]
	if 内容.序列 == nil then
		return
	end
	local 已食用 = 玩家数据[id].角色.附加潜能.人参果
	local 可食用 = math.ceil(玩家数据[id].角色.等级/3)-已食用
	if 已食用 >= 可食用 then
		发送数据(玩家数据[id].连接id, 7, "#Y/您当前等级可食用"..可食用.."颗人参果，你已食用"..已食用.."颗人参果。")
		return 0
	else
		玩家数据[id].角色.附加潜能.人参果=玩家数据[id].角色.附加潜能.人参果+1
		local 食材 = 玩家数据[id].道具.数据[self.临时id].食材
		if self.数据[self.临时id].五行=="金"  then
			玩家数据[id].角色.力量=玩家数据[id].角色.力量+2
			发送数据(玩家数据[id].连接id, 7, "#Y/食用一颗人参果提升2点力量，您还可以食用"..可食用.."颗人参果。")
		elseif self.数据[self.临时id].五行=="木" then
			玩家数据[id].角色.体质=玩家数据[id].角色.体质+2
			发送数据(玩家数据[id].连接id, 7, "#Y/食用一颗人参果提升2点体质，您还可以食用"..可食用.."颗人参果。")
		elseif self.数据[self.临时id].五行=="水" then
			玩家数据[id].角色.敏捷=玩家数据[id].角色.敏捷+2
			发送数据(玩家数据[id].连接id, 7, "#Y/食用一颗人参果提升2点敏捷，您还可以食用"..可食用.."颗人参果。")
		elseif self.数据[self.临时id].五行=="火" then
			玩家数据[id].角色.魔力=玩家数据[id].角色.魔力+2
			发送数据(玩家数据[id].连接id, 7, "#Y/食用一颗人参果提升2点魔力，您还可以食用"..可食用.."颗人参果。")
		elseif self.数据[self.临时id].五行=="土" then
			玩家数据[id].角色.耐力=玩家数据[id].角色.耐力+2
			发送数据(玩家数据[id].连接id, 7, "#Y/食用一颗人参果提升2点耐力，您还可以食用"..可食用.."颗人参果。")
		end
		if self.数据[self.临时id].数量 == nil then
			self.数据[self.临时id].数量 = 1
		end
		玩家数据[id].道具.数据[self.临时id].数量 = 玩家数据[id].道具.数据[self.临时id].数量 - 1
		if 玩家数据[id].道具.数据[self.临时id].数量 <= 0 then
			玩家数据[id].道具.数据[self.临时id].数量 = nil
			玩家数据[id].角色.道具[内容.编号] = nil
		end
	end
	玩家数据[id].角色:刷新信息()
	道具刷新(id)
end
function 道具处理类:超级人参果使用(连接id,id,内容)
	self.临时id = 玩家数据[id].角色.道具[内容.编号]
	if 内容.序列 == nil then
		return
	end
	if not 玩家数据[id].角色:属性转换检测() then
		return
	end
	if 玩家数据[id].角色.种族=="仙" then
		玩家数据[id].角色.体质 =  12+玩家数据[id].角色.等级
		玩家数据[id].角色.魔力 =  5+玩家数据[id].角色.等级
		玩家数据[id].角色.力量 =  11+玩家数据[id].角色.等级
		玩家数据[id].角色.耐力 =  12+玩家数据[id].角色.等级
		玩家数据[id].角色.敏捷 =  10+玩家数据[id].角色.等级
	elseif  玩家数据[id].角色.种族=="魔" then
		玩家数据[id].角色.体质 =  12+玩家数据[id].角色.等级
		玩家数据[id].角色.魔力 =  11+玩家数据[id].角色.等级
		玩家数据[id].角色.力量 =  11+玩家数据[id].角色.等级
		玩家数据[id].角色.耐力 =  8+玩家数据[id].角色.等级
		玩家数据[id].角色.敏捷 =  8+玩家数据[id].角色.等级
	else
		玩家数据[id].角色.体质 =  10+玩家数据[id].角色.等级
		玩家数据[id].角色.魔力 =  10+玩家数据[id].角色.等级
		玩家数据[id].角色.力量 =  10+玩家数据[id].角色.等级
		玩家数据[id].角色.耐力 =  10+玩家数据[id].角色.等级
		玩家数据[id].角色.敏捷 =  10+玩家数据[id].角色.等级
	end
	if 玩家数据[id].角色.历劫.飞升 == true then
		玩家数据[id].角色.潜力 = 玩家数据[id].角色.潜力 + 10
	end
	if 玩家数据[id].角色.历劫.渡劫 == true then
		玩家数据[id].角色.潜力 = 玩家数据[id].角色.潜力 + 25
	end
	玩家数据[id].角色.潜力 = 玩家数据[id].角色.潜力 + 玩家数据[id].角色.附加潜能.潜能果+玩家数据[id].角色.附加潜能.月饼*2+玩家数据[id].角色.附加潜能.粽子+玩家数据[id].角色.附加潜能.人参果
	添加最后对话(id,"#y/你的人物属性重置成功！\n#y/等级基础潜能：#r/" .. 玩家数据[id].角色.潜力 .. "\n#y/附加潜能：".."#y/潜能果：#r/"..玩家数据[id].角色.附加潜能.潜能果 .."#y/月饼：#r/"..玩家数据[id].角色.附加潜能.月饼*2 .."#y/粽子：#r/"..玩家数据[id].角色.附加潜能.粽子.."#y/人参果：#r/"..玩家数据[id].角色.附加潜能.人参果)
	if self.数据[self.临时id].数量 == nil then
		self.数据[self.临时id1].数量 = 1
	end
	玩家数据[id].道具.数据[self.临时id].数量 = 玩家数据[id].道具.数据[self.临时id].数量 - 1
	if 玩家数据[id].道具.数据[self.临时id].数量 <= 0 then
		玩家数据[id].道具.数据[self.临时id].数量 = nil
		玩家数据[id].角色.道具[内容.编号] = nil
	end
	道具刷新(id)
	玩家数据[id].角色:初始化装备属性()
	玩家数据[id].角色:刷新信息("0")
	玩家数据[id].角色:加点方案刷新()
end
function 道具处理类:染色处理(连接id,id,数据)
	self.染色参数 = 数据
	self.花豆消耗 = 0
	self.彩果消耗 = 0
	if self.染色参数 == nil or #self.染色参数 < 3 then
		发送数据(玩家数据[id].连接id, 7, "#Y/染色参数异常，请重新选择染色方案")
		return 0
	end
	if self.染色参数[1] ~= 玩家数据[id].角色.染色组[1] then
		if self.染色参数[1]<3 then
			self.花豆消耗=self.花豆消耗+10
		else
			self.彩果消耗=self.彩果消耗+10
		end
	end
	if self.染色参数[2] ~= 玩家数据[id].角色.染色组[2] then
		if self.染色参数[2]<3 then
			self.花豆消耗=self.花豆消耗+10
		else
			self.彩果消耗=self.彩果消耗+10
		end
	end
	if self.染色参数[3] ~= 玩家数据[id].角色.染色组[3] then
		if self.染色参数[3]<3 then
			self.花豆消耗=self.花豆消耗+10
		else
			self.彩果消耗=self.彩果消耗+10
		end
	end
	self.花豆计算 = {}
	self.花豆满足 = false
	self.彩果计算 = {}
	self.彩果满足 = false
	if self.花豆消耗>0 then
		for n=1,80 do
			if self.花豆消耗>0 then
				if self.花豆消耗>0 and  玩家数据[id].角色.道具[n]~=nil and 玩家数据[id].道具.数据[玩家数据[id].角色.道具[n]]~=nil and  玩家数据[id].道具.数据[玩家数据[id].角色.道具[n]].名称=="花豆" then
					if 玩家数据[id].道具.数据[玩家数据[id].角色.道具[n]].数量==nil then
						self.花豆消耗=self.花豆消耗-1
						self.花豆计算[#self.花豆计算+1]={格子=n,数量=1}
					elseif 玩家数据[id].道具.数据[玩家数据[id].角色.道具[n]].数量>=self.花豆消耗 then
						self.花豆计算[#self.花豆计算+1]={格子=n,数量=self.花豆消耗}
						self.花豆消耗=0
					else
						self.花豆计算[#self.花豆计算+1]={格子=n,数量=玩家数据[id].道具.数据[玩家数据[id].角色.道具[n]].数量}
						self.花豆消耗=self.花豆消耗-玩家数据[id].道具.数据[玩家数据[id].角色.道具[n]].数量
					end
					if self.花豆消耗==0 then
						self.花豆满足=true
					end
				end
			end
		end
	end
	if self.彩果消耗>0 then
		for n=1,80 do
			if self.彩果消耗>0 then
				if self.彩果消耗>0 and  玩家数据[id].角色.道具[n]~=nil and 玩家数据[id].道具.数据[玩家数据[id].角色.道具[n]].名称=="彩果" then
					if 玩家数据[id].道具.数据[玩家数据[id].角色.道具[n]].数量==nil then
						self.彩果消耗=self.彩果消耗-1
						self.彩果计算[#self.彩果计算+1]={格子=n,数量=1}
					elseif 玩家数据[id].道具.数据[玩家数据[id].角色.道具[n]].数量>=self.彩果消耗 then
						self.彩果计算[#self.彩果计算+1]={格子=n,数量=self.彩果消耗}
						self.彩果消耗=0
					else
						self.彩果计算[#self.彩果计算+1]={格子=n,数量=玩家数据[id].道具.数据[玩家数据[id].角色.道具[n]].数量}
						self.彩果消耗=self.彩果消耗-玩家数据[id].道具.数据[玩家数据[id].角色.道具[n]].数量
					end
					if self.彩果消耗==0 then
						self.彩果满足=true
					end
				end
			end
		end
	end
	if self.彩果消耗>0  and self.花豆消耗>0  then
		发送数据(玩家数据[id].连接id,7,"#Y/您还需要#R/"..self.彩果消耗.."#Y/个彩果和#R/"..self.花豆消耗.."#Y/个花豆才能染色")
		return 0
	elseif self.彩果消耗>0 then
		发送数据(玩家数据[id].连接id,7,"#Y/您还需要#R/"..self.彩果消耗.."#Y/个彩果才能染色")
		return 0
	elseif self.花豆消耗>0 then
		发送数据(玩家数据[id].连接id,7,"#Y/您还需要#R/"..self.花豆消耗.."#Y/个花豆才能染色")
		return 0
	else
		if #self.花豆计算>0 then
			for n=1,#self.花豆计算 do
				self.临时格子=self.花豆计算[n].格子
				self.临时id=玩家数据[id].角色.道具[self.临时格子]
				if 玩家数据[id].道具.数据[self.临时id].数量==nil then
					玩家数据[id].道具.数据[self.临时id]=0
					玩家数据[id].角色.道具[self.临时格子]=nil
				else
					玩家数据[id].道具.数据[self.临时id].数量=玩家数据[id].道具.数据[self.临时id].数量-self.花豆计算[n].数量
					if 玩家数据[id].道具.数据[self.临时id].数量<=0 then
						玩家数据[id].道具.数据[self.临时id]=0
						玩家数据[id].角色.道具[self.临时格子]=nil
					end
				end
			end
		end
		if #self.彩果计算>0 then
			for n=1,#self.彩果计算 do
				self.临时格子=self.彩果计算[n].格子
				self.临时id=玩家数据[id].角色.道具[self.临时格子]
				if 玩家数据[id].道具.数据[self.临时id].数量==nil then
					玩家数据[id].道具.数据[self.临时id]=0
					玩家数据[id].角色.道具[self.临时格子]=nil
				else
					玩家数据[id].道具.数据[self.临时id].数量=玩家数据[id].道具.数据[self.临时id].数量-self.彩果计算[n].数量
					if 玩家数据[id].道具.数据[self.临时id].数量<=0 then
						玩家数据[id].道具.数据[self.临时id]=0
						玩家数据[id].角色.道具[self.临时格子]=nil
					end
				end
			end
		end
	end
	玩家数据[id].角色.染色组[1]=self.染色参数[1]
	玩家数据[id].角色.染色组[2]=self.染色参数[2]
	玩家数据[id].角色.染色组[3]=self.染色参数[3]
	地图处理类:更改染色(id,数据,玩家数据[id].角色.染色方案)
	发送数据(连接id,30,数据)
	发送数据(连接id,3699)
	发送数据(玩家数据[id].连接id, 7, "#Y/染色成功")
end
function 道具处理类:增加摊位记录(摊位id,id,名称,花费)
	if 名称 then
		玩家数据[摊位id].摊位数据.摊位记录 = 玩家数据[摊位id].摊位数据.摊位记录 .. 时间转换1(os.time()) .. "出售"..名称.."("..花费.."两)\n"
	else
		玩家数据[摊位id].摊位数据.摊位记录 = 玩家数据[摊位id].摊位数据.摊位记录 .. 时间转换1(os.time()) .. 玩家数据[id].角色.名称 .. "查看了摊位\n"
	end
	if string.len(玩家数据[摊位id].摊位数据.摊位记录) > 10240 then 玩家数据[摊位id].摊位数据.摊位记录 = "" end
end
function 道具处理类:查看摊位日志(id)
	if 玩家数据[id]==nil or 玩家数据[id].摊位数据==nil then
		常规提示(id,"#Y/这个摊位并不存在2")
		return
	end
	发送数据(玩家数据[id].连接id,3522.1,玩家数据[id].摊位数据.摊位记录)
end
function 道具处理类:点击夏日泡泡(id,任务id)
	if 任务数据[任务id]==nil then
		常规提示(id,"#Y这个泡泡已经被其他玩家点过咯")
		return
	end
	地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
	任务数据[任务id]=nil
	local 奖励参数=取随机数()
	local 等级=玩家数据[id].角色.等级+1
	if 奖励参数<=40 then
		local 银子=取随机数(100,1000)
		玩家数据[id].角色:添加银子(银子,"夏日泡泡",1)
	else
		local 经验=等级*取随机数(10,100)
		玩家数据[id].角色:添加经验(经验,"夏日泡泡",1)
	end
end
function 道具处理类:拾取雪人物品(id,任务id)
	if 任务数据[任务id]==nil then
		常规提示(id,"#Y这个雪人已经被其他玩家点过咯")
		return
	end
	地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
	任务数据[任务id]=nil
	local 奖励参数=取随机数()
      local 名称,数量,参数="",nil,nil
	if 奖励参数<=70 then
        名称,数量="雪人鼻子",1
	elseif 奖励参数<=20 then
        名称,数量="雪人帽子",1
	elseif 奖励参数<=30 then
		名称,数量="雪人眼睛",1
	elseif 奖励参数<=40 then
		名称,数量="大雪块",1
	else
		名称,数量="小雪块",1
	end
     玩家数据[id].道具:给予超链接道具(id,名称,数量)
end
function 道具处理类:拾取王婆西瓜(id,任务id)
	if 任务数据[任务id]==nil then
		常规提示(id,"#Y这个西瓜已经被其他玩家点过咯")
		return
	end
	地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
	任务数据[任务id]=nil
	local 奖励参数=取随机数()
	if 奖励参数<=40 then
		战斗准备类:创建战斗(id+0,100021)
	else
		玩家数据[id].角色:添加积分(1,"活动积分")
	end
end
function 道具处理类:索要战斗道具(连接id,id)
	self.发送数据={道具={}}
	for n=1,80 do
		if 玩家数据[id].角色.道具[n]~=nil then
			if self.数据[玩家数据[id].角色.道具[n]]==nil then
				玩家数据[id].角色.道具[n]=nil
			else
				self.发送数据.道具[n]=table.loadstring(table.tostring(self.数据[玩家数据[id].角色.道具[n]]))
			end
		end
	end
	发送数据(玩家数据[id].对接id or 玩家数据[id].连接id,5555.5,self.发送数据)
end
function 道具处理类:加锁物品(连接id,id,数据)
	if  f函数.读配置(程序目录.."data/" .. 玩家数据[id].账号 .. "/账号信息.txt", "账号配置", "解锁密码") == nil or f函数.读配置(程序目录.."data/" .. 玩家数据[id].账号 .. "/账号信息.txt", "账号配置", "解锁密码") == "空" then
		发送数据(玩家数据[id].连接id,3700.2)
		发送数据(玩家数据[id].连接id, 7, "#Y/你还未设置解锁密码,请设置牢记你的解锁密码!")
		return 0
	end
	if 数据.物品~=0 then
		self.临时id1 = 玩家数据[id].角色.道具[数据.物品]
		if self.数据[self.临时id1] == nil then
			发送数据(玩家数据[id].连接id, 7, "#Y/你没有这样的道具")
			return 0
		elseif self.数据[self.临时id1].加锁 ~= nil then
			发送数据(玩家数据[id].连接id, 7, "#Y/该物品已经加锁!")
			return 0
		else
			self.数据[self.临时id1].加锁=true
			发送数据(玩家数据[id].连接id, 7, "#r/"..self.数据[self.临时id1].名称.."#Y/加锁成功!")
			发送数据(玩家数据[id].连接id,3699)
		end
	end
	if 数据.召唤兽~=0 then
		if 玩家数据[id].召唤兽.数据[数据.召唤兽]==nil then
			发送数据(玩家数据[id].连接id,7,"#Y/当前召唤兽不存在")
			return 0
		elseif 玩家数据[id].召唤兽.数据[数据.召唤兽].加锁 ~= nil then
			发送数据(玩家数据[id].连接id,7,"#Y/当前召唤兽已经加锁!")
			return 0
		else
			玩家数据[id].召唤兽.数据[数据.召唤兽].加锁=true
			发送数据(玩家数据[id].连接id, 7, "#r/"..玩家数据[id].召唤兽.数据[数据.召唤兽].名称.."#Y/加锁成功!")
			发送数据(玩家数据[id].连接id,3699)
		end
	end
end
function 道具处理类:解锁物品(连接id,id,数据)
	if 玩家数据[id].加锁==false then
		发送数据(玩家数据[id].连接id, 7, "#Y/请解锁一次密码!")
		发送数据(玩家数据[id].连接id,3700.3)
		return 0
	end
	if 数据.物品~=0 then
		self.临时id1 = 玩家数据[id].角色.道具[数据.物品]
		if self.数据[self.临时id1] == nil then
			发送数据(玩家数据[id].连接id, 7, "#Y/你没有这样的道具")
			return 0
		elseif self.数据[self.临时id1].加锁==nil then
			发送数据(玩家数据[id].连接id, 7, "#Y/该物品未加锁!")
			return 0
		else
			self.数据[self.临时id1].加锁=nil
			发送数据(玩家数据[id].连接id,3699)
			发送数据(玩家数据[id].连接id, 7, "#r/"..self.数据[self.临时id1].名称.."#Y/解锁成功!")
		end
	end
	if 数据.召唤兽~=0 then
		if 玩家数据[id].召唤兽.数据[数据.召唤兽]==nil then
			发送数据(玩家数据[id].连接id,7,"#Y/当前召唤兽不存在")
			return 0
		elseif 玩家数据[id].召唤兽.数据[数据.召唤兽].加锁==nil then
			发送数据(玩家数据[id].连接id,7,"#Y/当前召唤兽未加锁!")
			return 0
		else
			玩家数据[id].召唤兽.数据[数据.召唤兽].加锁=nil
			发送数据(玩家数据[id].连接id,3699)
			发送数据(玩家数据[id].连接id, 7, "#r/"..玩家数据[id].召唤兽.数据[数据.召唤兽].名称.."#Y/解锁成功!")
		end
	end
end
function 道具处理类:拆分处理(连接id,id,数据)
	local 格子 = 玩家数据[id].角色:取道具格子(数据.类型)
	local 道具id = 玩家数据[id].角色[数据.类型][数据.物品]
	if 格子==0 then
		常规提示(id,"#Y/你身上的包裹没有足够的空间")
	elseif self.数据[道具id]==nil or self.数据[道具id].数量==nil then
		常规提示(id,"#Y/数据错误")
	elseif self.数据[道具id].数量 - 数据.数量<1 then
		常规提示(id,"#Y/数据错误")
	else
		local 剩余数量 = self.数据[道具id].数量 - 数据.数量
		local 拆分物品 = self.数据[道具id]
		local 道具 = 物品类()
		local 道具编号 = #self.数据+1
		self.数据[道具id].数量  =  剩余数量
		道具:置对象(拆分物品.名称)
		玩家数据[id].角色[数据.类型][格子]=道具编号
		self.数据[道具编号] = 道具
		self.数据[道具编号].识别码 = id.."_"..os.time().."_"..取随机数(1,999).."_"..id
		self.数据[道具编号].数量 = 数据.数量
		self.数据[道具编号].可叠加=true
		if 拆分物品.阶品 ~= nil then
			self.数据[道具编号].阶品 = 拆分物品.阶品
		end
		if 拆分物品.食材 ~= nil then
			self.数据[道具编号].食材 = 拆分物品.食材
		end
		self:刷新道具行囊(id,数据.类型)
	end
end
function 道具处理类:给予跑商道具(id,名称,数量,参数,附加,专用)
	local 识别码=id.."_"..os.time().."_"..取随机数(1000,9999999).."_"..随机序列
	随机序列=随机序列+1
	local 道具格子=玩家数据[id].角色:取道具格子()
	if 道具格子==0 then
		常规提示(id,"您的道具栏物品已经满啦")
		return
	else
		local 重置id=0
		-- 修复跑商重复物品BUG：扩展搜索范围到80个格子，跑商商品可以叠加不考虑价格
		for n=1,80 do
			if 重置id==0 and 玩家数据[id].角色.道具[n]~=nil and self.数据[玩家数据[id].角色.道具[n]]~=nil and self.数据[玩家数据[id].角色.道具[n]].名称==名称 and self.数据[玩家数据[id].角色.道具[n]].数量~=nil then
				local 现有道具 = self.数据[玩家数据[id].角色.道具[n]]

				-- 跑商商品可以叠加，不考虑价格差异
				if 现有道具.数量+数量<=99 then
					现有道具.数量 = 现有道具.数量 + 数量
					道具id=玩家数据[id].角色.道具[n]
					识别码=现有道具.识别码
					重置id=1
				end
			end
		end
		if 重置id==0 then
			道具id=self:取新编号()
			self.数据[道具id]=物品类()
			self.数据[道具id]:置对象(名称)
			玩家数据[id].角色.道具[道具格子]=道具id

			-- 只有新道具才需要设置这些属性
			临时道具 = 取物品数据(名称)
			self.数据[道具id].总类=临时道具[2]
			self.数据[道具id].子类=临时道具[4]
			self.数据[道具id].分类=临时道具[3]

			-- 设置跑商商品的特殊属性
			if string.find(名称, "商品") and 跑商[名称] then
				self.数据[道具id].单价 = 跑商[名称]
			end

			if self.数据[道具id].可叠加 then
				self.数据[道具id].数量=数量
			end

			self.数据[道具id].识别码=识别码
		end
		-- 叠加到现有道具时（重置id==1），不需要重新设置任何属性，数量已经在上面更新了
	end
	-- 识别码已经在上面设置了，不需要重复设置
	if  self.数据[道具id].名称=="心魔宝珠" then
		self.数据[道具id].不可交易=true
	end
	if 专用~=nil then
		self.数据[道具id].专用=id
		self.数据[道具id].不可交易=true
	end
	发送数据(玩家数据[id].连接id,3699)
	return 道具id
end
function 道具处理类:删除指定道具(数字id,物品)
	for i=1,80 do
		临时id = 玩家数据[数字id].角色.道具[i]
		if 玩家数据[数字id].道具.数据[临时id] ~= nil then
			if 玩家数据[数字id].道具.数据[临时id].名称 == 物品 then
				玩家数据[数字id].道具.数据[临时id]=nil
			end
		end
	end
end
function 道具处理类:取道具编号(id)
	for n, v in pairs(玩家数据[id].道具.数据) do
		if 玩家数据[id].道具.数据[n] == nil then
			return n
		end
	end
	return #玩家数据[id].道具.数据 + 1
end
function 道具处理类:考古铁铲处理(id)
	local 链接 = {提示=format("#S(考古·逐梦敦煌)#R/%s/#Y/在挖掘古董时，意外获得了",玩家数据[id].角色.名称),频道="xt",结尾="#Y。#24"}
	local 名称,数量,参数="",nil,nil

	-- 分层古董系统，按价值和稀有度分类
	local 普通古董 = {"嵌绿松石象牙杯","熹平经残石","羊首勺","铁矛","绿釉陶狗","四帛书","史叔编钟"}
	local 稀有古董 = {"盘古神斧","四足鬲","鸭形玻璃注","神面卣","毛公鼎","朱雀纹瓦当"}
	local 珍贵古董 = {"刀币","骨算筹","十二枝灯","素纱禅衣","凉造新泉","鎏金铜尺"}
	local 传说古董 = {"古玉簪","雁足灯","菊月古鉴·饕鬄镜","铜车马"}
	local 神话古董 = {"楚国计官之玺","齐国大车之玺","燕国外司炉印","韩国武遂大夫玺","秦国工师之印","赵国榆平发弩玺"}

	local 奖励参数=取随机数()
	if 奖励参数<=0.2 then
		-- 0.2% 概率获得神话古董（楚国计官之玺等）
		local gdname = 神话古董[math.random(#神话古董)]
		玩家数据[id].道具:给予超链接道具(id,gdname,数量,参数,链接)
		发送数据(连接id,105,{头像="双蓝字",标题=gdname,说明="恭喜你获得了"..gdname.."古董"})
		广播消息({内容=format("#S(考古奇迹)#R/%s#Y/在考古挖掘中发现了神话级古董#G/%s#Y！这是千年难遇的考古发现！",玩家数据[id].角色.名称,gdname),频道="xt"})
	elseif 奖励参数<=0.7 then
		-- 0.5% 概率获得一代将军令
		玩家数据[id].道具:给予超链接道具(id,"一代将军令",数量,参数,链接)
		发送数据(连接id,105,{头像="双蓝字",标题="一代将军令",说明="恭喜你获得了".."一代将军令".."古董"})
		广播消息({内容=format("#S(考古奇迹)#R/%s#Y/在考古挖掘中发现了传说中的#G/一代将军令#Y！",玩家数据[id].角色.名称),频道="xt"})
	elseif 奖励参数<=1.7 then
		-- 1% 概率获得和田青白玉戈
		玩家数据[id].道具:给予超链接道具(id,"和田青白玉戈",数量,参数,链接)
		发送数据(连接id,105,{头像="双蓝字",标题="和田青白玉戈",说明="恭喜你获得了".."和田青白玉戈".."古董"})
		广播消息({内容=format("#S(考古奇迹)#R/%s#Y/在考古挖掘中发现了绝世珍宝#G/和田青白玉戈#Y！",玩家数据[id].角色.名称),频道="xt"})
	elseif 奖励参数<=2.7 then
		-- 1% 概率获得莲鹤方壶
		玩家数据[id].道具:给予超链接道具(id,"莲鹤方壶",数量,参数,链接)
		发送数据(连接id,105,{头像="双蓝字",标题="莲鹤方壶",说明="恭喜你获得了".."莲鹤方壶".."古董"})
		广播消息({内容=format("#S(考古奇迹)#R/%s#Y/在考古挖掘中发现了绝世珍宝#G/莲鹤方壶#Y！",玩家数据[id].角色.名称),频道="xt"})
	elseif 奖励参数<=3.7 then
		-- 1% 概率获得传说古董
		local gdname = 传说古董[math.random(#传说古董)]
		玩家数据[id].道具:给予超链接道具(id,gdname,数量,参数,链接)
		发送数据(连接id,105,{头像="双蓝字",标题=gdname,说明="恭喜你获得了"..gdname.."古董"})
		广播消息({内容=format("#S(考古发现)#R/%s#Y/在考古挖掘中发现了传说古董#G/%s#Y！",玩家数据[id].角色.名称,gdname),频道="xt"})
	elseif 奖励参数<=5 then
		-- 1.3% 概率触发战斗
		战斗准备类:创建战斗(id+0,100003)
		常规提示(id,"#Y/你一锄头挖下去，似乎触碰到了一个奇形怪状的物体")
	elseif 奖励参数<=8 then
		-- 3% 概率获得珍贵古董
		local gdname = 珍贵古董[math.random(#珍贵古董)]
		玩家数据[id].道具:给予超链接道具(id,gdname,数量,参数,链接)
		发送数据(连接id,105,{头像="双蓝字",标题=gdname,说明="恭喜你获得了"..gdname.."古董"})
	elseif 奖励参数<=15 then
		-- 7% 概率获得稀有古董
		local gdname = 稀有古董[math.random(#稀有古董)]
		玩家数据[id].道具:给予超链接道具(id,gdname,数量,参数,链接)
		发送数据(连接id,105,{头像="双蓝字",标题=gdname,说明="恭喜你获得了"..gdname.."古董"})
	elseif 奖励参数<=30 then
		-- 15% 概率获得普通古董
		local gdname = 普通古董[math.random(#普通古董)]
		玩家数据[id].道具:给予超链接道具(id,gdname,数量,参数,链接)
		发送数据(连接id,105,{头像="双蓝字",标题=gdname,说明="恭喜你获得了"..gdname.."古董"})
	elseif 奖励参数<=50 then
		-- 20% 概率获得未鉴定古董
		玩家数据[id].道具:给予超链接道具(id,"未鉴定的古董",数量,参数,链接)
	else
		-- 50% 概率什么都没有
		常规提示(id,"#Y/你一锄头挖下去，好像什么都没挖到……")
	end
end
function 道具处理类:炼药处理(连接id,id,数据)
  local 临时等级=玩家数据[id].角色:取生活技能等级("中药医理")
  local 临时消耗=math.floor(临时等级)+10
  if 玩家数据[id].角色.活力<临时消耗 then
    常规提示(id,"本次操作需要消耗"..临时消耗.."点活力")
    return
  elseif 临时等级<10 then
    常规提示(id,"您的中药医理技能尚未达到10级，无法进行炼药操作")
    return
  else
    发送数据(连接id,3537.1,玩家数据[id].道具:索要道具1(id))
  end
end
function 道具处理类:成药处理(连接id,id,数据)
  if 玩家数据[id].角色.活力 < 10 then
    常规提示(id,"炼制药品需要消耗10点活力,你没有足够的活力用于消耗")
    return
  end
  local 道具组 = {}
  for i=1,4 do
    if 数据[i] then
      道具组[#道具组+1]=数据[i]
    end
  end
  if #道具组 < 2 then
    常规提示(id,"炼制三级药品最少需要使用2个以上药品炼制")
    return
  end
  local function 取炼药药品(i)
    local 加血道具={"天不老","紫石英","血色茶花","熊胆","鹿茸","六道轮回","凤凰尾","硫磺草","龙之心屑","火凤之睛","天青地白","丁香水","月星子","仙狐涎","地狱灵芝","麝香","血珊瑚","餐风饮露","白露为霜","天龙水","孔雀红","紫丹罗","佛手","旋复花","龙须草","百色花","香叶","白玉骨头","鬼切草","灵脂","曼陀罗花","七叶莲","四叶花"}
    for n=1,#加血道具 do
      if 加血道具[n] == i then
        return true
      end
    end
    return false
  end
  local function 取药品等级 (i)
    local 二级药 = {"天不老","紫石英","血色茶花","熊胆","鹿茸","六道轮回","凤凰尾","硫磺草","龙之心屑","火凤之睛","天青地白","丁香水","月星子","仙狐涎","地狱灵芝","麝香","血珊瑚","餐风饮露","白露为霜","天龙水","孔雀红"}
    local 一级药 = {"紫丹罗","佛手","旋复花","龙须草","百色花","香叶","白玉骨头","鬼切草","灵脂","曼陀罗花","七叶莲","四叶花"}
    for n=1,#二级药 do
      if 二级药[n] == i then
        return 2
      elseif 一级药[n] == i then
        return 1
      end
    end
  end
  local 一级数量 = 0
  local 二级数量 = 0
  for i=1,#道具组 do
    local 道具id = 玩家数据[id].角色.道具[道具组[i]]
    if self.数据[道具id] == nil or not 取炼药药品(self.数据[道具id].名称) then
      常规提示(id,"炼制三级药品最少需要使用2个以上药品炼制,请不要移动背包内物品位置")
      return
    elseif 取药品等级(self.数据[道具id].名称) == 2 then
      二级数量 = 二级数量 + 1
    elseif 取药品等级(self.数据[道具id].名称) == 1 then
      一级数量 = 一级数量 + 1
    end
  end
  if 二级数量 + 一级数量 < 2 then
    常规提示(id,"炼制三级药品最少需要使用2个以上1级药品炼制")
    return
  end
  for i=1,#道具组 do
    local 道具id = 玩家数据[id].角色.道具[道具组[i]]
    if self.数据[道具id] == nil then
      常规提示(id,"请检查放入的药品是否正确！")
      return
    end
    self.数据[道具id].数量 = self.数据[道具id].数量 - 1
    if self.数据[道具id].数量 <= 0 then
      self.数据[道具id] = nil
      玩家数据[id].角色.道具[道具组[i]]=nil
    end
  end
  玩家数据[id].角色.活力= 玩家数据[id].角色.活力-20
  体活刷新(id)
  local 物品表={"金创药"}
  local 概率 = 0
  概率 = 二级数量*15
  概率 = 概率+一级数量*5
  local 临时等级=玩家数据[id].角色:取生活技能等级("中药医理")
  概率 = 概率 + 临时等级/10
  local 临时品质=取随机数(math.floor(临时等级*0.5),临时等级)
  if 取随机数() <= 概率 then
    物品表={"金香玉","小还丹","千年保心丹","风水混元丹","定神香","小还丹","千年保心丹","风水混元丹","定神香","蛇蝎美人","九转回魂丹","佛光舍利子","佛光舍利子","十香返生丸","十香返生丸","五龙丹","红雪散"}
  end
  local 临时物品=物品表[取随机数(1,#物品表)]
  常规提示(id,"#W/恭喜你成功炼制出了#R/"..临时物品)
  self:给予道具(id,临时物品,1,临时品质)
  发送数据(连接id,3699)
  发送数据(连接id,3537.1,玩家数据[id].道具:索要道具1(id))
end
function 道具处理类:发放雪人奖励(id)
  local 名称="2千万储备金"
  玩家数据[id].角色:添加储备(10000000,"雪人奖励",1)
  if 取随机数() <= 10 then
    广播消息({内容=format("#S(雪人奖励)#Y#R/%s#Y获得雪人赠送的#G/%s#Y",玩家数据[id].角色.名称,名称),频道="hd"})
  end
end
return 道具处理类