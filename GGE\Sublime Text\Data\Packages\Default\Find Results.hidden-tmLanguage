<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>name</key>
	<string>Find Results</string>

	<key>patterns</key>
	<array>
		<dict>
			<key>match</key>
			<string>^([^ ].*):$</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>entity.name.filename.find-in-files</string>
				</dict>
			</dict>
		</dict>
		<dict>
			<key>match</key>
			<string>^    (ERROR:)</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>constant.other.find-in-files</string>
				</dict>
			</dict>
		</dict>
		<dict>
			<key>match</key>
			<string>^ +([0-9]+) </string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.line-number.find-in-files</string>
				</dict>
			</dict>
		</dict>
		<dict>
			<key>match</key>
			<string>^ +([0-9]+):</string>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>constant.numeric.line-number.match.find-in-files</string>
				</dict>
			</dict>
		</dict>
		<dict>
			<key>match</key>
			<string>(... )?&lt;skipped [0-9]+ characters?&gt;( ...)?</string>
			<key>name</key>
			<string>comment.other.skipped.find-in-files</string>
		</dict>
	</array>
	<key>scopeName</key>
	<string>text.find-in-files</string>
</dict>
</plist>
