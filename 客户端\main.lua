-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2025-06-05 17:59:44
-- @Last Modified by:   baidwwy
-- @Last Modified time: 2025-07-14 10:24:28

-- 配置模块搜索路径以支持GMFramework
--package.path = package.path .. ";./script/?.lua;./Script/?.lua"

ffi = require("ffi")
local 梦盾 = ffi.load("lua52.dll")
ffi.cdef[[
  void messageBox(const char* sometext);
    bool Searches();//检测CE

    char* decrypt(char* file);
    bool encrypt(char* file,char* data);

]]
-- int hadProcess();//检测多开，放到ffi.cdef
图像类 = require("gge图像类1")
f函数 = require("ffi函数2")
版本号=0.03
标题=" 梦幻西游 "
客户端参数={}
客户端参数.分钟=os.date("%M", os.time())
客户端参数.小时=os.date("%H", os.time())
require("script/全局/变量1")
Youxijincheng=false
加载完成=false
玩家屏蔽 =false
摊位屏蔽 = false
连点模式 = false
显示坐骑 = true
显示变身卡造型 = true
全局禁止走路=false
帮战开关=false
变身显示=false
天气开关= true
低配模式 = false
内存优化开关=false
渡劫化生显示=false
传送圈显示开关 = 读配置("./config.ini","mhxy","传送圈显示") == "true"
宝宝队伍图排序={}
ServerDirectory = lfs.currentdir() .. [[\]]
__聊天框x=260
回调 = require("Script/网络/HPClient类")()
require("lfs")
require("Script/初系统/优化缓冲")


function WriteFile(fileName, content)
	fileName = ServerDirectory .. fileName
	local f = assert(io.open(fileName, 'w'))
	f:write(content)
	f:close()
end
function 写出内容(qq, ww)
	if qq == nil or ww == nil or ww == "" then
		return 0
	end
	qq = 程序目录 .. qq
	local file = io.open(qq,"w")
	file:write(ww)
	file:close()
	text =0
	程序目录=lfs.currentdir()..[[\]]
	return text
end
function 写出文件(qq,ww)
	写出内容(qq,ww)
	if 初始目录 then
		lfs.chdir(初始目录)
		程序目录=初始目录
	end
end
require("script/数据中心/物品库")
require("script/数据中心/头像库")
require("script/数据中心/技能库")
require("script/数据中心/经脉库")
require("script/数据中心/音效库")
require("script/数据中心/明雷库")
require("script/数据中心/特效库")
require("script/数据中心/普通模型库")
require("script/数据中心/战斗模型库")
require("script/数据中心/坐骑库")
require("script/数据中心/传送表")
require("script/数据中心/场景")
require("script/数据中心/梦战造型")
require("script/数据中心/符石组合")
require("script/数据中心/自定义库")
local maxCacheCnt = collectgarbage("count") > 250*1024 and 400 or 800
资源缓存 = require("Script/资源类/缓存资源")(maxCacheCnt)
-- 资源缓存:设置清理间隔(300)       
资源缓存:设置调试模式(false)
yq = 引擎
yq.场景 = require("script/全局/主控")()
tp = yq.场景
宝宝类=require("Script/属性控制/宝宝")
游戏公告=require("script/显示类/游戏公告类")(tp)
战斗指令类=require("script/战斗类/战斗命令类")
战斗类=require("script/战斗类/战斗类")(tp)
战斗单位类=require("script/战斗类/战斗单位类")
战斗动画类=require("script/战斗类/战斗动画类")
调试入口 = require("Script/调试类/调试入口").创建(tp)----2025.1.24
require("script/多重对话类/任务事件")

-- 初始化客户端热更新系统（调试模式下）
-- print("调试模式检查: __gge.isdebug = " .. tostring(__gge.isdebug))
-- -- 无论如何都启动热更新系统进行测试
-- 客户端热更新系统 = require("Script/系统/客户端热更新系统")()
-- print("客户端UI热更新系统已启动")

加载完成=true
全局dt = 0.8
菊部dt = 0.012


if not next(ItemData or {}) then
    ItemData={}
    加载物品数据()
end

if __gge.isdebug == nil then
	ffi.load(程序目录.."g22d.dll") --多开器
end

local oldtime = os.time()
local dttime = 0
local contleng = 0
local xttime=1
local sddsd=0
local Acceleration=os.time()
__fsyz=false
__fsyzxz=false
__fsyzzd=false
function checkSpeend_grrpk(dt)
	if os.time() - oldtime >= 1 then --2
		oldtime = os.time()
		xttime=xttime+1 --2
		local gametime=math.floor(引擎.取游戏时间()/1000)
		if gametime>xttime then
			if dttime>1 then --2.1
				sddsd=sddsd+1
				if sddsd>=3 then
					f函数.信息框("检测到异常数据！","下线通知")
					os.exit()
				end
			else
				sddsd=0
				xttime=gametime-2
			end
		end
		dttime = 0
		if __fsyz and (__fsyzxz or __fsyzzd) then
			if os.time() - Acceleration >= 5 then
				Acceleration = os.time()
				发送数据(6302)
			end
		end
	end
	dttime = dttime + dt
end


function 渲染函数(dt,x,y)--鼠标x,鼠标y

	if 引擎.取随机整数(1,300) ==1  and 梦盾.Searches() == true then
	 	--梦盾.messageBox("请把CE等作弊类软件关闭！！！")
	  引擎.关闭()
	end
	dt = dt*全局dt
	checkSpeend_grrpk(dt)

	-- 客户端热更新检查
	if 客户端热更新系统 then
		客户端热更新系统:定期检查()
	end

	鼠标.x,鼠标.y=x,y
	yq.渲染开始()
	yq.渲染清除()
	yq.场景:显示(dt,x,y)
	游戏公告:显示(dt,x,y)

 -- 调试入口:更新(dt)----2025.1.24
 -- 调试入口:显示()----2025.1.24

	yq.渲染结束()
end
local function 退出函数()
	if tp==nil then
		return false
	end
	if tp.进程 == 1 then
		return true
	elseif tp.进程 == 2 or tp.进程 == 3 or tp.进程 == 5 or tp.进程 == 6 or tp.进程 == 7 or tp.进程 == 8 or tp.进程 == 9 or tp.进程2 == 1 then
		tp.进程2 = 1
		return false
	else
		tp.窗口.系统设置:打开()
		return false
	end
	return false
end
引擎.置退出函数(退出函数)
function 引擎关闭开始()
	引擎.关闭()
end
