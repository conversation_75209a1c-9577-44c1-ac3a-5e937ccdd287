-- @Author: 反作弊系统
-- @Date: 2025-01-14
-- @Description: 跑商完成验证系统，防止脚本自动跑商

local 跑商验证系统 = {}

-- 验证配置
local 验证配置 = {
    启用验证 = true,                    -- 是否启用验证
    验证概率 = 0.3,                     -- 30%概率触发验证
    高频玩家验证概率 = 0.8,             -- 高频跑商玩家80%概率验证
    验证超时时间 = 120,                 -- 验证超时时间（秒）
    连续跑商阈值 = 5,                   -- 连续跑商次数阈值
    短时间跑商阈值 = 3,                 -- 短时间内跑商次数阈值
    短时间窗口 = 3600,                  -- 短时间窗口（1小时）
}

-- 验证题库
local 验证题库 = {
    {
        问题 = "请输入图片中显示的4位数字验证码",
        类型 = "验证码",
        答案 = nil, -- 动态生成
    },
    {
        问题 = "以下哪个不是跑商商品？",
        类型 = "选择题",
        选项 = {"商品棉布", "商品佛珠", "金创药", "商品扇子"},
        答案 = 3,
    },
    {
        问题 = "帮派跑商需要什么道具？",
        类型 = "选择题", 
        选项 = {"帮派银票", "飞行符", "回城符", "传送符"},
        答案 = 1,
    },
    {
        问题 = "跑商价格多长时间刷新一次？",
        类型 = "选择题",
        选项 = {"1个时辰", "2个时辰", "3个时辰", "4个时辰"},
        答案 = 2,
    },
    {
        问题 = "请计算：15 + 27 = ?",
        类型 = "计算题",
        答案 = 42,
    },
    {
        问题 = "请计算：8 × 9 = ?", 
        类型 = "计算题",
        答案 = 72,
    }
}

-- 玩家验证数据
local 玩家验证数据 = {}
local 玩家跑商统计 = {}

-- 初始化玩家数据
function 跑商验证系统:初始化玩家数据(玩家id)
    if not 玩家跑商统计[玩家id] then
        玩家跑商统计[玩家id] = {
            总跑商次数 = 0,
            连续跑商次数 = 0,
            最后跑商时间 = 0,
            短时间跑商次数 = 0,
            短时间窗口开始 = 0,
            验证通过次数 = 0,
            验证失败次数 = 0,
        }
    end
end

-- 检查是否需要验证
function 跑商验证系统:需要验证(玩家id)
    if not 验证配置.启用验证 then
        return false
    end
    
    self:初始化玩家数据(玩家id)
    local 统计数据 = 玩家跑商统计[玩家id]
    local 当前时间 = os.time()
    
    -- 更新短时间窗口统计
    if 当前时间 - 统计数据.短时间窗口开始 > 验证配置.短时间窗口 then
        统计数据.短时间跑商次数 = 1
        统计数据.短时间窗口开始 = 当前时间
    else
        统计数据.短时间跑商次数 = 统计数据.短时间跑商次数 + 1
    end
    
    -- 检查是否为高频跑商玩家
    local 是高频玩家 = false
    if 统计数据.连续跑商次数 >= 验证配置.连续跑商阈值 or
       统计数据.短时间跑商次数 >= 验证配置.短时间跑商阈值 then
        是高频玩家 = true
    end
    
    -- 根据玩家类型决定验证概率
    local 验证概率 = 是高频玩家 and 验证配置.高频玩家验证概率 or 验证配置.验证概率
    
    return math.random() < 验证概率
end

-- 开始验证流程
function 跑商验证系统:开始验证(玩家id)
    if not 玩家数据[玩家id] then
        return false
    end
    
    -- 随机选择验证题目
    local 题目索引 = math.random(1, #验证题库)
    local 题目 = 验证题库[题目索引]
    
    -- 生成验证数据
    local 验证id = "verify_" .. 玩家id .. "_" .. os.time()
    玩家验证数据[验证id] = {
        玩家id = 玩家id,
        题目 = 题目,
        开始时间 = os.time(),
        状态 = "等待回答",
        答案 = nil,
    }
    
    -- 特殊处理验证码类型
    if 题目.类型 == "验证码" then
        local 验证码 = self:生成验证码()
        玩家验证数据[验证id].题目.答案 = 验证码
        self:发送验证码界面(玩家id, 验证id, 验证码)
    elseif 题目.类型 == "选择题" then
        self:发送选择题界面(玩家id, 验证id, 题目)
    elseif 题目.类型 == "计算题" then
        self:发送计算题界面(玩家id, 验证id, 题目)
    end
    
    -- 设置超时处理
    self:设置验证超时(验证id)
    
    return 验证id
end

-- 生成4位数字验证码
function 跑商验证系统:生成验证码()
    return string.format("%04d", math.random(1000, 9999))
end

-- 发送验证码界面
function 跑商验证系统:发送验证码界面(玩家id, 验证id, 验证码)
    -- 暂停玩家操作
    玩家数据[玩家id].验证中 = true
    
    -- 发送验证界面（这里需要根据你的UI系统调整）
    发送数据(玩家数据[玩家id].连接id, 1501, {
        名称 = "系统验证",
        模型 = "系统",
        对话 = string.format("检测到频繁跑商行为，需要进行安全验证。\n请输入验证码：%s\n\n请在聊天框输入：验证 %s", 验证码, 验证码),
        验证id = 验证id
    })
    
    常规提示(玩家id, "#Y/系统安全验证：请输入验证码 " .. 验证码)
    常规提示(玩家id, "#Y/请在聊天框输入：验证 " .. 验证码)
end

-- 发送选择题界面
function 跑商验证系统:发送选择题界面(玩家id, 验证id, 题目)
    玩家数据[玩家id].验证中 = true
    
    local 选项文本 = ""
    for i, 选项 in ipairs(题目.选项) do
        选项文本 = 选项文本 .. string.format("%d. %s\n", i, 选项)
    end
    
    发送数据(玩家数据[玩家id].连接id, 1501, {
        名称 = "系统验证",
        模型 = "系统", 
        对话 = string.format("%s\n\n%s\n请在聊天框输入：验证 答案序号", 题目.问题, 选项文本),
        验证id = 验证id
    })
    
    常规提示(玩家id, "#Y/系统安全验证：" .. 题目.问题)
    常规提示(玩家id, "#Y/请在聊天框输入：验证 答案序号")
end

-- 发送计算题界面
function 跑商验证系统:发送计算题界面(玩家id, 验证id, 题目)
    玩家数据[玩家id].验证中 = true
    
    发送数据(玩家数据[玩家id].连接id, 1501, {
        名称 = "系统验证",
        模型 = "系统",
        对话 = string.format("%s\n\n请在聊天框输入：验证 答案", 题目.问题),
        验证id = 验证id
    })
    
    常规提示(玩家id, "#Y/系统安全验证：" .. 题目.问题)
    常规提示(玩家id, "#Y/请在聊天框输入：验证 答案")
end

-- 处理验证回答
function 跑商验证系统:处理验证回答(玩家id, 回答内容)
    if not 玩家数据[玩家id] or not 玩家数据[玩家id].验证中 then
        return false
    end
    
    -- 查找玩家的验证记录
    local 验证id = nil
    for id, 数据 in pairs(玩家验证数据) do
        if 数据.玩家id == 玩家id and 数据.状态 == "等待回答" then
            验证id = id
            break
        end
    end
    
    if not 验证id then
        return false
    end
    
    local 验证数据 = 玩家验证数据[验证id]
    local 题目 = 验证数据.题目
    local 正确答案 = 题目.答案
    local 玩家答案 = tonumber(回答内容) or 回答内容
    
    -- 检查答案
    local 验证通过 = false
    if 题目.类型 == "验证码" then
        验证通过 = (回答内容 == 正确答案)
    elseif 题目.类型 == "选择题" then
        验证通过 = (玩家答案 == 正确答案)
    elseif 题目.类型 == "计算题" then
        验证通过 = (玩家答案 == 正确答案)
    end
    
    -- 更新验证状态
    验证数据.状态 = 验证通过 and "通过" or "失败"
    验证数据.答案 = 回答内容
    
    -- 更新统计数据
    self:初始化玩家数据(玩家id)
    local 统计数据 = 玩家跑商统计[玩家id]
    
    if 验证通过 then
        统计数据.验证通过次数 = 统计数据.验证通过次数 + 1
        常规提示(玩家id, "#G/验证通过！可以继续完成跑商任务")
        self:完成验证(玩家id, true)
    else
        统计数据.验证失败次数 = 统计数据.验证失败次数 + 1
        常规提示(玩家id, "#R/验证失败！请重新验证")
        
        -- 验证失败的处理
        if 统计数据.验证失败次数 >= 3 then
            self:处理验证失败(玩家id)
        else
            -- 重新开始验证
            self:开始验证(玩家id)
        end
    end
    
    return 验证通过
end

-- 完成验证
function 跑商验证系统:完成验证(玩家id, 成功)
    if not 玩家数据[玩家id] then
        return
    end
    
    玩家数据[玩家id].验证中 = nil
    
    if 成功 then
        -- 允许完成跑商任务
        玩家数据[玩家id].验证通过 = true
        
        -- 记录日志
        local 日志信息 = string.format(
            "[跑商验证] 玩家%d(%s) 验证通过",
            玩家id,
            玩家数据[玩家id].角色.名称 or "未知"
        )
        __S服务:输出(日志信息)
    end
end

-- 处理验证失败
function 跑商验证系统:处理验证失败(玩家id)
    if not 玩家数据[玩家id] then
        return
    end
    
    -- 取消跑商任务
    local 任务id = 玩家数据[玩家id].角色:取任务(24)
    if 任务id and 任务id ~= 0 then
        玩家数据[玩家id].角色:取消任务(任务id)
        任务数据[任务id] = nil
    end
    
    -- 删除帮派银票
    for i = 1, 80 do
        local 道具id = 玩家数据[玩家id].角色.道具[i]
        if 道具id and 玩家数据[玩家id].道具.数据[道具id] then
            if 玩家数据[玩家id].道具.数据[道具id].名称 == "帮派银票" then
                玩家数据[玩家id].角色.道具[i] = nil
                玩家数据[玩家id].道具.数据[道具id] = nil
                break
            end
        end
    end
    
    玩家数据[玩家id].角色.跑商 = nil
    玩家数据[玩家id].角色.跑商时间 = nil
    玩家数据[玩家id].角色.跑商限制 = os.time() + 1800 -- 限制30分钟
    玩家数据[玩家id].验证中 = nil
    
    道具刷新(玩家id)
    常规提示(玩家id, "#R/多次验证失败，跑商任务已取消，30分钟内无法接取跑商任务")
    
    -- 通知管理员
    local 通知内容 = string.format(
        "[跑商验证] 玩家 %s(ID:%d) 多次验证失败，已限制跑商",
        玩家数据[玩家id].角色.名称 or "未知",
        玩家id
    )
    
    for id, data in pairs(玩家数据) do
        if data and data.角色 and data.角色.权限 and data.角色.权限 >= 5 then
            常规提示(id, "#R/" .. 通知内容)
        end
    end
    
    -- 记录日志
    __S服务:输出(通知内容)
    pcall(function()
        local 日志文件 = io.open("logs/trade_verification.log", "a")
        if 日志文件 then
            日志文件:write(os.date("%Y-%m-%d %H:%M:%S") .. " " .. 通知内容 .. "\n")
            日志文件:close()
        end
    end)
end

-- 设置验证超时
function 跑商验证系统:设置验证超时(验证id)
    -- 这里需要使用定时器，简化处理
    -- 在实际使用中，你可能需要在定时器中检查超时
end

-- 检查验证超时（在定时器中调用）
function 跑商验证系统:检查验证超时()
    local 当前时间 = os.time()
    
    for 验证id, 数据 in pairs(玩家验证数据) do
        if 数据.状态 == "等待回答" and 
           当前时间 - 数据.开始时间 > 验证配置.验证超时时间 then
            
            -- 超时处理
            self:处理验证失败(数据.玩家id)
            玩家验证数据[验证id] = nil
        end
    end
end

-- 更新跑商统计
function 跑商验证系统:更新跑商统计(玩家id)
    self:初始化玩家数据(玩家id)
    local 统计数据 = 玩家跑商统计[玩家id]
    local 当前时间 = os.time()
    
    统计数据.总跑商次数 = 统计数据.总跑商次数 + 1
    
    -- 检查连续跑商
    if 当前时间 - 统计数据.最后跑商时间 < 7200 then -- 2小时内算连续
        统计数据.连续跑商次数 = 统计数据.连续跑商次数 + 1
    else
        统计数据.连续跑商次数 = 1
    end
    
    统计数据.最后跑商时间 = 当前时间
end

return 跑商验证系统
