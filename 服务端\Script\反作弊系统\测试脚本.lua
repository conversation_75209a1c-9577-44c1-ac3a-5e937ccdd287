-- @Author: 反作弊系统
-- @Date: 2025-01-14
-- @Description: 跑商验证系统测试脚本

local 测试脚本 = {}

-- 测试跑商验证系统
function 测试脚本:测试跑商验证系统()
    local 跑商验证系统 = require("Script/反作弊系统/跑商验证系统")
    
    print("=== 跑商验证系统测试 ===")
    
    -- 测试玩家ID（请替换为实际的测试玩家ID）
    local 测试玩家id = 1001
    
    -- 测试1：检查是否需要验证（新玩家）
    print("测试1：新玩家验证检查")
    local 需要验证1 = 跑商验证系统:需要验证(测试玩家id)
    print("新玩家需要验证：", 需要验证1)
    
    -- 模拟多次跑商
    print("\n测试2：模拟连续跑商")
    for i = 1, 6 do
        跑商验证系统:更新跑商统计(测试玩家id)
        local 需要验证 = 跑商验证系统:需要验证(测试玩家id)
        print(string.format("第%d次跑商后需要验证：%s", i, tostring(需要验证)))
    end
    
    -- 测试3：开始验证流程
    print("\n测试3：开始验证流程")
    if 玩家数据[测试玩家id] then
        local 验证id = 跑商验证系统:开始验证(测试玩家id)
        print("验证ID：", 验证id)
        
        -- 测试验证回答
        print("\n测试4：验证回答")
        local 验证结果1 = 跑商验证系统:处理验证回答(测试玩家id, "错误答案")
        print("错误答案验证结果：", 验证结果1)
        
        local 验证结果2 = 跑商验证系统:处理验证回答(测试玩家id, "1234")
        print("可能正确答案验证结果：", 验证结果2)
    else
        print("测试玩家不在线，跳过验证流程测试")
    end
    
    print("=== 测试完成 ===")
end

-- 测试行为检测系统
function 测试脚本:测试行为检测系统()
    local 行为检测系统 = require("Script/反作弊系统/行为检测系统")
    
    print("=== 行为检测系统测试 ===")
    
    local 测试玩家id = 1001
    
    if 玩家数据[测试玩家id] then
        -- 测试移动检测
        print("测试1：移动异常检测")
        local 异常移动1 = 行为检测系统:检测移动异常(测试玩家id, {x = 100, y = 100})
        print("正常移动检测结果：", 异常移动1)
        
        local 异常移动2 = 行为检测系统:检测移动异常(测试玩家id, {x = 10000, y = 10000})
        print("异常移动检测结果：", 异常移动2)
        
        -- 测试跑商操作检测
        print("\n测试2：跑商操作检测")
        local 异常操作1 = 行为检测系统:检测跑商操作异常(测试玩家id, "价格查询", {})
        print("跑商操作检测结果：", 异常操作1)
        
        -- 获取玩家统计
        print("\n测试3：获取玩家统计")
        local 统计数据 = 行为检测系统:获取玩家统计(测试玩家id)
        if 统计数据 then
            print("可疑度评分：", 统计数据.可疑度评分)
            print("警告次数：", 统计数据.警告次数)
        else
            print("未找到统计数据")
        end
    else
        print("测试玩家不在线，跳过行为检测测试")
    end
    
    print("=== 测试完成 ===")
end

-- 测试验证命令处理
function 测试脚本:测试验证命令处理()
    local 验证命令处理 = require("Script/反作弊系统/验证命令处理")
    
    print("=== 验证命令处理测试 ===")
    
    local 测试玩家id = 1001
    
    if 玩家数据[测试玩家id] then
        -- 测试验证命令
        print("测试1：验证命令处理")
        local 处理结果1 = 验证命令处理:处理验证命令(测试玩家id, "验证 1234")
        print("验证命令处理结果：", 处理结果1)
        
        local 处理结果2 = 验证命令处理:处理验证命令(测试玩家id, "普通聊天")
        print("普通聊天处理结果：", 处理结果2)
        
        -- 测试管理员命令（如果玩家有管理权限）
        if 玩家数据[测试玩家id].角色.权限 and 玩家数据[测试玩家id].角色.权限 >= 5 then
            print("\n测试2：管理员命令处理")
            local 管理员结果1 = 验证命令处理:处理管理员命令(测试玩家id, "@重置可疑度 测试玩家")
            print("重置可疑度命令结果：", 管理员结果1)
            
            local 管理员结果2 = 验证命令处理:处理管理员命令(测试玩家id, "@查看统计 测试玩家")
            print("查看统计命令结果：", 管理员结果2)
        else
            print("测试玩家无管理权限，跳过管理员命令测试")
        end
    else
        print("测试玩家不在线，跳过命令处理测试")
    end
    
    print("=== 测试完成 ===")
end

-- 运行所有测试
function 测试脚本:运行所有测试()
    print("开始运行反作弊系统测试...")
    print("时间：", os.date("%Y-%m-%d %H:%M:%S"))
    print("=" .. string.rep("=", 50))
    
    -- 检查必要的全局变量
    if not 玩家数据 then
        print("错误：玩家数据表不存在，无法进行测试")
        return
    end
    
    -- 运行各项测试
    pcall(function() self:测试跑商验证系统() end)
    print("\n" .. string.rep("-", 50) .. "\n")
    
    pcall(function() self:测试行为检测系统() end)
    print("\n" .. string.rep("-", 50) .. "\n")
    
    pcall(function() self:测试验证命令处理() end)
    
    print("\n" .. string.rep("=", 50))
    print("所有测试完成")
end

-- 快速验证测试（用于GM命令）
function 测试脚本:快速测试(玩家id)
    if not 玩家数据[玩家id] then
        print("玩家不存在或不在线")
        return
    end
    
    local 跑商验证系统 = require("Script/反作弊系统/跑商验证系统")
    
    print("=== 快速验证测试 ===")
    print("测试玩家：", 玩家数据[玩家id].角色.名称)
    
    -- 强制触发验证
    local 验证id = 跑商验证系统:开始验证(玩家id)
    if 验证id then
        print("验证已触发，验证ID：", 验证id)
        常规提示(玩家id, "#Y/管理员触发了验证测试")
    else
        print("验证触发失败")
    end
end

-- 重置测试数据
function 测试脚本:重置测试数据(玩家id)
    if not 玩家数据[玩家id] then
        print("玩家不存在或不在线")
        return
    end
    
    local 行为检测系统 = require("Script/反作弊系统/行为检测系统")
    
    if 行为检测系统:重置可疑度评分(玩家id) then
        print("已重置玩家", 玩家数据[玩家id].角色.名称, "的测试数据")
        常规提示(玩家id, "#G/测试数据已重置")
    else
        print("重置失败")
    end
end

return 测试脚本
