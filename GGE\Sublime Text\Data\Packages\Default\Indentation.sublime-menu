[{"command": "toggle_setting", "args": {"setting": "translate_tabs_to_spaces"}, "caption": "使用空格缩进", "checkbox": true}, {"caption": "-"}, {"command": "set_setting", "args": {"setting": "tab_size", "value": 1}, "caption": "Tab宽度: 1", "checkbox": true}, {"command": "set_setting", "args": {"setting": "tab_size", "value": 2}, "caption": "Tab宽度: 2", "checkbox": true}, {"command": "set_setting", "args": {"setting": "tab_size", "value": 3}, "caption": "Tab宽度: 3", "checkbox": true}, {"command": "set_setting", "args": {"setting": "tab_size", "value": 4}, "caption": "Tab宽度: 4", "checkbox": true}, {"command": "set_setting", "args": {"setting": "tab_size", "value": 5}, "caption": "Tab宽度: 5", "checkbox": true}, {"command": "set_setting", "args": {"setting": "tab_size", "value": 6}, "caption": "Tab宽度: 6", "checkbox": true}, {"command": "set_setting", "args": {"setting": "tab_size", "value": 7}, "caption": "Tab宽度: 7", "checkbox": true}, {"command": "set_setting", "args": {"setting": "tab_size", "value": 8}, "caption": "Tab宽度: 8", "checkbox": true}, {"caption": "-"}, {"command": "detect_indentation", "caption": "猜测来自缓冲区的设置"}, {"caption": "-"}, {"command": "expand_tabs", "caption": "转换为空格缩进", "args": {"set_translate_tabs": true}}, {"command": "unexpand_tabs", "caption": "转换为tab缩进", "args": {"set_translate_tabs": true}}]