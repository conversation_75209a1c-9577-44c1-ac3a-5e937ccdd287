-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2024-05-17 13:33:58
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-05-26 22:23:23
-- 定义副本常量
local FUBEN_ID = 80
local FUBEN_NAME = "黑风山"
local FUBEN_DURATION = 7200 -- 副本持续时间（秒）
local MIN_LEVEL = 60 -- 最低等级要求
local MIN_PLAYERS = 5 -- 最少玩家数

-- 定义地图ID常量
local MAP_HEIFENGSHAN_1 = 6018
local MAP_HEIFENGSHAN_2 = 6019
local MAP_HEIFENGSHAN_3 = 6020
local MAP_HEIFENGSHAN_4 = 6021

-- 定义任务类型常量
local TASK_MAIN = 80
local TASK_CLEAN = 81 -- 黑风山打扫
local TASK_PATROL = 82 -- 黑风山巡逻 (注意: 战斗类型中也使用了82, 可能存在冲突或误用)
local TASK_MONSTER_STAGE2 = 83 -- 绝望的牛头, 懵懂的僵尸, 吊死的鬼魂, 奇怪的马面
local TASK_MONSTER_STAGE3 = 84 -- 恶虎
local TASK_MONSTER_STAGE4 = 85 -- 鬼鬼祟祟的黑熊精
local TASK_MONSTER_STAGE5 = 86 -- 熊熊烈火, 星星火光
local TASK_MONSTER_STAGE6 = 87 -- 黑汉
local TASK_MONSTER_STAGE7 = 88 -- 黑熊精队长
local TASK_MONSTER_STAGE8 = 89 -- 黑熊精

-- 定义战斗类型常量
local BATTLE_CLEAN = 81 -- 黑风山打扫
local BATTLE_PATROL = 100096 -- 黑风山巡逻 (注意: 与TASK_PATROL不一致)
local BATTLE_QUIZ = 82 -- 黑风山答题 (注意: 与TASK_PATROL冲突)
local BATTLE_MONSTER_STAGE2 = 155567
local BATTLE_MONSTER_STAGE3 = 155568
local BATTLE_MONSTER_STAGE4 = 155569
local BATTLE_MONSTER_STAGE5_FIRE = 155571
local BATTLE_MONSTER_STAGE6 = 155572
local BATTLE_MONSTER_STAGE7 = 155573
local BATTLE_MONSTER_STAGE8 = 155574

-- 定义副本阶段常量
local STAGE = {
    INIT_ASSIST_TASKS = 1,        -- 初始化协助任务阶段
    FIGHT_MONSTERS_STAGE2 = 2,    -- 战斗第二阶段怪物阶段
    FIGHT_MONSTER_STAGE3 = 3,     -- 战斗第三阶段怪物阶段
    FIGHT_MONSTER_STAGE4 = 4,     -- 战斗第四阶段怪物阶段
    FIGHT_MONSTERS_STAGE5 = 5,    -- 战斗第五阶段怪物阶段
    FIGHT_MONSTER_STAGE6 = 6,     -- 战斗第六阶段怪物阶段
    FIGHT_MONSTERS_STAGE7 = 7,    -- 战斗第七阶段怪物阶段
    FIGHT_MONSTER_STAGE8 = 8      -- 战斗第八阶段怪物阶段 (最终Boss)
}

local 副本_黑风山 = class()
function 副本_黑风山:初始化()
  地图处理类.地图数据[MAP_HEIFENGSHAN_1]={npc={},单位={},传送圈={}}
  地图处理类.地图玩家[MAP_HEIFENGSHAN_1]={}
  地图处理类.地图坐标[MAP_HEIFENGSHAN_1]=地图处理类.地图坐标[1042]
  地图处理类.地图单位[MAP_HEIFENGSHAN_1]={}
  地图处理类.单位编号[MAP_HEIFENGSHAN_1]=1000
  地图处理类.地图数据[MAP_HEIFENGSHAN_2]={npc={},单位={},传送圈={}}
  地图处理类.地图玩家[MAP_HEIFENGSHAN_2]={}
  地图处理类.地图坐标[MAP_HEIFENGSHAN_2]=地图处理类.地图坐标[1002]
  地图处理类.地图单位[MAP_HEIFENGSHAN_2]={}
  地图处理类.单位编号[MAP_HEIFENGSHAN_2]=1000
  地图处理类.地图数据[MAP_HEIFENGSHAN_3]={npc={},单位={},传送圈={}}
  地图处理类.地图玩家[MAP_HEIFENGSHAN_3]={}
  地图处理类.地图坐标[MAP_HEIFENGSHAN_3]=地图处理类.地图坐标[1210]
  地图处理类.地图单位[MAP_HEIFENGSHAN_3]={}
  地图处理类.单位编号[MAP_HEIFENGSHAN_3]=1000
  地图处理类.地图数据[MAP_HEIFENGSHAN_4]={npc={},单位={},传送圈={}}
  地图处理类.地图玩家[MAP_HEIFENGSHAN_4]={}
  地图处理类.地图坐标[MAP_HEIFENGSHAN_4]=地图处理类.地图坐标[1211]
  地图处理类.地图单位[MAP_HEIFENGSHAN_4]={}
  地图处理类.单位编号[MAP_HEIFENGSHAN_4]=1000
end
function 副本_黑风山:取黑风山次数限制(id,次数)
  if 副本数据.黑风山.完成[id] then
    添加最后对话(id,"你今日已经完成过该副本了")
    return true
  elseif 玩家数据[id].队伍 ~= 0 then
    for i=1,#队伍数据[玩家数据[id].队伍].成员数据 do
      local lsid = 队伍数据[玩家数据[id].队伍].成员数据[i]
      if 副本数据.黑风山.完成[lsid]then
        添加最后对话(id,玩家数据[lsid].角色.名称.."今日已经完成过该副本了")
        return true
      end
    end
  end
  return false
end
function 副本_黑风山:开启黑风山副本(id)
  if 玩家数据[id].队伍==0 or 玩家数据[id].队长==false  then
    常规提示(id,"#Y/该任务必须组队完成且由队长领取")
    return
  elseif 取队伍人数(id)<MIN_PLAYERS then
    常规提示(id,"#Y此副本要求队伍人数不低于"..MIN_PLAYERS.."人")
    return
  elseif  取等级要求(id,MIN_LEVEL)==false then
    常规提示(id,"#Y此副本要求角色等级不能低于"..MIN_LEVEL.."级")
    return
  elseif self:取黑风山次数限制(id) then
    return
  elseif 取队员任务存在(玩家数据[id].队伍,FUBEN_ID) then
    常规提示(id,"#Y/队伍中已有队员正在进行该副本任,无法领取新的副本")
    return
  end
  local 任务id=id.."_"..FUBEN_ID.."_"..os.time().."_"..随机序列.."_"..取随机数(1,999)
  副本数据.黑风山.进行[id]={进程=STAGE.INIT_ASSIST_TASKS,主任务id=任务id}
  self:设置黑风山副本(id)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=FUBEN_DURATION,
    玩家id={},
    队伍组={},
    副本id=id,
    类型=FUBEN_ID
  }
  local 队伍id=玩家数据[id].队伍
  for n=1,#队伍数据[队伍id].成员数据 do
    local 临时id=队伍数据[队伍id].成员数据[n]
    任务数据[任务id].队伍组[#任务数据[任务id].队伍组+1]=临时id
    玩家数据[临时id].角色:添加任务(任务id)
    常规提示(临时id,"#Y你开启了黑风山副本")
  end
end
-- 设置黑风山副本阶段1：初始化协助任务
function 副本_黑风山:设置黑风山阶段1(id)
    副本数据.黑风山.进行[id].协助任务 = {镇山太保=5,婆婆=5,唐玄奘=5}
end

-- 设置黑风山副本阶段2：生成第二阶段怪物
function 副本_黑风山:设置黑风山阶段2(id)
    local 随机名称={"绝望的牛头","懵懂的僵尸","吊死的鬼魂","奇怪的马面"}
    local 随机模型={"牛头","僵尸","野鬼","马面"}
    local 地图 = MAP_HEIFENGSHAN_1
    local 怪物数量 = 15
    local 随机序列 = os.time() % 1000

    副本数据.黑风山.进行[id].数量 = 怪物数量

    for i=1,怪物数量 do
        local sj = 取随机数(1,#随机名称)
        local 任务id = id.."_"..TASK_MONSTER_STAGE2.."_"..os.time().."_"..随机序列.."_"..取随机数(1,999)..i
        local xy = 地图处理类.地图坐标[地图]:取随机点()
        任务数据[任务id] = {
            id = 任务id,
            起始 = os.time(),
            结束 = 1200, -- 硬编码时间，可以考虑常量化
            玩家id = id,
            队伍组 = {},
            名称 = 随机名称[sj],
            模型 = 随机模型[sj],
            事件 = "明雷活动", -- 可以考虑常量化
            x = xy.x,
            y = xy.y,
            方向 = 方向, -- 方向变量未定义，可能需要修正
            副本id = id,
            地图编号 = 地图,
            地图名称 = 取地图名称(地图),
            类型 = TASK_MONSTER_STAGE2,
        }
        地图处理类:添加单位(任务id)
    end
end

-- 设置黑风山副本阶段3：生成恶虎
function 副本_黑风山:设置黑风山阶段3(id)
    local 地图 = MAP_HEIFENGSHAN_1
    local 随机序列 = os.time() % 1000
    local 任务id = id.."_"..TASK_MONSTER_STAGE3.."_"..os.time().."_"..随机序列.."_"..取随机数(1,999)
    local xy = 地图处理类.地图坐标[地图]:取随机点()

    任务数据[任务id] = {
        id = 任务id,
        起始 = os.time(),
        结束 = 1200, -- 硬编码时间，可以考虑常量化
        玩家id = id,
        队伍组 = {},
        名称 = "恶虎",
        模型 = "噬天虎",
        x = xy.x,
        y = xy.y,
        方向 = 1, -- 硬编码方向
        行走开关 = true, -- 可以考虑常量化
        事件 = "明雷活动", -- 可以考虑常量化
        副本id = id,
        地图编号 = 地图,
        地图名称 = 取地图名称(地图),
        类型 = TASK_MONSTER_STAGE3,
    }
    地图处理类:添加单位(任务id)
end

-- 设置黑风山副本阶段4：生成鬼鬼祟祟的黑熊精
function 副本_黑风山:设置黑风山阶段4(id)
    local 地图 = MAP_HEIFENGSHAN_1
    local 随机序列 = os.time() % 1000
    local 任务id = id.."_"..TASK_MONSTER_STAGE4.."_"..os.time().."_"..随机序列.."_"..取随机数(1,999)
    local xy = 地图处理类.地图坐标[地图]:取随机点()

    任务数据[任务id] = {
        id = 任务id,
        起始 = os.time(),
        结束 = 1200, -- 硬编码时间，可以考虑常量化
        玩家id = id,
        队伍组 = {},
        名称 = "鬼鬼祟祟的黑熊精",
        模型 = "黑熊精",
        x = xy.x,
        y = xy.y,
        方向 = 1, -- 硬编码方向
        行走开关 = true, -- 可以考虑常量化
        事件 = "明雷活动", -- 可以考虑常量化
        副本id = id,
        地图编号 = 地图,
        地图名称 = 取地图名称(地图),
        类型 = TASK_MONSTER_STAGE4,
    }
    地图处理类:添加单位(任务id)
end

-- 设置黑风山副本阶段5：生成熊熊烈火和星星火光
function 副本_黑风山:设置黑风山阶段5(id)
    local 地图 = MAP_HEIFENGSHAN_2
    local 怪物数量 = 15 -- 总数量
    local 熊熊烈火数量 = 10
    local 星星火光数量 = 5
    local 随机序列 = os.time() % 1000

    副本数据.黑风山.进行[id].数量 = 怪物数量

    for i=1,熊熊烈火数量 do
        local 任务id = id.."_"..TASK_MONSTER_STAGE5.."_"..os.time().."_"..随机序列.."_"..取随机数(1,999)..i
        local xy = 地图处理类.地图坐标[地图]:取随机点()
        任务数据[任务id] = {
            id = 任务id,
            起始 = os.time(),
            结束 = FUBEN_DURATION, -- 使用副本持续时间常量
            玩家id = id,
            队伍组 = {},
            名称 = "熊熊烈火",
            模型 = "凤凰",
            x = xy.x,
            y = xy.y,
            方向 = 方向, -- 方向变量未定义，可能需要修正
            副本id = id,
            行走开关 = true, -- 可以考虑常量化
            事件 = "明雷活动", -- 可以考虑常量化
            地图编号 = 地图,
            地图名称 = 取地图名称(地图),
            类型 = TASK_MONSTER_STAGE5,
        }
        地图处理类:添加单位(任务id)
    end

    for i=1,星星火光数量 do
        local 任务id = id.."_"..TASK_MONSTER_STAGE5.."_"..os.time().."_"..随机序列.."_"..取随机数(1,9999)..i -- 注意这里的随机数范围不同
        local xy = 地图处理类.地图坐标[地图]:取随机点()
        任务数据[任务id] = {
            id = 任务id,
            起始 = os.time(),
            结束 = FUBEN_DURATION, -- 使用副本持续时间常量
            玩家id = id,
            队伍组 = {},
            名称 = "星星火光",
            模型 = "凤凰",
            x = xy.x,
            y = xy.y,
            方向 = 方向, -- 方向变量未定义，可能需要修正
            副本id = id,
            行走开关 = true, -- 可以考虑常量化
            事件 = "明雷活动", -- 可以考虑常量化
            地图编号 = 地图,
            地图名称 = 取地图名称(地图),
            类型 = TASK_MONSTER_STAGE5,
        }
        地图处理类:添加单位(任务id)
    end
end

-- 设置黑风山副本阶段6：生成黑汉
function 副本_黑风山:设置黑风山阶段6(id)
    local 地图 = MAP_HEIFENGSHAN_3
    local 随机序列 = os.time() % 1000
    local 任务id = id.."_"..TASK_MONSTER_STAGE6.."_"..os.time().."_"..随机序列.."_"..取随机数(1,999)
    local xy = 地图处理类.地图坐标[地图]:取随机点()

    任务数据[任务id] = {
        id = 任务id,
        起始 = os.time(),
        结束 = 1200, -- 硬编码时间，可以考虑常量化
        玩家id = id,
        队伍组 = {},
        名称 = "黑汉",
        模型 = "黑熊精",
        x = xy.x,
        y = xy.y,
        方向 = 1, -- 硬编码方向
        行走开关 = true, -- 可以考虑常量化
        事件 = "明雷活动", -- 可以考虑常量化
        副本id = id,
        地图编号 = 地图,
        地图名称 = 取地图名称(地图),
        类型 = TASK_MONSTER_STAGE6,
    }
    地图处理类:添加单位(任务id)
end

-- 设置黑风山副本阶段7：生成黑熊精队长
function 副本_黑风山:设置黑风山阶段7(id)
    local 地图 = MAP_HEIFENGSHAN_3
    local 怪物数量 = 10
    local 随机序列 = os.time() % 1000

    副本数据.黑风山.进行[id].数量 = 怪物数量

    for i=1,怪物数量 do
        local 任务id = id.."_"..TASK_MONSTER_STAGE7.."_"..os.time().."_"..随机序列.."_"..取随机数(1,999)..i
        local xy = 地图处理类.地图坐标[地图]:取随机点()
        任务数据[任务id] = {
            id = 任务id,
            起始 = os.time(),
            结束 = 1200, -- 硬编码时间，可以考虑常量化
            玩家id = id,
            队伍组 = {},
            名称 = "黑熊精队长",
            模型 = "黑熊精",
            事件 = "明雷活动", -- 可以考虑常量化
            x = xy.x,
            y = xy.y,
            方向 = 方向, -- 方向变量未定义，可能需要修正
            副本id = id,
            地图编号 = 地图,
            地图名称 = 取地图名称(地图),
            类型 = TASK_MONSTER_STAGE7,
        }
        地图处理类:添加单位(任务id)
    end
end

-- 设置黑风山副本阶段8：生成最终Boss黑熊精
function 副本_黑风山:设置黑风山阶段8(id)
    local 地图 = MAP_HEIFENGSHAN_4
    local 随机名称={"黑熊精"}
    local 随机模型={"黑熊精"}
    local 随机序列 = os.time() % 1000

    -- 注意：原代码这里有一个循环，但只使用了随机名称/模型列表的第一个元素，且坐标是固定的。
    -- 如果只需要生成一个Boss，可以简化循环。这里按照原逻辑保留循环，但只处理第一个元素。
    for i=1,#随机模型 do
        local xy = 地图处理类.地图坐标[地图]:取随机点() -- 虽然获取了随机点，但实际使用了固定坐标
        local 任务id = id.."_"..TASK_MONSTER_STAGE8.."_"..os.time().."_"..随机序列.."_"..取随机数(1,999)..i
        任务数据[任务id] = {
            id = 任务id,
            起始 = os.time(),
            结束 = FUBEN_DURATION, -- 使用副本持续时间常量
            玩家id = id,
            队伍组 = {},
            名称 = 随机名称[i],
            模型 = 随机模型[i],
            x = 41, -- 硬编码坐标
            y = 19, -- 硬编码坐标
            方向 = 1, -- 硬编码方向
            副本id = id,
            地图编号 = 地图,
            地图名称 = 取地图名称(地图),
            类型 = TASK_MONSTER_STAGE8,
        }
        地图处理类:添加单位(任务id)
        -- 如果只需要一个Boss，可以在这里break
        break
    end
end


function 副本_黑风山:设置黑风山副本(id)
    local 副本实例 = 副本数据.黑风山.进行[id]
    if 副本实例 == nil then return end

    local 进程 = 副本实例.进程

    -- 清理当前地图上的所有副本单位 (如果需要，可以在每个阶段函数中单独处理清理)
    -- 遍历所有任务数据，查找属于当前副本的单位并删除
    for task_id, task_data in pairs(任务数据) do
        -- 检查任务是否属于当前副本实例
        if task_data.副本id == id then
            -- 检查任务是否是地图上的单位 (有地图编号和单位编号)
            if task_data.地图编号 and task_data.单位编号 then
                地图处理类:删除单位(task_data.地图编号, task_data.单位编号)
            end
            -- 从任务数据中移除该任务
            -- 注意：这里不应该删除主任务，只删除阶段生成的单位任务
            -- 需要更精确的判断，例如根据任务类型是否是怪物或协助任务
            if task_data.类型 ~= FUBEN_ID then -- 假设FUBEN_ID是主任务类型
                 任务数据[task_id] = nil
            end
        end
    end


    -- 根据进程调用相应的阶段设置函数
    if 进程 == STAGE.INIT_ASSIST_TASKS then
        self:设置黑风山阶段1(id)
    elseif 进程 == STAGE.FIGHT_MONSTERS_STAGE2 then
        self:设置黑风山阶段2(id)
    elseif 进程 == STAGE.FIGHT_MONSTER_STAGE3 then
        self:设置黑风山阶段3(id)
    elseif 进程 == STAGE.FIGHT_MONSTER_STAGE4 then
        self:设置黑风山阶段4(id)
    elseif 进程 == STAGE.FIGHT_MONSTERS_STAGE5 then
        self:设置黑风山阶段5(id)
    elseif 进程 == STAGE.FIGHT_MONSTER_STAGE6 then
        self:设置黑风山阶段6(id)
    elseif 进程 == STAGE.FIGHT_MONSTERS_STAGE7 then
        self:设置黑风山阶段7(id)
    elseif 进程 == STAGE.FIGHT_MONSTER_STAGE8 then
        self:设置黑风山阶段8(id)
    end
end
__GWdh111[83]=function(连接id,数字id,序列,标识,地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗==nil then
    local 副本id=任务数据[标识].副本id
    if (任务数据[玩家数据[数字id].角色:取任务(80)] or {}).副本id ~= 副本id then 添加最后对话(数字id,"我好像不认识你吧？？？") return end
    对话数据.对话="听说吃了唐僧的肉可以长生不老！"
    对话数据.选项={"送你轮回","我只是路过"}
  else
    对话数据.对话="我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[83]=function (连接id,数字id,序号,内容)
  local 事件=内容[1]
  local 名称=内容[3]
  if 事件=="送你轮回" then
    if 任务数据[玩家数据[数字id].地图单位.标识]==nil then return end
    if 任务数据[玩家数据[数字id].地图单位.标识].战斗~=nil then 常规提示(数字id,"#Y/对方正在战斗中") return  end
    if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
    if 取队伍人数(数字id)<1 then 常规提示(数字id,"#Y/挑战黑风山最少要有五人") return  end
    if 取等级要求(数字id,60)==false then 常规提示(数字id,"#Y/挑战黑风山至少要达到60级") return  end
    战斗准备类:创建战斗(数字id+0,155567,玩家数据[数字id].地图单位.标识)
    任务数据[玩家数据[数字id].地图单位.标识].战斗=true
    玩家数据[数字id].地图单位=nil
    return
  end
end
__GWdh111[84]=function(连接id,数字id,序列,标识,地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗==nil then
    local 副本id=任务数据[标识].副本id
    if (任务数据[玩家数据[数字id].角色:取任务(80)] or {}).副本id ~= 副本id then 添加最后对话(数字id,"我好像不认识你吧？？？") return end
    对话数据.对话="嗷呜！人肉的味道真是鲜美！"
    对话数据.选项={"去死吧","我只是路过"}
  else
    对话数据.对话="我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[84]=function (连接id,数字id,序号,内容)
  local 事件=内容[1]
  local 名称=内容[3]
  if 事件=="去死吧" then
    if 任务数据[玩家数据[数字id].地图单位.标识]==nil then return end
    if 任务数据[玩家数据[数字id].地图单位.标识].战斗~=nil then 常规提示(数字id,"#Y/对方正在战斗中") return  end
    if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
    if 取队伍人数(数字id)<1 then 常规提示(数字id,"#Y/挑战黑风山最少要有五人") return  end
    if 取等级要求(数字id,60)==false then 常规提示(数字id,"#Y/挑战黑风山至少要达到60级") return  end
    战斗准备类:创建战斗(数字id+0,155568,玩家数据[数字id].地图单位.标识)
    任务数据[玩家数据[数字id].地图单位.标识].战斗=true
    玩家数据[数字id].地图单位=nil
    return
  end
end
__GWdh111[85]=function(连接id,数字id,序列,标识,地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗==nil then
    local 副本id=任务数据[标识].副本id
    if (任务数据[玩家数据[数字id].角色:取任务(80)] or {}).副本id ~= 副本id then 添加最后对话(数字id,"我好像不认识你吧？？？") return end
    对话数据.对话="看那袈裟可是个好东西！"
    对话数据.选项={"鬼鬼祟祟的在干嘛","我只是路过"}
  else
    对话数据.对话="我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[85]=function (连接id,数字id,序号,内容)
  local 事件=内容[1]
  local 名称=内容[3]
  if 事件=="鬼鬼祟祟的在干嘛" then
    if 任务数据[玩家数据[数字id].地图单位.标识]==nil then return end
    if 任务数据[玩家数据[数字id].地图单位.标识].战斗~=nil then 常规提示(数字id,"#Y/对方正在战斗中") return  end
    if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
    if 取队伍人数(数字id)<1 then 常规提示(数字id,"#Y/挑战黑风山最少要有五人") return  end
    if 取等级要求(数字id,60)==false then 常规提示(数字id,"#Y/挑战黑风山至少要达到60级") return  end
    战斗准备类:创建战斗(数字id+0,155569,玩家数据[数字id].地图单位.标识)
    任务数据[玩家数据[数字id].地图单位.标识].战斗=true
    玩家数据[数字id].地图单位=nil
    return
  end
end
__GWdh111[86]=function(连接id,数字id,序列,标识,地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗==nil then
    local 副本id=任务数据[标识].副本id
    if (任务数据[玩家数据[数字id].角色:取任务(80)] or {}).副本id ~= 副本id then 添加最后对话(数字id,"我好像不认识你吧？？？") return end
    对话数据.对话="火！火！火！火！火！火！"
    对话数据.选项={"灭火","我只是路过"}
  else
    对话数据.对话="我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[86]=function (连接id,数字id,序号,内容)
  local 事件=内容[1]
  local 名称=内容[3]
  if 事件=="灭火" then
    if 任务数据[玩家数据[数字id].地图单位.标识]==nil then return end
    if 任务数据[玩家数据[数字id].地图单位.标识].战斗~=nil then 常规提示(数字id,"#Y/对方正在战斗中") return  end
    if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
    if 取队伍人数(数字id)<1 then 常规提示(数字id,"#Y/挑战黑风山最少要有五人") return  end
    if 取等级要求(数字id,60)==false then 常规提示(数字id,"#Y/挑战黑风山至少要达到60级") return  end
    战斗准备类:创建战斗(数字id+0,155571,玩家数据[数字id].地图单位.标识)
    任务数据[玩家数据[数字id].地图单位.标识].战斗=true
    玩家数据[数字id].地图单位=nil
    return
  end
end
__GWdh111[87]=function(连接id,数字id,序列,标识,地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗==nil then
    local 副本id=任务数据[标识].副本id
    if (任务数据[玩家数据[数字id].角色:取任务(80)] or {}).副本id ~= 副本id then 添加最后对话(数字id,"我好像不认识你吧？？？") return end
    对话数据.对话="你是谁，你来此作甚！"
    对话数据.选项={"看打","我只是路过"}
  else
    对话数据.对话="我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[87]=function (连接id,数字id,序号,内容)
  local 事件=内容[1]
  local 名称=内容[3]
  if 事件=="看打" then
    if 任务数据[玩家数据[数字id].地图单位.标识]==nil then return end
    if 任务数据[玩家数据[数字id].地图单位.标识].战斗~=nil then 常规提示(数字id,"#Y/对方正在战斗中") return  end
    if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
    if 取队伍人数(数字id)<1 then 常规提示(数字id,"#Y/挑战黑风山最少要有五人") return  end
    if 取等级要求(数字id,60)==false then 常规提示(数字id,"#Y/挑战黑风山至少要达到60级") return  end
    战斗准备类:创建战斗(数字id+0,155572,玩家数据[数字id].地图单位.标识)
    任务数据[玩家数据[数字id].地图单位.标识].战斗=true
    玩家数据[数字id].地图单位=nil
    return
  end
end
__GWdh111[88]=function(连接id,数字id,序列,标识,地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗==nil then
    local 副本id=任务数据[标识].副本id
    if (任务数据[玩家数据[数字id].角色:取任务(80)] or {}).副本id ~= 副本id then 添加最后对话(数字id,"我好像不认识你吧？？？") return end
    对话数据.对话="你是谁，你来此作甚！"
    对话数据.选项={"看打","我只是路过"}
  else
    对话数据.对话="我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[88]=function (连接id,数字id,序号,内容)
  local 事件=内容[1]
  local 名称=内容[3]
  if 事件=="看打" then
    if 任务数据[玩家数据[数字id].地图单位.标识]==nil then return end
    if 任务数据[玩家数据[数字id].地图单位.标识].战斗~=nil then 常规提示(数字id,"#Y/对方正在战斗中") return  end
    if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
    if 取队伍人数(数字id)<1 then 常规提示(数字id,"#Y/挑战黑风山最少要有五人") return  end
    if 取等级要求(数字id,60)==false then 常规提示(数字id,"#Y/挑战黑风山至少要达到60级") return  end
    战斗准备类:创建战斗(数字id+0,155573,玩家数据[数字id].地图单位.标识)
    任务数据[玩家数据[数字id].地图单位.标识].战斗=true
    玩家数据[数字id].地图单位=nil
    return
  end
end
__GWdh111[89]=function(连接id,数字id,序列,标识,地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗==nil then
    local 副本id=任务数据[标识].副本id
    if (任务数据[玩家数据[数字id].角色:取任务(80)] or {}).副本id ~= 副本id then 添加最后对话(数字id,"我好像不认识你吧？？？") return end
    对话数据.对话="这袈裟真是难得一见的宝物啊！"
    对话数据.选项={"把袈裟交出来","我只是路过"}
  else
    对话数据.对话="我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[89]=function (连接id,数字id,序号,内容)
  local 事件=内容[1]
  local 名称=内容[3]
  if 事件=="把袈裟交出来" then
    if 任务数据[玩家数据[数字id].地图单位.标识]==nil then return end
    if 任务数据[玩家数据[数字id].地图单位.标识].战斗~=nil then 常规提示(数字id,"#Y/对方正在战斗中") return  end
    if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
    if 取队伍人数(数字id)<1 then 常规提示(数字id,"#Y/挑战黑风山最少要有五人") return  end
    if 取等级要求(数字id,60)==false then 常规提示(数字id,"#Y/挑战黑风山至少要达到60级") return  end
    战斗准备类:创建战斗(数字id+0,155574,玩家数据[数字id].地图单位.标识)
    任务数据[玩家数据[数字id].地图单位.标识].战斗=true
    玩家数据[数字id].地图单位=nil
    return
  end
end
function 副本_黑风山:结束黑风山副本(任务id)
  local 副本id = 任务数据[任务id].副本id
  local 主任务id = 副本数据.黑风山.进行[副本id].主任务id
  副本数据.黑风山.进行[副本id]=nil
  local id组 = 任务数据[主任务id].队伍组
  任务数据[主任务id]=nil
  for i=1,#id组 do
    副本数据.黑风山.完成[id组[i]]=true
    if 玩家数据[id组[i]] then
      玩家数据[id组[i]].角色:取消任务(80)
      玩家数据[id组[i]].角色:刷新任务跟踪()
      if 玩家数据[id组[i]].战斗 ~= 0 and ((战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型==155566 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型==155567 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型==155568 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型==155569 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型==155570 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型==155571 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型==155572 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型==155573  or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型==155574) then
        战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]:结束战斗(0,0,1)
      end
    end
  end
  for i in pairs(任务数据) do
    if 任务数据[i].类型 >=80 and 任务数据[i].类型 <=89 and 任务数据[i].副本id == 副本id then
      if 任务数据[i].地图编号 and 任务数据[i].单位编号 then
        地图处理类:删除单位(任务数据[i].地图编号,任务数据[i].单位编号)
      end
      任务数据[i]=nil
    end
  end
end
function 黑风山副本失败(任务id)
  if os.time()-任务数据[任务id].起始>=任务数据[任务id].结束 and 任务数据[任务id].结束 ~= 99999999 then
    if 任务数据[任务id].战斗~=true then  --未进入战斗状态
      if  任务数据[任务id].类型== 80 then
        for i=1,#任务数据[任务id].队伍组 do
          if 玩家数据[任务数据[任务id].队伍组[i]] ~= nil then
            玩家数据[任务数据[任务id].队伍组[i]].角色:取消任务(玩家数据[任务数据[任务id].队伍组[i]].角色:取任务(80))
          end
        end
        任务数据[任务id]=nil
      else
        任务处理类:删除单位(任务id)
      end
    end
  end
end
function 副本_黑风山:黑风山打扫(id)
  if 玩家数据[id].角色:取任务(81) ~= 0 then
    添加最后对话(id,"少侠身上已经任务，请先完成任务再来！")
    return
  end
  local 地图=6018
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 任务id="_81_"..os.time().."_"..随机序列.."_"..取随机数(1,9999)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=1200,
    玩家id=id,
    队伍组={},
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=81,
    副本id = 任务数据[玩家数据[id].角色:取任务(80)].副本id
  }
  玩家数据[id].角色:添加任务(任务id)
  添加最后对话(id,format("麻烦你帮我到%s,%s处打扫一下吧！",任务数据[任务id].x,任务数据[任务id].y))
end
function 副本_黑风山:黑风山巡逻(id)
  if 玩家数据[id].角色:取任务(82) ~= 0 then
    添加最后对话(id,"少侠身上已经任务，请先完成任务再来！")
    return
  end
  local 地图=6018
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 任务id="_82_"..os.time().."_"..随机序列.."_"..取随机数(1,999)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=1200,
    玩家id=id,
    类型=82,
    队伍组={},
    副本id = 任务数据[玩家数据[id].角色:取任务(80)].副本id
  }
  玩家数据[id].角色:添加任务(任务id)
  添加最后对话(id,format("法会就要开始了，少侠在附近巡逻一番吧！",任务数据[任务id].目标))
end
function 副本_黑风山:完成黑风山任务(id组,任务id,战斗类型)
  local 奖励
  if 任务数据[任务id] then
    local 副本id=任务数据[任务id].副本id
    local 进度刷新=false
    for i=1,#id组 do
      local id = id组[i]
      local 等级 = 玩家数据[id].角色.等级
      local 经验
      local 银子
      local 储备
      if 战斗类型==81 then --黑风山打扫
        if 玩家数据[id].角色:取任务(80)~=0 and 副本数据.黑风山.进行[副本id].协助任务.婆婆 > 0 then
   local 等级=玩家数据[id].角色.等级
    local 经验=等级*取随机数(140,145)
    local 银子=等级*取随机数(90,120)
    玩家数据[id].角色:添加经验(经验,"初出江湖")
    玩家数据[id].角色:添加储备(银子,"初出江湖",1)
    玩家数据[id].战斗=0
    if 取随机数()<=25 then
      local 奖励参数=取随机数(1,100)
      链接={提示=format("#S(副本-黑风山)#R/%s#Y/在#R/黑风山#Y/表现卓越，额外获得了#G",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
      if 奖励参数<=10 then
        local 名称="金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=15 then
        local 名称="超级金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=16 then
        local 名称="召唤兽内丹"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=30 then
        local 名称="魔兽要诀"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=35 then
        local 名称="五宝盒"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)

      elseif 奖励参数<=50 then
        local 名称=取强化石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=55 then
        local 名称="九转金丹"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,150),nil,链接)
      elseif 奖励参数<=60 then
        local 名称=取宝石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=63 then
        local 名称=取宝宝装备()
        local lv = math.min(qz(等级/10),13)
        玩家数据[id].道具:给予超链接道具(id,名称,{lv+1,lv+2},nil,链接)
      elseif 奖励参数<=75 then
        local 名称="未激活的符石"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(1,1),nil,链接)
      end
    end
        end
      elseif 战斗类型==100096 then --黑风山巡逻
        if 玩家数据[id].角色:取任务(80)~=0 and 副本数据.黑风山.进行[副本id].协助任务.镇山太保 > 0 then
   local 等级=玩家数据[id].角色.等级
    local 经验=等级*取随机数(140,145)
    local 银子=等级*取随机数(90,120)
    玩家数据[id].角色:添加经验(经验,"初出江湖")
    玩家数据[id].角色:添加储备(银子,"初出江湖",1)
    玩家数据[id].战斗=0
    if 取随机数()<=25 then
      local 奖励参数=取随机数(1,100)
      链接={提示=format("#S(副本-黑风山)#R/%s#Y/在#R/黑风山#Y/表现卓越，额外获得了#G",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
      if 奖励参数<=10 then
        local 名称="金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=15 then
        local 名称="超级金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=16 then
        local 名称="召唤兽内丹"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=30 then
        local 名称="魔兽要诀"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=35 then
        local 名称="五宝盒"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)

      elseif 奖励参数<=50 then
        local 名称=取强化石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=55 then
        local 名称="九转金丹"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,150),nil,链接)
      elseif 奖励参数<=60 then
        local 名称=取宝石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=63 then
        local 名称=取宝宝装备()
        local lv = math.min(qz(等级/10),13)
        玩家数据[id].道具:给予超链接道具(id,名称,{lv+1,lv+2},nil,链接)
      elseif 奖励参数<=75 then
        local 名称="未激活的符石"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(1,1),nil,链接)
      end
    end
        end
      elseif 战斗类型==82 then --黑风山答题
        if 玩家数据[id].角色:取任务(80)~=0 and 副本数据.黑风山.进行[副本id].协助任务.唐玄奘 > 0 then
   local 等级=玩家数据[id].角色.等级
    local 经验=等级*取随机数(140,145)
    local 银子=等级*取随机数(90,120)
    玩家数据[id].角色:添加经验(经验,"初出江湖")
    玩家数据[id].角色:添加储备(银子,"初出江湖",1)
    玩家数据[id].战斗=0
    if 取随机数()<=25 then
      local 奖励参数=取随机数(1,100)
      链接={提示=format("#S(副本-黑风山)#R/%s#Y/在#R/黑风山#Y/表现卓越，额外获得了#G",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
      if 奖励参数<=10 then
        local 名称="金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=15 then
        local 名称="超级金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=16 then
        local 名称="召唤兽内丹"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=30 then
        local 名称="魔兽要诀"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=35 then
        local 名称="五宝盒"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)

      elseif 奖励参数<=50 then
        local 名称=取强化石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=55 then
        local 名称="九转金丹"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,150),nil,链接)
      elseif 奖励参数<=60 then
        local 名称=取宝石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=63 then
        local 名称=取宝宝装备()
        local lv = math.min(qz(等级/10),13)
        玩家数据[id].道具:给予超链接道具(id,名称,{lv+1,lv+2},nil,链接)
      elseif 奖励参数<=75 then
        local 名称="未激活的符石"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(1,1),nil,链接)
      end
    end
        end
      elseif 战斗类型==155567 then
    local 等级=玩家数据[id].角色.等级
    local 经验=等级*取随机数(140,145)
    local 银子=等级*取随机数(90,120)
    玩家数据[id].角色:添加经验(经验,"初出江湖")
    玩家数据[id].角色:添加储备(银子,"初出江湖",1)
    玩家数据[id].战斗=0
    if 取随机数()<=25 then
      local 奖励参数=取随机数(1,100)
      链接={提示=format("#S(副本-黑风山)#R/%s#Y/在#R/黑风山#Y/表现卓越，额外获得了#G",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
      if 奖励参数<=10 then
        local 名称="金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=15 then
        local 名称="超级金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=16 then
        local 名称="召唤兽内丹"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=30 then
        local 名称="魔兽要诀"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=35 then
        local 名称="五宝盒"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)

      elseif 奖励参数<=50 then
        local 名称=取强化石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=55 then
        local 名称="九转金丹"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,150),nil,链接)
      elseif 奖励参数<=60 then
        local 名称=取宝石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=63 then
        local 名称=取宝宝装备()
        local lv = math.min(qz(等级/10),13)
        玩家数据[id].道具:给予超链接道具(id,名称,{lv+1,lv+2},nil,链接)
      elseif 奖励参数<=75 then
        local 名称="未激活的符石"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(1,1),nil,链接)
      end
    end
      elseif 战斗类型==155568 then
    local 等级=玩家数据[id].角色.等级
    local 经验=等级*取随机数(140,145)
    local 银子=等级*取随机数(90,120)
    玩家数据[id].角色:添加经验(经验,"初出江湖")
    玩家数据[id].角色:添加储备(银子,"初出江湖",1)
    玩家数据[id].战斗=0
    if 取随机数()<=25 then
      local 奖励参数=取随机数(1,100)
      链接={提示=format("#S(副本-黑风山)#R/%s#Y/在#R/黑风山#Y/表现卓越，额外获得了#G",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
      if 奖励参数<=10 then
        local 名称="金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=15 then
        local 名称="超级金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=16 then
        local 名称="召唤兽内丹"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=30 then
        local 名称="魔兽要诀"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=35 then
        local 名称="五宝盒"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)

      elseif 奖励参数<=50 then
        local 名称=取强化石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=55 then
        local 名称="九转金丹"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,150),nil,链接)
      elseif 奖励参数<=60 then
        local 名称=取宝石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=63 then
        local 名称=取宝宝装备()
        local lv = math.min(qz(等级/10),13)
        玩家数据[id].道具:给予超链接道具(id,名称,{lv+1,lv+2},nil,链接)
      elseif 奖励参数<=75 then
        local 名称="未激活的符石"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(1,1),nil,链接)
      end
    end
      elseif 战斗类型==155569 then
    local 等级=玩家数据[id].角色.等级
    local 经验=等级*取随机数(140,145)
    local 银子=等级*取随机数(90,120)
    玩家数据[id].角色:添加经验(经验,"初出江湖")
    玩家数据[id].角色:添加储备(银子,"初出江湖",1)
    玩家数据[id].战斗=0
    if 取随机数()<=25 then
      local 奖励参数=取随机数(1,100)
      链接={提示=format("#S(副本-黑风山)#R/%s#Y/在#R/黑风山#Y/表现卓越，额外获得了#G",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
      if 奖励参数<=10 then
        local 名称="金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=15 then
        local 名称="超级金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=16 then
        local 名称="召唤兽内丹"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=30 then
        local 名称="魔兽要诀"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=35 then
        local 名称="五宝盒"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)

      elseif 奖励参数<=50 then
        local 名称=取强化石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=55 then
        local 名称="九转金丹"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,150),nil,链接)
      elseif 奖励参数<=60 then
        local 名称=取宝石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=63 then
        local 名称=取宝宝装备()
        local lv = math.min(qz(等级/10),13)
        玩家数据[id].道具:给予超链接道具(id,名称,{lv+1,lv+2},nil,链接)
      elseif 奖励参数<=75 then
        local 名称="未激活的符石"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(1,1),nil,链接)
      end
    end
      elseif 战斗类型==155571 then
    local 等级=玩家数据[id].角色.等级
    local 经验=等级*取随机数(140,145)
    local 银子=等级*取随机数(90,120)
    玩家数据[id].角色:添加经验(经验,"初出江湖")
    玩家数据[id].角色:添加储备(银子,"初出江湖",1)
    玩家数据[id].战斗=0
    if 取随机数()<=25 then
      local 奖励参数=取随机数(1,100)
      链接={提示=format("#S(副本-黑风山)#R/%s#Y/在#R/黑风山#Y/表现卓越，额外获得了#G",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
      if 奖励参数<=10 then
        local 名称="金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=15 then
        local 名称="超级金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=16 then
        local 名称="召唤兽内丹"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=30 then
        local 名称="魔兽要诀"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=35 then
        local 名称="五宝盒"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)

      elseif 奖励参数<=50 then
        local 名称=取强化石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=55 then
        local 名称="九转金丹"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,150),nil,链接)
      elseif 奖励参数<=60 then
        local 名称=取宝石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=63 then
        local 名称=取宝宝装备()
        local lv = math.min(qz(等级/10),13)
        玩家数据[id].道具:给予超链接道具(id,名称,{lv+1,lv+2},nil,链接)
      elseif 奖励参数<=75 then
        local 名称="未激活的符石"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(1,1),nil,链接)
      end
    end
      elseif 战斗类型==155572 then
    local 等级=玩家数据[id].角色.等级
    local 经验=等级*取随机数(140,145)
    local 银子=等级*取随机数(90,120)
    玩家数据[id].角色:添加经验(经验,"初出江湖")
    玩家数据[id].角色:添加储备(银子,"初出江湖",1)
    玩家数据[id].战斗=0
    if 取随机数()<=25 then
      local 奖励参数=取随机数(1,100)
      链接={提示=format("#S(副本-黑风山)#R/%s#Y/在#R/黑风山#Y/表现卓越，额外获得了#G",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
            if 奖励参数<=10 then
        local 名称="金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=15 then
        local 名称="超级金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=16 then
        local 名称="召唤兽内丹"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=30 then
        local 名称="魔兽要诀"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=35 then
        local 名称="五宝盒"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)

      elseif 奖励参数<=50 then
        local 名称=取强化石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=55 then
        local 名称="九转金丹"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,150),nil,链接)
      elseif 奖励参数<=60 then
        local 名称=取宝石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=63 then
        local 名称=取宝宝装备()
        local lv = math.min(qz(等级/10),13)
        玩家数据[id].道具:给予超链接道具(id,名称,{lv+1,lv+2},nil,链接)
      elseif 奖励参数<=75 then
        local 名称="未激活的符石"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(1,1),nil,链接)
      end
    end
      elseif 战斗类型==155573 then
    local 等级=玩家数据[id].角色.等级
    local 经验=等级*取随机数(140,145)
    local 银子=等级*取随机数(90,120)
    玩家数据[id].角色:添加经验(经验,"初出江湖")
    玩家数据[id].角色:添加储备(银子,"初出江湖",1)
    玩家数据[id].战斗=0
    if 取随机数()<=25 then
      local 奖励参数=取随机数(1,100)
      链接={提示=format("#S(副本-黑风山)#R/%s#Y/在#R/黑风山#Y/表现卓越，额外获得了#G",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
            if 奖励参数<=10 then
        local 名称="金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=15 then
        local 名称="超级金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=16 then
        local 名称="召唤兽内丹"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=30 then
        local 名称="魔兽要诀"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=35 then
        local 名称="五宝盒"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)

      elseif 奖励参数<=50 then
        local 名称=取强化石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=55 then
        local 名称="九转金丹"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,150),nil,链接)
      elseif 奖励参数<=60 then
        local 名称=取宝石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=63 then
        local 名称=取宝宝装备()
        local lv = math.min(qz(等级/10),13)
        玩家数据[id].道具:给予超链接道具(id,名称,{lv+1,lv+2},nil,链接)
      elseif 奖励参数<=75 then
        local 名称="未激活的符石"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(1,1),nil,链接)
      end
    end
      elseif 战斗类型==155574 then
    local 等级=玩家数据[id].角色.等级
    local 经验=等级*取随机数(140,145)
    local 银子=等级*取随机数(90,120)
    玩家数据[id].角色:添加经验(经验,"初出江湖")
    玩家数据[id].角色:添加储备(银子,"初出江湖",1)
    玩家数据[id].战斗=0
    if 取随机数()<=25 then
      local 奖励参数=取随机数(1,100)
      链接={提示=format("#S(副本-黑风山)#R/%s#Y/在#R/黑风山#Y/表现卓越，额外获得了#G",玩家数据[id].角色.名称),频道="xt",结尾="#Y。"}
            if 奖励参数<=10 then
        local 名称="金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=15 then
        local 名称="超级金柳露"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=16 then
        local 名称="召唤兽内丹"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=30 then
        local 名称="魔兽要诀"
        玩家数据[id].道具:给予超链接道具(id,名称,nil,nil,链接)
      elseif 奖励参数<=35 then
        local 名称="五宝盒"
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)

      elseif 奖励参数<=50 then
        local 名称=取强化石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=55 then
        local 名称="九转金丹"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(100,150),nil,链接)
      elseif 奖励参数<=60 then
        local 名称=取宝石()
        玩家数据[id].道具:给予超链接道具(id,名称,1,nil,链接)
      elseif 奖励参数<=63 then
        local 名称=取宝宝装备()
        local lv = math.min(qz(等级/10),13)
        玩家数据[id].道具:给予超链接道具(id,名称,{lv+1,lv+2},nil,链接)
      elseif 奖励参数<=75 then
        local 名称="未激活的符石"
        玩家数据[id].道具:给予超链接道具(id,名称,取随机数(1,1),nil,链接)
      end
    end
  end
end
    if 战斗类型==81 then --黑风山打扫
      if 玩家数据[id组[1]].角色:取任务(80) and 副本数据.黑风山.进行[副本id].协助任务.婆婆 > 0 then
        副本数据.黑风山.进行[副本id].协助任务.婆婆 = 副本数据.黑风山.进行[副本id].协助任务.婆婆 - 5
        if 副本数据.黑风山.进行[副本id].协助任务.婆婆 <= 0 then
          取消队伍任务(玩家数据[id组[1]].角色:取任务(80),81)
        else
          玩家数据[id组[1]].角色:取消任务(任务id)
        end
        if 副本数据.黑风山.进行[副本id].协助任务.婆婆 <= 0 and 副本数据.黑风山.进行[副本id].协助任务.镇山太保 <= 0 and 副本数据.黑风山.进行[副本id].协助任务.唐玄奘 <= 0 then
          local 副本id = 任务数据[任务id].副本id
          副本数据.黑风山.进行[副本id].进程 = 2
          self:设置黑风山副本(副本id)
          进度刷新=true
        else
          local id组 = 任务数据[副本数据.黑风山.进行[副本id].主任务id].队伍组
          for i=1,#id组 do
            if 玩家数据[id组[i]] then
              玩家数据[id组[i]].角色:刷新任务跟踪()
            end
          end
          return
        end
      end
    elseif 战斗类型==155566 then --黑风山巡逻
      if 玩家数据[id组[1]].角色:取任务(80) and 副本数据.黑风山.进行[副本id].协助任务.镇山太保 > 0 then
        副本数据.黑风山.进行[副本id].协助任务.镇山太保 = 副本数据.黑风山.进行[副本id].协助任务.镇山太保 - 5
        if 副本数据.黑风山.进行[副本id].协助任务.镇山太保 <= 0 then
          取消队伍任务(玩家数据[id组[1]].角色:取任务(80),82)
        else
          玩家数据[id组[1]].角色:取消任务(任务id)
        end
        if 副本数据.黑风山.进行[副本id].协助任务.婆婆 <= 0 and 副本数据.黑风山.进行[副本id].协助任务.镇山太保 <= 0 and 副本数据.黑风山.进行[副本id].协助任务.唐玄奘 <= 0 then
          local 副本id = 任务数据[任务id].副本id
          副本数据.黑风山.进行[副本id].进程 = 2
          self:设置黑风山副本(副本id)
          进度刷新=true
        end
        local id组 = 任务数据[副本数据.黑风山.进行[副本id].主任务id].队伍组
        for i=1,#id组 do
          if 玩家数据[id组[i]] then
            玩家数据[id组[i]].角色:刷新任务跟踪()
          end
        end
        return
      end
    elseif 战斗类型==82 then --黑风山答题
      if 玩家数据[id组[1]].角色:取任务(80) and 副本数据.黑风山.进行[副本id].协助任务.唐玄奘 > 0 then
        副本数据.黑风山.进行[副本id].协助任务.唐玄奘 = 副本数据.黑风山.进行[副本id].协助任务.唐玄奘 - 5
        if 副本数据.黑风山.进行[副本id].协助任务.婆婆 <= 0 and 副本数据.黑风山.进行[副本id].协助任务.唐玄奘 <= 0 and 副本数据.黑风山.进行[副本id].协助任务.唐玄奘 <= 0 then
          local 副本id = 任务数据[任务id].副本id
          副本数据.黑风山.进行[副本id].进程 = 2
          self:设置黑风山副本(副本id)
          local id组 = 任务数据[副本数据.黑风山.进行[副本id].主任务id].队伍组
          for i=1,#id组 do
            if 玩家数据[id组[i]] then
              玩家数据[id组[i]].角色:刷新任务跟踪()
              常规提示(id组[i],"#Y/您的副本进度已经更新")
            end
          end
        end
        return
      end
    elseif 战斗类型==155567 then
      副本数据.黑风山.进行[副本id].数量=副本数据.黑风山.进行[副本id].数量-1
      if 副本数据.黑风山.进行[副本id].数量 <= 0 then--15
        副本数据.黑风山.进行[副本id].进程=3
        self:设置黑风山副本(副本id)
        进度刷新=true
      end
    elseif 战斗类型==155568 then
      副本数据.黑风山.进行[副本id].进程=4
      self:设置黑风山副本(副本id)
      进度刷新=true
    elseif 战斗类型==155569 then
      副本数据.黑风山.进行[副本id].进程=5
      self:设置黑风山副本(副本id)
      进度刷新=true
      任务处理类:副本传送(id组[1],80)
    elseif 战斗类型==155571 then
      副本数据.黑风山.进行[副本id].数量=副本数据.黑风山.进行[副本id].数量-1
      if 副本数据.黑风山.进行[副本id].数量 <= 0 then--10
        副本数据.黑风山.进行[副本id].进程=6
        self:设置黑风山副本(副本id)
        任务处理类:副本传送(id组[1],80)
        进度刷新=true
      end
    elseif 战斗类型==155572 then
      副本数据.黑风山.进行[副本id].进程=7
      self:设置黑风山副本(副本id)
      进度刷新=true
    elseif 战斗类型==155573 then
      副本数据.黑风山.进行[副本id].数量=副本数据.黑风山.进行[副本id].数量-1
      if 副本数据.黑风山.进行[副本id].数量 <= 0 then--10
        副本数据.黑风山.进行[副本id].进程=8
        self:设置黑风山副本(副本id)
        任务处理类:副本传送(id组[1],80)
        进度刷新=true
      end
    elseif 战斗类型==155574 then
      local id组 = 任务数据[副本数据.黑风山.进行[副本id].主任务id].队伍组
      for i=1,#id组 do
        if 玩家数据[id组[i]] then
          地图处理类:跳转地图(id组[i],1001,358,35)
          --更新玩家每日(id组[i],"副本任务","黑风山")
          玩家数据[id组[i]].角色:添加积分(200,"副本积分")
          常规提示(id组[i],"#Y/恭喜你们完成黑风山副本")
        end
      end
      self:结束黑风山副本(任务id)
      return
    end
    local id组 = 任务数据[副本数据.黑风山.进行[副本id].主任务id].队伍组
    for i=1,#id组 do
      if 玩家数据[id组[i]] then
        玩家数据[id组[i]].角色:刷新任务跟踪()
        if 进度刷新 then
          常规提示(id组[i],"#Y/您的副本进度已经更新")
        end
      end
    end
    if 任务数据[任务id] and 任务数据[任务id].地图编号 and 任务数据[任务id].单位编号 then
      地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
    end
    任务数据[任务id]=nil
  end
end
function 怪物属性:黑风山黑熊精(任务id,玩家id,序号)
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local sx =  self:取属性(等级,"大唐官府","物理")
   战斗单位[1]={
      名称="熊精"
      ,模型="黑熊"
      ,等级=等级
      ,变异=true
      ,气血 = qz(sx.属性.气血)*15
      ,伤害 = qz(sx.属性.伤害)*1.5
      ,法伤 = qz(sx.属性.法伤)
      ,速度 = qz(sx.属性.速度)*2
      ,技能={"感知"}
      ,门派=sx.门派
      ,AI战斗 = {AI=sx.智能}
   }
  for i=2,8 do
      sx = self:取属性(等级)
      战斗单位[i]={
        名称="熊精妖卫"
        ,模型="黑熊精"
        ,等级=等级
        ,气血 = qz(sx.属性.气血)*12
        ,伤害 = qz(sx.属性.伤害)*1.5
        ,法伤 = qz(sx.属性.法伤)
        ,速度 = qz(sx.属性.速度)*2
        ,技能={"感知"}
        ,主动技能=sx.技能组
      }
   end
   return 战斗单位
end
function 怪物属性:黑风山巡山精怪(任务id,玩家id,序号)
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local sx =  self:取属性(等级,"五庄观","物理")
   战斗单位[1]={
      名称="黑熊精队长"
      ,模型="黑熊精"
      ,等级=等级
      ,变异=true
      ,气血 = qz(sx.属性.气血)*10
      ,伤害 = qz(sx.属性.伤害)*1.2
      ,法伤 = qz(sx.属性.法伤)
      ,速度 = qz(sx.属性.速度)*3
      ,技能={"感知","高级必杀","高级偷袭","高级强力","高级防御"}
      ,门派=sx.门派
      ,AI战斗 = {AI=sx.智能}
   }
  for i=2,8 do
      sx = self:取属性(等级)
      战斗单位[i]={
        名称="熊精"
        ,模型="黑熊"
        ,等级=等级
        ,气血 = qz(sx.属性.气血)*9
        ,伤害 = qz(sx.属性.伤害)*1.1
        ,法伤 = qz(sx.属性.法伤)
        ,速度 = qz(sx.属性.速度)*2.2
        ,技能={"感知","高级必杀","高级偷袭","高级强力","高级防御"}
        ,主动技能=sx.技能组
      }
   end
   return 战斗单位
end
function 怪物属性:黑风山三妖(任务id,玩家id,序号)
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local sx
      sx = self:取属性(等级,"阴曹地府","固伤")
      战斗单位[1]={
        名称="黑汉"
        ,模型="黑熊精"
        ,等级=等级
        ,气血 = qz(sx.属性.气血)*10
        ,伤害 = qz(sx.属性.伤害)*1.5
        ,法伤 = qz(sx.属性.法伤)
        ,速度 = qz(sx.属性.速度)*2.5
        ,固定伤害 = qz(等级*8)
        ,治疗能力 = qz(等级)
        ,门派=sx.门派
        ,AI战斗 = {AI=sx.智能}
      }
        sx = self:取属性(等级,"龙宫","神木林")
        战斗单位[2]={
        名称="道人"
        ,模型="雨师"
        ,等级=等级
        ,气血 = qz(sx.属性.气血)*10
        ,伤害 = qz(sx.属性.伤害)*1.2
        ,法伤 = qz(sx.属性.法伤)*1.2
        ,速度 = qz(sx.属性.速度)*2
        ,固定伤害 = qz(等级)
        ,治疗能力 = qz(等级)
        ,技能={"感知","高级魔之心"}
        ,门派=sx.门派
        ,AI战斗 = {AI=sx.智能}
      }
       sx =self:取属性(等级,"女儿村","固伤")
       战斗单位[3]={
        名称="白衣秀士"
        ,模型="净瓶女娲"
        ,等级=等级
        ,气血 = qz(sx.属性.气血)*8
        ,伤害 = qz(sx.属性.伤害)
        ,法伤 = qz(sx.属性.法伤)
        ,速度 = qz(sx.属性.速度)*4
        ,固定伤害 = qz(等级*8)
        ,治疗能力 = qz(等级)
        ,技能={"感知","高级敏捷"}
        ,门派=sx.门派
        ,AI战斗 = {AI=sx.智能}
      }
   return 战斗单位
end
function 怪物属性:黑风山星星火光(任务id,玩家id,序号)
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local sx = self:取属性(等级,"化生寺","法系")
   战斗单位[1]={
      名称="星星火光"
    ,模型="凤凰"
    ,等级=等级
    ,伤害=等级*10
    ,变异=true
    ,气血=等级*等级*0.5+5000
    ,法伤=等级*10
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,门派=sx.门派
    ,技能={}
    ,主动技能=取随机法术(8)
   }
    战斗单位[2]={
      名称="助火凤"
    ,模型="风伯"
    ,伤害=等级*9
    ,气血=等级*等级*0.5+2000
    ,法伤=等级*8
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,等级=等级
    ,技能={}
    ,主动技能=取随机法术(8)
   }
    战斗单位[3]={
      名称="柴火"
    ,模型="树怪"
    ,伤害=等级*15
    ,气血=等级*等级*0.5+1000
    ,法伤=等级*5
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,等级=等级
    ,技能={}
    ,主动技能=取随机法术(8)
   }
  for i=4,10 do
    战斗单位[i]={
    名称="火苗"
    ,模型="凤凰"
    ,伤害=等级*15
    ,气血=等级*等级*0.5+1000
    ,法伤=等级*6
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,等级=等级
    ,技能={}
    ,主动技能=取随机法术(8)
      }
   end
   return 战斗单位
end
function 怪物属性:黑风山熊熊烈火(任务id,玩家id,序号)
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local sx = self:取属性(等级,"阴曹地府","法系")
   战斗单位[1]={
      名称="熊熊烈火"
    ,模型="炎魔神"
    ,变异=true
    ,伤害=等级*10
    ,气血=等级*等级*0.5+5000
    ,法伤=等级*10
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,等级=等级
    ,技能={}
    ,主动技能=取随机法术(8)
   }
    战斗单位[2]={
      名称="助火凤"
    ,模型="风伯"
    ,伤害=等级*9
    ,气血=等级*等级*0.5+2000
    ,法伤=等级*8
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,等级=等级
    ,技能={}
    ,主动技能=取随机法术(8)
   }
    战斗单位[3]={
      名称="柴火"
    ,模型="树怪"
    ,伤害=等级*15
    ,气血=等级*等级*0.5+1000
    ,法伤=等级*5
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,等级=等级
    ,技能={}
    ,主动技能=取随机法术(8)
   }
  for i=4,10 do
    战斗单位[i]={
    名称="火苗"
    ,模型="凤凰"
    ,伤害=等级*15
    ,气血=等级*等级*0.5+1000
    ,法伤=等级*6
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,等级=等级
    ,技能={}
    ,主动技能=取随机法术(8)
      }
   end
   return 战斗单位
end
function 怪物属性:黑风山熊精(任务id,玩家id,序号)
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local sx = self:取属性(等级,"凌波城","物理")
   战斗单位[1]={
      名称="熊精"
      ,模型="黑熊精"
      ,等级=等级
      ,变异=true
      ,气血 = qz(sx.属性.气血)
      ,伤害 = qz(sx.属性.伤害)
      ,法伤 = qz(sx.属性.法伤)
      ,速度 = qz(sx.属性.速度)
      ,技能={"感知"}
      ,门派=sx.门派
      ,AI战斗 = {AI=sx.智能}
   }
  for i=2,10 do
      sx = self:取属性(等级)
      战斗单位[i]={
        名称="黑熊"
        ,模型="黑熊精"
        ,等级=等级
        ,气血 = qz(sx.属性.气血)
        ,伤害 = qz(sx.属性.伤害)
        ,法伤 = qz(sx.属性.法伤)
        ,速度 = qz(sx.属性.速度)
        ,技能={"感知"}
        ,主动技能=sx.技能组
      }
   end
   return 战斗单位
end
function 怪物属性:黑风山恶虎(任务id,玩家id,序号)
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local sx = self:取属性(等级,"狮驼岭","物理")
   战斗单位[1]={
      名称="恶虎"
      ,模型="噬天虎"
      ,等级=等级
      ,变异=true
      ,气血 = qz(sx.属性.气血)
      ,伤害 = qz(sx.属性.伤害)
      ,法伤 = qz(sx.属性.法伤)
      ,速度 = qz(sx.属性.速度)
      ,技能={"感知"}
      ,门派=sx.门派
      ,AI战斗 = {AI=sx.智能}
   }
  for i=2,10 do
      sx = self:取属性(等级)
      战斗单位[i]={
        名称="老虎"
        ,模型="老虎"
        ,等级=等级
        ,气血 = qz(sx.属性.气血)
        ,伤害 = qz(sx.属性.伤害)
        ,法伤 = qz(sx.属性.法伤)
        ,速度 = qz(sx.属性.速度)
        ,技能={"感知"}
        ,主动技能=sx.技能组
      }
   end
   return 战斗单位
end
function 怪物属性:黑风山巡逻(任务id,玩家id,序号)
   local 模型 ={"赌徒","强盗","山贼"}
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local sx
   local 数量=取随机数(2,2)
   for i=1,数量 do
      sx = self:取属性(等级)
      战斗单位[i]={
        名称="强盗"
        ,模型=模型[取随机数(1,#模型)]
        ,等级=等级
        ,气血 = qz(sx.属性.气血)
        ,伤害 = qz(sx.属性.伤害)
        ,法伤 = qz(sx.属性.法伤)
        ,速度 = qz(sx.属性.速度)
        ,固定伤害 = qz(等级)
        ,治疗能力 = qz(等级)
        ,主动技能=sx.技能组
      }
   end
   return 战斗单位
end
function 怪物属性:黑风山奇怪的马面(任务id,玩家id,序号)
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local 数量=取随机数(2,3)
   for i=1,数量 do
      战斗单位[i]={
    名称="奇怪的马面"
    ,模型="马面"
    ,伤害=等级*15
    ,气血=等级*等级*0.5
    ,法伤=等级*4
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,等级=等级
    ,技能={}
    ,主动技能=取随机法术(3)
      }
   end
   return 战斗单位
end
function 怪物属性:黑风山吊死的鬼魂(任务id,玩家id,序号)
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local 数量=取随机数(2,2)
   for i=1,数量 do
      战斗单位[i]={
    名称="吊死的鬼魂"
    ,模型="野鬼"
    ,伤害=等级*15
    ,气血=等级*等级*0.5
    ,法伤=等级*4
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,等级=等级
    ,技能={}
    ,主动技能=取随机法术(3)
      }
   end
   return 战斗单位
end
function 怪物属性:黑风山懵懂的僵尸(任务id,玩家id,序号)
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local 数量=取随机数(2,2)
   for i=1,数量 do
      战斗单位[i]={
    名称="懵懂的僵尸"
    ,模型="僵尸"
    ,伤害=等级*15
    ,气血=等级*等级*0.5
    ,法伤=等级*4
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,等级=等级
    ,技能={}
    ,主动技能=取随机法术(3)
      }
   end
   return 战斗单位
end
function 怪物属性:黑风山绝望的牛头(任务id,玩家id,序号)
   local 战斗单位={}
   local 等级=取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
   local 数量=取随机数(2,2)
   for i=1,数量 do
      战斗单位[i]={
      名称="绝望的牛头"
    ,模型="牛头"
    ,伤害=等级*15
    ,气血=等级*等级*0.5
    ,法伤=等级*4
    ,速度=等级*3.5
    ,防御=等级*8
    ,法防=等级
    ,躲闪=等级*2
    ,魔法=200
    ,等级=等级
    ,技能={}
    ,主动技能=取随机法术(3)
      }
   end
   return 战斗单位
end
return 副本_黑风山