-- @Author: baidwwy
-- @Date:   2024-05-13 00:08:03
-- @Last Modified by:   baidwwy
-- @Last Modified time: 2025-02-01 20:39:37

-- 在文件开头添加每周重置的判断函数
function 检查轮回境重置()
    -- 获取当前时间戳
    local current_time = os.time()
    -- 获取当前时间的详细信息
    local time_info = os.date("*t", current_time)
    
    -- 如果副本数据中没有上次重置时间,则初始化
    if not 副本数据.轮回境.上次重置时间 then
        副本数据.轮回境.上次重置时间 = current_time
        return true
    end
    
    -- 获取上次重置时间的详细信息
    local last_reset = os.date("*t", 副本数据.轮回境.上次重置时间)
    
    -- 计算周数差异
    local weeks_current = math.floor((time_info.yday - 1 + time_info.wday) / 7)
    local weeks_last = math.floor((last_reset.yday - 1 + last_reset.wday) / 7)
    
    -- 如果年份不同或者周数不同,则需要重置
    if time_info.year > last_reset.year or 
       (time_info.year == last_reset.year and weeks_current > weeks_last) then
        副本数据.轮回境.上次重置时间 = current_time
        return true
    end
    
    return false
end

local sj = 取随机数
local format = string.format
local insert = table.insert
local ceil = math.ceil
local floor = math.floor
local wps = 取物品数据
local typ = type
local random = 取随机数
function 设置任务630(id)
	if 玩家数据[id].队伍==0 or 玩家数据[id].队长==false  then
		常规提示(id,"#Y/该任务必须组队完成且由队长领取")
		return
	elseif 取队伍人数(id)<5 and 调试模式==false then
		常规提示(id,"#Y此副本要求队伍人数不低于5人")
		return
	elseif  取等级要求(id,155)==false then
		常规提示(id,"#Y此副本要求角色等级不能低于155级")
		return
	end
	
	-- 检查是否需要重置
	if 检查轮回境重置() then
		副本数据.轮回境.完成 = {} -- 重置完成记录
	end
	
	local 队伍id=玩家数据[id].队伍
	for n=1,#队伍数据[队伍id].成员数据 do
		local 临时id=队伍数据[队伍id].成员数据[n]
		if 副本数据.轮回境.完成[临时id]~=nil then
			常规提示(id,"#Y"..玩家数据[临时id].角色.名称.."本周已经完成过此副本了")
			return
		elseif 玩家数据[临时id].角色:取任务(630)~=0 then
			常规提示(id,"#Y"..玩家数据[临时id].角色.名称.."正在进行副本任务，无法领取新的副本")
			return
		end
	end
	副本数据.轮回境.进行[id]={进程=1}
	GetUpMOB630(id)
	local 任务id=id.."_630_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999)
	任务数据[任务id]={
	id=任务id,
	起始=os.time(),
	结束=7200,
	玩家id=0,
	队伍组=table.loadstring(table.tostring(队伍数据[玩家数据[id].队伍].成员数据)),
	副本id=id,
	类型=630
	}
	任务处理类:添加队伍任务(id,任务id,"#Y你开启了轮回境副本")
end
function GetUpMOB630(id)
	if 副本数据.轮回境.进行[id]==nil then
		return
	end
	if 副本数据.轮回境.进行[id].进程==1 then
		local 任务id=id.."_631_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=2000
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="中央鬼帝",
		模型="阎罗王",
		显示饰品=true,
		x=112,
		y=72,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		方向=0,
		类型=631
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_631_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=2000
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="",
		模型="轮回门关",
		x=93.5,
		y=61.5,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		方向=0,
		类型=631.5
		}
		地图处理类:添加单位(任务id)		
	elseif 副本数据.轮回境.进行[id].进程==2 then
		local 任务id=id.."_632_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=2000
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="九灵元圣",
		模型="大大王",
		显示饰品=true,
		x=112,
		y=72,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		方向=0,
		类型=632
		}
		地图处理类:添加单位(任务id)
	elseif 副本数据.轮回境.进行[id].进程==3 then
		local 任务id=id.."_633_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=2000
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="华岳圣母",
		模型="进阶曼珠沙华",
		显示饰品=true,
		x=112,
		y=72,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		方向=0,
		类型=633
		}
		地图处理类:添加单位(任务id)
	elseif 副本数据.轮回境.进行[id].进程==4 then
		local 任务id=id.."_634_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=2000
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="南斗星君",
		模型="进阶天兵",
		显示饰品=true,
		x=112,
		y=72,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		方向=0,
		类型=634
		}
		地图处理类:添加单位(任务id)
	elseif 副本数据.轮回境.进行[id].进程==5 then
		local 任务id=id.."_635_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=2000
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="天龙八部",
		模型="进阶金身罗汉",
		显示饰品=true,
		x=112,
		y=72,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		方向=0,
		类型=635
		}
		地图处理类:添加单位(任务id)
	elseif 副本数据.轮回境.进行[id].进程==6 then
		local 任务id=id.."_636_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=2000
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="无上天尊",
		模型="进阶风伯",
		x=112,
		y=72,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		方向=0,
		类型=636
		}
		地图处理类:添加单位(任务id)
	elseif 副本数据.轮回境.进行[id].进程==7 then
		local 任务id=id.."_637_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=2000
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="自在天魔",
		模型="自在天魔",
		x=20,
		y=18,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		类型=637
		}
		地图处理类:添加单位(任务id)
	end
end
__GWdh111[631]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,630)then
		return
	end
	local 副本id = 任务处理类:取副本id(数字id,630)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 任务数据[标识].战斗==nil then
		if 副本数据.轮回境.进行[数字id].进程==1  then
			if 取队伍人数(数字id)<1  and 调试模式==false then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[标识].战斗=true
			战斗准备类:创建战斗(数字id+0,100105,标识)
			玩家数据[数字id].地图单位=nil
		end
	end
end
__GWdh111[632]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,630)then
		return
	end
	local 副本id = 任务处理类:取副本id(数字id,630)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 任务数据[标识].战斗==nil then
		if 副本数据.轮回境.进行[数字id].进程==2  then
			if 取队伍人数(数字id)<1  and 调试模式==false then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[标识].战斗=true
			战斗准备类:创建战斗(数字id+0,100106,标识)
			玩家数据[数字id].地图单位=nil
		end
	end
end
__GWdh111[633]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,630)then
		return
	end
	local 副本id = 任务处理类:取副本id(数字id,630)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 任务数据[标识].战斗==nil then
		if 副本数据.轮回境.进行[数字id].进程==3  then
			if 取队伍人数(数字id)<1  and 调试模式==false then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[标识].战斗=true
			战斗准备类:创建战斗(数字id+0,100107,标识)
			玩家数据[数字id].地图单位=nil
		end
	end
end
__GWdh111[634]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,630)then
		return
	end
	local 副本id = 任务处理类:取副本id(数字id,630)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 任务数据[标识].战斗==nil then
		if 副本数据.轮回境.进行[数字id].进程==4  then
			if 取队伍人数(数字id)<1  and 调试模式==false then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[标识].战斗=true
			战斗准备类:创建战斗(数字id+0,100108,标识)
			玩家数据[数字id].地图单位=nil
		end
	end
end
__GWdh111[635]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,630)then
		return
	end
	local 副本id = 任务处理类:取副本id(数字id,630)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 任务数据[标识].战斗==nil then
		if 副本数据.轮回境.进行[数字id].进程==5  then
			if 取队伍人数(数字id)<1  and 调试模式==false then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[标识].战斗=true
			战斗准备类:创建战斗(数字id+0,100109,标识)
			玩家数据[数字id].地图单位=nil
		end
	end
end
__GWdh111[636]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,630)then
		return
	end
	local 副本id = 任务处理类:取副本id(数字id,630)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 任务数据[标识].战斗==nil then
		if 副本数据.轮回境.进行[数字id].进程==6  then
			if 取队伍人数(数字id)<1  and 调试模式==false then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[标识].战斗=true
			战斗准备类:创建战斗(数字id+0,100110,标识)
			玩家数据[数字id].地图单位=nil
		end
	end
end
__GWdh111[637]=function (连接id,数字id,序列,标识,地图)
    local 对话数据={}
    对话数据.模型=任务数据[标识].模型
    对话数据.名称=任务数据[标识].名称
    if not 任务处理类:判定队伍组(数字id,630) then
        return
    end
    local 副本id = 任务处理类:取副本id(数字id,630)
    if 副本id == 0 or 副本id ~= 数字id then
        对话数据.对话="只有创建副本的队长才能和我对话"
        return 对话数据
    end
    if 任务数据[标识].战斗==nil then
        if 副本数据.轮回境.进行[数字id].进程==7 then
            if 取队伍人数(数字id)<1 and 调试模式==false then 
                常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") 
                return  
            end
                        任务数据[标识].战斗=true
			战斗准备类:创建战斗(数字id+0,100111,标识)
			玩家数据[数字id].地图单位=nil 
        end
    end
end
function 怪物属性:中央鬼帝(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍平均等级(玩家数据[玩家id].队伍, 玩家id) + 5
    local xiulian = math.floor((等级 - 20) / 5) + 5
    战斗单位[1] = {
        名称 = "中央鬼帝",
        模型 = "阎罗王",
        伤害 = 等级 * 23,
        气血 = 等级 * 171,
        法伤 = 等级 * 20,
        速度 = 等级 * 4.57,
        防御 = 等级 * 11.43,
        法防 = 等级 * 5.71,
        躲闪 = 等级 * 2,
        固定伤害 = 等级 * 8,
        治疗能力 = 等级 * 8,
        炫彩=取染色id("阎罗王")  ,
		炫彩组=取炫彩染色("五方鬼帝"),
        魔法 = 200000,
        愤怒 = 9999,
        等级 = 等级,
        不可封印=true,
        技能 = {"高级感知", "高级驱鬼"},
        主动技能 ={"阎罗令"},
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian }
    }
	战斗单位[2] = {
	    名称 = "南方鬼帝",
	    模型 = "阎罗王",
	    伤害 = (等级 - 10) * 23,
	    气血 = 等级 * 171,
	    法伤 = (等级 - 10) * 20,
	    速度 = (等级 - 10) * 4.57,
	    防御 = (等级 - 10) * 11.43,
	    法防 = (等级 - 10) * 5.71,
	    躲闪 = (等级 - 10) * 2,
	    炫彩=取染色id("阎罗王")  ,
		炫彩组=取炫彩染色("五方鬼帝"),
	    魔法 = 200000,
	    愤怒 = 9999,
	    等级 = 等级 - 10,
	    技能 = {"高级感知", "高级驱鬼"},
	    主动技能 = {"六道无量"},
	    修炼 = {物抗 = xiulian - 10, 法抗 = xiulian - 10, 攻修 = xiulian - 10}
	}
	战斗单位[3] = {
	    名称 = "东方鬼帝",
	    模型 = "阎罗王",
	    伤害 = (等级 - 20) * 23,
	    气血 = 等级 * 171,
	    法伤 = (等级 - 20) * 20,
	    速度 = (等级 - 20) * 5.57,
	    防御 = (等级 - 20) * 11.43,
	    法防 = (等级 - 20) * 8.71,
	    躲闪 = (等级 - 20) * 2,
        炫彩=取染色id("阎罗王")  ,
		炫彩组=取炫彩染色("五方鬼帝"),
	    魔法 = 200000,
	    愤怒 = 9999,
	    等级 = 等级 - 20,
	    额外目标数 = 1,
	    封印命中等级=200,
	    技能 = {"高级感知", "高级驱鬼"},
	    主动技能 = {"日月乾坤"},
	    修炼 = {物抗 = xiulian, 法抗 = xiulian - 20, 攻修 = xiulian - 20}
	}
	战斗单位[4] = {
	    名称 = "西方鬼帝",
	    模型 = "阎罗王",
	    伤害 = (等级 + 10) * 23,
	    气血 = 等级 * 171,
	    法伤 = (等级 + 10) * 20,
	    速度 = (等级 + 10) * 4.57,
	    防御 = (等级 + 10) * 11.43,
	    法防 = (等级 + 10) * 5.71,
	    躲闪 = (等级 + 10) * 2,
        炫彩=取染色id("阎罗王")  ,
		炫彩组=取炫彩染色("五方鬼帝"),
	    魔法 = 200000,
	    愤怒 = 9999,
	    法宝={[1]={名称="九幽",境界=10}},
	    等级 = 等级 + 10,
	    技能 = {"高级感知", "高级驱鬼"},
	    主动技能 = {"尸腐毒"},
	    修炼 = {物抗 = xiulian + 10, 法抗 = xiulian + 10, 攻修 = xiulian + 10}
	}
	战斗单位[5] = {
	    名称 = "北方鬼帝",
	    模型 = "阎罗王",
	    伤害 = (等级 + 20) * 23,
	    气血 = 等级 * 171,
	    法伤 = (等级 + 20) * 20,
	    速度 = (等级 + 20) * 4.57,
	    防御 = (等级 + 20) * 11.43,
	    法防 = (等级 + 20) * 5.71,
	    躲闪 = (等级 + 20) * 2,
        炫彩=取染色id("阎罗王")  ,
		炫彩组=取炫彩染色("五方鬼帝"),
	    魔法 = 200000,
	    愤怒 = 9999,
	    等级 = 等级 + 20,
	    固定伤害 = 等级 * 8,
	    技能 = {"高级感知", "高级驱鬼"},
	    主动技能 = {"黄泉之息"},
	    修炼 = {物抗 = xiulian + 10, 法抗 = xiulian + 10, 攻修 = xiulian + 10}
	}
    return 战斗单位
end
function 怪物属性:九灵元圣(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍平均等级(玩家数据[玩家id].队伍, 玩家id) + 5
    local xiulian = math.floor((等级 - 20) / 5) + 5
    战斗单位[1] = {
        名称 = "九灵元圣",
        模型 = "大大王",
        伤害 = 等级 * 23,
        气血 = 等级 * 171,
        法伤 = 等级 * 20,
        速度 = 等级 * 4.57,
        防御 = 等级 * 11.43,
        法防 = 等级 * 5.71,
        躲闪 = 等级 * 2,
        魔法 = 200000,
        愤怒 = 9999,
        等级 = 等级,
        不可封印=true,
        饰品显示=true,
        技能 = {"高级必杀", "高级强力","狮搏","高级感知"},
        主动技能 = {"疯狂鹰击"},
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian }
    }
    战斗单位[2] = {
        名称 = "狻猊狮",
        模型 = "进阶古代瑞兽",
        伤害 = 等级 * 21,
        气血 = 等级 * 160,
        法伤 = 等级 * 18,
        速度 = 等级 * 4.5,
        防御 = 等级 * 10,
        法防 = 等级 * 5,
        躲闪 = 等级 * 1.8,
        魔法 = 150000,
        愤怒 = 8888,
        等级 = 等级,
        技能 = {"狮搏"},
        炫彩 = 取染色id("狻猊狮"),
        炫彩组 = 取炫彩染色("狻猊狮"),
        主动技能 = {"狮搏"},
        修炼 = {物抗 = xiulian - 5, 法抗 = xiulian - 5, 攻修 = xiulian - 5}
    }
    战斗单位[3] = {
        名称 = "抟象狮",
        模型 = "进阶古代瑞兽",
		伤害 = 等级 * 21,
        气血 = 等级 * 160,
        法伤 = 等级 * 18,
        速度 = 等级 * 4.5,
        防御 = 等级 * 10,
        法防 = 等级 * 5,
        躲闪 = 等级 * 1.8,
        魔法 = 150000,
        愤怒 = 8888,
        等级 = 等级,
        炫彩 = 取染色id("抟象狮"),
        炫彩组 = 取炫彩染色("抟象狮"),
        主动技能 = {"狮搏"},
        修炼 = {物抗 = xiulian - 5, 法抗 = xiulian - 5, 攻修 = xiulian - 5}
    }
    战斗单位[4] = {
        名称 = "白泽狮",
        模型 = "进阶古代瑞兽",
		伤害 = 等级 * 21,
        气血 = 等级 * 160,
        法伤 = 等级 * 18,
        速度 = 等级 * 4.5,
        防御 = 等级 * 10,
        法防 = 等级 * 5,
        躲闪 = 等级 * 1.8,
        魔法 = 150000,
        愤怒 = 8888,
        等级 = 等级,
        炫彩 = 取染色id("白泽狮"),
        炫彩组 = 取炫彩染色("白泽狮"),
        主动技能 = {"狮搏"},
        修炼 = {物抗 = xiulian - 5, 法抗 = xiulian - 5, 攻修 = xiulian - 5}
    }
    战斗单位[5] = {
        名称 = "伏狸狮",
        模型 = "进阶古代瑞兽",
		伤害 = 等级 * 21,
        气血 = 等级 * 160,
        法伤 = 等级 * 18,
        速度 = 等级 * 4.5,
        防御 = 等级 * 10,
        法防 = 等级 * 5,
        躲闪 = 等级 * 1.8,
        魔法 = 150000,
        愤怒 = 8888,
        等级 = 等级,
        炫彩 = 取染色id("伏狸狮"),
        炫彩组 = 取炫彩染色("伏狸狮"),
        主动技能 = {"狮搏"},
        修炼 = {物抗 = xiulian - 5, 法抗 = xiulian - 5, 攻修 = xiulian - 5}
    }
    战斗单位[6] = {
        名称 = "猱狮",
        模型 = "进阶古代瑞兽",
   		伤害 = 等级 * 21,
        气血 = 等级 * 160,
        法伤 = 等级 * 18,
        速度 = 等级 * 4.5,
        防御 = 等级 * 10,
        法防 = 等级 * 5,
        躲闪 = 等级 * 1.8,
        魔法 = 150000,
        愤怒 = 8888,
        等级 = 等级,
        炫彩 = 取染色id("猱狮"),
        炫彩组 = 取炫彩染色("猱狮"),
        主动技能 = {"狮搏"},
        修炼 = {物抗 = xiulian - 5, 法抗 = xiulian - 5, 攻修 = xiulian - 5}
    }
    战斗单位[7] = {
        名称 = "雪狮",
        模型 = "进阶古代瑞兽",
		伤害 = 等级 * 21,
        气血 = 等级 * 160,
        法伤 = 等级 * 18,
        速度 = 等级 * 4.5,
        防御 = 等级 * 10,
        法防 = 等级 * 5,
        躲闪 = 等级 * 1.8,
        魔法 = 150000,
        愤怒 = 8888,
        等级 = 等级,
        炫彩 = 取染色id("雪狮"),
        炫彩组 = 取炫彩染色("雪狮"),
        主动技能 = {"狮搏"},
        修炼 = {物抗 = xiulian - 5, 法抗 = xiulian - 5, 攻修 = xiulian - 5}
    }
    return 战斗单位
end
function 怪物属性:华岳圣母(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍平均等级(玩家数据[玩家id].队伍, 玩家id) + 5
    local xiulian = math.floor((等级 - 20) / 5) + 5
    local sx = self:取属性(等级)
    if sx.智能=="物理" or sx.智能=="法系" or sx.智能=="固伤" then
        主怪是否为输出 = true
    end  
    门派 = "神木林"  
    战斗单位[1] = {
        名称 = "华岳圣母",
        模型 = "进阶曼珠沙华",
        角色=true,
        伤害 = 等级 * 23,
        气血 = 等级 * 171,
        法伤 = 等级 * 20,
        速度 = 等级 * 4.57,
        防御 = 等级 * 11.43,
        法防 = 等级 * 5.71,
        躲闪 = 等级 * 2,
        魔法 = 200000,
        愤怒 = 9999,
        等级 = 等级,
        不可封印 = true,
        门派=sx.门派,
        招式特效=取招式特效(门派),
        技能 = {"落叶萧萧"},
        主动技能 = {""},
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian },
        AI战斗 = {AI=sx.智能}
    }
    战斗单位[2] = {
        名称 = "沉香",
        模型 = "进阶大力金刚",
        角色=true,
        伤害 = 等级 * 21,
        气血 = 等级 * 200,
        法伤 = 等级 * 18,
        速度 = 等级 * 5.5,
        防御 = 等级 * 10,
        法防 = 等级 * 5,
        躲闪 = 等级 * 1.8,
        魔法 = 150000,
        愤怒 = 8888,
        等级 = 等级,
        技能 =取物理被动(4),
        主动技能 = {"力劈华山"},
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian},
        AI战斗 = {AI=sx.智能}
    }
    战斗单位[3] = {
        名称 = "凤凰",
        模型 = "进阶凤凰",

        角色=true,
        伤害 = 等级 * 21,
        气血 = 等级 * 160,
        法伤 = 等级 * 18,
        速度 = 等级 * 4.5,
        防御 = 等级 * 17,
        法防 = 等级 * 10,
        躲闪 = 等级 * 1.8,
        治疗能力 = 等级*8,
        魔法 = 150000,
        愤怒 = 8888,
        等级 = 等级,
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian},
        AI战斗 = {AI=sx.智能}
    }
    for i = 4, 8 do
        战斗单位[i] = {
            名称 = "金甲力士",
            模型 = "进阶天将",
            伤害 = 等级 * 21,
            气血 = 等级 * 160,
            法伤 = 等级 * 18,
            速度 = 等级 * 4.5,
            防御 = 等级 * 10,
            法防 = 等级 * 5,
            躲闪 = 等级 * 1.8,
            魔法 = 150000,
            愤怒 = 8888,
            等级 = 等级,
            主动技能 = {"死亡之音","笑里藏刀"},
            修炼 = {物抗 = xiulian - 5, 法抗 = xiulian - 5, 攻修 = xiulian - 5}
        }
    end
    return 战斗单位
end
function 怪物属性:南斗星君(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍平均等级(玩家数据[玩家id].队伍, 玩家id) + 5
    local xiulian = math.floor((等级 - 20) / 5) + 5
    战斗单位[1] = {
        名称 = "南斗星君",
        模型 = "进阶天兵",
        饰品显示 = true,
        伤害 = 等级 * 23,
        气血 = 等级 * 171,
        法伤 = 等级 * 20,
        速度 = 等级 * 4.57,
        防御 = 等级 * 11.43,
        法防 = 等级 * 5.71,
        躲闪 = 等级 * 2,
        魔法 = 200000,
        愤怒 = 9999,
        等级 = 等级,
		染色方案=67 ,
		染色组={[1]=1,[2]=0},
        共生 = true,
        不可封印 = true,
        技能 =取物理被动(4),
        主动技能 = {"天雷斩"},
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian }
    }
    战斗单位[2] = {
        名称 = "北斗星君",
        模型 = "进阶天兵",
        饰品显示 = true,
        伤害 = 等级 * 21,
        气血 = 等级 * 160,
        法伤 = 等级 * 18,
        速度 = 等级 * 4.5,
        防御 = 等级 * 10,
        法防 = 等级 * 5,
        躲闪 = 等级 * 1.8,
        共生 = true,
        魔法 = 150000,
        愤怒 = 8888,
        等级 = 等级,
        染色方案=2078,
        染色组={[1]=5,[2]=0},
        技能 =取物理被动(4),
        主动技能 = {"天雷斩"},
        修炼 = {物抗 = xiulian - 5, 法抗 = xiulian - 5, 攻修 = xiulian - 5}
    }
    return 战斗单位
end
function 怪物属性:天龙八部(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍平均等级(玩家数据[玩家id].队伍, 玩家id) + 5
    local xiulian = math.floor((等级 - 20) / 5) + 5
    local sx = self:取属性(等级)
    if sx.智能=="物理" or sx.智能=="法系" or sx.智能=="固伤" then
		主怪是否为输出 = true
	end
    战斗单位[11] = {
        名称 = "天龙八部",
        模型 = "进阶金身罗汉",
        伤害 = 等级 * 23,
        气血 = 等级 * 171,
        法伤 = 等级 * 20,
        速度 = 2000,
        防御 = 等级 * 11.43,
        法防 = 等级 * 5.71,
        躲闪 = 等级 * 2,
        魔法 = 200000,
        愤怒 = 9999,
        等级 = 等级,
        不可操作 = true,
        开场发言="迦楼罗，金翅鸟神。鸣声悲苦，以龙为食。#Y命终之时，碎裂命珠，玉石俱焚#W。",
        技能 =取物理被动(4),
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian },
        AI战斗 = {AI=sx.智能}
    }
    for i = 1, 10 do
        战斗单位[i] = {
            名称 = "迦楼罗",
            模型 = "进阶灵鹤",
            饰品显示 = true,
            伤害 = 等级 * 22,
            气血 = 等级 * 160,
            法伤 = 等级 * 18,
            速度 = 等级 * 4.4,
            防御 = 等级 * 15,
            法防 = 等级,
            躲闪 = 等级 * 1.8,
            魔法 = 150000,
            愤怒 = 8000,
            等级 = 等级,
            --技能 = {"高级魔之心","高级法术连击"},
            主动技能=取大法(4),
            修炼 = {物抗 = xiulian - 1, 法抗 = xiulian - 1, 攻修 = xiulian - 1 },
            AI战斗 = {AI=sx.智能}
        }
       end
    return 战斗单位
end
function 怪物属性:天龙2(等级)
    local sx = self:取属性(等级)
    if sx.智能=="物理" or sx.智能=="法系" or sx.智能=="固伤" then
        主怪是否为输出 = true
    end
    local xiulian = math.floor((等级 - 20) / 5) + 5
    local 召唤单位={
    名称="夜叉",
    模型="进阶碧水夜叉",
    等级=等级,
    气血=35000,
    饰品显示 = true,
    伤害=等级*23,
    法伤=等级 * 15,
    速度=等级*5,
    防御=等级*15,
    法防=等级*10,
    躲闪=等级,
    修炼 = {物抗 = xiulian - 1, 法抗 = xiulian - 1, 攻修 = xiulian - 1 },
    主动技能={"龙卷雨击", "横扫千军", "龙腾", "龙卷雨击"},
    AI战斗 = {AI=sx.智能}
    }
    return 召唤单位
end
function 怪物属性:天龙3(等级)
    local sx = self:取属性(等级)
    if sx.智能=="物理" or sx.智能=="法系" or sx.智能=="固伤" then
        主怪是否为输出 = true
    end
    local xiulian = math.floor((等级 - 20) / 5) + 5
    local 召唤单位={
    名称="紧那罗",
    模型="进阶红萼仙子",
     饰品显示 = true,
    等级=等级,
    气血=22000,
    伤害=等级*23,
    法伤=等级,
    速度=等级*5,
    防御=等级*10,
    法防=等级,
    躲闪=等级,
    主动技能="",
    AI战斗 = {AI=sx.智能}
    }
    return 召唤单位
end
function 怪物属性:天龙4(等级)
    local 召唤单位={
    名称="摩呼罗迦",
    模型="进阶巴蛇",
     饰品显示 = true,
    等级=等级,
    气血=22000,
    伤害=等级*23,
    法伤=等级,
    速度=等级*5,
    防御=等级*10,
    法防=等级,
    躲闪=等级,
    主动技能="",
    }
    return 召唤单位
end
function 怪物属性:天龙5(等级)
    local 召唤单位={
    名称="阿修罗",
    模型="进阶修罗傀儡鬼",
     饰品显示 = true,
    等级=等级,
    气血=22000,
    伤害=等级*23,
    法伤=等级,
    速度=等级*5,
    防御=等级*10,
    法防=等级,
    躲闪=等级,
    主动技能="",
    }
    return 召唤单位
end
function 怪物属性:天龙6(等级)
    local 召唤单位={
    名称="乾达婆",
    模型="进阶星灵仙子",
     饰品显示 = true,
    等级=等级,
    气血=22000,
    伤害=等级*23,
    法伤=等级,
    速度=等级*5,
    防御=等级*10,
    法防=等级,
    躲闪=等级,
    主动技能="",
    }
    return 召唤单位
end
function 怪物属性:天龙7(等级)
    local 召唤单位={
    名称="天众",
    模型="进阶雨师",
    等级=等级,
    气血=22000,
    伤害=等级*23,
    法伤=等级,
    速度=等级*5,
    防御=等级*10,
    法防=等级,
    躲闪=等级,
    主动技能="",
    }
    return 召唤单位
end
function 怪物属性:天龙8(等级)
    local 召唤单位={
    名称="龙众",
    模型="进阶鲛人",
    角色=true,
    等级=等级,
    气血=22000,
    伤害=等级*23,
    法伤=等级,
    速度=等级*8,
    防御=等级*10,
    法防=等级,
    躲闪=等级,
    主动技能="",
    }
    return 召唤单位
end
function 怪物属性:无上天尊(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍平均等级(玩家数据[玩家id].队伍, 玩家id) + 5
    local xiulian = math.floor((等级 - 20) / 5) + 5
    战斗单位[1] = {
        名称 = "无上天尊",
        模型 = "进阶风伯",
        
        伤害 = 等级 * 23,
        气血 = 等级 * 171,
        法伤 = 等级 * 20,
        速度 = 等级 * 4.57,
        防御 = 等级 * 11.43,
        法防 = 等级 * 5.71,
        躲闪 = 等级 * 2,
        固定伤害 = 等级 * 8,
        治疗能力 = 等级 * 8,
        炫彩=取染色id("阎罗王")  ,
        炫彩组=取炫彩染色("黑色"),
        魔法 = 200000,
        愤怒 = 9999,
        等级 = 等级,
        不可封印=true,
        技能 = {"高级感知", "高级驱鬼"},
        主动技能 ={"阎罗令"},
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian }
    }
    战斗单位[2] = {
        名称 = "无上天尊",
        模型 = "进阶风伯",
        伤害 = (等级 - 10) * 23,
        气血 = 等级 * 171,
        法伤 = (等级 - 10) * 20,
        速度 = (等级 - 10) * 4.57,
        防御 = (等级 - 10) * 11.43,
        法防 = (等级 - 10) * 5.71,
        躲闪 = (等级 - 10) * 2,
        炫彩=取染色id("阎罗王")  ,
        炫彩组=取炫彩染色("白色"),
        魔法 = 200000,
        愤怒 = 9999,
        等级 = 等级 - 10,
        技能 = {"高级感知", "高级驱鬼"},
        主动技能 = {"六道无量"},
        修炼 = {物抗 = xiulian - 10, 法抗 = xiulian - 10, 攻修 = xiulian - 10}
    }
    战斗单位[3] = {
        名称 = "无上天尊",
        模型 = "进阶风伯",
        伤害 = (等级 - 20) * 23,
        气血 = 等级 * 171,
        法伤 = (等级 - 20) * 20,
        速度 = (等级 - 20) * 5.57,
        防御 = (等级 - 20) * 11.43,
        法防 = (等级 - 20) * 8.71,
        躲闪 = (等级 - 20) * 2,
		染色方案=98 ,
		染色组={[1]=1,[2]=0},
        魔法 = 200000,
        愤怒 = 9999,
        等级 = 等级 - 20,
        技能 = {"高级感知", "高级驱鬼"},
        主动技能 = {"日月乾坤"},
        修炼 = {物抗 = xiulian, 法抗 = xiulian - 20, 攻修 = xiulian - 20}
    }
    战斗单位[4] = {
        名称 = "无上天尊",
        模型 = "进阶风伯",
        伤害 = (等级 + 10) * 23,
        气血 = 等级 * 171,
        法伤 = (等级 + 10) * 20,
        速度 = (等级 + 10) * 4.57,
        防御 = (等级 + 10) * 11.43,
        法防 = (等级 + 10) * 5.71,
        躲闪 = (等级 + 10) * 2,
		染色方案=60 ,
	    染色组={[1]=1,[2]=1},
        魔法 = 200000,
        愤怒 = 9999,
        法宝={[1]={名称="九幽",境界=10}},
        等级 = 等级 + 10,
        技能 = {"高级感知", "高级驱鬼"},
        主动技能 = {"尸腐毒"},
        修炼 = {物抗 = xiulian + 10, 法抗 = xiulian + 10, 攻修 = xiulian + 10}
    }
    战斗单位[5] = {
        名称 = "无上天尊",
        模型 = "进阶风伯",
        伤害 = (等级 + 20) * 23,
        气血 = 等级 * 171,
        法伤 = (等级 + 20) * 20,
        速度 = (等级 + 20) * 4.57,
        防御 = (等级 + 20) * 11.43,
        法防 = (等级 + 20) * 5.71,
        躲闪 = (等级 + 20) * 2,
		染色方案=53 ,
 		染色组={[1]=1,[2]=0},
        魔法 = 200000,
        愤怒 = 9999,
        等级 = 等级 + 20,
        固定伤害 = 等级 * 8,
        技能 = {"高级感知", "高级驱鬼"},
        主动技能 = {"黄泉之息"},
        修炼 = {物抗 = xiulian + 10, 法抗 = xiulian + 10, 攻修 = xiulian + 10}
    }
    return 战斗单位
end
function 怪物属性:自在天魔(任务id,玩家id,序号)
    local 战斗单位={}
    local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
    local xiulian = math.floor((等级-20)/5)
    local 模型="自在天魔刀"
    local 模型={"自在天魔","自在天魔刀","自在天魔斧钺","自在天魔经筒","自在天魔弓弩","自在天魔宝剑","自在天魔宝珠","自在天魔法杖","自在天魔_饰品"}
    local sx = self:取属性(等级,门派)
    if sx.智能=="物理" or sx.智能=="法系" or sx.智能=="辅助" then
        主怪是否为输出 = true
    end
    战斗单位[1]={
        名称="自在天魔"
        ,模型="自在天魔"
        ,等级=等级
        ,角色=true
        ,伤害 = 4000
        ,法伤 = 1500
        ,防御 = 2500
        ,速度 = 400
        ,法防 = 0
        ,气血 = 150000
        ,修炼 = {物抗=xiulian,法抗=xiulian,攻修=xiulian}
        ,技能={""}
        ,AI战斗 = {AI=sx.智能}
        ,主动技能={"媚眼如丝","无间地狱"}
        ,附加阵法="自在天魔阵法"
        }
    战斗单位[2]={
        名称=""
        ,模型="自在天魔法杖"
        ,等级=等级
        ,角色=true
        ,伤害 = 1000
        ,法伤 = 2000
        ,防御 = 1200
        ,速度 = 400
        ,法防 = 0
        ,气血 = 99999
        ,修炼 = {物抗=xiulian,法抗=xiulian,攻修=xiulian}
        ,技能={""}
        ,AI战斗 = {AI=sx.智能}
        ,主动技能={"飞砂走石","横扫千军","龙卷雨季","翻江搅海"}
        }
    战斗单位[3]={
        名称=""
        ,模型="自在天魔刀"
        ,等级=等级
        ,角色=true
        ,伤害 = 4000
        ,法伤 = 1500
        ,防御 = 1200
        ,速度 = 400
        ,法防 = 0
        ,气血 = 99999
        ,修炼 = {物抗=xiulian,法抗=xiulian,攻修=xiulian}
        ,技能={""}
        ,AI战斗 = {AI=sx.智能}
        ,主动技能={"飞砂走石","横扫千军","龙卷雨季","翻江搅海"}
        }
    战斗单位[4]={
        名称=""
        ,模型="自在天魔斧钺"
        ,等级=等级
        ,角色=true
        ,伤害 = 4000
        ,法伤 = 1500
        ,防御 = 1200
        ,速度 = 400
        ,法防 = 0
        ,气血 = 99999
        ,修炼 = {物抗=xiulian,法抗=xiulian,攻修=xiulian}
        ,技能={""}
        ,AI战斗 = {AI=sx.智能}
        ,主动技能={"飞砂走石","横扫千军","龙卷雨季","翻江搅海"}
        }
    战斗单位[5]={
        名称=""
        ,模型="自在天魔经筒"
        ,等级=等级
        ,角色=true
        ,伤害 = 1500
        ,法伤 = 1500
        ,防御 = 1200
        ,速度 = 400
        ,法防 = 0
        ,气血 = 99999
        ,修炼 = {物抗=xiulian,法抗=xiulian,攻修=xiulian}
        ,技能={""}
        ,AI战斗 = {AI=sx.智能}
        ,主动技能={"飞砂走石","横扫千军","龙卷雨季","翻江搅海"}
        }
    战斗单位[6]={
        名称=""
        ,模型="自在天魔弓弩"
        ,等级=等级
        ,角色=true
        ,伤害 = 4000
        ,法伤 = 1500
        ,防御 = 1200
        ,速度 = 400
        ,法防 = 0
        ,气血 = 99999
        ,修炼 = {物抗=xiulian,法抗=xiulian,攻修=xiulian}
        ,技能={""}
        ,AI战斗 = {AI=sx.智能}
        ,主动技能={"飞砂走石","横扫千军","龙卷雨季","翻江搅海"}
        }
    战斗单位[7]={
        名称=""
        ,模型="自在天魔宝剑"
        ,等级=等级
        ,角色=true
        ,伤害 = 3000
        ,法伤 = 1500
        ,防御 = 1200
        ,速度 = 400
        ,法防 = 0
        ,气血 = 99999
        ,修炼 = {物抗=xiulian,法抗=xiulian,攻修=xiulian}
        ,技能={""}
        ,AI战斗 = {AI=sx.智能}
        ,主动技能={"飞砂走石","横扫千军","龙卷雨季","翻江搅海"}
        }
    战斗单位[8]={
        名称=""
        ,模型="自在天魔宝珠"
        ,等级=等级
        ,角色=true
        ,伤害 = 2000
        ,法伤 = 1500
        ,防御 = 1200
        ,速度 = 400
        ,法防 = 0
        ,气血 = 99999
        ,修炼 = {物抗=xiulian,法抗=xiulian,攻修=xiulian}
        ,技能={""}
        ,AI战斗 = {AI=sx.智能}
        ,主动技能={"飞砂走石","横扫千军","龙卷雨季","翻江搅海"}
        }
    战斗单位[9]={
        名称=""
        ,模型="自在天魔_饰品"
        ,等级=等级
      --  ,不可操作=true
        ,角色=true
        ,伤害 = 4000
        ,法伤 = 1500
        ,防御 = 1200
        ,速度 = 400
        ,法防 = 0
        ,气血 = 99999
        ,修炼 = {物抗=xiulian,法抗=xiulian,攻修=xiulian}
        ,技能={""}
        ,AI战斗 = {AI=sx.智能}
        }
    return 战斗单位
end
function 胜利MOB_100105(胜利id,战斗数据,id组)
	广播消息({内容=format("#S(轮回境)#R/%s#Y带领队伍经过一番激烈的战斗，最终战胜了轮回境的#R%s#Y".."#"..random(1,110),玩家数据[战斗数据.进入战斗玩家id].角色.名称,任务数据[战斗数据.任务id].名称),频道="xt"})
	地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号,任务数据[战斗数据.任务id].单位编号)
	local 副本id=任务数据[战斗数据.任务id].副本id
	任务数据[战斗数据.任务id]=nil
	副本数据.轮回境.进行[副本id]={进程=2}
	for n = 1, #队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据 do
        local 数字id = 队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据[n]
        if 玩家数据[数字id] ~= nil then
            玩家数据[数字id].角色:添加积分(200, "副本积分")
            -- 使用检查前缀称谓的方法来检查称谓
            if not 玩家数据[数字id].角色:检查前缀称谓("轮回境") then
                玩家数据[数字id].角色:添加称谓("轮回境·踏焰")
            end
        end
    end
	GetUpMOB630(副本id)
	刷新队伍任务追踪(胜利id)
end
function 胜利MOB_100106(胜利id,战斗数据,id组)
	广播消息({内容=format("#S(轮回境)#R/%s#Y带领队伍经过一番激烈的战斗，最终战胜了轮回境的#R%s#Y".."#"..random(1,110),玩家数据[战斗数据.进入战斗玩家id].角色.名称,任务数据[战斗数据.任务id].名称),频道="xt"})
	地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号,任务数据[战斗数据.任务id].单位编号)
	local 副本id=任务数据[战斗数据.任务id].副本id
	任务数据[战斗数据.任务id]=nil
	副本数据.轮回境.进行[副本id]={进程=3}
	for n=1,#队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据 do
		local 数字id = 队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据[n]
		 if 玩家数据[数字id] ~= nil then
            玩家数据[数字id].角色:添加积分(400, "副本积分")        
            -- 检查并删除"轮回境·踏焰"，然后添加"轮回境·升灵"
            if 玩家数据[数字id].角色:检查称谓("轮回境·踏焰") then
                玩家数据[数字id].角色:删除称谓("轮回境·踏焰")
                玩家数据[数字id].角色:添加称谓("轮回境·升灵")
            end
        end
	end
	GetUpMOB630(副本id)
	刷新队伍任务追踪(胜利id)
end
function 胜利MOB_100107(胜利id,战斗数据,id组)
	广播消息({内容=format("#S(轮回境)#R/%s#Y带领队伍经过一番激烈的战斗，最终战胜了轮回境的#R%s#Y".."#"..random(1,110),玩家数据[战斗数据.进入战斗玩家id].角色.名称,任务数据[战斗数据.任务id].名称),频道="xt"})
	地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号,任务数据[战斗数据.任务id].单位编号)
	local 副本id=任务数据[战斗数据.任务id].副本id
	任务数据[战斗数据.任务id]=nil
	副本数据.轮回境.进行[副本id]={进程=4}
	for n=1,#队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据 do
		local 数字id = 队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据[n]
		if 玩家数据[数字id] ~= nil then
			玩家数据[数字id].角色:添加积分(500,"副本积分")
            if 玩家数据[数字id].角色:检查称谓("轮回境·升灵") then
                玩家数据[数字id].角色:删除称谓("轮回境·升灵")
                玩家数据[数字id].角色:添加称谓("轮回境·澄澈")
            end
		end
	end
	GetUpMOB630(副本id)
	刷新队伍任务追踪(胜利id)
end
function 胜利MOB_100108(胜利id,战斗数据,id组)
	广播消息({内容=format("#S(轮回境)#R/%s#Y带领队伍经过一番激烈的战斗，最终战胜了轮回境的#R%s#Y".."#"..random(1,110),玩家数据[战斗数据.进入战斗玩家id].角色.名称,任务数据[战斗数据.任务id].名称),频道="xt"})
	地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号,任务数据[战斗数据.任务id].单位编号)
	local 副本id=任务数据[战斗数据.任务id].副本id
	任务数据[战斗数据.任务id]=nil
	副本数据.轮回境.进行[副本id]={进程=5}
	for n=1,#队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据 do
		local 数字id = 队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据[n]
		if 玩家数据[数字id] ~= nil then
			玩家数据[数字id].角色:添加积分(200,"副本积分")
			if 玩家数据[数字id].角色:检查称谓("轮回境·澄澈") then
                玩家数据[数字id].角色:删除称谓("轮回境·澄澈")
                玩家数据[数字id].角色:添加称谓("轮回境·红尘")
            end
		end
	end
	GetUpMOB630(副本id)
	刷新队伍任务追踪(胜利id)
end
function 胜利MOB_100109(胜利id,战斗数据,id组)
    local 所有龙众死亡 = true
    for i=1, #战斗数据.参战单位 do
        local 当前单位 = 战斗数据.参战单位[i]
        if 当前单位.名称 == "龙众" and 当前单位.气血 > 0 then
            -- 如果找到一个 "龙众" 单位尚未死亡，设置标志为 false 并退出循环
            所有龙众死亡 = false
            break
        end
    end

    if 所有龙众死亡 then
			广播消息({内容=format("#S(轮回境)#R/%s#Y带领队伍经过一番激烈的战斗，最终战胜了轮回境的#R%s#Y".."#"..random(1,110),玩家数据[战斗数据.进入战斗玩家id].角色.名称,任务数据[战斗数据.任务id].名称),频道="xt"})
			地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号,任务数据[战斗数据.任务id].单位编号)
			local 副本id=任务数据[战斗数据.任务id].副本id
			任务数据[战斗数据.任务id]=nil
			副本数据.轮回境.进行[副本id]={进程=6}
			for n=1,#队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据 do
				local 数字id = 队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据[n]
				if 玩家数据[数字id] ~= nil then
					玩家数据[数字id].角色:添加积分(800,"副本积分")
					if 玩家数据[数字id].角色:检查称谓("轮回境·红尘") then
		                玩家数据[数字id].角色:删除称谓("轮回境·红尘")
		                玩家数据[数字id].角色:添加称谓("轮回境·修罗")
		            end					
				end
			end
		GetUpMOB630(副本id)
		刷新队伍任务追踪(胜利id)
   end
end
function 胜利MOB_100110(胜利id,战斗数据,id组)
    -- 获取队伍id
    local 队伍id = 玩家数据[战斗数据.进入战斗玩家id].队伍
    
    -- 修正for循环，使用正确的队伍id
    for i=1,#队伍数据[队伍id].成员数据 do
        local 玩家id = 队伍数据[队伍id].成员数据[i]
        if 玩家数据[玩家id] ~= nil then
            发送数据(玩家数据[玩家id].连接id, 6557, {
                文本 = {"天地玄黄，宇宙洪荒"},  -- 建议保留文本
                类型 = "轮回境副本",
                字体 = nil,
                音乐 = nil,
                背景 = nil,
                横排显示 = true,
                动画调用 = {630, 13}
            })
        end
    end

	广播消息({内容=format("#S(轮回境)#R/%s#Y带领队伍经过一番激烈的战斗，最终战胜了轮回境的#R%s#Y".."#"..random(1,110),玩家数据[战斗数据.进入战斗玩家id].角色.名称,任务数据[战斗数据.任务id].名称),频道="xt"})
	地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号,任务数据[战斗数据.任务id].单位编号)
	
	-- 遍历地图上的所有单位,找到并删除轮回门关
	for k,v in pairs(任务数据) do
        if v.模型 == "轮回门关" and v.副本id == 任务数据[战斗数据.任务id].副本id then
            地图处理类:删除单位(v.地图编号, v.单位编号)
            任务数据[k] = nil
            break
        end
    end

    local 副本id=任务数据[战斗数据.任务id].副本id
    任务数据[战斗数据.任务id]=nil
    副本数据.轮回境.进行[副本id]={进程=7}
    for n=1,#队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据 do
        local 数字id = 队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据[n]
        if 玩家数据[数字id] ~= nil then
            玩家数据[数字id].角色:添加积分(300,"副本积分")
			if 玩家数据[数字id].角色:检查称谓("轮回境·修罗") then
                玩家数据[数字id].角色:删除称谓("轮回境·修罗")
                玩家数据[数字id].角色:添加称谓("轮回境·无量")
            end	            
        end
    end
    GetUpMOB630(副本id)
    刷新队伍任务追踪(胜利id)
end
function 胜利MOB_100111(胜利id,战斗数据,id组)
    广播消息({内容=format("#S(轮回境)#R/%s#Y带领队伍历经七关，击败了#R%s#Y".."#"..random(1,110),玩家数据[战斗数据.进入战斗玩家id].角色.名称,任务数据[战斗数据.任务id].名称),频道="xt"})
    发送公告("#S/(轮回境)#Y某队强者，逃过天魔劫数，超脱成圣。")
    地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号,任务数据[战斗数据.任务id].单位编号)
    local 副本id=任务数据[战斗数据.任务id].副本id
    任务数据[战斗数据.任务id]=nil
    副本数据.轮回境.进行[副本id]={进程=7}

    local text1 = {"一切有为法","如梦幻泡影","如露亦如电","应作如是观"}
        for n=1,#队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据 do
            local 数字id = 队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据[n]
           -- 玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(630))
            副本数据.轮回境.完成[数字id]=true
            发送数据(玩家数据[数字id].连接id,6557,{文本=text1,类型="自在天魔终",字体=nil,音乐=nil,背景=nil,横排显示=nil,动画=nil})
        end
    for n=1,#队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据 do
        local 数字id = 队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据[n]
        if 玩家数据[数字id] ~= nil then
            玩家数据[数字id].角色:添加积分(1000,"副本积分")
			if 玩家数据[数字id].角色:检查称谓("轮回境·无量") then
                玩家数据[数字id].角色:删除称谓("轮回境·无量")
                玩家数据[数字id].角色:添加称谓("轮回境·自在")
            end	             
        end
    end
    任务处理类:删除单位(战斗数据.任务id)
    副本数据.轮回境.进行[副本id]=nil
    刷新队伍任务追踪(胜利id)
end
function rwgx630(任务id)
	if os.time()-任务数据[任务id].起始>=任务数据[任务id].结束 and 任务数据[任务id].结束 ~= 99999999 then
		if 任务数据[任务id].战斗~=true then
			if  任务数据[任务id].类型== 630 then
				for i=1,#任务数据[任务id].队伍组 do
					if 玩家数据[任务数据[任务id].队伍组[i]] ~= nil then
						玩家数据[任务数据[任务id].队伍组[i]].角色:取消任务(玩家数据[任务数据[任务id].队伍组[i]].角色:取任务(630))
					end
				end
				任务数据[任务id]=nil
			else
				地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
				任务数据[任务id]=nil
			end
		end
	end
end
function 任务说明630(玩家id,任务id)
	local 说明 = {}
	local 副本id=任务数据[任务id].副本id
	if 副本数据.轮回境.进行[副本id]==nil then
		说明={"轮回境","#L您的副本已经完成"}
	else
		说明={"轮回境",format("#L挑战#R七关#L，击败自在天魔。(剩余%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
	end
	return 说明
end