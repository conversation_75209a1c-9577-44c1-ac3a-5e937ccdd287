-- @Author: baidwwy
-- @Date:   2024-07-04 12:48:19
-- @Last Modified by:   baidwwy
-- @Last Modified time: 2025-02-07 23:31:27
local ffi = require("ffi")
local 数据数量 = ffi.new("int[1]")
local 线程发送数据 = {}
local asdwe = os.time()
local 定时器 = class()
local 检测间隔 = 5 -- 每5秒检测一次


function 定时器:初始化(v, 循环文件)
    self.线程 = require("ggethread")(循环文件)
    self.isrunning = true

    self.线程.消息返回 = function(thread_self, ...)
        if ... == "取数量指针" then
            return tonumber(ffi.cast("intptr_t", 数据数量))
        elseif ... == "取数据" then
            return unpack(r)
        elseif ... == "循环更新" then
            if os.time() - asdwe >= 检测间隔 then
                asdwe = os.time()
                self:执行检测()
            end
            for _, player in pairs(玩家数据) do
                if player.角色 and player.角色.扣除人气 then
                    player.角色:扣除人气(0)
                end
            end
        else
            print("线程返回", ...)
        end
    end

    self:启动(v)
end

function 定时器:启动(v)
    self.线程:启动(v)
    self.线程:置延迟(v)
end

function 定时器:发送(...)
    数据数量[0] = 数据数量[0] + 1
    table.insert(线程发送数据, {...})
end

function 定时器:执行检测()
   -- print("执行定时检测")

    self:处理勾魂索()
    self:处理坐牢名单()
    self:检查天罚名单()
    self:检查跑商验证超时()
end

function 定时器:检查跑商验证超时()
    -- 检查跑商验证超时
    local 跑商验证系统 = require("Script/反作弊系统/跑商验证系统")
    跑商验证系统:检查验证超时()

    -- 清理过期的行为检测数据
    local 行为检测系统 = require("Script/反作弊系统/行为检测系统")
    行为检测系统:清理过期数据()
end

function 定时器:检查天罚名单()
    for k, v in pairs(天罚名单) do
        if 玩家数据[v.ID] and 玩家数据[v.ID].战斗 == 0 and not 玩家数据[v.ID].坐牢中  and 取随机数(1,1000)<10 then
            if 玩家数据[v.ID].队伍 ~= 0 then
                队伍处理类:退出队伍(v.ID)
            end
            战斗准备类:创建战斗(v.ID, 111112) -- 执法天兵
            table.remove(天罚名单, k)
        end
    end
end

function 定时器:处理勾魂索()
    for k,v in pairs(勾魂索名单) do
        if v.倒计时开始==false then
            if 玩家数据[v.主动] and 玩家数据[v.被动] then
                if 玩家数据[v.主动].战斗==0 and 玩家数据[v.被动].战斗==0 then
                    v.倒计时开始=true
                    v.战斗开始=true
                    v.开始时间 = os.time()

                    -- 初始化准备模式相关字段
                    if v.准备模式 == nil then
                        v.准备模式 = false
                    end
                    if v.主动方确认 == nil then
                        v.主动方确认 = false
                    end
                    if v.被动方确认 == nil then
                        v.被动方确认 = false
                    end

                    -- 发送倒计时开始信号
                    local 主动队伍id = 玩家数据[v.主动].队伍
                    if 主动队伍id ~= 0 then
                        for n=1,#队伍数据[主动队伍id].成员数据 do
                            local 成员id=队伍数据[主动队伍id].成员数据[n]
                            发送数据(玩家数据[成员id].连接id,3707)
                        end
                    else
                        发送数据(玩家数据[v.主动].连接id,3707)
                    end

                    local 被动队伍id = 玩家数据[v.被动].队伍
                    if 被动队伍id ~= 0 then
                        for n=1,#队伍数据[被动队伍id].成员数据 do
                            local 成员id=队伍数据[被动队伍id].成员数据[n]
                            发送数据(玩家数据[成员id].连接id,3707)
                        end
                    else
                        发送数据(玩家数据[v.被动].连接id,3707)
                    end
                end
            end
        else
            if 玩家数据[v.主动] and 玩家数据[v.被动] then
                -- 检查是否需要进入准备模式（双方都已确认）
                if not v.准备模式 and v.主动方确认 and v.被动方确认 then
                    v.准备模式 = true
                    v.准备时间 = os.time()

                    -- 通知双方进入快速准备模式
                    local 主动队伍id = 玩家数据[v.主动].队伍
                    if 主动队伍id ~= 0 then
                        for n=1,#队伍数据[主动队伍id].成员数据 do
                            local 成员id=队伍数据[主动队伍id].成员数据[n]
                            发送数据(玩家数据[成员id].连接id,3708)
                        end
                    else
                        发送数据(玩家数据[v.主动].连接id,3708)
                    end

                    local 被动队伍id = 玩家数据[v.被动].队伍
                    if 被动队伍id ~= 0 then
                        for n=1,#队伍数据[被动队伍id].成员数据 do
                            local 成员id=队伍数据[被动队伍id].成员数据[n]
                            发送数据(玩家数据[成员id].连接id,3708)
                        end
                    else
                        发送数据(玩家数据[v.被动].连接id,3708)
                    end
                end

                -- 检查是否满足战斗条件
                local 可以战斗 = false
                if v.准备模式 and v.准备时间 then
                    local 已过时间 = os.time() - v.准备时间
                    -- 快速准备模式，10秒后战斗
                    -- 由于定时器检测间隔为5秒，这里使用>=8确保不会错过10秒的时间点
                    if 已过时间 >= 8 then
                        可以战斗 = true
                    end
                elseif not v.准备模式 and v.开始时间 then
                    local 已过时间 = os.time() - v.开始时间
                    -- 普通模式，300秒后战斗
                    -- 由于定时器检测间隔为5秒，这里使用>=295确保不会错过300秒的时间点
                    if 已过时间 >= 295 then
                        可以战斗 = true
                    end
                end

                -- 立即创建战斗
                if 可以战斗 and 玩家数据[v.主动].战斗==0 and 玩家数据[v.被动].战斗==0 then
                    v.战斗开始=false
                    local 被动方队伍 = v.被动

                    -- 创建战斗前清理状态
                    local 主动队伍id = 玩家数据[v.主动].队伍
                    if 主动队伍id ~= 0 then
                        for n=1,#队伍数据[主动队伍id].成员数据 do
                            local 成员id=队伍数据[主动队伍id].成员数据[n]
                            玩家数据[成员id].勾魂索中=nil
                            发送数据(玩家数据[成员id].连接id,3719)
                        end
                    else
                        玩家数据[v.主动].勾魂索中=nil
                        发送数据(玩家数据[v.主动].连接id,3719)
                    end

                    local 被动队伍id = 玩家数据[v.被动].队伍
                    if 被动队伍id ~= 0 then
                        被动方队伍=被动队伍id
                        for n=1,#队伍数据[被动队伍id].成员数据 do
                            local 成员id=队伍数据[被动队伍id].成员数据[n]
                            玩家数据[成员id].勾魂索中=nil
                            发送数据(玩家数据[成员id].连接id,3719)
                        end
                    else
                        玩家数据[v.被动].勾魂索中=nil
                        发送数据(玩家数据[v.被动].连接id,3719)
                    end

                    战斗准备类:创建玩家战斗(v.主动, 200005, 被动方队伍, 1001)
                    table.remove(勾魂索名单, k)
                end
            end
        end
    end
end

function 定时器:处理坐牢名单()
    for k,v in pairs(坐牢名单) do
        if 玩家数据[v.ID] and 玩家数据[v.ID].坐牢中 and os.time() - v.时间>1800 then --30分钟
            玩家数据[v.ID].坐牢中=nil
            玩家数据[v.ID].角色:出狱处理()
            table.remove(坐牢名单, k)
        end
    end
end

return 定时器
