-- @Author: 反作弊系统
-- @Date: 2025-01-14
-- @Description: 处理验证相关的聊天命令

local 验证命令处理 = {}

-- 引入跑商验证系统
local 跑商验证系统 = require("Script/反作弊系统/跑商验证系统")

-- 处理验证命令
function 验证命令处理:处理验证命令(玩家id, 消息内容)
    if not 玩家数据[玩家id] then
        return false
    end
    
    -- 检查是否为验证命令
    local 验证前缀 = "验证 "
    if string.sub(消息内容, 1, string.len(验证前缀)) == 验证前缀 then
        local 回答内容 = string.sub(消息内容, string.len(验证前缀) + 1)
        
        -- 处理验证回答
        local 验证结果 = 跑商验证系统:处理验证回答(玩家id, 回答内容)
        
        return true -- 表示已处理该命令
    end
    
    return false -- 不是验证命令
end

-- 管理员命令处理
function 验证命令处理:处理管理员命令(玩家id, 消息内容)
    if not 玩家数据[玩家id] or not 玩家数据[玩家id].角色.权限 or 玩家数据[玩家id].角色.权限 < 5 then
        return false
    end
    
    -- 重置玩家可疑度
    local 重置前缀 = "@重置可疑度 "
    if string.sub(消息内容, 1, string.len(重置前缀)) == 重置前缀 then
        local 目标玩家名 = string.sub(消息内容, string.len(重置前缀) + 1)
        
        -- 查找目标玩家
        local 目标id = nil
        for id, data in pairs(玩家数据) do
            if data.角色.名称 == 目标玩家名 then
                目标id = id
                break
            end
        end
        
        if 目标id then
            local 行为检测系统 = require("Script/反作弊系统/行为检测系统")
            if 行为检测系统:重置可疑度评分(目标id) then
                常规提示(玩家id, "#G/已重置玩家 " .. 目标玩家名 .. " 的可疑度评分")
            else
                常规提示(玩家id, "#R/未找到玩家 " .. 目标玩家名 .. " 的行为数据")
            end
        else
            常规提示(玩家id, "#R/未找到玩家：" .. 目标玩家名)
        end
        
        return true
    end
    
    -- 查看玩家统计
    local 统计前缀 = "@查看统计 "
    if string.sub(消息内容, 1, string.len(统计前缀)) == 统计前缀 then
        local 目标玩家名 = string.sub(消息内容, string.len(统计前缀) + 1)
        
        -- 查找目标玩家
        local 目标id = nil
        for id, data in pairs(玩家数据) do
            if data.角色.名称 == 目标玩家名 then
                目标id = id
                break
            end
        end
        
        if 目标id then
            local 行为检测系统 = require("Script/反作弊系统/行为检测系统")
            local 统计数据 = 行为检测系统:获取玩家统计(目标id)
            
            if 统计数据 then
                local 统计信息 = string.format(
                    "玩家 %s 的行为统计：\n可疑度评分：%d\n警告次数：%d\n异常移动：%d\n快速操作：%d\n移动记录：%d\n跑商记录：%d",
                    目标玩家名,
                    统计数据.可疑度评分,
                    统计数据.警告次数,
                    统计数据.异常移动计数,
                    统计数据.连续快速操作计数,
                    统计数据.移动记录数量,
                    统计数据.跑商操作记录数量
                )
                常规提示(玩家id, "#Y/" .. 统计信息)
            else
                常规提示(玩家id, "#R/未找到玩家 " .. 目标玩家名 .. " 的统计数据")
            end
        else
            常规提示(玩家id, "#R/未找到玩家：" .. 目标玩家名)
        end
        
        return true
    end
    
    return false
end

return 验证命令处理
