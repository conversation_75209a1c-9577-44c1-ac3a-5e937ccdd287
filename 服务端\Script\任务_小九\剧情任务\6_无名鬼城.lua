-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2025-06-08 00:40:59
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-29 12:41:05
local sj = 取随机数
local format = string.format
local insert = table.insert
local ceil = math.ceil
local floor = math.floor
local wps = 取物品数据
local typ = type
local random = 取随机数

-- 无名鬼城千年怨鬼怪物属性
function 怪物属性:千年怨鬼(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=80
	local 模型={"幽灵"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称="千年怨鬼",
	模型="幽灵",
	饰品显示=true,
	气血=math.floor(85*80 + 1800),
	伤害=math.floor(85*8 + 250),
	法伤=math.floor(sx.属性.法伤),
	速度=math.floor(85*2 + 100),
	防御=math.floor(85*3.8),
	法防=math.floor(5+(等级/2)*25.5),
	等级=等级,
	技能={"高级神迹"},
	主动技能={"判官令","阎罗令","死亡召唤"},
	}
	for n=2,5 do
		local 模型={"幽灵"}
		local 模型=模型[取随机数(1,#模型)]
		战斗单位[n]={
		名称="怨灵",
		模型=模型,
		气血=math.floor(65*80 + 1200),
		伤害=math.floor(65*8 + 200),
		法伤=math.floor(sx.属性.法伤*0.8),
		速度=math.floor(65*2 + 80),
		防御=math.floor(65*3.8),
		法防=math.floor(5+(等级/2)*20.5),
		等级=等级-5,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end

-- 无名鬼城千年怨鬼胜利函数（纯单人剧情）
function 胜利MOB_110050(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	-- 纯单人剧情，只处理战斗发起者
	local v = 数字id
		-- 检查是否已经完成过无名鬼城任务，防止重复奖励
		if 玩家数据[v].角色.历劫 and 玩家数据[v].角色.历劫.无名鬼城 == true then
			-- 已经完成过，不给奖励
			local wb_page1 =  {"千年怨鬼","千年怨鬼","你已经化解了我的怨念，我已经安息了..."}
			local wb_page2 = {"千年怨鬼","千年怨鬼","无名鬼城现在已经平静，你可以自由进出那里。",{}}
			发送数据(玩家数据[v].连接id, 1501, {模型= wb_page1[1],名称 = wb_page1[2],对话 =wb_page1[3] ,选项 = {},下一页=wb_page2,剧情=玩家数据[v].角色.剧情})
		else
			-- 确保历劫表存在
			if not 玩家数据[v].角色.历劫 then
				玩家数据[v].角色.历劫 = {}
			end
			
			-- 首次击败千年怨鬼，给予战斗奖励（经验和剧情点）
			玩家数据[v].角色:添加经验(337500,"无名鬼城支线")
			玩家数据[v].角色:增加剧情点(1)
			
			-- 设置战斗完成标记，但任务还未完全完成（需要找阎罗王领取最终奖励）
			玩家数据[v].角色.历劫.无名鬼城_战斗完成 = true
			
			-- 支线任务不推进主线剧情，保持原有剧情状态
			local wb_page1 =  {"千年怨鬼","千年怨鬼","不可能...我的怨念...竟然被你化解了..."}
			local wb_page2 = {"千年怨鬼","千年怨鬼","无名鬼城的怨气已经消散，感谢你的帮助。现在你回去告知阎王现状，完成鬼城任务吧。快去找阎罗王领取最终奖励。",{}}
			发送数据(玩家数据[v].连接id, 1501, {模型= wb_page1[1],名称 = wb_page1[2],对话 =wb_page1[3] ,选项 = {},下一页=wb_page2,剧情=玩家数据[v].角色.剧情})
		-- 提示玩家战斗奖励和后续任务
		常规提示(v,"#G/打败千年怨鬼获得337500经验和1点剧情点，请回去找阎罗王领取最终奖励")
	end
end
