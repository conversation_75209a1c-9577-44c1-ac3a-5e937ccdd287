{"file_history": ["/D/myfwd/服务端/main.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗单位类.lua", "/D/myfwd/客户端/Script/数据中心/技能库.lua", "/D/myfwd/客户端/main.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/调皮的泡泡.lua", "/D/myfwd/服务端/Script/任务_小九/夏日泡泡.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗处理.lua", "/D/myfwd/服务端/Script/任务_小九/副本任务/一斛珠.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/五更寒.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/属性控制/宠物.lua", "/D/myfwd/服务端/Script/数据中心/宝宝.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/道具处理类.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/周末玩法/游泳活动.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/任务处理类/任务处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/召唤兽处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/装备处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/管理工具类.lua", "/D/myfwd/服务端/Script/任务_小九/古董商人.lua", "/D/myfwd/服务端/<PERSON>ript/数据中心/技能数据库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗命令类.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa176632.44400/鼠标.lua", "/D/myfwd/服务端/<PERSON>ript/假人处理类/假人玩家.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa19888.41469/道具处理类.lua", "/D/客户端/<PERSON><PERSON>t/战斗类/战斗命令类.lua", "/D/客户端/<PERSON><PERSON>t/战斗类/战斗单位类.lua", "/C/Users/<USER>/Desktop/聊天处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/聊天处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局循环类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/剧情处理器/剧情对话调用.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/副本任务/锻刀村之战.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa57676.43095/NPC对话内容.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/剧情任务/3_玄奘的身世.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa57676.12904/3_玄奘的身世.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa127236.26646/3_玄奘的身世.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa81640.21495/怪物对话内容.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa81640.20042/NPC对话内容.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/网络/数据交换.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/跑商商店.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/帮派任务/帮派白虎.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/商店处理类.lua", "/D/myfwd/服务端/Script/初始化脚本.lua", "/D/myfwd/服务端/<PERSON>ript/对话处理类/对话调用/1001.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主显.lua", "/D/myfwd/客户端/script/小九UI/跑商商店.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/全局函数类/全局函数.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/系统处理类.lua", "/D/myfwd/客户端/seamless_update.bat", "/D/myfwd/客户端/start_game.bat", "/D/myfwd/客户端/update.bat", "/D/myfwd/客户端/update_process.bat", "/D/myfwd/服务端/Script/任务_小九/天罡星处理.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/角色处理类/角色处理类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/缓冲.lua", "/G/jingruihutong/互通授权源码/服务端源码更新中/main.lua", "/G/神来修复卖/1.Server/main.lua", "/D/myfwd/客户端/<PERSON>ript/初系统/游戏更新说明.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/系统设置.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/变量1.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/底图框.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/时辰.lua", "/D/myfwd/客户端/<PERSON>ript/系统类/人物框.lua", "/D/myfwd/客户端/<PERSON>ript/更新类/星盘.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主控.lua", "/D/myfwd/服务端/Script/任务_小九/天罡星.lua", "/D/myfwd/GGE/Core/Server/ggeengine.lua", "/D/myfwd/GGE/Core/Server/ggeserver.lua", "/D/myfwd/客户端/script/全局/主控.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/系统处理类/网络处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/商店处理类/商城处理类.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa29868.39794/商城处理类.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/怪物属性.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/地煞星.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/助战处理类/MateControl.lua", "/D/myfwd/客户端/<PERSON>ript/场景类/第二场景.lua", "/D/myfwd/客户端/galaxy2d.log", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗自动栏.lua", "/D/myfwd/客户端/script/全局/主显.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/加载类.lua", "/D/myfwd/GGE/Core/Game/gge引擎.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/小地图.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/地图类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/变量2.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/缓存资源.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/缓存资源_优化.lua", "/D/myfwd/客户端/Script/数据中心/自定义库.lua", "/D/myfwd/GGE/Core/Server/ggedebug.lua", "/G/神来修复卖/1.Server/Script/对话处理类/NPC对话处理.lua", "/G/神来修复卖/1.Server/Script/Dismay/Dismay.lua", "/G/神来修复卖/1.Server/Script/角色处理类/角色处理类.lua", "/G/神来修复卖/1.Server/Script/角色处理类/道具处理类.lua", "/F/BaiduNetdiskDownload/0222-花好(梦回)/花好三端源码+服务端/服务端/服务端/Script/战斗处理类/天罡星AI.lua", "/D/myfwd/服务端/<PERSON>ript/战斗处理类/天罡星AI.lua", "/D/myfwd/服务端/data/aming1/10276/日志记录/13.txt", "/D/myfwd/GGE/Extend/文件类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗类.lua", "/F/BaiduNetdiskDownload/0222-花好(梦回)/花好三端源码+服务端/服务端/服务端/Script/战斗处理类/战斗计算/战斗计算.lua", "/F/BaiduNetdiskDownload/0222-花好(梦回)/花好三端源码+服务端/服务端/服务端/Script/战斗处理类/战斗计算/普攻计算.lua", "/F/BaiduNetdiskDownload/0222-花好(梦回)/花好三端源码+服务端/服务端/服务端/Script/战斗处理类/战斗处理.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗计算.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/物理技能计算.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/战斗计算/战斗法术计算.lua", "/F/BaiduNetdiskDownload/0222-花好(梦回)/花好三端源码+服务端/服务端/服务端/Script/战斗处理类/战斗计算/战斗固伤计算.lua", "/G/jingruihutong/互通授权源码/精锐西游测试版客户端/main.lua", "/G/jingruihutong/互通授权源码/精锐西游测试版客户端/Script/数据中心/场景.lua", "/G/jingruihutong/互通授权源码/精锐西游测试版客户端/Script/全局/玩家.lua", "/G/jingruihutong/互通授权源码/精锐西游测试版客户端/Script/数据中心/梦战造型.lua", "/G/jingruihutong/互通授权源码/精锐西游测试版客户端/Script/数据中心/场景NPC.lua", "/G/jingruihutong/互通授权源码/精锐西游测试版客户端/Script/数据中心/物品库.lua", "/G/jingruihutong/互通授权源码/精锐西游测试版客户端/Script/网络/数据交换.lua", "/G/jingruihutong/互通授权源码/服务端源码更新中/sql/房屋数据.txt", "/G/jingruihutong/互通授权源码/服务端源码更新中/Debug/数据信息/排行数据.txt", "/G/jingruihutong/互通授权源码/服务端源码更新中/Script/系统处理类/系统处理类.lua", "/G/jingruihutong/互通授权源码/服务端源码更新中/Script/系统处理类/聊天处理类.lua", "/D/myfwd/客户端/script/小九UI/法宝.lua", "/D/myfwd/客户端/<PERSON>ript/小九UI/公告弹窗.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/更新内容.lua", "/F/BaiduNetdiskDownload/王者西游-全套源码/服务端/服务端源码/Script/战斗处理类/怪物调用/结算处理.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/按钮.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/聊天框.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/任务追踪栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/分区.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/标题.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/任务栏.lua", "/D/myfwd/客户端/<PERSON>ript/系统类/丰富文本.lua", "/D/myfwd/客户端/Script/数据中心/坐骑库.lua", "/D/myfwd/服务端/<PERSON>ript/任务_小九/常规任务/人物修任务.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/对话处理类/对话调用/1208.lua", "/D/myfwd/服务端/<PERSON><PERSON>t/战斗处理类/创建战斗.lua"], "folder_history": ["/F/BaiduNetdiskDownload/0222-花好(梦回)/花好三端源码+服务端/pc客户端源码等/Script", "/G/神来修复卖/2.客户端/Script", "/G/神来修复卖/2.客户端/.vscode", "/F/BaiduNetdiskDownload/0222-花好(梦回)/花好三端源码+服务端/服务端/服务端/Script", "/D/myfwd/服务端", "/G/jingruihutong/互通授权源码/服务端源码更新中", "/F/BaiduNetdiskDownload/王者西游-全套源码/服务端/服务端源码/Script", "/D/myfwd/Server", "/G/三花修复版-低调使用/客户端/Script", "/F/BaiduNetdiskDownload/0222-花好(梦回)/花好三端源码+服务端/服务端/服务端", "/F/BaiduNetdiskDownload/0222-花好(梦回)/花好三端源码+服务端/pc客户端源码等", "/D/myfwd/服务端/<PERSON><PERSON>t", "/D/myfwd/备份/服务端/Script", "/F/BaiduNetdiskDownload/王者西游-全套源码/客户端/客户端/Script", "/D/myfwd/Client/<PERSON>ript", "/F/BaiduNetdiskDownload/王者西游-全套源码/服务端/服务端源码", "/F/BaiduNetdiskDownload/无双开团/无双服务端----九黎版/Script", "/F/BaiduNetdiskDownload/王者西游-全套源码", "/F/BaiduNetdiskDownload/王者西游-全套源码/客户端/客户端", "/F/BaiduNetdiskDownload/吊3九黎城/服务端/服务端/Script", "/D/梦幻服务端/客户端/Script", "/H/12000/服务端", "/D/2024.07.04团/服务端/Script", "/D/梦幻服务端/新建文件夹/服务端/<PERSON>ript", "/D/梦幻服务端/新建文件夹/服务端", "/D/梦幻服务端/shenqi/客户端/Script", "/D/梦幻服务端/shenqi/服务端/Script", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script", "/D/my2/客户端/script", "/D/数据库版本/客户端早期源码/script", "/D/三花修复版-低调使用/客户端/Script", "/D/紫禁之巅九黎/梦回长安/Script", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script/全局", "/D/梦幻服务端/源码备份/0.49/客户端/<PERSON><PERSON>t", "/D/2024.07.04团/PC客户端/Script", "/D/梦幻服务端/源码备份/0.543/客户端/<PERSON>ript", "/D/my2/客户端", "/D/my2/服务端/<PERSON><PERSON>t", "/D/08源码/服务端/Script", "/D/08源码/客户端", "/D/梦幻服务端/源码备份/0.49/客户端", "/D/紫禁之巅九黎/服务端/Script", "/D/三花修复版-低调使用/服务端/Script", "/D/梦幻服务端/源码备份/0.49/服务端/<PERSON><PERSON>t", "/D/梦神3/服务端/<PERSON><PERSON>t"], "last_version": 4192, "last_window_id": 407, "log_indexing": false, "next_update_check": 1752105271, "settings": {"new_window_full_screen": false, "new_window_height": 852.0, "new_window_maximized": false, "new_window_position": [434.0, 134.0], "new_window_settings": {"auto_complete": {"selected_items": []}, "build_system_choices": [[[["Packages/Lua/ggegame.sublime-build", ""], ["Packages/Lua/ggegame.sublime-build", "Run"], ["Packages/Lua/ggegame.sublime-build", "RunInCommand"], ["Packages/Lua/ggegame.sublime-build", "SetGGE"], ["Packages/Lua/ggegame.sublime-build", "Stop"], ["Packages/Lua/ggegame.sublime-build", "AboutGGE"]], ["Packages/Lua/ggegame.sublime-build", ""]]], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "console": {"height": 0.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "file_history": ["/D/myfwd/GGE/Core/Game/gge精灵类.lua", "/D/myfwd/GGE/Core/Game/gge图像类.lua", "/D/myfwd/GGE/Core/Game/gge资源类.lua", "/D/myfwd/GGE/Core/Game/gge动画类.lua", "/D/myfwd/GGE/Core/Game/gge轨迹带.lua", "/D/myfwd/客户端/main.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗单位类.lua", "/D/myfwd/客户端/Script/数据中心/技能库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗命令类.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa176632.44400/鼠标.lua", "/D/客户端/<PERSON><PERSON>t/战斗类/战斗命令类.lua", "/C/Users/<USER>/Desktop/聊天处理类.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa81640.21495/怪物对话内容.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa81640.20042/NPC对话内容.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/网络/数据交换.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/跑商商店.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主显.lua", "/D/myfwd/客户端/script/小九UI/跑商商店.lua", "/D/myfwd/客户端/seamless_update.bat", "/D/myfwd/客户端/start_game.bat", "/D/myfwd/客户端/update.bat", "/D/myfwd/客户端/update_process.bat", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/缓冲.lua", "/D/myfwd/客户端/<PERSON>ript/初系统/游戏更新说明.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/系统设置.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/变量1.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/底图框.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/时辰.lua", "/D/myfwd/客户端/<PERSON>ript/系统类/人物框.lua", "/D/myfwd/客户端/<PERSON>ript/更新类/星盘.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主控.lua", "/D/myfwd/GGE/Core/Server/ggeengine.lua", "/D/myfwd/GGE/Core/Server/ggeserver.lua", "/D/myfwd/客户端/script/全局/主控.lua", "/D/myfwd/客户端/<PERSON>ript/场景类/第二场景.lua", "/D/myfwd/客户端/galaxy2d.log", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗自动栏.lua", "/D/myfwd/客户端/script/全局/主显.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/加载类.lua", "/D/myfwd/GGE/Core/Game/gge引擎.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/小地图.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/地图类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/变量2.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/缓存资源.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/缓存资源_优化.lua", "/D/myfwd/客户端/Script/数据中心/自定义库.lua", "/D/myfwd/GGE/Extend/文件类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗类.lua", "/D/myfwd/客户端/script/小九UI/法宝.lua", "/D/myfwd/客户端/<PERSON>ript/小九UI/公告弹窗.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/更新内容.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/按钮.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/聊天框.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/任务追踪栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/分区.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/标题.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/任务栏.lua", "/D/myfwd/客户端/<PERSON>ript/系统类/丰富文本.lua", "/D/myfwd/客户端/Script/数据中心/坐骑库.lua", "/D/myfwd/Client/Script/战斗类/战斗类.lua", "/D/myfwd/Client/script/战斗类/战斗动画类.lua", "/D/myfwd/Client/Script/全局/变量2.lua", "/D/myfwd/Client/Script/全局/变量1.lua", "/D/myfwd/Server/Script/任务处理类/任务处理类.lua", "/D/myfwd/Server/main.lua", "/D/myfwd/Server/Script/任务_小九/星官.lua", "/D/myfwd/Server/Script/任务_小九/天罡星.lua", "/D/myfwd/Server/Script/战斗处理类/战斗处理.lua", "/D/myfwd/Server/Script/战斗处理类/AI战斗.lua", "/D/myfwd/Server/Script/系统处理类/系统处理类.lua", "/D/myfwd/Server/Script/角色处理类/角色处理类.lua", "/D/myfwd/Server/Script/战斗处理类/战斗计算/战斗执行类.lua", "/D/myfwd/Server/Script/角色处理类/帮派处理.lua", "/D/myfwd/Server/Script/角色处理类/道具处理类.lua", "/D/myfwd/Server/Script/地图处理类/地图处理类.lua", "/D/myfwd/Server/Script/战斗处理类/天罡星AI.lua", "/D/myfwd/Server/Script/战斗处理类/怪物属性.lua", "/D/myfwd/Server/Script/数据中心/物品数据.lua", "/D/myfwd/Server/Script/战斗处理类/战斗计算/战斗技能.lua", "/D/myfwd/Server/Script/任务_小九/中秋任务.lua", "/D/myfwd/Server/Script/任务_小九/地煞星.lua", "/D/myfwd/Server/Script/助战处理类/MateControl.lua", "/D/myfwd/Server/Script/任务_小九/副本任务/一斛珠.lua", "/D/myfwd/Server/Script/任务_小九/副本任务/齐天大圣.lua", "/D/myfwd/Server/Script/战斗处理类/战斗计算/战斗计算.lua", "/D/myfwd/Server/Script/战斗处理类/战斗计算/状态处理.lua", "/D/myfwd/Server/Script/战斗处理类/战斗计算/初始技能计算.lua", "/D/myfwd/Server/Script/任务_小九/周末玩法/剑会.lua", "/D/myfwd/Server/Script/战斗处理类/创建战斗.lua", "/D/myfwd/Server/Script/地图处理类/地图坐标类.lua", "/D/myfwd/Server/Script/地图处理类/路径类.lua", "/D/myfwd/Server/Script/地图处理类/MAP.lua", "/D/myfwd/Server/Script/角色处理类/队伍处理类.lua", "/D/myfwd/战斗命令类.lua", "/D/myfwd/队伍处理类.lua", "/D/myfwd/Server/Script/系统处理类/ScriptInit.lua", "/D/myfwd/Server/Script/角色处理类/多开系统.lua", "/D/myfwd/Server/Script/任务_小九/副本任务/红孩儿.lua", "/D/myfwd/Server/Script/任务_小九/副本任务/轮回境副本.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/AI战斗.lua", "/D/梦幻服务端/服务端/main.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/战斗计算/普攻计算.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/战斗处理.lua", "/D/梦幻服务端/服务端/Script/任务_小九/地煞星.lua", "/D/梦幻服务端/服务端/Script/角色处理类/道具处理类.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/战斗计算/物理技能计算.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/战斗计算/战斗执行类.lua", "/D/梦幻服务端/服务端/Script/初始化脚本.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/星魂处理.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/星魂战斗桥接.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/战斗处理补丁.lua", "/D/梦幻服务端/服务端/Script/任务_小九/星官.lua", "/D/梦幻服务端/服务端/Script/属性控制/宠物.lua", "/D/梦幻服务端/服务端/Script/属性控制/队伍.lua", "/D/梦幻服务端/服务端/Script/角色处理类/孩子处理类.lua", "/D/梦幻服务端/服务端/Script/任务_小九/周末玩法/降妖伏魔.lua", "/D/梦幻服务端/服务端/Script/角色处理类/角色处理类.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/怪物属性.lua", "/D/梦幻服务端/服务端/Script/任务_小九/常规任务/宝图_妖王.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/红孩儿.lua", "/D/梦幻服务端/服务端/Script/地图处理类/地图处理类.lua", "/D/梦幻服务端/服务端/Script/数据中心/宝宝.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/车迟斗法.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/水陆大会.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/双城记.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/乌鸡国.lua", "/D/梦幻服务端/服务端/Script/助战处理类/MateControl.lua", "/D/梦幻服务端/服务端/Script/角色处理类/队伍处理类.lua"], "find": {"height": 32.0}, "find_in_files": {"height": 138.0, "where_history": ["D:\\myfwd\\客户端\\Script,<project filters>", "D:\\myfwd\\客户端,<project filters>", "D:\\myfwd\\客户端\\Script,<project filters>"]}, "find_state": {"case_sensitive": true, "find_history": ["self.挨打坐标.x", "六道轮回", "[2]", "抓捕", "朴拙", "🔥", "--", "传送", "c欢送", "消息框:分辨率更改事件()", "全局切换界面", "qiehuan", "星盘", "转盘", "备份", "boolean", "处理", "自动战斗", "正在加载资源", "是否红字体", "wdf配置", "q=", "q", "lsph", "zt", "载入", "载入1", "结果", "内存", "猫灵", "607.1", "字体", "内容3", "字体", "官府", "公文", "官府公文", "大分区OL[i]", "追忆", "2008", "全局小分区", "字体", "任务追踪", "一般字体", "一般字体1", "坐骑库", "字体表", "字体", "停止", "移动事件", "移动"], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": [], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "incremental_find": {"height": 32.0}, "input": {"height": 44.0}, "menu_visible": true, "output.exec": {"height": 217.0}, "output.find_results": {"height": 0.0}, "pinned_build_system": "Packages/Lua/ggegame.sublime-build", "replace": {"height": 60.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [], "width": 380.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 223.0, "status_bar_visible": true, "template_settings": {}}, "new_window_width": 1222.0}, "windows": [{"auto_complete": {"selected_items": []}, "buffers": [{"file": "main.lua", "settings": {"buffer_size": 3887, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[1, 1, "revert", null, "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", "KgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvw"], [1, 1, "revert", null, "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", "KAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/"], [2, 1, "revert", null, "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", "KAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/"], [1, 1, "revert", null, "BQAAAAAAAAAAAAAAAAAAAAAAAAAPFAAALS0gQEF1dGhvcjog5bCP5Lmd5ZGA5Li2IC0gUVHvvJo1MjY4NDE2Ci0tIEBEYXRlOiAgIDIwMjUtMDMtMTYgMjI6MzE6MTIKLS0gQExhc3QgTW9kaWZpZWQgYnk6ICAg5Lqk5rWBUVHnvqTvvJo4MzQyNDgxOTEKLS0gQExhc3QgTW9kaWZpZWQgdGltZTogMjAyNS0wNC0xMSAwMDowNTowNwpmZmkgPSByZXF1aXJlKCJmZmkiKQpsb2NhbCDmoqbnm74gPSBmZmkubG9hZCgibHVhNTIuZGxsIikKZmZpLmNkZWZbWwogIHZvaWQgbWVzc2FnZUJveChjb25zdCBjaGFyKiBzb21ldGV4dCk7CiAgICBib29sIFNlYXJjaGVzKCk7Ly/mo4DmtYtDRQoKICAgIGNoYXIqIGRlY3J5cHQoY2hhciogZmlsZSk7CiAgICBib29sIGVuY3J5cHQoY2hhciogZmlsZSxjaGFyKiBkYXRhKTsKCl1dCi0tIGludCBoYWRQcm9jZXNzKCk7Ly/mo4DmtYvlpJrlvIDvvIzmlL7liLBmZmkuY2RlZgrlm77lg4/nsbsgPSByZXF1aXJlKCJnZ2Xlm77lg4/nsbsxIikKZuWHveaVsCA9IHJlcXVpcmUoImZmaeWHveaVsDIiKQrniYjmnKzlj7c9MC41OQrmoIfpopg9IiDmoqblubvopb/muLggIgrlrqLmiLfnq6/lj4LmlbA9e30K5a6i5oi356uv5Y+C5pWwLuWIhumSnz1vcy5kYXRlKCIlTSIsIG9zLnRpbWUoKSkK5a6i5oi356uv5Y+C5pWwLuWwj+aXtj1vcy5kYXRlKCIlSCIsIG9zLnRpbWUoKSkKcmVxdWlyZSgic2NyaXB0L+WFqOWxgC/lj5jph48xIikKWW91eGlqaW5jaGVuZz1mYWxzZQrliqDovb3lrozmiJA9ZmFsc2UK546p5a625bGP6JS9ID1mYWxzZQrmkYrkvY3lsY/olL0gPSBmYWxzZQrov57ngrnmqKHlvI8gPSBmYWxzZQrmmL7npLrlnZDpqpEgPSB0cnVlCuaYvuekuuWPmOi6q+WNoemAoOWeiyA9IHRydWUK5YWo5bGA56aB5q2i6LWw6LevPWZhbHNlCuW4ruaImOW8gOWFsz1mYWxzZQrlj5jouqvmmL7npLo9ZmFsc2UK5aSp5rCU5byA5YWzPSB0cnVlCuS9jumFjeaooeW8jyA9IGZhbHNlCuWGheWtmOS8mOWMluW8gOWFsz1mYWxzZQrmuKHliqvljJbnlJ/mmL7npLo9ZmFsc2UK5a6d5a6d6Zif5LyN5Zu+5o6S5bqPPXt9ClNlcnZlckRpcmVjdG9yeSA9IGxmcy5jdXJyZW50ZGlyKCkgLi4gW1tcXV0KX1/ogYrlpKnmoYZ4PTI2MArlm57osIMgPSByZXF1aXJlKCJTY3JpcHQv572R57ucL0hQQ2xpZW5057G7IikoKQpyZXF1aXJlKCJsZnMiKQpyZXF1aXJlKCJTY3JpcHQv5Yid57O757ufL+e8k+WGsiIpCgoKZnVuY3Rpb24gV3JpdGVGaWxlKGZpbGVOYW1lLCBjb250ZW50KQoJZmlsZU5hbWUgPSBTZXJ2ZXJEaXJlY3RvcnkgLi4gZmlsZU5hbWUKCWxvY2FsIGYgPSBhc3NlcnQoaW8ub3BlbihmaWxlTmFtZSwgJ3cnKSkKCWY6d3JpdGUoY29udGVudCkKCWY6Y2xvc2UoKQplbmQKZnVuY3Rpb24g5YaZ5Ye65YaF5a65KHFxLCB3dykKCWlmIHFxID09IG5pbCBvciB3dyA9PSBuaWwgb3Igd3cgPT0gIiIgdGhlbgoJCXJldHVybiAwCgllbmQKCXFxID0g56iL5bqP55uu5b2VIC4uIHFxCglsb2NhbCBmaWxlID0gaW8ub3BlbihxcSwidyIpCglmaWxlOndyaXRlKHd3KQoJZmlsZTpjbG9zZSgpCgl0ZXh0ID0wCgnnqIvluo/nm67lvZU9bGZzLmN1cnJlbnRkaXIoKS4uW1tcXV0KCXJldHVybiB0ZXh0CmVuZApmdW5jdGlvbiDlhpnlh7rmlofku7YocXEsd3cpCgnlhpnlh7rlhoXlrrkocXEsd3cpCglpZiDliJ3lp4vnm67lvZUgdGhlbgoJCWxmcy5jaGRpcijliJ3lp4vnm67lvZUpCgkJ56iL5bqP55uu5b2VPeWIneWni+ebruW9lQoJZW5kCmVuZApyZXF1aXJlKCJzY3JpcHQv5pWw5o2u5Lit5b+DL+eJqeWTgeW6kyIpCnJlcXVpcmUoInNjcmlwdC/mlbDmja7kuK3lv4Mv5aS05YOP5bqTIikKcmVxdWlyZSgic2NyaXB0L+aVsOaNruS4reW/gy/mioDog73lupMiKQpyZXF1aXJlKCJzY3JpcHQv5pWw5o2u5Lit5b+DL+e7j+iEieW6kyIpCnJlcXVpcmUoInNjcmlwdC/mlbDmja7kuK3lv4Mv6Z+z5pWI5bqTIikKcmVxdWlyZSgic2NyaXB0L+aVsOaNruS4reW/gy/mmI7pm7flupMiKQpyZXF1aXJlKCJzY3JpcHQv5pWw5o2u5Lit5b+DL+eJueaViOW6kyIpCnJlcXVpcmUoInNjcmlwdC/mlbDmja7kuK3lv4Mv5pmu6YCa5qih5Z6L5bqTIikKcmVxdWlyZSgic2NyaXB0L+aVsOaNruS4reW/gy/miJjmlpfmqKHlnovlupMiKQpyZXF1aXJlKCJzY3JpcHQv5pWw5o2u5Lit5b+DL+WdkOmqkeW6kyIpCnJlcXVpcmUoInNjcmlwdC/mlbDmja7kuK3lv4Mv5Lyg6YCB6KGoIikKcmVxdWlyZSgic2NyaXB0L+aVsOaNruS4reW/gy/lnLrmma8iKQpyZXF1aXJlKCJzY3JpcHQv5pWw5o2u5Lit5b+DL+aipuaImOmAoOWeiyIpCnJlcXVpcmUoInNjcmlwdC/mlbDmja7kuK3lv4Mv56ym55+z57uE5ZCIIikKcmVxdWlyZSgic2NyaXB0L+aVsOaNruS4reW/gy/oh6rlrprkuYnlupMiKQotLSDliJ3lp4vljJbotYTmupDnvJPlrZjvvIzorr7nva7mnIDlpKfnvJPlrZjmlbDkuLoyMDAw6aG5Cui1hOa6kOe8k+WtmD1yZXF1aXJlKCJTY3JpcHQv6LWE5rqQ57G7L+e8k+WtmOi1hOa6kCIpKDIwMDApCi0tIOiuvue9rua4heeQhumXtOmalOS4ujEw5YiG6ZKfCui1hOa6kOe8k+WtmDrorr7nva7muIXnkIbpl7TpmpQoNjAwKQotLSDlpoLmnpzpnIDopoHosIPor5XnvJPlrZjmg4XlhrXvvIzlj6/ku6XlvIDlkK/osIPor5XmqKHlvI8K6LWE5rqQ57yT5a2YOuiuvue9ruiwg+ivleaooeW8jyh0cnVlKQp5cSA9IOW8leaTjgp5cS7lnLrmma8gPSByZXF1aXJlKCJzY3JpcHQv5YWo5bGAL+S4u+aOpyIpKCkKdHAgPSB5cS7lnLrmma8K5a6d5a6d57G7PXJlcXVpcmUoIlNjcmlwdC/lsZ7mgKfmjqfliLYv5a6d5a6dIikK5ri45oiP5YWs5ZGKPXJlcXVpcmUoInNjcmlwdC/mmL7npLrnsbsv5ri45oiP5YWs5ZGK57G7IikodHApCuaImOaWl+aMh+S7pOexuz1yZXF1aXJlKCJzY3JpcHQv5oiY5paX57G7L+aImOaWl+WRveS7pOexuyIpCuaImOaWl+exuz1yZXF1aXJlKCJzY3JpcHQv5oiY5paX57G7L+aImOaWl+exuyIpKHRwKQrmiJjmlpfljZXkvY3nsbs9cmVxdWlyZSgic2NyaXB0L+aImOaWl+exuy/miJjmlpfljZXkvY3nsbsiKQrmiJjmlpfliqjnlLvnsbs9cmVxdWlyZSgic2NyaXB0L+aImOaWl+exuy/miJjmlpfliqjnlLvnsbsiKQrosIPor5XlhaXlj6MgPSByZXF1aXJlKCJTY3JpcHQv6LCD6K+V57G7L+iwg+ivleWFpeWPoyIpLuWIm+W7uih0cCktLS0tMjAyNS4xLjI0CnJlcXVpcmUoInNjcmlwdC/lpJrph43lr7nor53nsbsv5Lu75Yqh5LqL5Lu2IikK5Yqg6L295a6M5oiQPXRydWUK5YWo5bGAZHQgPSAwLjgK6I+K6YOoZHQgPSAwLjAxMgotLSDlnKjov5nph4zliJ3lp4vljJbnianlk4HmlbDmja7vvIzkv53or4Hlj6rliqDovb3kuIDmrKEKaWYgbm90IG5leHQoSXRlbURhdGEgb3Ige30pIHRoZW4KICAgIEl0ZW1EYXRhPXt9CiAgICDliqDovb3nianlk4HmlbDmja4oKQplbmQKCmlmIF9fZ2dlLmlzZGVidWcgPT0gbmlsIHRoZW4KCWZmaS5sb2FkKOeoi+W6j+ebruW9lS4uImcyMmQuZGxsIikgLS3lpJrlvIDlmagKZW5kCgpsb2NhbCBvbGR0aW1lID0gb3MudGltZSgpCmxvY2FsIGR0dGltZSA9IDAKbG9jYWwgY29udGxlbmcgPSAwCmxvY2FsIHh0dGltZT0xCmxvY2FsIHNkZHNkPTAKbG9jYWwgQWNjZWxlcmF0aW9uPW9zLnRpbWUoKQpfX2ZzeXo9ZmFsc2UKX19mc3l6eHo9ZmFsc2UKX19mc3l6emQ9ZmFsc2UKZnVuY3Rpb24gY2hlY2tTcGVlbmRfZ3JycGsoZHQpCglpZiBvcy50aW1lKCkgLSBvbGR0aW1lID49IDEgdGhlbiAtLTIKCQlvbGR0aW1lID0gb3MudGltZSgpCgkJeHR0aW1lPXh0dGltZSsxIC0tMgoJCWxvY2FsIGdhbWV0aW1lPW1hdGguZmxvb3Io5byV5pOOLuWPlua4uOaIj+aXtumXtCgpLzEwMDApCgkJaWYgZ2FtZXRpbWU+eHR0aW1lIHRoZW4KCQkJaWYgZHR0aW1lPjEgdGhlbiAtLTIuMQoJCQkJc2Rkc2Q9c2Rkc2QrMQoJCQkJaWYgc2Rkc2Q+PTMgdGhlbgoJCQkJCWblh73mlbAu5L+h5oGv5qGGKCLmo4DmtYvliLDlvILluLjmlbDmja7vvIEiLCLkuIvnur/pgJrnn6UiKQoJCQkJCW9zLmV4aXQoKQoJCQkJZW5kCgkJCWVsc2UKCQkJCXNkZHNkPTAKCQkJCXh0dGltZT1nYW1ldGltZS0yCgkJCWVuZAoJCWVuZAoJCWR0dGltZSA9IDAKCQlpZiBfX2ZzeXogYW5kIChfX2ZzeXp4eiBvciBfX2ZzeXp6ZCkgdGhlbgoJCQlpZiBvcy50aW1lKCkgLSBBY2NlbGVyYXRpb24gPj0gNSB0aGVuCgkJCQlBY2NlbGVyYXRpb24gPSBvcy50aW1lKCkKCQkJCeWPkemAgeaVsOaNrig2MzAyKQoJCQllbmQKCQllbmQKCWVuZAoJZHR0aW1lID0gZHR0aW1lICsgZHQKZW5kCmZ1bmN0aW9uIOa4suafk+WHveaVsChkdCx4LHkpLS3pvKDmoId4LOm8oOagh3kKCglpZiDlvJXmk44u5Y+W6ZqP5py65pW05pWwKDEsMzAwKSA9PTEgIGFuZCDmoqbnm74uU2VhcmNoZXMoKSA9PSB0cnVlIHRoZW4KCSAJLS3moqbnm74ubWVzc2FnZUJveCgi6K+35oqKQ0XnrYnkvZzlvIrnsbvova/ku7blhbPpl63vvIHvvIHvvIEiKQoJICDlvJXmk44u5YWz6ZetKCkKCWVuZAoJZHQgPSBkdCrlhajlsYBkdAoJY2hlY2tTcGVlbmRfZ3JycGsoZHQpCgnpvKDmoIcueCzpvKDmoIcueT14LHkKCXlxLua4suafk+W8gOWniygpCgl5cS7muLLmn5PmuIXpmaQoKQoJeXEu5Zy65pmvOuaYvuekuihkdCx4LHkpCgnmuLjmiI/lhazlkYo65pi+56S6KGR0LHgseSkKCiAgLS0g6LCD6K+V5YWl5Y+jOuabtOaWsChkdCktLS0tMjAyNS4xLjI0CiAgLS0g6LCD6K+V5YWl5Y+jOuaYvuekuigpLS0tLTIwMjUuMS4yNAoJeXEu5riy5p+T57uT5p2fKCkKZW5kCmxvY2FsIGZ1bmN0aW9uIOmAgOWHuuWHveaVsCgpCglpZiB0cD09bmlsIHRoZW4KCQlyZXR1cm4gZmFsc2UKCWVuZAoJaWYgdHAu6L+b56iLID09IDEgdGhlbgoJCXJldHVybiB0cnVlCgllbHNlaWYgdHAu6L+b56iLID09IDIgb3IgdHAu6L+b56iLID09IDMgb3IgdHAu6L+b56iLID09IDUgb3IgdHAu6L+b56iLID09IDYgb3IgdHAu6L+b56iLID09IDcgb3IgdHAu6L+b56iLID09IDggb3IgdHAu6L+b56iLID09IDkgb3IgdHAu6L+b56iLMiA9PSAxIHRoZW4KCQl0cC7ov5vnqIsyID0gMQoJCXJldHVybiBmYWxzZQoJZWxzZQoJCXRwLueql+WPoy7ns7vnu5/orr7nva465omT5byAKCkKCQlyZXR1cm4gZmFsc2UKCWVuZAoJcmV0dXJuIGZhbHNlCmVuZArlvJXmk44u572u6YCA5Ye65Ye95pWwKOmAgOWHuuWHveaVsCkKZnVuY3Rpb24g5byV5pOO5YWz6Zet5byA5aeLKCkKCeW8leaTji7lhbPpl60oKQplbmQAAAAAAAAAAOEOAAAAAAAAAAAAAAAAAAAAAAAA4Q4AAAAAAAAAAAAAAAAAAAAAAADhDgAAAAAAAAAAAAAAAAAAAAAAAOEOAAAAAAAAAAAAAA", "JwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvw"], [3, 1, "insert", {"characters": "8"}, "AgAAAMIBAAAAAAAAwwEAAAAAAAAAAAAAwwEAAAAAAADDAQAAAAAAAAEAAAA5", "JAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAMIBAAAAAAAAwwEAAAAAAAAAAAAAAADwvw"], [5, 1, "file_header_replace", {"a": 83, "b": 101, "strings": "   baidwwy"}, "AQAAAFMAAAAAAAAAXQAAAAAAAAAaAAAAICAg5Lqk5rWBUVHnvqTvvJo4MzQyNDgxOTE", "JAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAMMBAAAAAAAAwwEAAAAAAAAAAAAAAADwvw"], [6, 1, "file_header_replace", {"a": 117, "b": 137, "strings": " 2025-04-28 15:23:31"}, "AQAAAHUAAAAAAAAAiQAAAAAAAAAUAAAAIDIwMjUtMDQtMTEgMDA6MDU6MDc", "JAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAALsBAAAAAAAAuwEAAAAAAAAAAAAAAADwvw"], [1, 1, "revert", null, "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", "FwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"], [2, 1, "revert", null, "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", "FwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"]]}, {"file": "/D/myfwd/GGE/Core/Game/gge轨迹带.lua", "settings": {"buffer_size": 266, "encoding": "UTF-8", "line_ending": "Windows"}}, {"file": "/D/myfwd/GGE/Core/Game/gge动画类.lua", "settings": {"buffer_size": 2498, "encoding": "UTF-8", "line_ending": "Windows"}}, {"file": "/D/myfwd/GGE/Core/Game/gge资源类.lua", "settings": {"buffer_size": 1942, "encoding": "UTF-8", "line_ending": "Windows"}}, {"file": "/D/myfwd/GGE/Core/Game/gge图像类.lua", "settings": {"buffer_size": 3203, "encoding": "UTF-8", "line_ending": "Windows"}}, {"file": "/D/myfwd/GGE/Core/Game/gge精灵类.lua", "settings": {"buffer_size": 5937, "encoding": "UTF-8", "line_ending": "Windows"}}], "build_system": "Packages/Lua/ggegame.sublime-build", "build_system_choices": [[[["Packages/Lua/ggegame.sublime-build", ""], ["Packages/Lua/ggegame.sublime-build", "Run"], ["Packages/Lua/ggegame.sublime-build", "RunInCommand"], ["Packages/Lua/ggegame.sublime-build", "SetGGE"], ["Packages/Lua/ggegame.sublime-build", "Stop"], ["Packages/Lua/ggegame.sublime-build", "AboutGGE"]], ["Packages/Lua/ggegame.sublime-build", ""]]], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "console": {"height": 0.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/D/myfwd/客户端", "/D/myfwd/客户端/<PERSON><PERSON>t"], "file_history": ["/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗单位类.lua", "/D/myfwd/客户端/Script/数据中心/技能库.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗命令类.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa176632.44400/鼠标.lua", "/D/客户端/<PERSON><PERSON>t/战斗类/战斗命令类.lua", "/C/Users/<USER>/Desktop/聊天处理类.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa81640.21495/怪物对话内容.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa81640.20042/NPC对话内容.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/网络/数据交换.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/跑商商店.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主显.lua", "/D/myfwd/客户端/script/小九UI/跑商商店.lua", "/D/myfwd/客户端/seamless_update.bat", "/D/myfwd/客户端/start_game.bat", "/D/myfwd/客户端/update.bat", "/D/myfwd/客户端/update_process.bat", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/缓冲.lua", "/D/myfwd/客户端/<PERSON>ript/初系统/游戏更新说明.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/系统设置.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/变量1.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/底图框.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/时辰.lua", "/D/myfwd/客户端/<PERSON>ript/系统类/人物框.lua", "/D/myfwd/客户端/<PERSON>ript/更新类/星盘.lua", "/D/myfwd/客户端/<PERSON>ript/全局/主控.lua", "/D/myfwd/GGE/Core/Server/ggeengine.lua", "/D/myfwd/GGE/Core/Server/ggeserver.lua", "/D/myfwd/客户端/script/全局/主控.lua", "/D/myfwd/客户端/<PERSON>ript/场景类/第二场景.lua", "/D/myfwd/客户端/galaxy2d.log", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗自动栏.lua", "/D/myfwd/客户端/script/全局/主显.lua", "/D/myfwd/客户端/main.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/加载类.lua", "/D/myfwd/GGE/Core/Game/gge引擎.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/小地图.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/地图类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/全局/变量2.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/缓存资源.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/资源类/缓存资源_优化.lua", "/D/myfwd/客户端/Script/数据中心/自定义库.lua", "/D/myfwd/GGE/Extend/文件类.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/战斗类/战斗类.lua", "/D/myfwd/客户端/script/小九UI/法宝.lua", "/D/myfwd/客户端/<PERSON>ript/小九UI/公告弹窗.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/更新内容.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/系统类/按钮.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/功能界面/聊天框.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/任务追踪栏.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/分区.lua", "/D/myfwd/客户端/<PERSON><PERSON>t/初系统/标题.lua", "/D/myfwd/客户端/<PERSON><PERSON><PERSON>/小九UI/任务栏.lua", "/D/myfwd/客户端/<PERSON>ript/系统类/丰富文本.lua", "/D/myfwd/客户端/Script/数据中心/坐骑库.lua", "/D/myfwd/Client/Script/战斗类/战斗类.lua", "/D/myfwd/Client/script/战斗类/战斗动画类.lua", "/D/myfwd/Client/Script/全局/变量2.lua", "/D/myfwd/Client/Script/全局/变量1.lua", "/D/myfwd/Server/Script/任务处理类/任务处理类.lua", "/D/myfwd/Server/main.lua", "/D/myfwd/Server/Script/任务_小九/星官.lua", "/D/myfwd/Server/Script/任务_小九/天罡星.lua", "/D/myfwd/Server/Script/战斗处理类/战斗处理.lua", "/D/myfwd/Server/Script/战斗处理类/AI战斗.lua", "/D/myfwd/Server/Script/系统处理类/系统处理类.lua", "/D/myfwd/Server/Script/角色处理类/角色处理类.lua", "/D/myfwd/Server/Script/战斗处理类/战斗计算/战斗执行类.lua", "/D/myfwd/Server/Script/角色处理类/帮派处理.lua", "/D/myfwd/Server/Script/角色处理类/道具处理类.lua", "/D/myfwd/Server/Script/地图处理类/地图处理类.lua", "/D/myfwd/Server/Script/战斗处理类/天罡星AI.lua", "/D/myfwd/Server/Script/战斗处理类/怪物属性.lua", "/D/myfwd/Server/Script/数据中心/物品数据.lua", "/D/myfwd/Server/Script/战斗处理类/战斗计算/战斗技能.lua", "/D/myfwd/Server/Script/任务_小九/中秋任务.lua", "/D/myfwd/Server/Script/任务_小九/地煞星.lua", "/D/myfwd/Server/Script/助战处理类/MateControl.lua", "/D/myfwd/Server/Script/任务_小九/副本任务/一斛珠.lua", "/D/myfwd/Server/Script/任务_小九/副本任务/齐天大圣.lua", "/D/myfwd/Server/Script/战斗处理类/战斗计算/战斗计算.lua", "/D/myfwd/Server/Script/战斗处理类/战斗计算/状态处理.lua", "/D/myfwd/Server/Script/战斗处理类/战斗计算/初始技能计算.lua", "/D/myfwd/Server/Script/任务_小九/周末玩法/剑会.lua", "/D/myfwd/Server/Script/战斗处理类/创建战斗.lua", "/D/myfwd/Server/Script/地图处理类/地图坐标类.lua", "/D/myfwd/Server/Script/地图处理类/路径类.lua", "/D/myfwd/Server/Script/地图处理类/MAP.lua", "/D/myfwd/Server/Script/角色处理类/队伍处理类.lua", "/D/myfwd/战斗命令类.lua", "/D/myfwd/队伍处理类.lua", "/D/myfwd/Server/Script/系统处理类/ScriptInit.lua", "/D/myfwd/Server/Script/角色处理类/多开系统.lua", "/D/myfwd/Server/Script/任务_小九/副本任务/红孩儿.lua", "/D/myfwd/Server/Script/任务_小九/副本任务/轮回境副本.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/AI战斗.lua", "/D/梦幻服务端/服务端/main.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/战斗计算/普攻计算.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/战斗处理.lua", "/D/梦幻服务端/服务端/Script/任务_小九/地煞星.lua", "/D/梦幻服务端/服务端/Script/角色处理类/道具处理类.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/战斗计算/物理技能计算.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/战斗计算/战斗执行类.lua", "/D/梦幻服务端/服务端/Script/初始化脚本.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/星魂处理.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/星魂战斗桥接.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/战斗处理补丁.lua", "/D/梦幻服务端/服务端/Script/任务_小九/星官.lua", "/D/梦幻服务端/服务端/Script/属性控制/宠物.lua", "/D/梦幻服务端/服务端/Script/属性控制/队伍.lua", "/D/梦幻服务端/服务端/Script/角色处理类/孩子处理类.lua", "/D/梦幻服务端/服务端/Script/任务_小九/周末玩法/降妖伏魔.lua", "/D/梦幻服务端/服务端/Script/角色处理类/角色处理类.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/怪物属性.lua", "/D/梦幻服务端/服务端/Script/任务_小九/常规任务/宝图_妖王.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/红孩儿.lua", "/D/梦幻服务端/服务端/Script/地图处理类/地图处理类.lua", "/D/梦幻服务端/服务端/Script/数据中心/宝宝.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/车迟斗法.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/水陆大会.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/双城记.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/乌鸡国.lua", "/D/梦幻服务端/服务端/Script/助战处理类/MateControl.lua", "/D/梦幻服务端/服务端/Script/角色处理类/队伍处理类.lua", "/D/梦幻服务端/服务端/Script/系统处理类/网络处理类.lua", "/D/梦幻服务端/服务端/Script/线程/定时器3.lua", "/D/梦幻服务端/服务端/Script/全局函数类/玩家操作类.lua", "/D/梦幻服务端/服务端/Script/任务_小九/神器任务/神器任务.lua", "/D/梦幻服务端/服务端/Script/全局函数类/全局循环类.lua"], "find": {"height": 32.0}, "find_in_files": {"height": 138.0, "where_history": ["D:\\myfwd\\客户端\\Script,<project filters>", "D:\\myfwd\\客户端,<project filters>", "D:\\myfwd\\客户端\\Script,<project filters>"]}, "find_state": {"case_sensitive": true, "find_history": ["self.挨打坐标.x", "六道轮回", "[2]", "抓捕", "朴拙", "🔥", "--", "传送", "c欢送", "消息框:分辨率更改事件()", "全局切换界面", "qiehuan", "星盘", "转盘", "备份", "boolean", "处理", "自动战斗", "正在加载资源", "是否红字体", "wdf配置", "q=", "q", "lsph", "zt", "载入", "载入1", "结果", "内存", "猫灵", "607.1", "字体", "内容3", "字体", "官府", "公文", "官府公文", "大分区OL[i]", "追忆", "2008", "全局小分区", "字体", "任务追踪", "一般字体", "一般字体1", "坐骑库", "字体表", "字体", "停止", "移动事件", "移动"], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": [], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "main.lua", "semi_transient": false, "settings": {"buffer_size": 3887, "regions": {}, "selection": [[0, 0]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 233, 4, 11, 8, 40, 37, 7, 221, 49, 113, 1, 133, 113, 2, 82, 113, 3, 46], "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 2, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 2, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "file": "/D/myfwd/GGE/Core/Game/gge轨迹带.lua", "semi_transient": false, "settings": {"buffer_size": 266, "regions": {}, "selection": [[0, 0]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 232, 10, 21, 15, 2, 9, 14, 142, 198, 113, 1, 133, 113, 2, 82, 113, 3, 46], "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax"}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 5, "stack_multiselect": false, "type": "text"}, {"buffer": 2, "file": "/D/myfwd/GGE/Core/Game/gge动画类.lua", "semi_transient": false, "settings": {"buffer_size": 2498, "regions": {}, "selection": [[0, 0]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 232, 10, 21, 15, 2, 9, 14, 45, 24, 113, 1, 133, 113, 2, 82, 113, 3, 46], "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 2178.0, "zoom_level": 1.0}, "stack_index": 4, "stack_multiselect": false, "type": "text"}, {"buffer": 3, "file": "/D/myfwd/GGE/Core/Game/gge资源类.lua", "semi_transient": false, "settings": {"buffer_size": 1942, "regions": {}, "selection": [[0, 0]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 232, 10, 21, 15, 2, 9, 14, 138, 222, 113, 1, 133, 113, 2, 82, 113, 3, 46], "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 1311.0, "zoom_level": 1.0}, "stack_index": 3, "stack_multiselect": false, "type": "text"}, {"buffer": 4, "file": "/D/myfwd/GGE/Core/Game/gge图像类.lua", "semi_transient": false, "settings": {"buffer_size": 3203, "regions": {}, "selection": [[0, 0]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 232, 10, 21, 15, 2, 9, 14, 52, 233, 113, 1, 133, 113, 2, 82, 113, 3, 46], "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 2407.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 5, "file": "/D/myfwd/GGE/Core/Game/gge精灵类.lua", "selected": true, "semi_transient": false, "settings": {"buffer_size": 5937, "regions": {}, "selection": [[0, 0]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 232, 10, 21, 15, 2, 9, 14, 131, 13, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 4997.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 32.0}, "input": {"height": 44.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.exec": {"height": 217.0}, "output.find_results": {"height": 0.0}, "pinned_build_system": "Packages/Lua/ggegame.sublime-build", "position": "0,0,1,-1,-1,-1,-1,994,426,83,1664,d866abf532d7634db7f3138602a53bd3", "project": "开发客户端.sublime-project", "replace": {"height": 60.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [], "width": 380.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 223.0, "status_bar_visible": true, "template_settings": {}, "window_id": 404, "workspace_name": "/D/myfwd/客户端/开发客户端.sublime-workspace"}], "workspaces": {"recent_workspaces": ["/D/myfwd/服务端/开发服务端.sublime-workspace", "/D/myfwd/客户端/开发客户端.sublime-workspace", "/G/jingruihutong/互通授权源码/服务端源码更新中/开发服务端.sublime-workspace", "/G/神来修复卖/1.Server/开发服务端.sublime-workspace", "/G/jingruihutong/互通授权源码/精锐西游测试版客户端/开发梦幻西游.sublime-workspace", "/D/myfwd/Client/开发客户端.sublime-workspace", "/D/myfwd/Server/开发服务端.sublime-workspace", "/F/BaiduNetdiskDownload/0222-花好(梦回)/花好三端源码+服务端/pc客户端源码等/开发梦幻西游.sublime-workspace", "/F/BaiduNetdiskDownload/0222-花好(梦回)/花好三端源码+服务端/服务端/服务端/开发服务端.sublime-workspace", "/F/BaiduNetdiskDownload/王者西游-全套源码/客户端/客户端/游戏模板.sublime-workspace", "/D/梦幻服务端/服务端/开发服务端.sublime-workspace", "/D/梦幻服务端/客户端/开发客户端.sublime-workspace", "/D/梦幻服务端/网关源码/游戏模板.sublime-workspace", "/F/BaiduNetdiskDownload/无双开团/无双客户端/开发梦幻西游.sublime-workspace", "/F/BaiduNetdiskDownload/王者西游-全套源码/服务端/网关源码/游戏模板.sublime-workspace", "/F/BaiduNetdiskDownload/王者西游-全套源码/服务端/服务端源码/游戏模板.sublime-workspace", "/F/BaiduNetdiskDownload/吊3九黎城/服务端/服务端/服务端.sublime-workspace", "/D/梦幻服务端/GGE/开发服务端.sublime-workspace", "/G/三花修复版-低调使用/客户端/开发梦幻西游.sublime-workspace", "/D/2024.07.04团/服务端/开发服务端.sublime-workspace", "/D/08正版钓鱼/NewServer/服务端模板.sublime-workspace", "/D/Server/服务端.sublime-workspace", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa20048.31156/服务端模板.sublime-workspace", "/G/三花修复版-低调使用/服务端/开发服务端.sublime-workspace", "/D/2024.07.04团/PC客户端/开发梦幻西游.sublime-workspace", "/D/BaiduNetdiskDownload/2008/客户端/开发服务端.sublime-workspace", "/D/BaiduNetdiskDownload/2008/GGE/开发服务端.sublime-workspace", "/D/BaiduNetdiskDownload/2008/服务端/开发服务端.sublime-workspace"]}}