<snippet>
    <content><![CDATA[
--======================================================================--
${1:列表} =  ${2:窗口}:创建列表("${1:名称}",0,0)
    function ${1:列表}:初始化()
        for i=1,20 do
            self:添加('测试'..i)
        end
        self:置编辑模式(true)
    end

    function ${1:列表}:消息事件(消息,a,b)
        if 消息 == '选中项目' then

        end
    end

]]></content>
    <tabTrigger>guilb_</tabTrigger>
    <scope>source.lua</scope>
    <description>列表模版</description>
</snippet>
