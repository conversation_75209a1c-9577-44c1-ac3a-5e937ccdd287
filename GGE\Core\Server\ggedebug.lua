-- @Author: 作者QQ381990860
-- @Date:   2021-03-12 01:19:44
-- @Last Modified by:   baidwwy
-- @Last Modified time: 2025-04-09 15:43:35

local errored--只显示一次
local _print = print
function __gge.traceback(msg)
	if not errored then
		 if  调试模式 or DebugMode then
			print(tostring(msg).."..//按F4或双击此行可转到错误代码页。//")
			print("---------------------------------以下为可能错误的方---------------------------------")
			print(debug.traceback())
          else
         	--错误日志=错误日志.."\n"..os.date("[%Y年%m月%d日%X]")..":"..msg
	  		--错误数目=错误数目+1
		  end
	end
end
