-- @Author: 反作弊系统
-- @Date: 2025-01-14
-- @Description: 玩家行为模式检测系统，用于检测自动化脚本和异常行为

local 行为检测系统 = {}

-- 玩家行为数据存储
local 玩家行为数据 = {}

-- 检测配置参数
local 检测配置 = {
    -- 移动检测参数
    移动 = {
        最大移动速度 = 300,           -- 每秒最大移动距离（像素）
        异常移动次数阈值 = 5,         -- 连续异常移动次数
        重复路径检测长度 = 10,        -- 检测重复路径的长度
        机械化移动阈值 = 0.95,        -- 移动模式相似度阈值
    },
    
    -- 跑商操作检测参数
    跑商 = {
        最小操作间隔 = 2,             -- 最小操作间隔（秒）
        连续操作阈值 = 10,            -- 连续快速操作次数阈值
        价格查询频率限制 = 30,        -- 每分钟最大价格查询次数
        异常利润率阈值 = 0.8,         -- 异常高利润率阈值
    },
    
    -- 时间模式检测参数
    时间模式 = {
        精确时间操作阈值 = 5,         -- 精确时间操作次数阈值
        操作时间方差阈值 = 1.0,       -- 操作时间间隔方差阈值
    }
}

-- 初始化玩家行为数据
function 行为检测系统:初始化玩家数据(玩家id)
    if not 玩家行为数据[玩家id] then
        玩家行为数据[玩家id] = {
            -- 移动相关数据
            移动记录 = {},
            上次移动时间 = 0,
            上次移动坐标 = {x = 0, y = 0},
            异常移动计数 = 0,
            移动路径历史 = {},
            
            -- 跑商操作数据
            跑商操作记录 = {},
            上次跑商操作时间 = 0,
            连续快速操作计数 = 0,
            价格查询计数 = {},
            利润率记录 = {},
            
            -- 时间模式数据
            操作时间间隔 = {},
            精确时间操作计数 = 0,
            
            -- 可疑行为计分
            可疑度评分 = 0,
            警告次数 = 0,
            最后检测时间 = os.time(),
        }
    end
end

-- 检测移动行为异常
function 行为检测系统:检测移动异常(玩家id, 新坐标)
    if not 玩家数据[玩家id] or not 新坐标 then
        return false
    end
    
    self:初始化玩家数据(玩家id)
    local 行为数据 = 玩家行为数据[玩家id]
    local 当前时间 = os.time()
    
    -- 计算移动距离和速度
    if 行为数据.上次移动时间 > 0 then
        local 时间差 = 当前时间 - 行为数据.上次移动时间
        if 时间差 > 0 then
            local 距离 = math.sqrt(
                (新坐标.x - 行为数据.上次移动坐标.x)^2 + 
                (新坐标.y - 行为数据.上次移动坐标.y)^2
            )
            local 移动速度 = 距离 / 时间差
            
            -- 检测异常移动速度
            if 移动速度 > 检测配置.移动.最大移动速度 then
                行为数据.异常移动计数 = 行为数据.异常移动计数 + 1
                self:记录可疑行为(玩家id, "异常移动速度", {
                    速度 = 移动速度,
                    距离 = 距离,
                    时间差 = 时间差
                })
                
                if 行为数据.异常移动计数 >= 检测配置.移动.异常移动次数阈值 then
                    return true -- 检测到异常
                end
            else
                行为数据.异常移动计数 = math.max(0, 行为数据.异常移动计数 - 1)
            end
            
            -- 记录移动历史用于模式检测
            table.insert(行为数据.移动记录, {
                坐标 = {x = 新坐标.x, y = 新坐标.y},
                时间 = 当前时间,
                速度 = 移动速度
            })
            
            -- 限制记录长度
            if #行为数据.移动记录 > 50 then
                table.remove(行为数据.移动记录, 1)
            end
        end
    end
    
    -- 更新移动数据
    行为数据.上次移动时间 = 当前时间
    行为数据.上次移动坐标 = {x = 新坐标.x, y = 新坐标.y}
    
    -- 检测重复路径模式
    self:检测重复移动模式(玩家id)
    
    return false
end

-- 检测重复移动模式
function 行为检测系统:检测重复移动模式(玩家id)
    local 行为数据 = 玩家行为数据[玩家id]
    if #行为数据.移动记录 < 检测配置.移动.重复路径检测长度 * 2 then
        return false
    end
    
    local 记录长度 = #行为数据.移动记录
    local 检测长度 = 检测配置.移动.重复路径检测长度
    
    -- 比较最近的移动模式
    local 相似度 = 0
    for i = 1, 检测长度 do
        local 当前点 = 行为数据.移动记录[记录长度 - i + 1]
        local 对比点 = 行为数据.移动记录[记录长度 - 检测长度 - i + 1]
        
        if 当前点 and 对比点 then
            local 距离差 = math.abs(
                math.sqrt((当前点.坐标.x - 对比点.坐标.x)^2 + (当前点.坐标.y - 对比点.坐标.y)^2)
            )
            if 距离差 < 50 then -- 50像素内认为是相似位置
                相似度 = 相似度 + 1
            end
        end
    end
    
    local 相似度比例 = 相似度 / 检测长度
    if 相似度比例 >= 检测配置.移动.机械化移动阈值 then
        self:记录可疑行为(玩家id, "重复移动模式", {
            相似度 = 相似度比例,
            检测长度 = 检测长度
        })
        return true
    end
    
    return false
end

-- 检测跑商操作异常
function 行为检测系统:检测跑商操作异常(玩家id, 操作类型, 操作数据)
    if not 玩家数据[玩家id] then
        return false
    end
    
    self:初始化玩家数据(玩家id)
    local 行为数据 = 玩家行为数据[玩家id]
    local 当前时间 = os.time()
    
    -- 检测操作频率
    if 行为数据.上次跑商操作时间 > 0 then
        local 时间间隔 = 当前时间 - 行为数据.上次跑商操作时间
        
        if 时间间隔 < 检测配置.跑商.最小操作间隔 then
            行为数据.连续快速操作计数 = 行为数据.连续快速操作计数 + 1
            
            if 行为数据.连续快速操作计数 >= 检测配置.跑商.连续操作阈值 then
                self:记录可疑行为(玩家id, "跑商操作过于频繁", {
                    连续次数 = 行为数据.连续快速操作计数,
                    时间间隔 = 时间间隔
                })
                return true
            end
        else
            行为数据.连续快速操作计数 = 0
        end
        
        -- 记录操作时间间隔用于模式分析
        table.insert(行为数据.操作时间间隔, 时间间隔)
        if #行为数据.操作时间间隔 > 20 then
            table.remove(行为数据.操作时间间隔, 1)
        end
    end
    
    -- 记录跑商操作
    table.insert(行为数据.跑商操作记录, {
        类型 = 操作类型,
        时间 = 当前时间,
        数据 = 操作数据
    })
    
    if #行为数据.跑商操作记录 > 100 then
        table.remove(行为数据.跑商操作记录, 1)
    end
    
    行为数据.上次跑商操作时间 = 当前时间
    
    -- 检测价格查询频率
    if 操作类型 == "价格查询" then
        self:检测价格查询频率(玩家id)
    end
    
    -- 检测时间模式异常
    self:检测时间模式异常(玩家id)
    
    return false
end

-- 检测价格查询频率
function 行为检测系统:检测价格查询频率(玩家id)
    local 行为数据 = 玩家行为数据[玩家id]
    local 当前时间 = os.time()
    local 当前分钟 = math.floor(当前时间 / 60)
    
    -- 初始化当前分钟的计数
    if not 行为数据.价格查询计数[当前分钟] then
        行为数据.价格查询计数[当前分钟] = 0
    end
    
    行为数据.价格查询计数[当前分钟] = 行为数据.价格查询计数[当前分钟] + 1
    
    -- 清理过期数据
    for 分钟, 计数 in pairs(行为数据.价格查询计数) do
        if 当前分钟 - 分钟 > 5 then -- 保留5分钟数据
            行为数据.价格查询计数[分钟] = nil
        end
    end
    
    -- 检测是否超过频率限制
    if 行为数据.价格查询计数[当前分钟] > 检测配置.跑商.价格查询频率限制 then
        self:记录可疑行为(玩家id, "价格查询过于频繁", {
            当前分钟查询次数 = 行为数据.价格查询计数[当前分钟],
            限制 = 检测配置.跑商.价格查询频率限制
        })
        return true
    end
    
    return false
end

-- 检测时间模式异常
function 行为检测系统:检测时间模式异常(玩家id)
    local 行为数据 = 玩家行为数据[玩家id]
    
    if #行为数据.操作时间间隔 >= 10 then
        -- 计算时间间隔的方差
        local 平均间隔 = 0
        for _, 间隔 in ipairs(行为数据.操作时间间隔) do
            平均间隔 = 平均间隔 + 间隔
        end
        平均间隔 = 平均间隔 / #行为数据.操作时间间隔
        
        local 方差 = 0
        for _, 间隔 in ipairs(行为数据.操作时间间隔) do
            方差 = 方差 + (间隔 - 平均间隔)^2
        end
        方差 = 方差 / #行为数据.操作时间间隔
        
        -- 如果方差过小，说明操作时间过于规律
        if 方差 < 检测配置.时间模式.操作时间方差阈值 then
            self:记录可疑行为(玩家id, "操作时间过于规律", {
                方差 = 方差,
                平均间隔 = 平均间隔,
                样本数 = #行为数据.操作时间间隔
            })
            return true
        end
    end
    
    return false
end

-- 记录可疑行为
function 行为检测系统:记录可疑行为(玩家id, 行为类型, 详细信息)
    if not 玩家数据[玩家id] then
        return
    end
    
    local 行为数据 = 玩家行为数据[玩家id]
    local 当前时间 = os.time()
    
    -- 增加可疑度评分
    local 评分增加 = {
        ["异常移动速度"] = 10,
        ["重复移动模式"] = 15,
        ["跑商操作过于频繁"] = 20,
        ["价格查询过于频繁"] = 15,
        ["操作时间过于规律"] = 25,
    }
    
    行为数据.可疑度评分 = 行为数据.可疑度评分 + (评分增加[行为类型] or 5)
    
    -- 记录日志
    local 日志信息 = string.format(
        "[反作弊] 玩家%d(%s) 检测到可疑行为: %s, 详情: %s, 当前可疑度: %d",
        玩家id,
        玩家数据[玩家id].角色.名称 or "未知",
        行为类型,
        table.tostring(详细信息 or {}),
        行为数据.可疑度评分
    )
    
    __S服务:输出(日志信息)
    
    -- 写入日志文件
    pcall(function()
        local 日志文件 = io.open("logs/anti_cheat.log", "a")
        if 日志文件 then
            日志文件:write(os.date("%Y-%m-%d %H:%M:%S") .. " " .. 日志信息 .. "\n")
            日志文件:close()
        end
    end)
    
    -- 根据可疑度评分采取行动
    self:处理可疑行为(玩家id, 行为数据.可疑度评分)
end

-- 处理可疑行为
function 行为检测系统:处理可疑行为(玩家id, 可疑度评分)
    if not 玩家数据[玩家id] then
        return
    end
    
    local 行为数据 = 玩家行为数据[玩家id]
    
    if 可疑度评分 >= 100 then
        -- 高度可疑，暂时限制跑商
        self:限制跑商活动(玩家id, 3600) -- 限制1小时
        常规提示(玩家id, "#R/系统检测到异常行为，暂时限制跑商活动1小时")
        
        -- 通知管理员
        self:通知管理员(玩家id, "高度可疑行为", 可疑度评分)
        
    elseif 可疑度评分 >= 50 then
        -- 中度可疑，发出警告
        行为数据.警告次数 = 行为数据.警告次数 + 1
        常规提示(玩家id, "#Y/系统检测到可疑操作，请注意游戏行为规范")
        
        if 行为数据.警告次数 >= 3 then
            self:限制跑商活动(玩家id, 1800) -- 限制30分钟
            常规提示(玩家id, "#R/多次检测到可疑行为，暂时限制跑商活动30分钟")
        end
    end
end

-- 限制跑商活动
function 行为检测系统:限制跑商活动(玩家id, 限制时长)
    if not 玩家数据[玩家id] then
        return
    end

    玩家数据[玩家id].角色.跑商限制 = os.time() + 限制时长

    -- 如果玩家正在跑商，强制取消
    if 玩家数据[玩家id].角色.跑商 then
        -- 取消跑商任务的逻辑
        local 任务id = 玩家数据[玩家id].角色:取任务(24)
        if 任务id and 任务id ~= 0 then
            玩家数据[玩家id].角色:取消任务(任务id)
            任务数据[任务id] = nil
        end

        玩家数据[玩家id].角色.跑商 = nil
        玩家数据[玩家id].角色.跑商时间 = nil

        -- 删除帮派银票
        for i = 1, 80 do
            local 道具id = 玩家数据[玩家id].角色.道具[i]
            if 道具id and 玩家数据[玩家id].道具.数据[道具id] then
                if 玩家数据[玩家id].道具.数据[道具id].名称 == "帮派银票" then
                    玩家数据[玩家id].角色.道具[i] = nil
                    玩家数据[玩家id].道具.数据[道具id] = nil
                    break
                end
            end
        end

        道具刷新(玩家id)
    end
end

-- 通知管理员
function 行为检测系统:通知管理员(玩家id, 行为类型, 可疑度评分)
    if not 玩家数据[玩家id] then
        return
    end

    local 通知内容 = string.format(
        "[反作弊警报] 玩家 %s(ID:%d) 检测到%s，可疑度评分: %d",
        玩家数据[玩家id].角色.名称 or "未知",
        玩家id,
        行为类型,
        可疑度评分
    )

    -- 向所有在线管理员发送通知
    for id, data in pairs(玩家数据) do
        if data and data.角色 and data.角色.权限 and data.角色.权限 >= 5 then
            常规提示(id, "#R/" .. 通知内容)
        end
    end

    -- 记录到管理员日志
    pcall(function()
        local 日志文件 = io.open("logs/admin_alerts.log", "a")
        if 日志文件 then
            日志文件:write(os.date("%Y-%m-%d %H:%M:%S") .. " " .. 通知内容 .. "\n")
            日志文件:close()
        end
    end)
end

-- 重置玩家可疑度评分（管理员命令）
function 行为检测系统:重置可疑度评分(玩家id)
    if 玩家行为数据[玩家id] then
        玩家行为数据[玩家id].可疑度评分 = 0
        玩家行为数据[玩家id].警告次数 = 0
        return true
    end
    return false
end

-- 获取玩家行为统计
function 行为检测系统:获取玩家统计(玩家id)
    if not 玩家行为数据[玩家id] then
        return nil
    end

    local 数据 = 玩家行为数据[玩家id]
    return {
        可疑度评分 = 数据.可疑度评分,
        警告次数 = 数据.警告次数,
        异常移动计数 = 数据.异常移动计数,
        连续快速操作计数 = 数据.连续快速操作计数,
        移动记录数量 = #数据.移动记录,
        跑商操作记录数量 = #数据.跑商操作记录,
        最后检测时间 = 数据.最后检测时间
    }
end

-- 清理过期数据
function 行为检测系统:清理过期数据()
    local 当前时间 = os.time()
    local 清理阈值 = 24 * 60 * 60 -- 24小时

    for 玩家id, 数据 in pairs(玩家行为数据) do
        if 当前时间 - 数据.最后检测时间 > 清理阈值 then
            -- 如果玩家不在线且数据过期，清理数据
            if not 玩家数据[玩家id] then
                玩家行为数据[玩家id] = nil
            end
        end
    end
end

-- 导出检测系统
return 行为检测系统
