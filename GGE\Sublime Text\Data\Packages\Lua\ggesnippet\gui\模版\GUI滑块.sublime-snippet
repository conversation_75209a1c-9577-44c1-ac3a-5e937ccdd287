<snippet>
    <content><![CDATA[
--======================================================================--
${1:滑块} = ${2:对象}:创建滑块("${1:名称}",0,0)
    function ${1:滑块}:初始化()
        self:置正常纹理(${3:纹理})
        self:置按下纹理(${3:纹理})
        self:置编辑模式(true)
    end
    function ${1:滑块}:消息事件(消息,a,b)
        if 消息 == '位置改变' then

        end
    end
]]></content>
    <tabTrigger>guihk_</tabTrigger>
    <scope>source.lua</scope>
    <description>滑块竖型模版</description>
</snippet>
