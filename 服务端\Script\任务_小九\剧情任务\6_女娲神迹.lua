-- @Author: baidwwy
-- @Date:   2024-06-07 18:12:58
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-02-16 10:11:42

function 怪物属性:春三十娘(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=80
	local 模型={"狐狸精"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(85*80 + 1800),
	伤害=math.floor(85*8 + 250),
	法伤=math.floor(sx.属性.法伤),
	速度=math.floor(85*2 + 100),
	防御=math.floor(85*3.8),
	法防=math.floor(5+(等级/2)*25.5),
	等级=等级,
	技能={"高级神迹"},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(85*80 + 1800),
		伤害=math.floor(85*6 + 250),
		法伤=math.floor(sx.属性.法伤),
		速度=math.floor(85*2 + 100),
		防御=math.floor(85*3.8),
		法防=math.floor(5+(等级/2)*25.5),
		等级=等级,
		技能={"高级神迹"},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110003(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(352500),"主线剧情")
		玩家数据[v].角色.历劫.女娲 = true
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=6,编号 = 1,地图 = 1100,进度 = 10,附加={}}
		发送数据(玩家数据[v].连接id,227,{剧情=玩家数据[v].角色.剧情})
	end
end