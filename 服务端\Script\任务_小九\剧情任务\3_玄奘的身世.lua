-- @Author: baidwwy
-- @Date:   2025-01-20 19:07:57
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-12 22:48:23

function 怪物属性:玄奘的身世一白鹿精(任务id,玩家id)
	local 战斗单位={}
	local 等级=25
	local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	
	-- 白鹿精（旧版造型为赌徒，使用普通物理攻击）
	战斗单位[1]={
		名称=剧情名称.名称,
		模型="赌徒",      -- 保持经典造型
		伤害=等级*16,    -- 旧版物理伤害系数
		气血=等级*120,   -- 气血公式调整（旧版约3000血）
		法伤=等级*8,     -- 非法系怪
		速度=等级*3,     -- 速度系数
		防御=等级*2,     -- 防御系数
		法防=0,         -- 旧版无法防设定
		躲闪=等级*1,
		等级=等级,
		技能={"连击","必杀"},  -- 经典物理技能
		主动技能=取随机法术(3) -- 旧版只有3个技能槽
	}
	
	-- 花妖喽啰保持原造型（旧版为4个花妖）
	for i=2,5 do
		战斗单位[i]={
			名称="花妖喽啰",
			模型="花妖",
			伤害=等级*12,   -- 物理系小怪
			气血=等级*80,   -- 旧版血量较低
			法伤=0,
			速度=等级*2,
			防御=等级*1,
			法防=0,
			躲闪=等级*0.5,
			等级=等级,
			技能={},
			主动技能={}  -- 旧版无特殊技能
		}
	end
	return 战斗单位
end
function 胜利MOB_110005(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(25000),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v,"鹿茸",10)
		玩家数据[v].角色.剧情={主线=3,编号 = 9,地图 = 1070,进度 = 3,附加={}}
		local wb =  {"赌徒","白鹿精","英雄莫要伤我，我同你回去见主人便是。~"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:玄奘的身世一玉面狐狸(任务id,玩家id)
    local 战斗单位={}
    local 等级=25
    local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
    
    -- 玉面狐狸（旧版物理系设定）
    战斗单位[1]={
        名称="玉面狐狸",
        模型="狐狸精",      -- 保持经典造型
        伤害=等级*14,      -- 下调伤害系数（旧版约350伤害）
        气血=等级*100,     -- 气血公式调整（旧版约2500血）
        法伤=0,           -- 非法系怪
        速度=等级*2.5,     -- 速度系数
        防御=等级*1.5,     -- 防御系数
        法防=0,           -- 旧版无法防设定
        躲闪=等级*1,
        等级=等级,
        技能={"高级连击","感知"},  -- 经典物理技能
        主动技能={}     -- 旧版无特殊技能
    }
    
    -- 花妖喽啰保持原设定
    for i=2,3 do
        战斗单位[i]={
            名称="花妖喽啰",
            模型="花妖",
            伤害=等级*10,   -- 物理系小怪
            气血=等级*60,   -- 旧版血量较低
            法伤=0,
            速度=等级*1.8,
            防御=等级*1,
            法防=0,
            躲闪=等级*0.5,
            等级=等级,
            技能={},
            主动技能={}  -- 旧版无特殊技能
        }
    end
    return 战斗单位
end
function 胜利MOB_110006(胜利id,战斗数据)
    local 数字id = 战斗数据.进入战斗玩家id
    local id组={数字id}
    if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
        for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
            if v~=数字id then
                if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
                    id组[#id组+1]=v
                end
            end
        end
    end
    for k,v in pairs(id组) do
        玩家数据[v].角色:添加经验(math.floor(15000),"主线剧情")  -- 经验下调至旧版标准
        玩家数据[v].角色:增加剧情点(1)
        玩家数据[v].道具:给予道具(v,"仙狐涎",5)  -- 数量从20下调至5
        玩家数据[v].角色.剧情.附加.玉面狐狸 = true
        local wb =  {"狐狸精","玉面狐狸","果然不是你的对手……老公，我先闪了啊，你留下慢慢玩~"}
        local xx = {}
        local wb2 = {}
        发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
    end
end
function 怪物属性:玄奘的身世一酒肉和尚(任务id,玩家id)
    local 战斗单位={}
    local 等级=25
    local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
    
    -- 酒肉和尚（2015-2017年主流设定）
    战斗单位[1]={
        名称=剧情名称.名称,
        模型="雨师",        -- 保持山贼造型（2016客户端资源）
        伤害=等级*14,       -- 物理伤害约400
        气血=等级*150,      -- 约3750血
        法伤=等级*14,       -- 混合伤害设定
        速度=等级*3,
        防御=等级*2,
        法防=等级*1.5,
        等级=等级,
        技能={"魔之心","神佑复生"},  -- 通用技能
        主动技能={"推气过宫","唧唧歪歪","金刚护体"}  -- 化生寺技能组合
    }
    
    -- 赌徒喽啰（2015版设定）
    for i=2,5 do
        战斗单位[i]={
            名称="赌徒喽啰",
            模型="赌徒",
            伤害=等级*12,     -- 物理系小怪
            气血=等级*80,     -- 约2000血
            法伤=0,
            速度=等级*2,
            防御=等级*1,
            法防=0,
            等级=等级,
            技能={},
            主动技能={}  -- 无特殊技能
        }
    end
    return 战斗单位
end
function 胜利MOB_110007(胜利id,战斗数据)
    local 数字id = 战斗数据.进入战斗玩家id
    local id组={数字id}
    if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
        for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
            if v~=数字id then
                if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
                    id组[#id组+1]=v
                end
            end
        end
    end
    for k,v in pairs(id组) do
        玩家数据[v].角色:添加经验(math.floor(22000),"主线剧情")  -- 经验下调至旧版标准
        玩家数据[v].角色:增加剧情点(1)
        玩家数据[v].道具:给予道具(v,"琥珀腰链",1)  -- 旧版只掉落1个
        玩家数据[v].角色.剧情={主线=3,编号 = 2,地图 = 1153,进度 = 6,附加={}}
        local wb =  {"雨师","酒肉和尚","哎，我念佛念不过他，打架打不过你，真丢脸啊！"}  -- 模型名称对应调整
        local xx = {}
        local wb2 = {}
        发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
    end
end
function 怪物属性:玄奘的身世一白琉璃(任务id,玩家id)
    local 战斗单位={}
    local 等级=25
    local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
    
    -- 白琉璃（旧版星灵仙子造型，法系设定）
    战斗单位[1]={
        名称=剧情名称.名称,
        模型="星灵仙子",    -- 保持经典造型
        伤害=等级*10,       -- 低物理伤害
        气血=等级*120,      -- 血量约3000
        法伤=等级*20,       -- 高法伤系数（旧版约500法伤）
        速度=等级*3,
        防御=等级*1.5,
        法防=等级*3,        -- 较高法抗
        躲闪=等级*1,
        等级=等级,
        技能={"魔之心","法术暴击"},  -- 经典法系技能
        主动技能={"龙卷雨击","龙腾"}  -- 龙宫法术
    }
    
    -- 狐狸精喽啰保持原设定
    for i=2,5 do
        战斗单位[i]={
            名称="狐狸精喽啰",
            模型="狐狸精",
            伤害=等级*12,     -- 物理系小怪
            气血=等级*80,     -- 旧版血量约2000
            法伤=0,
            速度=等级*2,
            防御=等级*1,
            法防=0,
            躲闪=等级*0.5,
            等级=等级,
            技能={},
            主动技能={}  -- 无特殊技能
        }
    end
    return 战斗单位
end
function 胜利MOB_110008(胜利id,战斗数据)
    local 数字id = 战斗数据.进入战斗玩家id
    local id组={数字id}
    if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
        for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
            if v~=数字id then
                if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
                    id组[#id组+1]=v
                end
            end
        end
    end
    for k,v in pairs(id组) do
        玩家数据[v].角色:添加经验(math.floor(25000),"主线剧情")  -- 经验下调至旧版标准
        玩家数据[v].角色:增加剧情点(1)
        玩家数据[v].道具:给予道具(v,"紧身衣",1)  -- 旧版只掉落1件
        玩家数据[v].道具:给予道具(v,"佛光舍利子",5)  -- 数量从10下调至5
        玩家数据[v].角色.剧情={主线=3,编号 = 6,地图 = 1002,进度 = 8,分支=4,附加={物品="佛光舍利子"}}
        local wb =  {"星灵仙子","白琉璃","我认输了"}
        local xx = {}
        local wb2 = {}
        发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
    end
end
function 怪物属性:玄奘的身世一酒肉和尚1(任务id,玩家id)
    local 战斗单位={}
    local 等级=25
    local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
    
    -- 酒肉和尚1（旧版山贼造型，高血量物理怪）
    战斗单位[1]={
        名称=剧情名称.名称,
        模型="雨师",        
        伤害=等级*16,       -- 物理伤害约400
        气血=等级*150,      -- 约3750血
        法伤=等级*14,       -- 混合伤害设定
        速度=等级*3,
        防御=等级*2,
        法防=等级*1.5,
        等级=等级,
        技能={"魔之心","神佑复生"},  -- 通用技能
        主动技能={"推气过宫","唧唧歪歪","金刚护体"}  -- 化生寺技能组合
    }
    
    -- 赌徒喽啰（2015版设定）
    for i=2,5 do
        战斗单位[i]={
            名称="赌徒喽啰",
            模型="赌徒",
            伤害=等级*12,     -- 物理系小怪
            气血=等级*80,     -- 约2000血
            法伤=0,
            速度=等级*2,
            防御=等级*1,
            法防=0,
            等级=等级,
            技能={},
            主动技能={}  -- 无特殊技能
        }
    end
    return 战斗单位
end
function 胜利MOB_110009(胜利id,战斗数据)
    local 数字id = 战斗数据.进入战斗玩家id
    local id组={数字id}
    if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
        for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
            if v~=数字id then
                if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
                    id组[#id组+1]=v
                end
            end
        end
    end
    for k,v in pairs(id组) do
        玩家数据[v].角色:添加经验(math.floor(50000),"主线剧情")  -- 经验下调至旧版标准
        玩家数据[v].角色:增加剧情点(1)
        玩家数据[v].角色.剧情={主线=3,编号 = 2,地图 = 1141,进度 = 15,附加={}}
        local wb =  {"雨师","酒肉和尚","大侠放过我啊，这解药九转还魂丹只有神仙才会炼制，我也没有啊……"}  -- 模型名称对应调整
        local xx = {}
        local wb2 = {}
        发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
    end
end
function 怪物属性:玄奘的身世一幽冥鬼(任务id,玩家id)
	local 战斗单位={}
	local 等级=40
	local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	
	-- 幽冥鬼（2016版巡游天神造型）
	战斗单位[1]={
		名称=剧情名称.名称,
		模型="巡游天神", 
		伤害=等级*10,      -- 物理伤害约400（40*10）
		气血=等级*200,     -- 血量约8000（40*200）
		法伤=0,           -- 非法系怪
		速度=等级*3.5,
		防御=等级*3,
		法防=等级*1.2,
		等级=等级,
		技能={"高级夜战","驱鬼"},  -- 经典物理技能
		主动技能={"阎罗令","尸腐毒"}  -- 地府基础技能
	}
	
	-- 喽啰配置（4个野鬼+1个僵尸）
	战斗单位[2]={名称="幽冥喽啰",模型="野鬼",伤害=等级*8,气血=等级*150,法伤=0,速度=等级*3,防御=等级*2,法防=0,等级=等级,技能={"夜战"},主动技能={"阎罗令"}}
	战斗单位[3]={名称="幽冥喽啰",模型="野鬼",伤害=等级*8,气血=等级*150,法伤=0,速度=等级*3,防御=等级*2,法防=0,等级=等级,技能={"夜战"},主动技能={"尸腐毒"}}
	战斗单位[4]={名称="幽冥喽啰",模型="野鬼",伤害=等级*8,气血=等级*150,法伤=0,速度=等级*3,防御=等级*2,法防=0,等级=等级,技能={"夜战"},主动技能={"判官令"}}
	战斗单位[5]={名称="幽冥护卫",模型="僵尸",伤害=等级*9,气血=等级*180,法伤=0,速度=等级*2.8,防御=等级*2.5,法防=0,等级=等级,技能={"高级防御"},主动技能={"普通攻击"}}
	return 战斗单位
end
function 胜利MOB_110010(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(60000),"主线剧情")  -- 经验下调至2016标准
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v,"地狱灵芝",15)  -- 数量减半
		玩家数据[v].角色.剧情={主线=3,编号 = 1,地图 = 1167,进度 = 18,附加={}}
		local wb =  {"巡游天神","幽冥鬼","呜,我只是在等我的文秀,你们为什么都要说我死了……我本是江州尾生,和隔壁的文秀姑娘青梅竹马,可是她父母看不起我的出身,不同意我们的婚事。情急之下我俩决意私奔,可是我在约定的地方等她却怎么也等不来…呜呜,文秀,你为什么负我……"}
		local xx = {}
		local wb2 = {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"原来这傻鬼还不知道自己已经死了,也罢,去江州找他的文秀姑娘问问来龙去脉吧。",{},nil}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:玄奘的身世一衙门守卫(任务id,玩家id)
	local 战斗单位={}
	local 等级=41
	local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	
	-- 主怪（衙役造型，高防御型）
	战斗单位[1]={
		名称=剧情名称.名称,
		模型="护卫",        -- 2016年新造型
		伤害=等级*9,       -- 物理伤害约369（41*9）
		气血=等级*220,      -- 血量约9020（41*220）
		法伤=0,
		速度=等级*3.2,
		防御=等级*4.5,     -- 高防御设定
		法防=等级*1.8,
		等级=等级,
		技能={"高级防御","反震"},  -- 防御型技能
		主动技能={"普通攻击"}      -- 无特殊技能
	}
	
	-- 喽啰配置（4个护卫）
	for i=2,5 do
		战斗单位[i]={
			名称="衙役护卫",
			模型="护卫",
			伤害=等级*10,     -- 物理伤害约410
			气血=等级*150,    -- 血量约6150
			法伤=0,
			速度=等级*2.8,
			防御=等级*3,
			法防=等级*1.2,
			等级=等级,
			技能={"防御"},
			主动技能={"普通攻击"}  -- 无特殊技能
		}
	end
	return 战斗单位
end
function 胜利MOB_110011(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(75000),"主线剧情")  -- 经验下调至2016标准
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予任务道具(v,"官府公文")  -- 给予任务道具
		玩家数据[v].角色.剧情={主线=3,编号 = 1,地图 = 1049,进度 = 39,附加={}}  -- 战斗胜利直接跳转到找殷丞相
		local wb =  {"护卫","衙门守卫","好汉饶命！我这就带你去见殷丞相！"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:玄奘的身世一虾兵(任务id,玩家id)
	local 战斗单位={}
	local 等级=42
	local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	
	-- 虾兵（龙宫物理系设定）
	战斗单位[1]={
		名称=剧情名称.名称,
		模型="虾兵",
		伤害=等级*13,      -- 微调物理伤害（原15→13）
		气血=等级*180,     -- 血量约7560
		法伤=等级*12,       -- 增加法伤属性（约504法伤）
		速度=等级*3.5,
		防御=等级*3,
		法防=等级*1.5,
		等级=等级,
		技能={"必杀","水属性吸收"},  -- 龙宫特色技能
		主动技能={"破浪诀","龙卷雨击"},  -- 修正技能组合
	}
	
	-- 蛤蟆精喽啰（法术型小怪）
	for i=2,5 do
		战斗单位[i]={
			名称="蛤蟆精喽啰",
			模型="蛤蟆精",
			伤害=等级*5,     -- 保留少量物理伤害
			气血=等级*120,   -- 血量约5040
			法伤=等级*10,    -- 法伤约420
			速度=等级*2.8,
			防御=等级*2,
			法防=等级*3,
			等级=等级,
			技能={"魔之心"},
			主动技能={"水攻"}  -- 单法技能
		}
	end
	return 战斗单位
end
function 胜利MOB_110012(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(65000),"主线剧情")  -- 经验下调至2016标准
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v,"定颜珠",1)  -- 明确掉落数量
		玩家数据[v].角色.剧情={主线=3,编号 = 29,地图 = 1110,进度 = 25,附加={}}
		local wb =  {"虾兵","虾兵","哎呦，我的腿一定折了，英雄，这颗定岩珠归你了，不要再打了……"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:玄奘的身世一山神(任务id,玩家id)
	local 战斗单位={}
	local 等级=43
	local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	
	-- 主怪山神（官服设定为天兵造型，高物抗物理系）
	战斗单位[1]={
		名称=剧情名称.名称,
		模型="雨师",        -- 2016年后改为天兵造型
		伤害=等级*15,      -- 物理伤害约645（43*15）
		气血=等级*220,     -- 血量约9460（43*220）
		法伤=0,           -- 非法系怪
		速度=等级*3.5,
		防御=等级*5,      -- 高物抗设定
		法防=等级*2,
		等级=等级,
		技能={"高级必杀","偷袭","神迹" },  -- 物理系技能
		主动技能={"破血狂攻"},    -- 官服特技

	}
	
	-- 喽啰配置（2僵尸+2骷髅怪，官服经典组合）
	战斗单位[2]={
		名称="僵尸喽啰",
		模型="僵尸",
		伤害=等级*12,     -- 物理伤害约516
		气血=等级*120,    -- 血量约5160
		法伤=0,
		速度=等级*2.5,
		防御=等级*3,
		法防=0,
		等级=等级,
		技能={"连击"},
		主动技能={}
	}
	战斗单位[3]={
		名称="僵尸喽啰",
		模型="僵尸",
		伤害=等级*12,
		气血=等级*120,
		法伤=0,
		速度=等级*2.5,
		防御=等级*3,
		法防=0,
		等级=等级,
		技能={"吸血"},
		主动技能={}
	}
	战斗单位[4]={
		名称="骷髅怪喽啰",
		模型="骷髅怪",
		伤害=等级*13,     -- 稍高伤害
		气血=等级*100,    -- 较低血量
		法伤=0,
		速度=等级*3,
		防御=等级*2,
		法防=0,
		等级=等级,
		技能={"必杀"},
		主动技能={}
	}
	战斗单位[5]={
		名称="骷髅怪喽啰",
		模型="骷髅怪",
		伤害=等级*13,
		气血=等级*100,
		法伤=0,
		速度=等级*3,
		防御=等级*2,
		法防=0,
		等级=等级,
		技能={"驱鬼"},
		主动技能={}
	}
	return 战斗单位
end
function 胜利MOB_110013(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(80000),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予任务道具(v,"辟水宝珠")
		玩家数据[v].角色.剧情={主线=3,编号 = 2,地图 = 1116,进度 = 29,附加={战斗 = 1}}
		local wb =  {"天兵","山神","强盗，把辟水宝珠还给我。"}
		local xx = {}
		local wb2 = {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"我先走了，馋嘴神仙你自己慢慢玩吧！",{},nil}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:玄奘的身世一蟹将军(任务id,玩家id)
	local 战斗单位={}
	local 等级=44
	local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	
	-- 主怪蟹将军（官服设定为高级龙宫怪）
	战斗单位[1]={
		名称=剧情名称.名称,
		模型="蟹将",
		伤害=等级*10,      -- 物理伤害较低
		气血=等级*250,     -- 血量约11000（44*250）
		法伤=等级*18,      -- 法伤约792
		速度=等级*3.2,
		防御=等级*3,
		法防=等级*4,      -- 高法抗
		等级=等级,
		技能={"水属性吸收","法术连击","法术暴击"},
		主动技能={"龙卷雨击","龙腾"},  -- 龙宫双技能
	}
	
	-- 喽啰配置（2虾兵+2蛤蟆精）
	战斗单位[2]={
		名称="虾兵喽啰",
		模型="虾兵",
		伤害=等级*14,     -- 物理系
		气血=等级*120,
		法伤=0,
		速度=等级*2.8,
		防御=等级*3,
		法防=0,
		等级=等级,
		技能={"连击"},
		主动技能={}
	}
	战斗单位[3]={
		名称="虾兵喽啰",
		模型="虾兵",
		伤害=等级*14,
		气血=等级*120,
		法伤=0,
		速度=等级*2.8,
		防御=等级*3,
		法防=0,
		等级=等级,
		技能={"必杀"},
		主动技能={}
	}
	战斗单位[4]={
		名称="蛤蟆精喽啰",
		模型="蛤蟆精",
		伤害=等级*8,
		气血=等级*100,
		法伤=等级*12,    -- 法系小怪
		速度=等级*2.5,
		防御=等级*2,
		法防=等级*3,
		等级=等级,
		技能={"魔之心"},
		主动技能={"水攻"}
	}
	战斗单位[5]={
		名称="蛤蟆精喽啰",
		模型="蛤蟆精",
		伤害=等级*8,
		气血=等级*100,
		法伤=等级*12,
		速度=等级*2.5,
		防御=等级*2,
		法防=等级*3,
		等级=等级,
		技能={"法术暴击"},
		主动技能={"水攻"}
	}
	return 战斗单位
end
function 胜利MOB_110014(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(100000),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=3,编号 = 5,地图 = 1116,进度 = 30,附加={物品="定颜珠"}}
		local wb =  {"蟹将","蟹将军","英雄你好厉害啊，千万不要伤害我家大王……"}
		local xx = {}
		local wb2 = {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"既然鲤鱼是你们龙王变的，我保证不伤害她就是。还有我得到了一颗定颜珠，听说是龙王用来保某人的尸身不坏的，特来还回。",{},
		{"蟹将","蟹将军","啊，你怎么不早说，快快请进，我们龟丞相正为丢了这宝物着急呢",{},
		{玩家数据[v].角色.模型,玩家数据[v].角色.名称,"我是想要说啊，你哪里给我机会……-——-！",{},
		nil}}}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:玄奘的身世一刘洪(任务id,玩家id)
    local 战斗单位={}
    local 等级=50
    local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
    
    -- 刘洪（官服设定为高攻物理系，造型改为强盗）
    战斗单位[1]={
        名称=剧情名称.名称,
        模型="强盗",        -- 改为强盗造型
        伤害=等级*20,      -- 物理伤害约1000（50*20）
        气血=等级*200,     -- 血量约10000
        法伤=0,
        速度=等级*4,
        防御=等级*4,
        法防=等级*2,
        等级=等级,
        技能={"高级连击","吸血","高级反震"},  -- 物理系技能组合
        主动技能={"破血狂攻","横扫千军"}      -- 物理特技
    }
    
    -- 喽啰配置（4个衙役护卫）
    for i=2,5 do
        战斗单位[i]={
            名称="衙役护卫",
            模型="护卫",
            伤害=等级*12,     -- 物理伤害约600
            气血=等级*120,    -- 血量约6000
            法伤=0,
            速度=等级*3,
            防御=等级*3,
            法防=0,
            等级=等级,
            技能={"连击","必杀"},
            主动技能={}
        }
    end
    return 战斗单位
end
function 胜利MOB_110015(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(100000),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v,"绿靴")
		玩家数据[v].角色.剧情={主线=3,编号 = 16,地图 = 1173,进度 = 47,附加={战斗=1}}
		local wb =  {"强盗","刘洪","我终于败了，李彪你个吃里爬外的家伙，你也不得好死！"}
		local xx = {}
		local wb2 = {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"快说，刘洪去哪了！",{},
		{"强盗","刘洪","今天一个叫李彪的人来找老爷，然后两个人就一起急匆匆的走了，说事去什么狮驼岭附近避避风头，还带走了江州大印……",{},
		{玩家数据[v].角色.模型,玩家数据[v].角色.名称,"哼，被刘洪这奸贼骗了！",{},
		nil}}}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:玄奘的身世一李彪(任务id,玩家id)
    local 战斗单位={}
    local 等级=46
    local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
    
    -- 李彪（官服设定为高攻山贼造型）
    战斗单位[1]={
        名称="李彪",
        模型="骷髅怪",  -- 早期为骷髅怪造型
        伤害=等级*18,      -- 物理伤害约828（46*18）
        气血=等级*180,     -- 血量约8280
        法伤=0,
        速度=等级*4.2,
        防御=等级*4,
        法防=等级*1.8,
        等级=等级,
        技能={"高级连击","吸血","神迹"},  -- 物理系技能组合
        主动技能={"破血狂攻"}            -- 物理特技
    }
    
    -- 喽啰配置（4个赌徒）
    for i=2,5 do
        战斗单位[i]={
            名称="赌徒喽啰",
            模型="强盗",  -- 早期喽啰用强盗造型
            伤害=等级*14,     -- 物理伤害约644
            气血=等级*100,    -- 血量约4600
            法伤=0,
            速度=等级*3.5,
            防御=等级*2.5,
            法防=0,
            等级=等级,
            技能={"必杀","偷袭"},
            主动技能={}
        }
    end
    return 战斗单位
end
function 胜利MOB_110016(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(100000),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=3,编号 = 17,地图 = 1173,进度 = 48,附加={战斗=1}}
		local wb =  {"山贼","李彪","呜……呜……居然输了，肯定是刘洪那厮拖累了我！"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:玄奘的身世一刘洪1(任务id,玩家id)
    local 战斗单位={}
    local 等级=55
    local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
    
    -- 刘洪最终战（早期造型为强盗）
    战斗单位[1]={
        名称="刘洪",  -- 移除狂暴前缀
        模型="强盗",  -- 早期造型
        伤害=等级*22,
        气血=等级*300,
        法伤=0,
        速度=等级*4.5,
        防御=等级*5,
        法防=等级*3,
        等级=等级,
        技能={"高级必杀","高级吸血","神迹"},
        主动技能={"破血狂攻"}  -- 早期只有破血
    }
    
    -- 精英护卫（早期为护卫造型）
    for i=2,5 do
        战斗单位[i]={
            名称="精英护卫",
            模型="护卫",  -- 改为早期护卫造型
            伤害=等级*15,
            气血=等级*150,
            法伤=0,
            速度=等级*3.8,
            防御=等级*4,
            法防=等级*2,
            等级=等级,
            技能={"连击","必杀"},  -- 简化技能
            主动技能={}
        }
    end
    return 战斗单位
end
function 胜利MOB_110017(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(150000),"主线剧情")
		玩家数据[v].角色:增加剧情点(2)
		玩家数据[v].角色.剧情={主线=3,编号 = 17,地图 = 1173,进度 = 49,附加={}}
		local wb =  {"强盗","刘洪","不...这不可能！我怎么会输给一个和尚！"}  -- 同步模型和名称
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end