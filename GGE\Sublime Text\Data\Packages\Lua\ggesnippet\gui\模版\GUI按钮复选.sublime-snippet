<snippet>
	<content><![CDATA[
--======================================================================--
${1:按钮} = ${2:对象}:创建复选按钮("${1:名称}",0,0)
    function ${1:按钮}:初始化()
    	self:置正常纹理(${3:纹理})
    	self:置按下纹理(${4:纹理})
    	self:置选中正常纹理(${5:纹理})
    	self:置选中按下纹理(${6:纹理})
    end

    function ${1:按钮}:消息事件(消息,a,b)
    	if 消息 =="选中事件" then

    	end
    end
]]></content>
    <tabTrigger>guian_fx</tabTrigger>
    <scope>source.lua</scope>
    <description>复选按钮模版</description>
</snippet>
