<snippet>
	<content><![CDATA[
--======================================================================--
${1:网格} = ${2:对象}:创建网格('${1:网格}',0,0,300,300)
    function ${1:网格}:初始化()
        self:置格子宽高(35,30)
        self:置行列数(3,3)
        self:置行列间距(3,3)
        self:置编辑模式(true)
    end
    function ${1:网格}:子显示(i,x,y)

    end
    function ${1:网格}:消息事件(消息,b,c)

    end
]]></content>
    <tabTrigger>guiwg_</tabTrigger>
    <scope>source.lua</scope>
    <description>网格模版</description>
</snippet>
