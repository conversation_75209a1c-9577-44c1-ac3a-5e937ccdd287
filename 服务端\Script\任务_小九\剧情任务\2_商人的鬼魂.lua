
-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2024-05-12 22:27:43
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-02-16 17:14:01
function 怪物属性:沉船内室一商人的鬼魂(任务id,玩家id)
	local 战斗单位={}
	local 等级=5
	local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	战斗单位[1]={名称=剧情名称.名称,模型="野鬼",伤害=等级*45,气血=等级*300,法伤=等级*40,速度=等级*4,防御=等级*2,法防=等级*2,躲闪=等级*2,等级=等级,技能={"鬼魂术"},主动技能=取随机法术(5)}
	战斗单位[2]={名称="蟹将喽啰",模型="蟹将",伤害=等级*35,气血=等级*200,法伤=等级*30,速度=等级*4,防御=等级*2,法防=等级*2,躲闪=等级*2,等级=等级,技能={},主动技能=取随机法术(5)}
	战斗单位[3]={名称="虾兵喽啰",模型="虾兵",伤害=等级*35,气血=等级*200,法伤=等级*30,速度=等级*4,防御=等级*2,法防=等级*2,躲闪=等级*2,等级=等级,技能={},主动技能=取随机法术(5)}
	战斗单位[4]={名称="恶灵",模型="幽灵",伤害=等级*40,气血=等级*200,法伤=等级*35,速度=等级*4,防御=等级*2,法防=等级*2,躲闪=等级*2,等级=等级,技能={},主动技能=取随机法术(5)}
	战斗单位[5]={名称="恶鬼",模型="骷髅怪",伤害=等级*40,气血=等级*200,法伤=等级*35,速度=等级*4,防御=等级*2,法防=等级*2,躲闪=等级*2,等级=等级,技能={},主动技能=取随机法术(5)}
	return 战斗单位
end
function 怪物属性:商人鬼魂召唤(等级)
	local 召唤单位={
	名称="海浪",
	模型="泪妖",
	等级=等级,
	气血=800,
	伤害=等级*2,
	法伤=等级,
	速度=等级,
	防御=等级,
	法防=等级,
	躲闪=等级,
	主动技能=取小法(1),
	}
	return 召唤单位
end
function 怪物属性:沉船内室一妖风(任务id,玩家id)
	local 战斗单位={}
	local 等级=10
	local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	战斗单位[1]={名称=剧情名称.名称,模型="吸血鬼",伤害=等级*12,气血=等级*300,法伤=等级*8,速度=等级*4,防御=等级*4,法防=等级*2,躲闪=等级*2,等级=等级,技能={},主动技能=取随机法术(5)}
	return 战斗单位
end
function 胜利MOB_110002(胜利id,战斗数据)
	local 数字id=战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(459119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=2,编号=1,地图=1534,进度=12,附加={}}
		发送数据(玩家数据[v].连接id,227,{剧情=玩家数据[v].角色.剧情})
		成就数据[v]:判断进度(v,"梦幻剧情","商人的鬼魂")
	end
end