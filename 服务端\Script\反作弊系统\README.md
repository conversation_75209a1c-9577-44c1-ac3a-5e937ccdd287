# 跑商脚本检测系统

## 系统概述

这是一个专门针对跑商系统的脚本检测系统，主要在玩家完成跑商任务时进行验证，防止自动化脚本的使用。

## 核心特性

### 🎯 智能检测触发
- **频率检测**：连续跑商5次以上触发高概率验证
- **时间窗口**：1小时内跑商3次以上触发验证
- **概率机制**：普通玩家30%概率，高频玩家80%概率

### 🔐 多样化验证方式
1. **数字验证码**：4位随机数字验证码
2. **游戏知识问答**：跑商相关的选择题
3. **简单计算题**：基础数学运算

### ⚡ 实时处理
- 验证失败3次自动取消跑商任务
- 限制30分钟内无法重新接取跑商
- 管理员实时通知可疑行为

## 文件结构

```
服务端/Script/反作弊系统/
├── 跑商验证系统.lua          # 核心验证逻辑
├── 行为检测系统.lua          # 行为模式分析（预留）
├── 移动验证系统.lua          # 移动验证（预留）
├── 验证命令处理.lua          # 聊天命令处理
└── README.md                # 使用说明
```

## 使用方法

### 玩家操作
当系统提示需要验证时，玩家需要在聊天框输入：

```
验证 答案
```

例如：
- 验证码：`验证 1234`
- 选择题：`验证 2`（选择第2个选项）
- 计算题：`验证 42`

### 管理员命令

#### 重置玩家可疑度
```
@重置可疑度 玩家名称
```

#### 查看玩家统计
```
@查看统计 玩家名称
```

## 配置参数

### 验证触发条件
```lua
验证概率 = 0.3                    -- 30%概率触发验证
高频玩家验证概率 = 0.8             -- 高频跑商玩家80%概率验证
连续跑商阈值 = 5                   -- 连续跑商次数阈值
短时间跑商阈值 = 3                 -- 短时间内跑商次数阈值
短时间窗口 = 3600                  -- 短时间窗口（1小时）
```

### 验证设置
```lua
验证超时时间 = 120                 -- 验证超时时间（秒）
启用验证 = true                    -- 是否启用验证
```

## 日志记录

系统会自动记录以下日志：

### 验证日志
- 文件：`logs/trade_verification.log`
- 内容：验证通过/失败记录

### 管理员警报
- 文件：`logs/admin_alerts.log`
- 内容：可疑行为通知

## 集成说明

### 1. 跑商完成验证
系统已集成到 `道具处理类.lua` 的"给予银票"事件中，在玩家完成跑商时自动触发验证。

### 2. 聊天命令处理
系统已集成到 `聊天处理类.lua` 中，自动处理验证相关的聊天命令。

### 3. 定时器检查
系统已集成到 `定时器6.lua` 中，定期检查验证超时和清理过期数据。

## 防绕过机制

1. **验证状态锁定**：验证期间玩家无法进行其他操作
2. **超时处理**：120秒内未完成验证自动失败
3. **失败惩罚**：连续失败3次限制跑商30分钟
4. **随机题目**：每次验证随机选择不同类型题目

## 性能优化

- 使用概率触发，避免每次都验证
- 定期清理过期数据，防止内存泄漏
- 异步处理验证逻辑，不影响游戏性能

## 扩展功能（预留）

### 行为检测系统
- 移动模式分析
- 操作时间间隔检测
- 异常行为评分

### 移动验证系统
- 路径合理性检查
- 移动速度验证
- 瞬移检测

## 注意事项

1. **兼容性**：系统设计为非侵入式，不影响正常游戏流程
2. **误判处理**：管理员可以手动重置玩家状态
3. **用户体验**：验证题目简单易懂，不会给正常玩家造成困扰

## 故障排除

### 常见问题

**Q: 验证系统不工作？**
A: 检查 `验证配置.启用验证` 是否为 `true`

**Q: 玩家无法通过验证？**
A: 使用管理员命令 `@重置可疑度 玩家名称` 重置状态

**Q: 验证题目显示异常？**
A: 检查客户端UI系统是否正常工作

### 调试模式

可以临时修改验证概率为1.0来测试系统：
```lua
验证概率 = 1.0  -- 100%触发验证（仅用于测试）
```

## 更新日志

### v1.0.0 (2025-01-14)
- 初始版本发布
- 实现基础验证功能
- 集成到跑商完成流程
- 添加管理员命令支持
