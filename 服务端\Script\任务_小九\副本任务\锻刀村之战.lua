-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2025-05-09 20:45:09
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-06 17:16:54
-- @Author: Roo
-- @Date:   2025-05-08
-- @Description: 鬼灭之刃 - 锻刀村之战 副本任务

-- 定义副本阶段常量
STAGE = {
    ENTER_VILLAGE = 1,        -- 进入村庄阶段
    FIGHT_YUHUFISH = 2,       -- 战斗玉壶鱼怪阶段
    FIGHT_HANTENGU_CLONES = 3, -- 战斗半天狗分身阶段
    BOSS_YUHUBODY = 4,        -- 战斗玉壶本体阶段
    BOSS_ZOHAKUTEN = 5,       -- 战斗憎珀天阶段
    FIND_HANTENGU_BODY = 6,   -- 寻找半天狗本体阶段
    COMPLETE = 7              -- 完成阶段
}

-- 副本常量定义（统一放在文件顶部）
local FUBEN_ID = 1005 -- 副本任务类型ID
local FUBEN_NAME = "锻刀村之战"
local FUBEN_DURATION = 3600 -- 副本持续时间（秒），例如1小时
local MIN_LEVEL = 60 -- 最低等级要求
local MIN_PLAYERS = 1 -- 最少玩家数
local FUBEN_ENTRY_MAP = 1218 -- 副本入口地图ID (长安城)
local FUBEN_ENTRY_X = 13 -- 副本入口X坐标
local FUBEN_ENTRY_Y = 94 -- 副本入口Y坐标
local FUBEN_EXIT_MAP = 1001 -- 副本出口地图ID (长安城)
local FUBEN_EXIT_X = 13 -- 副本出口X坐标
local FUBEN_EXIT_Y = 94 -- 副本出口Y坐标
local FUBEN_COOLDOWN = 86400 -- 24小时冷却

-- 调试设置
local 调试模式 = false -- 设置为true开启调试模式，false关闭调试模式
local 调试阶段 = STAGE.FIND_HANTENGU_BODY -- 设置为最后一个战斗阶段（寻找半天狗本体）

-- 将副本类定义为全局变量，以便其他函数可以访问
副本_锻刀村之战 = class()

-- 全局函数，用于开启副本
function 开启锻刀村副本(id)
    -- 调用类方法实现
    return 副本_锻刀村之战:开启锻刀村副本(id)
end

-- 全局函数实现（保留但不再使用，由类方法替代）
function 开启锻刀村副本实现(id)
  -- 副本常量定义（在函数内部定义，确保可以访问）
  local FUBEN_ID = 1005 -- 副本任务类型ID
  local MIN_PLAYERS = 1 -- 最少玩家数
  local MIN_LEVEL = 100 -- 最低等级要求
  local FUBEN_DURATION = 3600 -- 副本持续时间（秒）
  local 随机序列 = os.time() % 1000 -- 生成随机序列

  -- 调试模式下跳过一些检查
  if not 调试模式 then
    if 玩家数据[id].队伍 == 0 or 玩家数据[id].队长 == false then
      常规提示(id, "#Y/该任务必须组队完成且由队长领取")
      return
    elseif 取队伍人数(id) < MIN_PLAYERS then
      常规提示(id, "#Y此副本要求队伍人数不低于" .. MIN_PLAYERS .. "人")
      return
    elseif 取等级要求(id, MIN_LEVEL) == false then
      常规提示(id, "#Y此副本要求角色等级不能低于" .. MIN_LEVEL .. "级")
      return
    elseif 副本_锻刀村之战:取锻刀村次数限制(id) then
      return
    elseif 取队员任务存在(玩家数据[id].队伍, FUBEN_ID) then
      常规提示(id, "#Y/队伍中已有队员正在进行该副本任务,无法领取新的副本")
      return
    end
  end

  local 任务id = id .. "_" .. FUBEN_ID .. "_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 999)
  if not 副本数据 then 副本数据 = {} end
  if not 副本数据.锻刀村之战 then 副本数据.锻刀村之战 = { 进行 = {}, 完成 = {} } end

  -- 根据调试模式决定初始阶段
  local 初始阶段 = STAGE.ENTER_VILLAGE
  -- 调试模式下使用调试阶段作为初始阶段
  if 调试模式 then
    初始阶段 = 调试阶段
  end
  副本数据.锻刀村之战.进行[id] = { 进程 = 初始阶段, 主任务id = 任务id }

  任务数据[任务id] = {
    id = 任务id,
    起始 = os.time(),
    结束 = FUBEN_DURATION,
    玩家id = {},
    队伍组 = {},
    副本id = id,
    类型 = FUBEN_ID,
    进程 = 初始阶段 -- 同步进程到任务数据
  }

  -- 初始化阶段数据
  副本数据.锻刀村之战.进行[id].阶段数据 = {}
  任务数据[任务id].阶段数据 = {}

  local 队伍id = 玩家数据[id].队伍
  if 队伍id and 队伍id > 0 and 队伍数据[队伍id] and 队伍数据[队伍id].成员数据 then
    for n = 1, #队伍数据[队伍id].成员数据 do
      local 临时id = 队伍数据[队伍id].成员数据[n]
      任务数据[任务id].队伍组[#任务数据[任务id].队伍组 + 1] = 临时id
      玩家数据[临时id].角色:添加任务(任务id)
      常规提示(临时id, "#Y你开启了锻刀村之战副本")

      -- 将玩家传送到副本地图
      地图处理类:跳转地图(临时id, 6100, 7, 155) -- 使用预设的坐标

      -- 设置黑夜状态
      发送数据(玩家数据[临时id].连接id, 6560, true)

      -- 发送红色闪烁效果
      --发送数据(玩家数据[临时id].连接id, 6561, true)
    end
  else
    -- 单人模式（调试用）
    任务数据[任务id].队伍组[1] = id
    玩家数据[id].角色:添加任务(任务id)
    常规提示(id, "#Y你开启了锻刀村之战副本（调试模式）")
    地图处理类:跳转地图(id, 6100, 13, 138)

    -- 设置黑夜状态
    发送数据(玩家数据[id].连接id, 6560, true)

    --发送数据(玩家数据[id].连接id, 6561, true)
  end

  -- 设置副本单位
  设置锻刀村副本(id)

  -- 添加锻刀村村长NPC（如果是第一阶段或者调试模式下需要村长）
  if 初始阶段 == STAGE.ENTER_VILLAGE then
    副本_锻刀村之战:添加村长NPC(id)
  end

  -- 显示副本开启信息
  if 调试模式 and 初始阶段 ~= STAGE.ENTER_VILLAGE then
    常规提示(id, string.format("#G调试模式：副本已开启，直接进入阶段：%d", 初始阶段))
  else
    常规提示(id, string.format("#G副本已开启，当前副本阶段：%d", 初始阶段))
  end
end

-- 更新副本进度并发送红色闪烁效果
function 更新副本进度(副本id, 新进度)
    -- 简化检查，直接获取副本实例
    local 副本实例 = 副本数据 and 副本数据.锻刀村之战 and 副本数据.锻刀村之战.进行 and 副本数据.锻刀村之战.进行[副本id]
    if not 副本实例 then
        return false
    end

    -- 获取旧进度和主任务ID
    local 旧进度 = 副本实例.进程
    local 主任务id = 副本实例.主任务id
    local 任务实例 = 主任务id and 任务数据[主任务id]

    -- 如果主任务不存在，无法更新
    if not 任务实例 then
        return false
    end

    -- 更新副本实例进度
    副本实例.进程 = 新进度

    -- 同步更新任务数据中的进程
    任务实例.进程 = 新进度

    -- 定义阶段名称映射表，使进度显示更加明确
    local 阶段名称 = {
        [STAGE.ENTER_VILLAGE] = "进入锻刀村",
        [STAGE.FIGHT_YUHUFISH] = "击退玉壶召唤物",
        [STAGE.FIGHT_HANTENGU_CLONES] = "击败半天狗分身",
        [STAGE.BOSS_YUHUBODY] = "挑战玉壶本体",
        [STAGE.BOSS_ZOHAKUTEN] = "挑战憎珀天",
        [STAGE.FIND_HANTENGU_BODY] = "寻找半天狗本体",
        [STAGE.COMPLETE] = "副本完成"
    }

    -- 计算总进度百分比
    local 总阶段数 = 7 -- STAGE.COMPLETE的值
    local 进度百分比 = math.floor((新进度 / 总阶段数) * 100)

    -- 如果进度发生变化，发送红色闪烁效果并刷新任务追踪
    if 旧进度 ~= 新进度 then
        -- 获取队伍组
        local 队伍组 = 任务实例.队伍组
        if 队伍组 then
            -- 向所有参与的玩家发送红色闪烁效果并刷新任务追踪
            for _, player_id in ipairs(队伍组) do
                -- 只在非最终阶段发送红色闪烁效果
                if 新进度 ~= STAGE.COMPLETE then
                    -- 发送红色闪烁指令（只发送一次，客户端会处理闪烁3次的效果）
                    发送数据(玩家数据[player_id].连接id, 6561, true)
                end

                -- 刷新任务追踪
                玩家数据[player_id].角色:刷新任务跟踪()

                -- 发送详细的进度变化提示
                常规提示(player_id, string.format("#G副本进度更新：#Y%s #G(第%d/%d阶段 - 完成度%d%%)",
                    阶段名称[新进度] or "未知阶段",
                    新进度,
                    总阶段数,
                    进度百分比))
            end
        end

        -- 根据新进度设置副本单位
        设置锻刀村副本(副本id)

        -- 根据新进度设置障碍
        --副本_锻刀村之战:设置副本障碍(副本id, 新进度)

        return true
    end

    return false
end

-- 初始化函数
function 副本_锻刀村之战:初始化()
  -- 初始化副本数据结构
  if not 副本数据 then 副本数据 = {} end
  if not 副本数据.锻刀村之战 then
    副本数据.锻刀村之战 = {
      进行 = {}, -- 存储进行中的副本
      完成 = {},  -- 存储已完成副本的玩家ID和完成时间
      上次重置时间 = os.time() -- 添加重置时间记录
    }
  end

  -- 检查是否需要重置
  self:检查重置()

  -- 添加地图名称映射
  if not mapdmc[6100] then
    mapdmc[6100] = "锻刀村"
  end

  -- 在初始化时创建副本入口NPC
  self:创建入口NPC()

  -- 注册胜利处理函数到全局表
  胜利MOB_160004 = function(胜利id, 战斗数据, id组)
      self:处理胜利_160004(胜利id, 战斗数据, id组)
  end

  胜利MOB_160005 = function(胜利id, 战斗数据, id组)
      self:处理胜利_160005(胜利id, 战斗数据, id组)
  end

  胜利MOB_160006 = function(胜利id, 战斗数据, id组)
      self:处理胜利_160006(胜利id, 战斗数据, id组)
  end

  胜利MOB_160007 = function(胜利id, 战斗数据, id组)
      self:处理胜利_160007(胜利id, 战斗数据, id组)
  end

  -- 注册其他胜利处理函数
  胜利160000 = function(id组, 战斗类型, 任务id)
      self:处理胜利_160000(id组, 战斗类型, 任务id)
  end

  胜利160001 = function(id组, 战斗类型, 任务id)
      self:处理胜利_160001(id组, 战斗类型, 任务id)
  end

  胜利160002 = function(id组, 战斗类型, 任务id)
      self:处理胜利_160002(id组, 战斗类型, 任务id)
  end

  胜利160003 = function(id组, 战斗类型, 任务id)
      self:处理胜利_160003(id组, 战斗类型, 任务id)
  end
end

function 副本_锻刀村之战:取锻刀村次数限制(id, 次数)
  if 副本数据 and 副本数据.锻刀村之战 and 副本数据.锻刀村之战.完成 and 副本数据.锻刀村之战.完成[id] then
    添加最后对话(id, "你今日已经完成过该副本了")
    return true
  elseif 玩家数据[id].队伍 ~= 0 then
    for i = 1, #队伍数据[玩家数据[id].队伍].成员数据 do
      local lsid = 队伍数据[玩家数据[id].队伍].成员数据[i]
      if 副本数据 and 副本数据.锻刀村之战 and 副本数据.锻刀村之战.完成 and 副本数据.锻刀村之战.完成[lsid] then
        添加最后对话(id, 玩家数据[lsid].角色.名称 .. "今日已经完成过该副本了")
        return true
      end
    end
  end
  return false
end

function 副本_锻刀村之战:开启锻刀村副本(id)
  -- 调试模式已在文件顶部设置
  -- 调试模式下跳过一些检查
  if not 调试模式 then
    if 玩家数据[id].队伍 == 0 or 玩家数据[id].队长 == false then
      常规提示(id, "#Y/该任务必须组队完成且由队长领取")
      return
    elseif 取队伍人数(id) < MIN_PLAYERS then
      常规提示(id, "#Y此副本要求队伍人数不低于" .. MIN_PLAYERS .. "人")
      return
    elseif 取等级要求(id, MIN_LEVEL) == false then
      常规提示(id, "#Y此副本要求角色等级不能低于" .. MIN_LEVEL .. "级")
      return
    elseif self:取锻刀村次数限制(id) then
      return
    elseif 取队员任务存在(玩家数据[id].队伍, FUBEN_ID) then
      常规提示(id, "#Y/队伍中已有队员正在进行该副本任务,无法领取新的副本")
      return
    end
  end

  local 任务id = id .. "_" .. FUBEN_ID .. "_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 999)
  if not 副本数据 then 副本数据 = {} end
  if not 副本数据.锻刀村之战 then 副本数据.锻刀村之战 = { 进行 = {}, 完成 = {} } end

  -- 根据调试模式决定初始阶段
  local 初始阶段 = STAGE.ENTER_VILLAGE
  -- 调试模式下使用调试阶段作为初始阶段
  if 调试模式 then
    初始阶段 = 调试阶段
  end
  副本数据.锻刀村之战.进行[id] = { 进程 = 初始阶段, 主任务id = 任务id }

  任务数据[任务id] = {
    id = 任务id,
    起始 = os.time(),
    结束 = FUBEN_DURATION,
    玩家id = {},
    队伍组 = {},
    副本id = id,
    类型 = FUBEN_ID,
    进程 = 初始阶段 -- 同步进程到任务数据
  }

  -- 初始化阶段数据
  副本数据.锻刀村之战.进行[id].阶段数据 = {}
  任务数据[任务id].阶段数据 = {}

  local 队伍id = 玩家数据[id].队伍
  if 队伍id and 队伍id > 0 and 队伍数据[队伍id] and 队伍数据[队伍id].成员数据 then
    for n = 1, #队伍数据[队伍id].成员数据 do
      local 临时id = 队伍数据[队伍id].成员数据[n]
      任务数据[任务id].队伍组[#任务数据[任务id].队伍组 + 1] = 临时id
      玩家数据[临时id].角色:添加任务(任务id)
      常规提示(临时id, "#Y你开启了锻刀村之战副本")

      -- 将玩家传送到副本地图
      地图处理类:跳转地图(临时id, 6100, 9, 158) -- 使用预设的坐标

      -- 设置黑夜状态
      发送数据(玩家数据[临时id].连接id, 6560, true)

      -- 发送红色闪烁效果（限制为3次）
      发送数据(玩家数据[临时id].连接id, 6561, true)
    end
  else
    -- 单人模式（调试用）
    任务数据[任务id].队伍组[1] = id
    玩家数据[id].角色:添加任务(任务id)
    常规提示(id, "#Y你开启了锻刀村之战副本（调试模式）")
    地图处理类:跳转地图(id, 6100, 9, 158)

    -- 设置黑夜状态
    发送数据(玩家数据[id].连接id, 6560, true)

    -- 发送红色闪烁效果（限制为3次）
    发送数据(玩家数据[id].连接id, 6561, true)
  end

  -- 设置副本单位
  设置锻刀村副本(id)

  -- 设置副本障碍（进入副本时封锁某些区域）
 -- self:设置副本障碍(id, 初始阶段)

  -- 添加锻刀村村长NPC（如果是第一阶段或者调试模式下需要村长）
  if 初始阶段 == STAGE.ENTER_VILLAGE then
    self:添加村长NPC(id)
  end

  -- 显示副本开启信息
  if 调试模式 and 初始阶段 ~= STAGE.ENTER_VILLAGE then
    常规提示(id, string.format("#G调试模式：副本已开启，直接进入阶段：%d", 初始阶段))
  else
    常规提示(id, string.format("#G副本已开启，当前副本阶段：%d", 初始阶段))
  end
end

-- 设置副本障碍函数
function 副本_锻刀村之战:设置副本障碍(副本ID, 阶段)
  local 游戏障碍集成 = require("Script/工具/游戏障碍集成")

  if 阶段 == STAGE.ENTER_VILLAGE then
    -- 进入村庄阶段：封锁村庄入口
    游戏障碍集成.设置副本障碍方案(副本ID, 6, 6100) -- 使用锻刀村村庄入口封锁方案

  elseif 阶段 == STAGE.FIGHT_YUHUFISH then
    -- 战斗玉壶鱼怪阶段：设置战斗区域边界
    游戏障碍集成.设置副本障碍方案(副本ID, 7, 6100) -- 使用锻刀村战斗区域限制方案

  elseif 阶段 == STAGE.FIGHT_HANTENGU_CLONES then
    -- 战斗半天狗分身阶段：保持战斗区域限制
    游戏障碍集成.设置副本障碍方案(副本ID, 7, 6100) -- 继续使用战斗区域限制

  elseif 阶段 == STAGE.BOSS_YUHUBODY then
    -- 玉壶本体BOSS战：封锁BOSS房间
    游戏障碍集成.设置副本障碍方案(副本ID, 8, 6100) -- 使用BOSS房间封锁方案

  elseif 阶段 == STAGE.BOSS_ZOHAKUTEN then
    -- 憎珀天BOSS战：清除障碍，开放全地图
    游戏障碍集成.清除副本障碍(副本ID, 6100)

  elseif 阶段 == STAGE.FIND_HANTENGU_BODY then
    -- 寻找半天狗本体阶段：设置迷宫式障碍
    游戏障碍集成.设置副本障碍方案(副本ID, 9, 6100) -- 使用锻刀村迷宫路径方案

  elseif 阶段 == STAGE.COMPLETE then
    -- 完成阶段：不在这里清除障碍，由副本清理函数统一处理
    -- 游戏障碍集成.清除副本障碍(副本ID, 6100)
  end
end

-- 清除副本障碍函数（副本结束时调用）
function 副本_锻刀村之战:清除副本障碍(副本ID)
  local 游戏障碍集成 = require("Script/工具/游戏障碍集成")
  游戏障碍集成.清除副本障碍(副本ID, 6100)
end

-- 添加村长NPC函数
function 副本_锻刀村之战:添加村长NPC(id)
  if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[id] then return end
  local 任务id = 副本数据.锻刀村之战.进行[id].主任务id
  if 任务数据[任务id] == nil then return end

  -- 在副本地图中心位置添加村长NPC
  local 村长任务id = id .. "_村长_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 999)
  任务数据[村长任务id] = {
    id = 村长任务id,
    起始 = os.time(),
    结束 = FUBEN_DURATION,
    玩家id = id,
    队伍组 = {},
    名称 = "锻刀村村长",
    模型 = "村长",
    x = 24,  -- 地图中心位置
    y = 134,
    方向 = 4,  -- 朝向
    副本id = id,
    小地图名称颜色 = 3,  -- 黄色名称
    地图编号 = 6100,
    地图名称 = 取地图名称(6100),
    类型 = FUBEN_ID + 5  -- 使用副本NPC类型
  }
  地图处理类:添加单位(村长任务id)


end

function 设置锻刀村副本(id)
  local 地图 = 6100
  if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 then return end
  local 副本实例 = 副本数据.锻刀村之战.进行[id]
  if 副本实例 == nil then return end

  local 进程 = 副本实例.进程

  -- 清理当前地图上的所有副本单位 (除了入口NPC)
  for unit_id, unit_data in pairs(任务数据) do
      if unit_data.副本id == id and unit_data.类型 ~= FUBEN_ID then -- 不删除入口NPC
          if unit_data.地图编号 and unit_data.单位编号 then
              地图处理类:删除单位(unit_data.地图编号, unit_data.单位编号)
          end
          任务数据[unit_id] = nil
      end
  end

  if 进程 == STAGE.ENTER_VILLAGE then
    -- 在ENTER_VILLAGE阶段，只添加村长NPC (已经在开启副本时添加，这里不需要重复添加)
    -- 副本数据.锻刀村之战.进行[id].三大力士 = { 锄树力士 = 1, 浇水力士 = 1, 修桃力士 = 1 } -- 这个数据结构可能需要根据实际用途调整位置或移除
  elseif 进程 == STAGE.FIGHT_YUHUFISH then
    -- 添加第一波鱼怪 (数量在NPC对话选项处理中设置)
    local 怪物数量 = 副本实例.阶段数据.鱼怪总数 or 10
    for i = 1, 怪物数量 do
      local 怪物任务id, ZU = 取唯一任务(FUBEN_ID.."_fish_"..os.time().."_"..i, id)
      local xy = 地图处理类.地图坐标[地图]:取随机点()
      任务数据[怪物任务id] = {
        id = 怪物任务id,
        起始 = os.time(),
        结束 = FUBEN_DURATION,
        玩家id = id,
        队伍组 = {},
        名称 = "玉壶召唤物",
        模型 = "树怪", -- 使用实际模型
        事件 = "攻击", -- 修改为"攻击"以匹配七绝山的模式
        小地图名称颜色 = 3,
        x = xy.x,
        y = xy.y,
        方向 = 取随机数(0, 7),
        副本id = id,
        地图编号 = 地图,
        地图名称 = 取地图名称(地图),
        类型 = FUBEN_ID + 10, -- 鱼怪类型
        战斗关键字 = "锻刀村玉壶鱼怪"
      }
      地图处理类:添加单位(怪物任务id)
    end
    常规提示(id, string.format("#R发现 %d 只玉壶召唤物！", 怪物数量))

  elseif 进程 == STAGE.FIGHT_HANTENGU_CLONES then
    -- 添加半天狗分身
    local 分身列表 = {
        {名称 = "可畏", 模型 = "树怪", 战斗关键字 = "锻刀村半天狗可畏", 类型 = FUBEN_ID + 21},
        {名称 = "积怒", 模型 = "树怪", 战斗关键字 = "锻刀村半天狗积怒", 类型 = FUBEN_ID + 22},
        {名称 = "空喜", 模型 = "树怪", 战斗关键字 = "锻刀村半天狗空喜", 类型 = FUBEN_ID + 23},
        {名称 = "哀绝", 模型 = "树怪", 战斗关键字 = "锻刀村半天狗哀绝", 类型 = FUBEN_ID + 24}
    }
    for _, 分身 in ipairs(分身列表) do
        local 怪物任务id, ZU = 取唯一任务(FUBEN_ID.."_clone_"..分身.名称, id)
        local xy = 地图处理类.地图坐标[地图]:取随机点()
        任务数据[怪物任务id] = {
            id = 怪物任务id,
            起始 = os.time(),
            结束 = FUBEN_DURATION,
            玩家id = id,
            队伍组 = {},
            名称 = 分身.名称,
            模型 = 分身.模型,
            事件 = "攻击",
            小地图名称颜色 = 3,
            x = xy.x,
            y = xy.y,
            方向 = 取随机数(0, 7),
            副本id = id,
            地图编号 = 地图,
            地图名称 = 取地图名称(地图),
            类型 = 分身.类型, -- 使用具体的子类型
            战斗关键字 = 分身.战斗关键字
        }
        地图处理类:添加单位(怪物任务id)
    end
    常规提示(id, string.format("#R半天狗的四个分身 (%s, %s, %s, %s) 出现在村中！", 分身列表[1].名称, 分身列表[2].名称, 分身列表[3].名称, 分身列表[4].名称))

  elseif 进程 == STAGE.BOSS_YUHUBODY then
    -- 添加玉壶本体Boss
    local 怪物任务id, ZU = 取唯一任务(FUBEN_ID.."_boss_yuhu", id)
    local xy = 地图处理类.地图坐标[地图]:取随机点() -- 或者指定一个Boss刷新点
    任务数据[怪物任务id] = {
        id = 怪物任务id,
        起始 = os.time(),
        结束 = FUBEN_DURATION,
        玩家id = id,
        队伍组 = {},
        名称 = "玉壶本体",
        模型 = "树怪", -- 使用实际模型
        事件 = "攻击",
        小地图名称颜色 = 3,
        x = xy.x,
        y = xy.y,
        方向 = 取随机数(0, 7),
        副本id = id,
        地图编号 = 地图,
        地图名称 = 取地图名称(地图),
        类型 = FUBEN_ID + 30, -- 玉壶本体类型
        战斗关键字 = "锻刀村玉壶本体"
    }
    地图处理类:添加单位(怪物任务id)
    常规提示(id, "#R上弦之伍·玉壶现身！")

  elseif 进程 == STAGE.BOSS_ZOHAKUTEN then
    -- 添加憎珀天Boss
    local 怪物任务id, ZU = 取唯一任务(FUBEN_ID.."_boss_zohakuten", id)
    local xy = 地图处理类.地图坐标[地图]:取随机点() -- 或者指定一个Boss刷新点
    任务数据[怪物任务id] = {
        id = 怪物任务id,
        起始 = os.time(),
        结束 = FUBEN_DURATION,
        玩家id = id,
        队伍组 = {},
        名称 = "憎珀天",
        模型 = "树怪", -- 使用实际模型
        事件 = "攻击",
        小地图名称颜色 = 3,
        x = xy.x,
        y = xy.y,
        方向 = 取随机数(0, 7),
        副本id = id,
        地图编号 = 地图,
        地图名称 = 取地图名称(地图),
        类型 = FUBEN_ID + 40, -- 憎珀天类型
        战斗关键字 = "锻刀村憎珀天"
    }
    地图处理类:添加单位(怪物任务id)
    常规提示(id, "#R半天狗合体·憎珀天降临！")

  elseif 进程 == STAGE.FIND_HANTENGU_BODY then
    -- 添加半天狗本体 (作为可交互单位或弱怪)
    local 怪物任务id, ZU = 取唯一任务(FUBEN_ID.."_body_hantengu", id)
    local xy = 地图处理类.地图坐标[地图]:取随机点() -- 或者指定一个刷新点
    任务数据[怪物任务id] = {
        id = 怪物任务id,
        起始 = os.time(),
        结束 = FUBEN_DURATION,
        玩家id = id,
        队伍组 = {},
        名称 = "半天狗本体",
        模型 = "树怪", -- 使用实际模型
        事件 = "攻击", -- 修改为"攻击"以匹配七绝山的模式
        小地图名称颜色 = 3,
        x = xy.x,
        y = xy.y,
        方向 = 取随机数(0, 7),
        副本id = id,
        地图编号 = 地图,
        地图名称 = 取地图名称(地图),
        类型 = FUBEN_ID + 50, -- 半天狗本体类型
        战斗关键字 = "锻刀村半天狗本体" -- 如果需要战斗
        -- 执行事件 = "处理半天狗本体交互" -- 如果是NPC对话
    }
    地图处理类:添加单位(怪物任务id)
    常规提示(id, "#Y一个微弱的气息在附近，似乎是半天狗的本体！")

  elseif 进程 == STAGE.COMPLETE then
    -- 添加副本结束NPC
    local 结束NPC任务id = id .. "_结束NPC_" .. os.time() .. "_" .. 取随机数(1, 999)
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[结束NPC任务id] = {
        id = 结束NPC任务id,
        起始 = os.time(),
        结束 = FUBEN_DURATION,
        玩家id = id,
        队伍组 = {},
        名称 = "副本结束NPC",
        模型 = "村长",
        x = xy.x,
        y = xy.y,
        方向 = 4,
        副本id = id,
        小地图名称颜色 = 3,
        地图编号 = 地图,
        地图名称 = 取地图名称(地图),
        类型 = FUBEN_ID + 60  -- 使用副本结束NPC类型
    }
    地图处理类:添加单位(结束NPC任务id)
    常规提示(id, "#G副本已完成！请与副本结束NPC对话领取奖励。")
  end
end


-- 生成任务说明 (在 任务处理类.lua 中调用)
function 副本_锻刀村之战:任务说明(玩家id, 任务id)


    -- 默认任务说明
    local 说明 = {FUBEN_NAME, "#Y锻刀村之战#W是鬼杀队与上弦之鬼的激烈战斗。锻刀村是制作日轮刀的重要之地，现在遭到了上弦之伍·玉壶和上弦之肆·半天狗的袭击！\n\n#W第一阶段：与#Y锻刀村村长#W对话，了解村子的情况。\n\n村长告诉你，锻刀村遭到了上弦之鬼的袭击，村民们正在被疏散。你需要与其他鬼杀队成员一起保护村子和日轮刀工匠们。\n\n#G完成副本可获得丰厚的经验、银两和副本积分奖励！有几率获得日轮刀碎片和玉钢材料！#W\n剩余#R/60#W/分钟"}

    -- 检查任务数据是否存在
    if 任务数据[任务id] == nil then

        return 说明
    end

    -- 直接从任务数据获取进程，不依赖副本实例
    local 进程 = 任务数据[任务id].进程 or STAGE.ENTER_VILLAGE


    -- 获取副本ID（存储在任务数据中的副本ID）
    local 副本id = 任务数据[任务id].副本id
    if 副本id == nil then

        副本id = 任务id -- 使用任务ID作为备用
    end

    -- 获取任务阶段数据
    local 阶段数据 = 任务数据[任务id].阶段数据 or {}

    -- 尝试从副本实例获取更多数据（如果存在）
    if 副本数据 and 副本数据.锻刀村之战 and 副本数据.锻刀村之战.进行 and 副本数据.锻刀村之战.进行[副本id] then
        local 副本实例 = 副本数据.锻刀村之战.进行[副本id]
        if not 阶段数据 or next(阶段数据) == nil then
            阶段数据 = 副本实例.阶段数据 or {}
        end
        -- 如果副本实例存在进程但任务数据没有，更新任务数据
        if not 任务数据[任务id].进程 and 副本实例.进程 then
            任务数据[任务id].进程 = 副本实例.进程
            进程 = 任务数据[任务id].进程
        end
    end

    local 剩余时间 = 任务数据[任务id].结束 - (os.time() - 任务数据[任务id].起始)
    local 剩余分钟 = 取分(剩余时间)

    -- 定义阶段名称映射表，使进度显示更加明确
    local 阶段名称 = {
        [STAGE.ENTER_VILLAGE] = "进入锻刀村",
        [STAGE.FIGHT_YUHUFISH] = "击退玉壶召唤物",
        [STAGE.FIGHT_HANTENGU_CLONES] = "击败半天狗分身",
        [STAGE.BOSS_YUHUBODY] = "挑战玉壶本体",
        [STAGE.BOSS_ZOHAKUTEN] = "挑战憎珀天",
        [STAGE.FIND_HANTENGU_BODY] = "寻找半天狗本体",
        [STAGE.COMPLETE] = "副本完成"
    }

    -- 计算总进度百分比
    local 总阶段数 = 7 -- STAGE.COMPLETE的值
    local 进度百分比 = math.floor((进程 / 总阶段数) * 100)

    -- 副本总体说明
    local 副本总体说明 = "#Y锻刀村之战#W是鬼杀队与上弦之鬼的激烈战斗。锻刀村是制作日轮刀的重要之地，现在遭到了上弦之伍·玉壶和上弦之肆·半天狗的袭击！"
    local 副本奖励说明 = "#G完成副本可获得丰厚的经验、银两和副本积分奖励！有几率获得日轮刀碎片和玉钢材料！#W"
    -- 使用与其他副本相同的格式
    local 副本时间说明 = format("剩余#R/%s#W/分钟", 剩余分钟)


    if 进程 == STAGE.ENTER_VILLAGE then
        说明 = {FUBEN_NAME, string.format("%s\n\n#W第一阶段：与#Y锻刀村村长#W对话，了解村子的情况。\n\n村长告诉你，锻刀村遭到了上弦之鬼的袭击，村民们正在被疏散。你需要与其他鬼杀队成员一起保护村子和日轮刀工匠们。\n\n%s\n%s",
            副本总体说明, 副本奖励说明, 副本时间说明)}
    elseif 进程 == STAGE.FIGHT_YUHUFISH then
        local 击杀数 = 阶段数据.鱼怪击杀数 or 0
        local 总数 = 阶段数据.鱼怪总数 or 10
        说明 = {FUBEN_NAME, string.format("%s\n\n#W第二阶段：击败上弦之伍·玉壶召唤的#R鱼形怪物#W！\n\n玉壶使用血鬼术召唤出了大量鱼形怪物攻击村民。这些怪物能在空中游动，攻击迅猛！\n进度：#G%d#W/#R%d#W\n\n%s\n%s",
            副本总体说明, 击杀数, 总数, 副本奖励说明, 副本时间说明)}
    elseif 进程 == STAGE.FIGHT_HANTENGU_CLONES then
        local 可畏 = 阶段数据.可畏已击败 and "#G已击败#W" or "#R未击败#W"
        local 积怒 = 阶段数据.积怒已击败 and "#G已击败#W" or "#R未击败#W"
        local 空喜 = 阶段数据.空喜已击败 and "#G已击败#W" or "#R未击败#W"
        local 哀绝 = 阶段数据.哀绝已击败 and "#G已击败#W" or "#R未击败#W"

        local 已击败数 = 0
        if 阶段数据.可畏已击败 then 已击败数 = 已击败数 + 1 end
        if 阶段数据.积怒已击败 then 已击败数 = 已击败数 + 1 end
        if 阶段数据.空喜已击败 then 已击败数 = 已击败数 + 1 end
        if 阶段数据.哀绝已击败 then 已击败数 = 已击败数 + 1 end

        说明 = {FUBEN_NAME, string.format("%s\n\n#W第三阶段：击败上弦之肆·#R半天狗#W的四个情绪分身！\n\n半天狗能够分裂出代表不同情绪的分身，每个分身都有独特的能力：\n可畏：力量型，擅长近战 %s\n积怒：防御型，能抵挡强力攻击 %s\n空喜：速度型，行动迅捷 %s\n哀绝：混合型，攻防兼备 %s\n\n进度：#G%d#W/#R4#W\n\n%s\n%s",
            副本总体说明, 可畏, 积怒, 空喜, 哀绝, 已击败数, 副本奖励说明, 副本时间说明)}
    elseif 进程 == STAGE.BOSS_YUHUBODY then
        说明 = {FUBEN_NAME, string.format("%s\n\n#W第四阶段：挑战上弦之伍·#R玉壶本体#W！\n\n击败了玉壶的召唤物后，她现身与你正面交锋！玉壶擅长水系血鬼术，能操控水流形成强力攻击。她的血鬼术\"水虎招来\"能召唤巨大水虎攻击敌人！\n\n%s\n%s",
            副本总体说明, 副本奖励说明, 副本时间说明)}
    elseif 进程 == STAGE.BOSS_ZOHAKUTEN then
        说明 = {FUBEN_NAME, string.format("%s\n\n#W第五阶段：挑战半天狗合体·#R憎珀天#W！\n\n半天狗的四个分身合体形成了更强大的存在——憎珀天！它拥有所有分身的能力，是极其危险的敌人。憎珀天的血鬼术\"破坏杀\"能释放毁灭性的攻击！\n\n%s\n%s",
            副本总体说明, 副本奖励说明, 副本时间说明)}
    elseif 进程 == STAGE.FIND_HANTENGU_BODY then
        说明 = {FUBEN_NAME, string.format("%s\n\n#W第六阶段：找到并击败隐藏起来的#R半天狗本体#W！\n\n憎珀天被击败后，你需要找到半天狗的真正本体！半天狗本体非常弱小，但速度极快，会不断逃跑。只有击败本体，才能真正消灭这个上弦之鬼！\n\n%s\n%s",
            副本总体说明, 副本奖励说明, 副本时间说明)}
    elseif 进程 == STAGE.COMPLETE then
        说明 = {FUBEN_NAME, string.format("%s\n\n#G恭喜！你成功击败了上弦之伍·玉壶和上弦之肆·半天狗，保护了锻刀村！#W\n\n锻刀村的工匠们得以继续制作日轮刀，为鬼杀队提供武器对抗鬼舞辻无惨的大军。\n\n与副本结束NPC对话领取最终奖励。",
            副本总体说明)}
    else
        说明 = {FUBEN_NAME, string.format("%s\n\n#R未知副本阶段！进程值: %d\n\n%s\n%s",
            副本总体说明, 进程, 副本奖励说明, 副本时间说明)}
    end


    return 说明
end

-- 副本失败处理 (在 任务处理类.lua 中调用 rwgx[FUBEN_ID])
function 副本_锻刀村之战:处理副本失败(任务id)
    -- 简化检查，只保留必要的检查
    if not 任务数据[任务id] or not 任务数据[任务id].队伍组 then
        if 任务数据[任务id] then
            任务数据[任务id] = nil
        end
        return
    end

    if os.time() - 任务数据[任务id].起始 >= 任务数据[任务id].结束 and 任务数据[任务id].结束 ~= 99999999 then
        if 任务数据[任务id].战斗 ~= true then  -- 未进入战斗状态
            if 任务数据[任务id].类型 == FUBEN_ID then
                local 副本id = 任务数据[任务id].副本id
                for i = 1, #任务数据[任务id].队伍组 do
                    if 玩家数据[任务数据[任务id].队伍组[i]] ~= nil then
                        -- 记录完成状态，防止重复开启
                        if not 副本数据 then 副本数据 = {} end
                        if not 副本数据.锻刀村之战 then 副本数据.锻刀村之战 = { 进行 = {}, 完成 = {} } end
                        副本数据.锻刀村之战.完成[任务数据[任务id].队伍组[i]] = os.time()
                        玩家数据[任务数据[任务id].队伍组[i]].角色:取消任务(玩家数据[任务数据[任务id].队伍组[i]].角色:取任务(FUBEN_ID))
                        常规提示(任务数据[任务id].队伍组[i], "#R副本时间已到，任务失败！")
                        -- 恢复白天状态
                        发送数据(玩家数据[任务数据[任务id].队伍组[i]].连接id, 6560, false)
                        -- 传送回城
                        地图处理类:跳转地图(任务数据[任务id].队伍组[i], FUBEN_EXIT_MAP, FUBEN_EXIT_X, FUBEN_EXIT_Y)
                    end
                end
                -- 清除副本障碍
                副本_锻刀村之战:清除副本障碍(任务数据[任务id].副本id or 任务id)
                任务数据[任务id] = nil
            else
                任务处理类:删除单位(任务id)
            end
        else
            -- 在战斗状态时也更新任务栏
            for i = 1, #任务数据[任务id].队伍组 do
                if 玩家数据[任务数据[任务id].队伍组[i]] ~= nil then
                    玩家数据[任务数据[任务id].队伍组[i]].角色:刷新任务跟踪()
                end
            end
        end
    else
        -- 未到失败时间也更新任务栏，但添加全局时间限制
        -- 检查上次全局刷新时间，避免频繁刷新
        if not 任务数据[任务id].全局刷新时间 then
            任务数据[任务id].全局刷新时间 = os.time()
        elseif os.time() - 任务数据[任务id].全局刷新时间 < 10 then
            -- 如果距离上次全局刷新不足10秒，则不进行刷新
            return
        else
            任务数据[任务id].全局刷新时间 = os.time()
        end

        -- 检查上次刷新时间，避免频繁刷新
        if not 任务数据[任务id].副本失败刷新时间 then
            任务数据[任务id].副本失败刷新时间 = os.time()
        else
            -- 如果距离上次刷新不足5秒，则不进行刷新
            if os.time() - 任务数据[任务id].副本失败刷新时间 < 5 then
                return
            end
            任务数据[任务id].副本失败刷新时间 = os.time()
        end

        for i = 1, #任务数据[任务id].队伍组 do
            if 玩家数据[任务数据[任务id].队伍组[i]] ~= nil then
                -- 检查玩家是否有摄妖香效果
                if 玩家数据[任务数据[任务id].队伍组[i]].角色:取任务(9) ~= 0 then
                    -- 如果有摄妖香效果，则每15秒才刷新一次
                    if not 任务数据[任务id].摄妖香副本失败刷新时间 then
                        任务数据[任务id].摄妖香副本失败刷新时间 = {}
                    end

                    local 队员id = 任务数据[任务id].队伍组[i]
                    if not 任务数据[任务id].摄妖香副本失败刷新时间[队员id] then
                        任务数据[任务id].摄妖香副本失败刷新时间[队员id] = os.time()
                        玩家数据[队员id].角色:刷新任务跟踪()
                    elseif os.time() - 任务数据[任务id].摄妖香副本失败刷新时间[队员id] >= 15 then
                        任务数据[任务id].摄妖香副本失败刷新时间[队员id] = os.time()
                        玩家数据[队员id].角色:刷新任务跟踪()
                    end
                else
                    -- 如果没有摄妖香效果，则正常刷新但仍有时间限制
                    if not 任务数据[任务id].普通刷新时间 then
                        任务数据[任务id].普通刷新时间 = {}
                    end

                    local 队员id = 任务数据[任务id].队伍组[i]
                    if not 任务数据[任务id].普通刷新时间[队员id] then
                        任务数据[任务id].普通刷新时间[队员id] = os.time()
                        玩家数据[队员id].角色:刷新任务跟踪()
                    elseif os.time() - 任务数据[任务id].普通刷新时间[队员id] >= 5 then
                        任务数据[任务id].普通刷新时间[队员id] = os.time()
                        玩家数据[队员id].角色:刷新任务跟踪()
                    end
                end
            end
        end
    end
end

-- 注册到任务处理类的rwgx表中，用于任务处理类调用
rwgx1005 = function(任务id)
    副本_锻刀村之战:处理副本失败(任务id)
end

-- 全局函数入口，用于任务处理类调用
function 锻刀村之战副本失败(任务id)
    副本_锻刀村之战:处理副本失败(任务id)
end

function 结束锻刀村副本(任务id)
  if not 任务数据[任务id] then return end
  local 副本id = 任务数据[任务id].副本id
  if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then return end
  local 主任务id = 副本数据.锻刀村之战.进行[副本id].主任务id
  副本数据.锻刀村之战.进行[副本id] = nil
  if not 任务数据[主任务id] then return end
  local id组 = 任务数据[主任务id].队伍组
  任务数据[主任务id] = nil
  for i = 1, #id组 do
    if not 副本数据 then 副本数据 = {} end
    if not 副本数据.锻刀村之战 then 副本数据.锻刀村之战 = { 进行 = {}, 完成 = {} } end
    副本数据.锻刀村之战.完成[id组[i]] = true
    if 玩家数据[id组[i]] then
      玩家数据[id组[i]].角色:取消任务(FUBEN_ID)
      玩家数据[id组[i]].角色:刷新任务跟踪()

      -- 恢复白天状态
      发送数据(玩家数据[id组[i]].连接id, 6560, false)

      if 玩家数据[id组[i]].战斗 ~= 0 and ((战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 160000 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 160001 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 160002 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 160003 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 160004 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 160005 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 160006 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 160007) then
        战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]:结束战斗(0, 0, 1)
      end
    end
  end
  for i in pairs(任务数据) do
    if 任务数据[i].类型 >= FUBEN_ID and 任务数据[i].类型 <= FUBEN_ID + 10 and 任务数据[i].副本id == 副本id then
      if 任务数据[i].地图编号 and 任务数据[i].单位编号 then
        地图处理类:删除单位(任务数据[i].地图编号, 任务数据[i].单位编号)
      end
      任务数据[i] = nil
    end
  end
end

-- 旧的锻刀村副本失败函数已移除，使用副本_锻刀村之战:处理副本失败替代

-- 添加战斗胜利回调函数
function 胜利160000(id组, 战斗类型, 任务id)
  if 任务数据[任务id] == nil then return end
  任务数据[任务id].战斗 = nil
end

function 胜利160001(id组, 战斗类型, 任务id)
  if 任务数据[任务id] == nil then return end
  任务数据[任务id].战斗 = nil

end

function 胜利160002(id组, 战斗类型, 任务id)
  if 任务数据[任务id] == nil then return end
  任务数据[任务id].战斗 = nil

end

function 胜利160003(id组, 战斗类型, 任务id)
  if 任务数据[任务id] == nil then return end
  任务数据[任务id].战斗 = nil

end

function 胜利160004(id组, 战斗类型, 任务id)
  if 任务数据[任务id] == nil then return end
  任务数据[任务id].战斗 = nil

end

function 胜利160005(id组, 战斗类型, 任务id)
  if 任务数据[任务id] == nil then return end
  任务数据[任务id].战斗 = nil

end

function 胜利160006(id组, 战斗类型, 任务id)
  if 任务数据[任务id] == nil then return end
  任务数据[任务id].战斗 = nil

end

function 胜利160007(id组, 战斗类型, 任务id)
  if 任务数据[任务id] == nil then return end
  任务数据[任务id].战斗 = nil

end
function 副本_锻刀村之战:处理副本完成(任务id)
     if 任务数据[任务id] == nil then return end
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 then return end
    local 副本实例 = 副本数据.锻刀村之战.进行[任务id]
    if 副本实例 == nil then return end

    local 队伍成员 = 副本实例.队伍组 or 任务数据[任务id].队伍组



    -- 使用更新副本进度函数来更新副本状态为完成
    更新副本进度(任务id, STAGE.COMPLETE)

    -- 通知队员副本完成，可以领取奖励
    for _, 队员id in ipairs(队伍成员) do
        if 玩家数据[队员id] then
            常规提示(队员id, string.format("#G恭喜！成功完成副本 %s！请与副本结束NPC对话领取奖励。", FUBEN_NAME))
            玩家数据[队员id].角色:刷新任务跟踪()
            -- 记录完成时间，用于冷却
            if not 副本数据 then 副本数据 = {} end
            if not 副本数据.锻刀村之战 then 副本数据.锻刀村之战 = { 进行 = {}, 完成 = {} } end
            副本数据.锻刀村之战.完成[队员id] = os.time()
        end
    end

    -- 注意：任务数据在此处不直接删除，等待玩家与结束NPC对话领取奖励后再清理
end

-- 改进副本完成奖励函数
function 副本_锻刀村之战:发放副本奖励(副本id)
    -- 检查副本数据是否存在
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        return false
    end

    local 副本实例 = 副本数据.锻刀村之战.进行[副本id]
    local 主任务id = 副本实例.主任务id

    -- 如果主任务ID不存在，无法发放奖励
    if not 主任务id or not 任务数据[主任务id] or not 任务数据[主任务id].队伍组 then
        return false
    end

    -- 向所有参与的玩家发放奖励
    for _, player_id in ipairs(任务数据[主任务id].队伍组) do
        if 玩家数据[player_id] then
            -- 基础奖励
            local 等级 = 玩家数据[player_id].角色.等级
            local 经验 = 等级 * 取随机数(1500, 2000)
            local 银子 = 等级 * 取随机数(150, 200) + 10000
            local 积分 = 500

            -- 发放奖励
            玩家数据[player_id].角色:添加经验(经验, "锻刀村之战")
            玩家数据[player_id].角色:添加银子(银子, "锻刀村之战", 1)
            玩家数据[player_id].角色:添加积分(积分, "副本积分")

            -- 随机特殊奖励（参考五更寒副本）
            if 取随机数() <= 50 then
                local 奖励参数 = 取随机数(1, 100)
                local 链接 = {
                    提示 = string.format("#S(锻刀村之战)#G/%s#Y成功击败半天狗，保卫了锻刀村，并获得", 玩家数据[player_id].角色.名称),
                    频道 = "xt",
                    结尾 = "#Y一个！"
                }

                -- 根据随机值发放不同奖励
                if 奖励参数 <= 10 then
                    -- 稀有道具
                    local 名称 = "高级兽决碎片"
                    玩家数据[player_id].道具:给予超链接道具(player_id, 名称, 2, nil, 链接)
                    常规提示(player_id, "#G恭喜获得稀有道具：#Y" .. 名称 .. " x2")
                elseif 奖励参数 <= 30 then
                    -- 中等道具
                    local 名称 = "高级魔兽要诀"
                    玩家数据[player_id].道具:给予超链接道具(player_id, 名称, 1, nil, 链接)
                    常规提示(player_id, "#G恭喜获得道具：#Y" .. 名称 .. " x1")
                elseif 奖励参数 <= 50 then
                    -- 普通道具
                    local 名称 = "超级金柳露"
                    玩家数据[player_id].道具:给予超链接道具(player_id, 名称, 2, nil, 链接)
                    常规提示(player_id, "#G恭喜获得道具：#Y" .. 名称 .. " x2")
                elseif 奖励参数 <= 70 then
                    -- 强化石
                    local 名称 = 取强化石()
                    玩家数据[player_id].道具:给予超链接道具(player_id, 名称, 3, nil, 链接)
                    常规提示(player_id, "#G恭喜获得道具：#Y" .. 名称 .. " x3")
                elseif 奖励参数 <= 90 then
                    -- 五宝盒
                    local 名称 = "五宝盒"
                    玩家数据[player_id].道具:给予超链接道具(player_id, 名称, 2, nil, 链接)
                    常规提示(player_id, "#G恭喜获得道具：#Y" .. 名称 .. " x2")
                else
                    -- 高级召唤兽内丹
                    local 名称 = "高级召唤兽内丹"
                    玩家数据[player_id].道具:给予超链接道具(player_id, 名称, 1, nil, 链接)
                    常规提示(player_id, "#G恭喜获得道具：#Y" .. 名称 .. " x1")
                end
            end

            -- 记录玩家完成副本
            if not 副本数据.锻刀村之战.完成 then
                副本数据.锻刀村之战.完成 = {}
            end
            副本数据.锻刀村之战.完成[player_id] = os.time()

            -- 发送完成提示
            常规提示(player_id, "#G恭喜你成功完成锻刀村之战副本！获得了丰厚奖励！")
        end
    end

    副本_锻刀村之战:记录日志("副本奖励已发放：ID=" .. tostring(副本id))
    return true
end

-- 修改处理副本完成函数，调用奖励发放
function 副本_锻刀村之战:处理副本完成(副本id)
    -- 检查副本数据是否存在
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        return false
    end

    -- 更新副本进度为完成
    副本数据.锻刀村之战.进行[副本id].进程 = STAGE.COMPLETE
    if 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] then
        任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].进程 = STAGE.COMPLETE
    end

    -- 向所有参与的玩家发送提示
    if 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组 then
        for _, player_id in ipairs(任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组) do
            常规提示(player_id, "#G恭喜你击败了上弦之肆·半天狗，成功保卫了锻刀村！")
            -- 添加副本结束NPC，让玩家选择奖励
            self:添加副本结束NPC(player_id)
        end
    end

    -- 成就记录已移除

    副本_锻刀村之战:记录日志("副本已完成：ID=" .. tostring(副本id))
    return true
end

-- 添加副本结束NPC
function 副本_锻刀村之战:添加副本结束NPC(玩家id)
    -- 获取玩家当前地图和坐标
    local 地图id = 玩家数据[玩家id].角色.地图
    local x = 玩家数据[玩家id].角色.x
    local y = 玩家数据[玩家id].角色.y

    -- 在玩家附近添加NPC
    local npc_x = x + 取随机数(-2, 2)
    local npc_y = y + 取随机数(-2, 2)

    -- 创建NPC
    local npc = {
        id = "副本结束NPC_" .. 玩家id,
        名称 = "副本结束NPC",
        模型 = 6005, -- 使用适当的模型ID
        地图 = 地图id,
        x = npc_x,
        y = npc_y,
        方向 = 取随机数(0, 7),
        类型 = "副本结束NPC"
    }

    -- 添加NPC到地图
    添加动态NPC(npc)

    -- 发送提示
    常规提示(玩家id, "#Y请与附近的#G副本结束NPC#Y对话，选择你想要的奖励！")
end

-- 记录副本成就函数已移除

-- 修改发放奖励并结束副本函数，使用新的奖励系统
function 副本_锻刀村之战:发放奖励并结束(任务id, 玩家id, 选择物品)
    if 任务数据[任务id] == nil then return end
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 then return end
    local 副本实例 = 副本数据.锻刀村之战.进行[任务id]
    if 副本实例 == nil then return end
    if 副本实例.进程 ~= STAGE.COMPLETE then
        常规提示(玩家id, "#R副本尚未完成，无法领取奖励！")
        return
    end

    -- 检查是否已领取奖励 (防止重复领取)
    if 副本实例.奖励已领取 and 副本实例.奖励已领取[玩家id] then
        常规提示(玩家id, "#Y你已经领取过本次副本的奖励了。")
        return
    end

    -- 初始化奖励已领取表
    副本实例.奖励已领取 = 副本实例.奖励已领取 or {}

    -- 标记该玩家已领取奖励
    副本实例.奖励已领取[玩家id] = true

    -- 发放基础奖励
    local 等级 = 玩家数据[玩家id].角色.等级
    local 经验奖励 = 等级 * 取随机数(1500, 2000)
    local 银子奖励 = 等级 * 取随机数(150, 200) + 10000
    local 副本积分奖励 = 500

    玩家数据[玩家id].角色:添加经验(经验奖励, "锻刀村之战", 1)
    玩家数据[玩家id].角色:添加银子(银子奖励, "锻刀村之战", 1)
    玩家数据[玩家id].角色:添加积分(副本积分奖励, "副本积分")

    常规提示(玩家id, string.format("#G获得副本奖励：经验 %d，银两 %d，副本积分 %d", 经验奖励, 银子奖励, 副本积分奖励))

    -- 根据选择发放物品奖励
    if 选择物品 == "战魄" then
        -- 给予战魄及额外奖励
        玩家数据[玩家id].道具:给予道具(玩家id, "战魄", 1)
        常规提示(玩家id, "#G获得物品：战魄 x1")

        -- 额外给予一些随机奖励
        if 取随机数() <= 30 then
            local 额外物品 = "高级魔兽要诀"
            玩家数据[玩家id].道具:给予道具(玩家id, 额外物品, 1)
            常规提示(玩家id, "#G额外获得物品：" .. 额外物品 .. " x1")
        end
    elseif 选择物品 == "陨铁" then
        -- 给予陨铁及额外奖励
        玩家数据[玩家id].道具:给予道具(玩家id, "陨铁", 1)
        常规提示(玩家id, "#G获得物品：陨铁 x1")

        -- 额外给予一些随机奖励
        if 取随机数() <= 30 then
            local 额外物品 = "高级召唤兽内丹"
            玩家数据[玩家id].道具:给予道具(玩家id, 额外物品, 1)
            常规提示(玩家id, "#G额外获得物品：" .. 额外物品 .. " x1")
        end
    else
        -- 默认给予战魄
        玩家数据[玩家id].道具:给予道具(玩家id, "战魄", 1)
        常规提示(玩家id, "#G获得物品：战魄 x1")
    end

    -- 取消玩家的副本任务
    if 玩家数据[玩家id] then
        -- 查找并取消与副本相关的所有任务
        local 任务列表 = 玩家数据[玩家id].角色:取所有任务()
        for _, 任务 in pairs(任务列表) do
            if 任务.类型 == FUBEN_ID then
                玩家数据[玩家id].角色:取消任务(任务.任务id)
            end
        end
        -- 强制刷新任务追踪栏
        玩家数据[玩家id].角色:刷新任务跟踪()
        -- 发送清空任务追踪的指令
        发送数据(玩家数据[玩家id].连接id, 5001, {类型="清空任务追踪", 名称="锻刀村之战"})
    end

    -- 检查是否所有人都领取了奖励，如果是，则清理副本
    local all_rewarded = true

    -- 获取主任务ID
    local 主任务id = 副本实例.主任务id

    -- 检查主任务数据是否存在
    if 主任务id and 任务数据[主任务id] and 任务数据[主任务id].队伍组 then
        for _, 队员id in ipairs(任务数据[主任务id].队伍组) do
            if not 副本实例.奖励已领取[队员id] then
                all_rewarded = false
                break
            end
        end

        -- 如果所有人都领取了奖励，清理副本
        if all_rewarded then
            self:清理副本(任务id)
        end
    else
        -- 如果没有队伍组数据，则只检查当前玩家
        self:清理副本(任务id)
    end

    副本_锻刀村之战:记录日志("玩家" .. 玩家id .. "领取了副本奖励：" .. (选择物品 or "战魄"))

    -- 将玩家传送到地图1001（无论当前在哪个地图）
    常规提示(玩家id, "#G正在将你传送回长安城...")
    地图处理类:跳转地图(玩家id, 1001, 421, 76)
end

-- 添加清理副本函数
function 副本_锻刀村之战:清理副本(任务id)
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[任务id] then
        return false
    end

    local 副本实例 = 副本数据.锻刀村之战.进行[任务id]
    local 主任务id = 副本实例.主任务id

    -- 如果主任务ID不存在，无法清理副本
    if not 主任务id or not 任务数据[主任务id] then
        return false
    end

    -- 将所有玩家传送回城
    if 任务数据[主任务id].队伍组 then
        for _, player_id in ipairs(任务数据[主任务id].队伍组) do
            if 玩家数据[player_id] then
                -- 取消玩家的所有副本相关任务
                local 任务列表 = 玩家数据[player_id].角色:取所有任务()
                for _, 任务 in pairs(任务列表) do
                    if 任务.类型 == FUBEN_ID then
                        玩家数据[player_id].角色:取消任务(任务.任务id)
                    end
                end

                -- 强制刷新任务追踪栏
                玩家数据[player_id].角色:刷新任务跟踪()

                -- 发送清空任务追踪的指令
                发送数据(玩家数据[player_id].连接id, 5001, {类型="清空任务追踪", 名称="锻刀村之战"})

                -- 恢复白天状态
                发送数据(玩家数据[player_id].连接id, 6560, false)
            end
        end
    end

    -- 清除副本障碍
    副本_锻刀村之战:清除副本障碍(任务id)

    -- 清理副本数据
    副本数据.锻刀村之战.进行[任务id] = nil
    任务数据[主任务id] = nil

    副本_锻刀村之战:记录日志("副本已清理：ID=" .. tostring(任务id))
    return true
end

-- 记录日志函数
function 副本_锻刀村之战:记录日志(内容)
    __S服务:输出("[锻刀村之战] " .. 内容)
end

-- 副本内事件处理函数 (示例：击杀怪物)
function 副本_锻刀村之战:处理怪物击杀(副本id, 怪物名称)
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 then return end
    local 副本实例 = 副本数据.锻刀村之战.进行[副本id]
    if 副本实例 == nil then return end

    local 进程 = 副本实例.进程
    local 队长id = (任务数据[副本id] or {}).领取人id -- Use (table or {}) to safely access field

    if 进程 == STAGE.FIGHT_YUHUFISH then
        if 怪物名称 == "玉壶召唤物" then
            副本实例.阶段数据 = 副本实例.阶段数据 or {} -- Ensure阶段数据 exists
            副本实例.阶段数据.鱼怪击杀数 = (副本实例.阶段数据.鱼怪击杀数 or 0) + 1
            -- 同步到任务数据
            if 任务数据[副本实例.主任务id] then
                任务数据[副本实例.主任务id].阶段数据 = 任务数据[副本实例.主任务id].阶段数据 or {}
                任务数据[副本实例.主任务id].阶段数据.鱼怪击杀数 = 副本实例.阶段数据.鱼怪击杀数
            end
        end
    elseif 进程 == STAGE.FIGHT_HANTENGU_CLONES then
        副本实例.阶段数据 = 副本实例.阶段数据 or {} -- Ensure阶段数据 exists
        -- 更新分身击败状态
        if 怪物名称 == "可畏" then
            副本实例.阶段数据.可畏已击败 = true
        elseif 怪物名称 == "积怒" then
            副本实例.阶段数据.积怒已击败 = true
        elseif 怪物名称 == "空喜" then
            副本实例.阶段数据.空喜已击败 = true
        elseif 怪物名称 == "哀绝" then
            副本实例.阶段数据.哀绝已击败 = true
        end
        -- 同步到任务数据
        if 任务数据[副本实例.主任务id] then
             任务数据[副本实例.主任务id].阶段数据 = 任务数据[副本实例.主任务id].阶段数据 or {}
             任务数据[副本实例.主任务id].阶段数据.可畏已击败 = 副本实例.阶段数据.可畏已击败
             任务数据[副本实例.主任务id].阶段数据.积怒已击败 = 副本实例.阶段数据.积怒已击败
             任务数据[副本实例.主任务id].阶段数据.空喜已击败 = 副本实例.阶段数据.空喜已击败
             任务数据[副本实例.主任务id].阶段数据.哀绝已击败 = 副本实例.阶段数据.哀绝已击败
        end
    end

    -- 刷新任务追踪
    if 任务数据[副本实例.主任务id] and 任务数据[副本实例.主任务id].队伍组 then
        for _, 队员id in ipairs(任务数据[副本实例.主任务id].队伍组) do
            if 玩家数据[队员id] then
                玩家数据[队员id].角色:刷新任务跟踪()
            end
        end
    end
end

-- 任务追踪刷新函数已移除，使用系统标准刷新方式

-- 副本内NPC对话处理 (示例)
function 副本_锻刀村之战:处理NPC对话(玩家id, NPC名称)
    local 任务id = 玩家数据[玩家id].角色:取任务(FUBEN_ID)
    if 任务id == 0 then return end -- 玩家没有此副本任务

    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 then return end
    local 副本实例 = 副本数据.锻刀村之战.进行[任务id]
    if 副本实例 == nil then return end

    local 进程 = 副本实例.进程
    local 队长id = 任务数据[任务id].领取人id

    if 进程 == STAGE.ENTER_VILLAGE and NPC名称 == "锻刀村村长" then
        -- 初始化阶段数据
        副本实例.阶段数据 = {} -- 初始化阶段数据
        副本实例.阶段数据.鱼怪击杀数 = 0
        副本实例.阶段数据.鱼怪总数 = 10 -- 假设需要击杀10只

        -- 使用更新副本进度函数来推进到下一阶段
        更新副本进度(任务id, STAGE.FIGHT_YUHUFISH)

        常规提示(队长id, "#G村长告知：上弦之伍的手下已经开始攻击村子了！")
        -- 添加刷新第一波鱼怪的逻辑
        local 地图id = 玩家数据[玩家id].角色.地图数据.编号 -- 获取当前地图ID
        local 怪物数量 = 5 -- 每次刷新5只
        for i = 1, 怪物数量 do
            local 怪物任务id, ZU = 取唯一任务(FUBEN_ID.."_fish_"..i, 队长id) -- 生成唯一的怪物任务ID
            local xy = 地图处理类.地图坐标[地图id]:取随机点() -- 在当前地图随机位置刷怪
            任务数据[怪物任务id] = {
                id = 怪物任务id,
                起始 = os.time(),
                结束 = FUBEN_DURATION, -- 怪物持续时间同副本
                玩家id = 队长id, -- 关联到队长ID
                队伍组 = {},
                名称 = "玉壶召唤物",
                模型 = "鲛人", -- 使用鲛人模型，适合水系怪物
                事件 = "明雷活动", -- 或者其他合适的事件类型
                x = xy.x,
                y = xy.y,
                方向 = 取随机数(0, 7),
                副本id = 队长id, -- 使用队长ID作为副本ID
                地图编号 = 地图id,
                地图名称 = 取地图名称(地图id),
                类型 = FUBEN_ID + 10, -- 为副本内怪物分配一个子类型，方便战斗回调区分 (例如 1011)
                战斗关键字 = "锻刀村玉壶鱼怪" -- 用于战斗处理类查找怪物属性定义
            }
            地图处理类:添加单位(怪物任务id)
        end
        常规提示(队长id, string.format("#R发现 %d 只玉壶召唤物！", 怪物数量))

        刷新队伍任务追踪(队长id)
        -- 返回对话内容给客户端
        return {"锻刀村村长", "快！那些怪物冲进来了！保护好大家！"}

    elseif 进程 == STAGE.COMPLETE and NPC名称 == "副本结束NPC" then
         -- 不在这里直接发放奖励，而是让玩家选择获取战魄或陨铁
         -- 返回对话内容
         return {"副本结束NPC", "辛苦了！请选择你想要的奖励。"}
    end

    -- 其他NPC对话或非当前阶段的对话可以返回默认内容或不处理
    return nil
end

-- 发放奖励并结束副本
function 副本_锻刀村之战:发放奖励并结束(任务id, 玩家id, 选择物品)
    if 任务数据[任务id] == nil then return end
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 then return end
    local 副本实例 = 副本数据.锻刀村之战.进行[任务id]
    if 副本实例 == nil then return end
    if 副本实例.进程 ~= STAGE.COMPLETE then
        常规提示(玩家id, "#R副本尚未完成，无法领取奖励！")
        return
    end

    -- 检查是否已领取奖励 (防止重复领取)
    if 副本实例.奖励已领取 and 副本实例.奖励已领取[玩家id] then
        常规提示(玩家id, "#Y你已经领取过本次副本的奖励了。")
        return
    end

    -- 初始化奖励已领取表
    副本实例.奖励已领取 = 副本实例.奖励已领取 or {}
    -- 标记该玩家已领取奖励
    副本实例.奖励已领取[玩家id] = true

    -- 发放奖励逻辑 (经验和银子奖励)
    local 经验奖励 = 500 * 玩家数据[玩家id].角色.等级
    local 银子奖励 = 100 * 玩家数据[玩家id].角色.等级
    local 副本积分奖励 = 50 -- 假设给50点副本积分
    玩家数据[玩家id].角色:添加经验(经验奖励, "副本奖励", 1)
    玩家数据[玩家id].角色:添加银子(银子奖励, "副本奖励", 1)
    玩家数据[玩家id].角色:添加积分(副本积分奖励, "副本积分") -- 使用正确的添加积分函数

    常规提示(玩家id, string.format("#G获得副本奖励：经验 %d，银两 %d，副本积分 %d", 经验奖励, 银子奖励, 副本积分奖励))

    -- 根据选择发放物品奖励
    if 选择物品 == "战魄" then
        -- 只给予战魄
        玩家数据[玩家id].道具:给予道具(玩家id, "战魄", 1)
        常规提示(玩家id, "#G获得物品：战魄 x1")
    elseif 选择物品 == "陨铁" then
        -- 只给予陨铁
        玩家数据[玩家id].道具:给予道具(玩家id, "陨铁", 1)
        常规提示(玩家id, "#G获得物品：陨铁 x1")
    else
        玩家数据[玩家id].道具:给予道具(玩家id, "战魄", 1)
        常规提示(玩家id, "#G获得物品：战魄 x1")
    end

    -- 取消玩家的副本任务
    if 玩家数据[玩家id] then
        -- 直接取消副本任务
        local 任务id = 玩家数据[玩家id].角色:取任务(FUBEN_ID)
        if 任务id and 任务id ~= 0 then
            玩家数据[玩家id].角色:取消任务(任务id)
        end

        -- 强制刷新任务追踪栏
        玩家数据[玩家id].角色:刷新任务跟踪()
        -- 发送清空任务追踪的指令
        发送数据(玩家数据[玩家id].连接id, 5001, {类型="清空任务追踪", 名称="锻刀村之战"})
    end

    -- 检查是否所有人都领取了奖励，如果是，则清理副本
    local all_rewarded = true

    -- 获取主任务ID
    local 主任务id = 副本实例.主任务id

    -- 检查主任务数据是否存在
    if 主任务id and 任务数据[主任务id] and 任务数据[主任务id].队伍组 then
        for _, 队员id in ipairs(任务数据[主任务id].队伍组) do
            if not 副本实例.奖励已领取[队员id] then
                all_rewarded = false
                break
            end
        end
    else
        -- 如果没有队伍组数据，则只检查当前玩家
        all_rewarded = true -- 当前玩家已领取奖励
    end

    if all_rewarded then
        __S服务:输出(string.format("副本 %s (任务ID: %s) 所有成员已领取奖励，清理副本数据。", FUBEN_NAME, 任务id))
        副本数据.锻刀村之战.进行[任务id] = nil
        任务数据[任务id] = nil -- 从全局任务数据中移除
        -- 注意：玩家任务列表中的任务ID在领取奖励时或之前就应该被取消
        if 主任务id and 任务数据[主任务id] and 任务数据[主任务id].队伍组 then
            for _, 队员id in ipairs(任务数据[主任务id].队伍组) do
                if 玩家数据[队员id] then
                    local 副本任务id = 玩家数据[队员id].角色:取任务(FUBEN_ID)
                    if 副本任务id and 副本任务id ~= 0 then
                        玩家数据[队员id].角色:取消任务(副本任务id) -- 确保任务从玩家身上移除
                    end
                end
            end
        end
    end

    -- 将玩家传送到地图1001（无论当前在哪个地图）
    常规提示(玩家id, "#G正在将你传送回长安城...")
    地图处理类:跳转地图(玩家id, 1001, 421, 76)
end

-- 统一的阶段推进函数，确保所有阶段推进都通过这个函数进行
function 副本_锻刀村之战:推进阶段(副本id, 新阶段, 原因)
    -- 检查副本数据是否存在
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        return false
    end

    -- 获取副本实例
    local 副本实例 = 副本数据.锻刀村之战.进行[副本id]
    local 主任务id = 副本实例.主任务id

    -- 记录日志
    local 旧阶段 = 副本实例.进程
    __S服务:输出(string.format("锻刀村之战副本(ID:%s)阶段推进: %d -> %d, 原因: %s", 副本id, 旧阶段, 新阶段, 原因 or "未指定"))

    -- 使用更新副本进度函数来推进阶段
    更新副本进度(副本id, 新阶段)

    -- 清理上一阶段数据
    副本实例.阶段数据 = {}
    if 任务数据[主任务id] then
        任务数据[主任务id].阶段数据 = {}
    end

    -- 根据新阶段设置副本单位
    设置锻刀村副本(副本id)

    return true
end

-- 战斗胜利处理函数（统一处理所有战斗胜利事件）
function 副本_锻刀村之战:处理战斗胜利(副本id, 怪物名称, 战斗数据)
    -- 简化检查，直接获取副本实例
    local 副本实例 = 副本数据 and 副本数据.锻刀村之战 and 副本数据.锻刀村之战.进行 and 副本数据.锻刀村之战.进行[副本id]
    if not 副本实例 then
        return false
    end

    -- 获取当前进程和主任务ID
    local 进程 = 副本实例.进程
    local 主任务id = 副本实例.主任务id

    -- 确保阶段数据存在
    副本实例.阶段数据 = 副本实例.阶段数据 or {}

    -- 根据当前进程和怪物名称处理战斗胜利
    if 进程 == STAGE.FIGHT_YUHUFISH then
        if 怪物名称 == "玉壶召唤物" then
            -- 更新击杀数
            副本实例.阶段数据.鱼怪击杀数 = (副本实例.阶段数据.鱼怪击杀数 or 0) + 1
            副本实例.阶段数据.鱼怪总数 = 副本实例.阶段数据.鱼怪总数 or 10

            -- 同步到任务数据
            if 任务数据[主任务id] then
                任务数据[主任务id].阶段数据 = 任务数据[主任务id].阶段数据 or {}
                任务数据[主任务id].阶段数据.鱼怪击杀数 = 副本实例.阶段数据.鱼怪击杀数
                任务数据[主任务id].阶段数据.鱼怪总数 = 副本实例.阶段数据.鱼怪总数
            end

            -- 检查是否击杀了足够数量的鱼怪
            if 副本实例.阶段数据.鱼怪击杀数 >= 副本实例.阶段数据.鱼怪总数 then
                -- 推进到下一阶段
                self:推进阶段(副本id, STAGE.FIGHT_HANTENGU_CLONES, "击败所有玉壶召唤物")

                -- 通知玩家
                if 任务数据[主任务id] and 任务数据[主任务id].队伍组 then
                    for _, player_id in ipairs(任务数据[主任务id].队伍组) do
                        常规提示(player_id, "#G玉壶的召唤物已被击退！半天狗的分身出现了！")
                    end
                end
            end
        end
    elseif 进程 == STAGE.FIGHT_HANTENGU_CLONES then
        -- 更新分身击败状态
        if 怪物名称 == "可畏" then
            副本实例.阶段数据.可畏已击败 = true
        elseif 怪物名称 == "积怒" then
            副本实例.阶段数据.积怒已击败 = true
        elseif 怪物名称 == "空喜" then
            副本实例.阶段数据.空喜已击败 = true
        elseif 怪物名称 == "哀绝" then
            副本实例.阶段数据.哀绝已击败 = true
        end

        -- 同步到任务数据
        if 任务数据[主任务id] then
            任务数据[主任务id].阶段数据 = 任务数据[主任务id].阶段数据 or {}
            任务数据[主任务id].阶段数据.可畏已击败 = 副本实例.阶段数据.可畏已击败
            任务数据[主任务id].阶段数据.积怒已击败 = 副本实例.阶段数据.积怒已击败
            任务数据[主任务id].阶段数据.空喜已击败 = 副本实例.阶段数据.空喜已击败
            任务数据[主任务id].阶段数据.哀绝已击败 = 副本实例.阶段数据.哀绝已击败
        end

        -- 检查是否所有分身都被击败
        if 副本实例.阶段数据.可畏已击败 and
           副本实例.阶段数据.积怒已击败 and
           副本实例.阶段数据.空喜已击败 and
           副本实例.阶段数据.哀绝已击败 then

            -- 推进到下一阶段
            self:推进阶段(副本id, STAGE.BOSS_YUHUBODY, "击败所有半天狗分身")

            -- 通知玩家
            if 任务数据[主任务id] and 任务数据[主任务id].队伍组 then
                for _, player_id in ipairs(任务数据[主任务id].队伍组) do
                    常规提示(player_id, "#G半天狗的分身已被击败！玉壶本体出现了！")
                end
            end
        end
    elseif 进程 == STAGE.BOSS_YUHUBODY then
        if 怪物名称 == "玉壶本体" then
            -- 推进到下一阶段
            self:推进阶段(副本id, STAGE.BOSS_ZOHAKUTEN, "击败玉壶本体")

            -- 通知玩家
            if 任务数据[主任务id] and 任务数据[主任务id].队伍组 then
                for _, player_id in ipairs(任务数据[主任务id].队伍组) do
                    常规提示(player_id, "#G玉壶已被击败！憎珀天出现了！")
                end
            end
        end
    elseif 进程 == STAGE.BOSS_ZOHAKUTEN then
        if 怪物名称 == "憎珀天" then
            -- 推进到下一阶段
            self:推进阶段(副本id, STAGE.FIND_HANTENGU_BODY, "击败憎珀天")

            -- 通知玩家
            if 任务数据[主任务id] and 任务数据[主任务id].队伍组 then
                for _, player_id in ipairs(任务数据[主任务id].队伍组) do
                    常规提示(player_id, "#G憎珀天已被击败！快找到隐藏的半天狗本体！")
                end
            end
        end
    elseif 进程 == STAGE.FIND_HANTENGU_BODY then
        if 怪物名称 == "半天狗本体" then
            -- 推进到完成阶段
            self:推进阶段(副本id, STAGE.COMPLETE, "击败半天狗本体")

            -- 处理副本完成
            self:处理副本完成(副本id)

            -- 通知玩家
            if 任务数据[主任务id] and 任务数据[主任务id].队伍组 then
                for _, player_id in ipairs(任务数据[主任务id].队伍组) do
                    常规提示(player_id, "#G恭喜！你们成功击败了半天狗本体，完成了锻刀村之战副本！")
                end
            end
        end
    end

    -- 刷新任务追踪
    if 战斗数据 and 战斗数据.进入战斗玩家id then
        刷新队伍任务追踪(战斗数据.进入战斗玩家id)
    end

    return true
end

function 副本_锻刀村之战:检查重置()
    -- 获取当前时间戳
    local current_time = os.time()
    -- 获取当前时间的详细信息
    local time_info = os.date("*t", current_time)

    -- 如果副本数据中没有上次重置时间,则初始化
    if not 副本数据.锻刀村之战.上次重置时间 then
        副本数据.锻刀村之战.上次重置时间 = current_time
        return true
    end

    -- 获取上次重置时间的详细信息
    local last_reset = os.date("*t", 副本数据.锻刀村之战.上次重置时间)

    -- 如果日期不同，则需要重置（每日重置）
    if time_info.year > last_reset.year or
       time_info.month > last_reset.month or
       time_info.day > last_reset.day then
        副本数据.锻刀村之战.上次重置时间 = current_time
        副本数据.锻刀村之战.完成 = {} -- 清空完成记录
        副本_锻刀村之战:记录日志("锻刀村之战副本已重置（每日）")
        return true
    end

    return false
end


-- ==================== 怪物属性定义 ====================

-- 示例：玉壶召唤物 (鱼怪)
function 怪物属性:锻刀村玉壶鱼怪(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
    local sx = self:取属性(等级, "龙宫", "法系") -- 假设鱼怪是法系
    local 数量 = 取随机数(3, 5) -- 每波刷新的数量

    for i = 1, 数量 do
        战斗单位[i] = {
            名称 = "玉壶召唤物",
            模型 = "鲛人", -- 使用鲛人模型，适合水系怪物
            等级 = 等级,
            变异 = true, -- Added变异=true for consistency
            气血 = qz(sx.属性.气血) * 8, -- 相对较弱
            伤害 = qz(sx.属性.伤害) * 0.8,
            法伤 = qz(sx.属性.法伤) * 1.2,
            速度 = qz(sx.属性.速度) * 1.5,
            防御 = 等级 * 8,
            法防 = 等级 * 6,
            技能 = {"水攻", "高级水属性吸收"}, -- 示例技能
            主动技能 = {"龙卷雨击"} -- 示例主动技能
        }
    end
    return 战斗单位
end

-- 示例：半天狗分身 - 可畏 (假设物理攻击型)
function 怪物属性:锻刀村半天狗可畏(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
    local sx = self:取属性(等级, "大唐官府", "物理")
    local xiulian = math.floor((等级 - 20) / 5) + 5

    战斗单位[1] = {
        名称 = "可畏",
        模型 = "树怪", -- 暂时使用树怪模型
        等级 = 等级,
        变异 = true,
        气血 = qz(sx.属性.气血) * 15,
        伤害 = qz(sx.属性.伤害) * 1.5,
        法伤 = qz(sx.属性.法伤),
        速度 = qz(sx.属性.速度) * 3,
        防御 = 等级 * 10,
        法防 = 等级 * 8,
        技能 = {"高级必杀", "高级强力", "感知"}, -- 示例技能
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian},
        门派 = sx.门派,
        AI战斗 = {AI = sx.智能}
    }
    -- 可以添加几个小怪
    for i = 2, 5 do
         local sx_minion = self:取属性(等级)
         战斗单位[i] = {
              名称 = "可畏的随从",
              模型 = "强盗", -- 示例模型
              等级 = 等级,
              气血 = qz(sx_minion.属性.气血) * 8,
              伤害 = qz(sx_minion.属性.伤害) * 1.1,
              法伤 = qz(sx_minion.属性.法伤) * 1.1,
              速度 = qz(sx_minion.属性.速度) * 2,
              防御 = 等级 * 7,
              法防 = 等级 * 5,
              主动技能 = sx_minion.技能组
         }
    end
    return 战斗单位
end

-- [新增] 添加副本NPC函数
function 副本_锻刀村之战:添加副本NPC(任务id, 名称, 模型, x, y)
    if 任务数据[任务id] == nil then return end

    local 队长id = 任务数据[任务id].领取人id
    local 地图id = 6100
    local NPC任务id = 任务id.."_NPC_"..名称.."_"..os.time().."_"..取随机数(1, 999)

    任务数据[NPC任务id] = {
        id = NPC任务id,
        起始 = os.time(),
        结束 = FUBEN_DURATION,
        玩家id = 队长id,
        队伍组 = {},
        名称 = 名称,
        模型 = 模型,
        x = x,
        y = y,
        方向 = 取随机数(0, 7),
        副本id = 队长id,
        地图编号 = 地图id,
        地图名称 = 取地图名称(地图id),
        小地图名称颜色 = 3,
        类型 = FUBEN_ID + 5 -- NPC类型
    }


    地图处理类:添加单位(NPC任务id)
    return NPC任务id
end

-- [新增] 副本内NPC对话处理函数
if not __GWdh111 then __GWdh111 = {} end

-- 添加玉壶召唤物的对话处理函数
__GWdh111[FUBEN_ID + 10] = function(连接id, 数字id, 序列, 标识, 地图)
    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if 副本数据.锻刀村之战.进行[副本id] and 副本数据.锻刀村之战.进行[副本id].进程 == STAGE.FIGHT_YUHUFISH then
        local 对话数据 = {}
        对话数据.模型 = 任务数据[标识].模型
        对话数据.名称 = 任务数据[标识].名称

        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "咕噜咕噜...（玉壶召唤物扭动着身体，眼睛里闪烁着嗜血的光芒）\n\n这些都是玉壶大人的血鬼术召唤出来的使魔，它们正在袭击锻刀村的居民！"
            对话数据.选项 = {"斩杀使魔", "暂避锋芒"}
        else
            对话数据.对话 = "咕噜咕噜...（玉壶召唤物正在与其他人战斗）"
        end

        return 对话数据
    end
end

-- 添加半天狗分身-可畏的对话处理函数
__GWdh111[FUBEN_ID + 21] = function(连接id, 数字id, 序列, 标识, 地图)
    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if 副本数据.锻刀村之战.进行[副本id] and 副本数据.锻刀村之战.进行[副本id].进程 == STAGE.FIGHT_HANTENGU_CLONES then
        local 对话数据 = {}
        对话数据.模型 = 任务数据[标识].模型
        对话数据.名称 = 任务数据[标识].名称

        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "我是半天狗的情绪分身「可畏」！\n\n恐惧吧！在我面前你们只是蝼蚁！我会让你们体验什么是真正的恐惧！"
            对话数据.选项 = {"全集中·水之呼吸", "暂避锋芒"}
        else
            对话数据.对话 = "可畏正在与其他剑士战斗，周围弥漫着恐怖的气息..."
        end

        return 对话数据
    end
end

-- 添加半天狗分身-可畏的对话选项处理函数
if not __GWdh222 then __GWdh222 = {} end
__GWdh222[FUBEN_ID + 21] = function(连接id, 数字id, 序号, 内容)
    local 事件 = 内容[1]
    local 标识 = 内容[2]
    local 名称 = 内容[3]

    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        常规提示(数字id, "#Y/副本数据异常，请尝试重新开启副本。")
        return
    end

    local 进程 = 副本数据.锻刀村之战.进行[副本id].进程

    if 进程 == STAGE.FIGHT_HANTENGU_CLONES then
        if 事件 == "全集中·水之呼吸" then
            if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                常规提示(数字id, "#Y/对方正在战斗中")
                return
            end
            if 取队伍人数(数字id) < MIN_PLAYERS and 调试模式 == false then
                常规提示(数字id, "#Y队伍人数低于" .. MIN_PLAYERS .. "人，无法进入战斗")
                return
            end

            -- 战斗前的对话
            常规提示(数字id, "#R可畏：#W恐惧吧！人类！")

            -- 设置战斗标记
            任务数据[玩家数据[数字id].地图单位.标识].战斗 = true

            -- 保存副本ID到任务数据中，以便战斗胜利后能够正确处理奖励
            任务数据[玩家数据[数字id].地图单位.标识].副本id = 副本id

            -- 创建战斗
            战斗准备类:创建战斗(数字id + 0, 160004, 玩家数据[数字id].地图单位.标识, "锻刀村半天狗可畏")

            -- 清除玩家地图单位引用
            玩家数据[数字id].地图单位 = nil
        elseif 事件 == "暂避锋芒" then
            常规提示(数字id, "#G你决定暂时避开这场战斗，寻找更好的时机。")
        end
    end
end

-- 添加半天狗分身-积怒的对话处理函数
__GWdh111[FUBEN_ID + 22] = function(连接id, 数字id, 序列, 标识, 地图)
    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if 副本数据.锻刀村之战.进行[副本id] and 副本数据.锻刀村之战.进行[副本id].进程 == STAGE.FIGHT_HANTENGU_CLONES then
        local 对话数据 = {}
        对话数据.模型 = 任务数据[标识].模型
        对话数据.名称 = 任务数据[标识].名称

        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "我是半天狗的情绪分身「积怒」！\n\n愤怒吧！我会将你们撕成碎片！你们的血肉将成为我的养料！"
            对话数据.选项 = {"全集中·炎之呼吸", "暂避锋芒"}
        else
            对话数据.对话 = "积怒正在与其他剑士战斗，周围充满了愤怒的气息..."
        end

        return 对话数据
    end
end

-- 添加半天狗分身-积怒的对话选项处理函数
if not __GWdh222 then __GWdh222 = {} end
__GWdh222[FUBEN_ID + 22] = function(连接id, 数字id, 序号, 内容)
    local 事件 = 内容[1]
    local 标识 = 内容[2]
    local 名称 = 内容[3]

    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        常规提示(数字id, "#Y/副本数据异常，请尝试重新开启副本。")
        return
    end

    local 进程 = 副本数据.锻刀村之战.进行[副本id].进程

    if 进程 == STAGE.FIGHT_HANTENGU_CLONES then
        if 事件 == "全集中·炎之呼吸" then
            if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                常规提示(数字id, "#Y/对方正在战斗中")
                return
            end
            if 取队伍人数(数字id) < MIN_PLAYERS and 调试模式 == false then
                常规提示(数字id, "#Y队伍人数低于" .. MIN_PLAYERS .. "人，无法进入战斗")
                return
            end

            -- 战斗前的对话
            常规提示(数字id, "#R积怒：#W愤怒吧！人类！")

            -- 设置战斗标记
            任务数据[玩家数据[数字id].地图单位.标识].战斗 = true

            -- 保存副本ID到任务数据中，以便战斗胜利后能够正确处理奖励
            任务数据[玩家数据[数字id].地图单位.标识].副本id = 副本id

            -- 创建战斗
            战斗准备类:创建战斗(数字id + 0, 160005, 玩家数据[数字id].地图单位.标识, "锻刀村半天狗积怒")

            -- 清除玩家地图单位引用
            玩家数据[数字id].地图单位 = nil
        elseif 事件 == "暂避锋芒" then
            常规提示(数字id, "#G你决定暂时避开这场战斗，寻找更好的时机。")
        end
    end
end

-- 添加半天狗分身-空喜的对话处理函数
__GWdh111[FUBEN_ID + 23] = function(连接id, 数字id, 序列, 标识, 地图)
    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if 副本数据.锻刀村之战.进行[副本id] and 副本数据.锻刀村之战.进行[副本id].进程 == STAGE.FIGHT_HANTENGU_CLONES then
        local 对话数据 = {}
        对话数据.模型 = 任务数据[标识].模型
        对话数据.名称 = 任务数据[标识].名称

        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "我是半天狗的情绪分身「空喜」！\n\n哈哈哈！多么有趣的猎物啊！让我们来玩个游戏吧，看看谁能活到最后！"
            对话数据.选项 = {"全集中·雷之呼吸", "暂避锋芒"}
        else
            对话数据.对话 = "空喜正在与其他剑士战斗，诡异的笑声回荡在空气中..."
        end

        return 对话数据
    end
end

-- 添加半天狗分身-空喜的对话选项处理函数
if not __GWdh222 then __GWdh222 = {} end
__GWdh222[FUBEN_ID + 23] = function(连接id, 数字id, 序号, 内容)
    local 事件 = 内容[1]
    local 标识 = 内容[2]
    local 名称 = 内容[3]

    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        常规提示(数字id, "#Y/副本数据异常，请尝试重新开启副本。")
        return
    end

    local 进程 = 副本数据.锻刀村之战.进行[副本id].进程

    if 进程 == STAGE.FIGHT_HANTENGU_CLONES then
        if 事件 == "全集中·雷之呼吸" then
            if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                常规提示(数字id, "#Y/对方正在战斗中")
                return
            end
            if 取队伍人数(数字id) < MIN_PLAYERS and 调试模式 == false then
                常规提示(数字id, "#Y队伍人数低于" .. MIN_PLAYERS .. "人，无法进入战斗")
                return
            end

            -- 战斗前的对话
            常规提示(数字id, "#R空喜：#W哈哈哈！游戏开始了！")

            -- 设置战斗标记
            任务数据[玩家数据[数字id].地图单位.标识].战斗 = true

            -- 保存副本ID到任务数据中，以便战斗胜利后能够正确处理奖励
            任务数据[玩家数据[数字id].地图单位.标识].副本id = 副本id

            -- 创建战斗
            战斗准备类:创建战斗(数字id + 0, 160006, 玩家数据[数字id].地图单位.标识, "锻刀村半天狗空喜")

            -- 清除玩家地图单位引用
            玩家数据[数字id].地图单位 = nil
        elseif 事件 == "暂避锋芒" then
            常规提示(数字id, "#G你决定暂时避开这场战斗，寻找更好的时机。")
        end
    end
end

-- 添加半天狗分身-哀绝的对话处理函数
__GWdh111[FUBEN_ID + 24] = function(连接id, 数字id, 序列, 标识, 地图)
    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if 副本数据.锻刀村之战.进行[副本id] and 副本数据.锻刀村之战.进行[副本id].进程 == STAGE.FIGHT_HANTENGU_CLONES then
        local 对话数据 = {}
        对话数据.模型 = 任务数据[标识].模型
        对话数据.名称 = 任务数据[标识].名称

        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "我是半天狗的情绪分身「哀绝」！\n\n悲伤吧！你们的命运已经注定，只有死亡才能结束你们的痛苦..."
            对话数据.选项 = {"全集中·风之呼吸", "暂避锋芒"}
        else
            对话数据.对话 = "哀绝正在与其他剑士战斗，悲伤的气息笼罩着周围..."
        end

        return 对话数据
    end
end

-- 添加玉壶本体的对话处理函数
__GWdh111[FUBEN_ID + 30] = function(连接id, 数字id, 序列, 标识, 地图)
    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if 副本数据.锻刀村之战.进行[副本id] and 副本数据.锻刀村之战.进行[副本id].进程 == STAGE.BOSS_YUHUBODY then
        local 对话数据 = {}
        对话数据.模型 = 任务数据[标识].模型
        对话数据.名称 = 任务数据[标识].名称

        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "去死！\n\n我的血鬼术「血沸」会让你们的血液沸腾而死！「水虎招来」会将你们撕成碎片！\n\n你们这些可悲的人类，准备好迎接痛苦了吗？"
            对话数据.选项 = { "拔刀迎战", "暂避锋芒" }
        else
            对话数据.对话 = "玉壶正在与其他剑士战斗，血液在空中飞舞形成诡异的形状..."
        end

        return 对话数据
    end
end

-- 添加玉壶召唤物的对话选项处理函数
if not __GWdh222 then __GWdh222 = {} end
__GWdh222[FUBEN_ID + 10] = function(连接id, 数字id, 序号, 内容)
    local 事件 = 内容[1]
    local 标识 = 内容[2]
    local 名称 = 内容[3]

    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        常规提示(数字id, "#Y/副本数据异常，请尝试重新开启副本。")
        return
    end

    local 进程 = 副本数据.锻刀村之战.进行[副本id].进程

    if 进程 == STAGE.FIGHT_YUHUFISH then
        if 事件 == "斩杀使魔" then
            if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                常规提示(数字id, "#Y/对方正在战斗中")
                return
            end
            if 取队伍人数(数字id) < MIN_PLAYERS and 调试模式 == false then
                常规提示(数字id, "#Y队伍人数低于" .. MIN_PLAYERS .. "人，无法进入战斗")
                return
            end

            -- 战斗前的对话
            常规提示(数字id, "#R玉壶召唤物：#W咕噜咕噜...（发出威胁的声音）")

            -- 设置战斗标记
            任务数据[玩家数据[数字id].地图单位.标识].战斗 = true

            -- 保存副本ID到任务数据中，以便战斗胜利后能够正确处理奖励
            任务数据[玩家数据[数字id].地图单位.标识].副本id = 副本id

            -- 创建战斗
            战斗准备类:创建战斗(数字id + 0, 160000, 玩家数据[数字id].地图单位.标识, "锻刀村玉壶鱼怪")

            -- 清除玩家地图单位引用
            玩家数据[数字id].地图单位 = nil
        elseif 事件 == "暂避锋芒" then
            常规提示(数字id, "#G你决定暂时避开这场战斗，寻找更好的时机。")
        end
    end
end

-- 添加玉壶本体的对话选项处理函数
if not __GWdh222 then __GWdh222 = {} end
__GWdh222[FUBEN_ID + 30] = function(连接id, 数字id, 序号, 内容)
    local 事件 = 内容[1]
    local 标识 = 内容[2]
    local 名称 = 内容[3]

    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        常规提示(数字id, "#Y/副本数据异常，请尝试重新开启副本。")
        return
    end

    local 进程 = 副本数据.锻刀村之战.进行[副本id].进程

    if 进程 == STAGE.BOSS_YUHUBODY then
        if 事件 == "拔刀迎战" then
            if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                常规提示(数字id, "#Y/对方正在战斗中")
                return
            end
            if 玩家数据[数字id].队伍 == 0 and 调试模式 == false then
                常规提示(数字id, "#Y必须组队才能触发该活动")
                return
            end
            if 取队伍人数(数字id) < MIN_PLAYERS and 调试模式 == false then
                常规提示(数字id, string.format("#Y/挑战玉壶本体最少要有%d人", MIN_PLAYERS))
                return
            end
            if 取等级要求(数字id, MIN_LEVEL) == false and 调试模式 == false then
                常规提示(数字id, string.format("#Y/挑战玉壶本体至少要达到%d级", MIN_LEVEL))
                return
            end

            -- 战斗前的对话
            常规提示(数字id, "#R玉壶：#W来吧，人类！让我看看你们的血液沸腾的样子！")

            -- 设置战斗标记
            任务数据[玩家数据[数字id].地图单位.标识].战斗 = true

            -- 保存副本ID到任务数据中，以便战斗胜利后能够正确处理奖励
            任务数据[玩家数据[数字id].地图单位.标识].副本id = 副本id

            -- 创建战斗
            战斗准备类:创建战斗(数字id + 0, 160001, 玩家数据[数字id].地图单位.标识)

            -- 清除玩家地图单位引用
            玩家数据[数字id].地图单位 = nil
        elseif 事件 == "暂避锋芒" then
            常规提示(数字id, "#G你决定暂时避开这场战斗，寻找更好的时机。")
        end
    end
end

-- 添加半天狗分身-哀绝的对话选项处理函数
if not __GWdh222 then __GWdh222 = {} end
__GWdh222[FUBEN_ID + 24] = function(连接id, 数字id, 序号, 内容)
    local 事件 = 内容[1]
    local 标识 = 内容[2]
    local 名称 = 内容[3]

    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        常规提示(数字id, "#Y/副本数据异常，请尝试重新开启副本。")
        return
    end

    local 进程 = 副本数据.锻刀村之战.进行[副本id].进程

    if 进程 == STAGE.FIGHT_HANTENGU_CLONES then
        if 事件 == "全集中·风之呼吸" then
            if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                常规提示(数字id, "#Y/对方正在战斗中")
                return
            end
            if 取队伍人数(数字id) < MIN_PLAYERS and 调试模式 == false then
                常规提示(数字id, "#Y队伍人数低于" .. MIN_PLAYERS .. "人，无法进入战斗")
                return
            end

            -- 战斗前的对话
            常规提示(数字id, "#R哀绝：#W悲伤吧！人类！")

            -- 设置战斗标记
            任务数据[玩家数据[数字id].地图单位.标识].战斗 = true

            -- 保存副本ID到任务数据中，以便战斗胜利后能够正确处理奖励
            任务数据[玩家数据[数字id].地图单位.标识].副本id = 副本id

            -- 创建战斗
            战斗准备类:创建战斗(数字id + 0, 160007, 玩家数据[数字id].地图单位.标识, "锻刀村半天狗哀绝")

            -- 清除玩家地图单位引用
            玩家数据[数字id].地图单位 = nil
        elseif 事件 == "暂避锋芒" then
            常规提示(数字id, "#G你决定暂时避开这场战斗，寻找更好的时机。")
        end
    end
end

-- 添加憎珀天的对话处理函数
__GWdh111[FUBEN_ID + 40] = function(连接id, 数字id, 序列, 标识, 地图)
    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if 副本数据.锻刀村之战.进行[副本id] and 副本数据.锻刀村之战.进行[副本id].进程 == STAGE.BOSS_ZOHAKUTEN then
        local 对话数据 = {}
        对话数据.模型 = 任务数据[标识].模型
        对话数据.名称 = 任务数据[标识].名称

        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "哈哈哈哈！你们竟然击败了我的四个分身！\n\n但这只是开始！我是上弦之肆·半天狗的最强形态——憎珀天！\n\n我的情绪操控「怒髪天」会让你们在绝望中死去！鬼杀队的杂碎们，你们的血将成为无惨大人的祭品！"
            对话数据.选项 = {"全集中·日之呼吸", "暂避锋芒"}
        else
            对话数据.对话 = "憎珀天正在与其他剑士战斗，强大的鬼气让周围的空气都变得扭曲..."
        end

        return 对话数据
    end
end

-- 添加憎珀天的对话选项处理函数
if not __GWdh222 then __GWdh222 = {} end
__GWdh222[FUBEN_ID + 40] = function(连接id, 数字id, 序号, 内容)
    local 事件 = 内容[1]
    local 标识 = 内容[2]
    local 名称 = 内容[3]

    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        常规提示(数字id, "#Y/副本数据异常，请尝试重新开启副本。")
        return
    end

    local 进程 = 副本数据.锻刀村之战.进行[副本id].进程

    if 进程 == STAGE.BOSS_ZOHAKUTEN then
        if 事件 == "全集中·日之呼吸" then
            if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                常规提示(数字id, "#Y/对方正在战斗中")
                return
            end
            if 取队伍人数(数字id) < MIN_PLAYERS and 调试模式 == false then
                常规提示(数字id, "#Y队伍人数低于" .. MIN_PLAYERS .. "人，无法进入战斗")
                return
            end

            -- 战斗前的对话
            常规提示(数字id, "#R憎珀天：#W来吧！让我看看你们的绝望！")

            -- 设置战斗标记
            任务数据[玩家数据[数字id].地图单位.标识].战斗 = true

            -- 保存副本ID到任务数据中，以便战斗胜利后能够正确处理奖励
            任务数据[玩家数据[数字id].地图单位.标识].副本id = 副本id

            -- 创建战斗
            战斗准备类:创建战斗(数字id + 0, 160002, 玩家数据[数字id].地图单位.标识, "锻刀村憎珀天")

            -- 清除玩家地图单位引用
            玩家数据[数字id].地图单位 = nil
        elseif 事件 == "暂避锋芒" then
            常规提示(数字id, "#G你决定暂时避开这场战斗，寻找更好的时机。")
        end
    end
end

-- 添加半天狗本体的对话处理函数
__GWdh111[FUBEN_ID + 50] = function(连接id, 数字id, 序列, 标识, 地图)
    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if 副本数据.锻刀村之战.进行[副本id] and 副本数据.锻刀村之战.进行[副本id].进程 == STAGE.FIND_HANTENGU_BODY then
        local 对话数据 = {}
        对话数据.模型 = 任务数据[标识].模型
        对话数据.名称 = 任务数据[标识].名称

        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "不...不可能！你们竟然击败了我的最强形态！\n\n但这还没结束！只要我的本体还在，我就能无限再生！\n\n我是上弦之肆·半天狗，无惨大人赐予我的力量不是你们这些人类能够理解的！"
            对话数据.选项 = {"斩首示众", "暂避锋芒"}
        else
            对话数据.对话 = "半天狗本体正在与其他剑士战斗，它的身体不断变形，试图逃脱..."
        end

        return 对话数据
    end
end

-- 添加半天狗本体的对话选项处理函数
if not __GWdh222 then __GWdh222 = {} end
__GWdh222[FUBEN_ID + 50] = function(连接id, 数字id, 序号, 内容)
    local 事件 = 内容[1]
    local 标识 = 内容[2]
    local 名称 = 内容[3]

    local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        常规提示(数字id, "#Y/副本数据异常，请尝试重新开启副本。")
        return
    end

    local 进程 = 副本数据.锻刀村之战.进行[副本id].进程

    if 进程 == STAGE.FIND_HANTENGU_BODY then
        if 事件 == "斩首示众" then
            if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                常规提示(数字id, "#Y/对方正在战斗中")
                return
            end
            if 取队伍人数(数字id) < MIN_PLAYERS and 调试模式 == false then
                常规提示(数字id, "#Y队伍人数低于" .. MIN_PLAYERS .. "人，无法进入战斗")
                return
            end

            -- 战斗前的对话
            常规提示(数字id, "#R半天狗本体：#W不！不要过来！我不想死！")

            -- 设置战斗标记
            任务数据[玩家数据[数字id].地图单位.标识].战斗 = true

            -- 保存副本ID到任务数据中，以便战斗胜利后能够正确处理奖励
            任务数据[玩家数据[数字id].地图单位.标识].副本id = 副本id

            -- 创建战斗
            战斗准备类:创建战斗(数字id + 0, 160003, 玩家数据[数字id].地图单位.标识, "锻刀村半天狗本体")

            -- 清除玩家地图单位引用
            玩家数据[数字id].地图单位 = nil
        elseif 事件 == "暂避锋芒" then
            常规提示(数字id, "#G你决定暂时避开这场战斗，寻找更好的时机。")
        end
    end
end

-- 村长NPC对话处理函数
__GWdh111[FUBEN_ID + 5] = function(连接id, 数字id, 序列, 标识, 地图)
    local 对话数据 = {}
    if not 任务数据[标识] then return end -- Added nil check for 任务数据[标识]
    对话数据.模型 = 任务数据[标识].模型
    对话数据.名称 = 任务数据[标识].名称



    -- 检查玩家是否有副本任务
    local 任务id = 玩家数据[数字id].角色:取任务(FUBEN_ID)
    if 任务id == 0 then
        添加最后对话(数字id, "我好像不认识你吧？？？")
        return
    end

    local 副本id = (任务数据[任务id] or {}).副本id -- Safely access 副本id
    if (任务数据[玩家数据[数字id].角色:取任务(FUBEN_ID)] or {}).副本id ~= 副本id then
      添加最后对话(数字id, "我好像不认识你吧？？？")
      return
    end

    -- 根据NPC名称提供不同的对话
    if 对话数据.名称 == "锻刀村村长" then
        对话数据.对话 = "感谢鬼杀队的支援！我们锻刀村是制作日轮刀的重要之地，最近遭到了上弦之鬼的袭击。\n\n上弦之伍·玉壶已经召唤出了大量使魔，请先击退它们！"
        对话数据.选项 = {"我们这就去", "能详细说说情况吗", "测试红色闪烁"}
    elseif 对话数据.名称 == "玉壶召唤物" then
        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "咕噜咕噜...（玉壶召唤物扭动着身体，眼睛里闪烁着嗜血的光芒）\n\n这些都是玉壶大人的血鬼术召唤出来的使魔，它们正在袭击锻刀村的居民！"
            对话数据.选项 = {"斩杀使魔"}
        else
            对话数据.对话 = "咕噜咕噜...（玉壶召唤物正在与其他人战斗）"
        end
    elseif 对话数据.名称 == "玉壶" or 对话数据.名称 == "玉壶本体" then
        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "去死！\n\n我的血鬼术「血沸」会让你们的血液沸腾而死！「水虎招来」会将你们撕成碎片！\n\n你们这些可悲的人类，准备好迎接痛苦了吗？"
            对话数据.选项 = { "拔刀迎战", "暂避锋芒" }
        else
            对话数据.对话 = "玉壶正在与其他剑士战斗，血液在空中飞舞形成诡异的形状..."
        end
    elseif 对话数据.名称 == "可畏" then
        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "我是半天狗的情绪分身「可畏」！\n\n恐惧吧！在我面前你们只是蝼蚁！我会让你们体验什么是真正的恐惧！"
            对话数据.选项 = {"全集中·水之呼吸"}
        else
            对话数据.对话 = "可畏正在与其他剑士战斗，周围弥漫着恐怖的气息..."
        end
    elseif 对话数据.名称 == "积怒" then
        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "我是半天狗的情绪分身「积怒」！\n\n愤怒吧！我会将你们撕成碎片！你们的血肉将成为我的养料！"
            对话数据.选项 = {"全集中·炎之呼吸"}
        else
            对话数据.对话 = "积怒正在与其他剑士战斗，周围充满了愤怒的气息..."
        end
    elseif 对话数据.名称 == "空喜" then
        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "我是半天狗的情绪分身「空喜」！\n\n哈哈哈！多么有趣的猎物啊！让我们来玩个游戏吧，看看谁能活到最后！"
            对话数据.选项 = {"全集中·雷之呼吸"}
        else
            对话数据.对话 = "空喜正在与其他剑士战斗，诡异的笑声回荡在空气中..."
        end
    elseif 对话数据.名称 == "哀绝" then
        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "我是半天狗的情绪分身「哀绝」！\n\n悲伤吧！你们的命运已经注定，只有死亡才能结束你们的痛苦..."
            对话数据.选项 = {"全集中·风之呼吸"}
        else
            对话数据.对话 = "哀绝正在与其他剑士战斗，悲伤的气息笼罩着周围..."
        end
    elseif 对话数据.名称 == "憎珀天" then
        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "哈哈哈哈！你们竟然击败了我的四个分身！\n\n但这只是开始！我是上弦之肆·半天狗的最强形态——憎珀天！\n\n我的情绪操控「怒髪天」会让你们在绝望中死去！鬼杀队的杂碎们，你们的血将成为无惨大人的祭品！"
            对话数据.选项 = { "全集中·日之呼吸", "暂避锋芒" }
        else
            对话数据.对话 = "憎珀天正在与其他剑士战斗，强大的鬼气让周围的空气都变得扭曲..."
        end
    elseif 对话数据.名称 == "半天狗本体" then
        if 任务数据[标识].战斗 == nil then
            对话数据.对话 = "不...不可能！你们竟然击败了我的最强形态！\n\n但这还没结束！只要我的本体还在，我就能无限再生！\n\n我是上弦之肆·半天狗，无惨大人赐予我的力量不是你们这些人类能够理解的！"
            对话数据.选项 = { "斩首示众", "暂避锋芒" }
        else
            对话数据.对话 = "半天狗本体正在与其他剑士战斗，它的身体不断变形，试图逃脱..."
        end
    elseif 对话数据.名称 == "副本结束NPC" then
        -- 副本结束NPC的对话内容由 __GWdh111[FUBEN_ID + 60] 函数处理
        -- 这里不设置对话内容，避免重复定义
        return 对话数据
    else
        -- 其他NPC对话或非当前阶段的对话可以返回默认内容或不处理
        对话数据.对话 = "锻刀村正在遭受上弦之鬼的袭击，请尽快击退敌人！"
        对话数据.选项 = {"我明白了"}
    end

    return 对话数据
end

-- [新增] 副本内NPC选项处理函数
if not __GWdh222 then __GWdh222 = {} end
__GWdh222[FUBEN_ID + 5] = function(连接id, 数字id, 序号, 内容)
    local 事件 = 内容[1]
    local 标识 = 内容[2]
    local 名称 = 内容[3]



    -- 检查玩家是否有副本任务
    local 任务id = 玩家数据[数字id].角色:取任务(FUBEN_ID)
    if 任务id == 0 then
        常规提示(数字id, "#Y/你没有正在进行的锻刀村之战副本任务。")
        return
    end

    local 副本id = (任务数据[任务id] or {}).副本id -- Safely access 副本id
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or 副本数据.锻刀村之战.进行[副本id] == nil then
        常规提示(数字id, "#Y/副本数据异常，请尝试重新开启副本。")
        return
    end

    local 副本实例 = 副本数据.锻刀村之战.进行[副本id]
    local 进程 = 副本实例.进程

    -- 根据NPC名称和事件处理不同的选项
    if 名称 == "锻刀村村长" then
        if 进程 == STAGE.ENTER_VILLAGE then
            if 事件 == "我们这就去" then
                常规提示(数字id, "#G村长：请击败那些玉壶召唤物，它们是玉壶的血鬼术召唤出来的使魔！")
                -- 推进到下一阶段
                副本实例.进程 = STAGE.FIGHT_YUHUFISH
                任务数据[任务id].进程 = STAGE.FIGHT_YUHUFISH
                副本实例.阶段数据 = {鱼怪击杀数 = 0, 鱼怪总数 = 10} -- 初始化阶段数据
                任务数据[任务id].阶段数据 = {鱼怪击杀数 = 0, 鱼怪总数 = 10} -- 同步到任务数据
                -- 添加刷新第一波鱼怪的逻辑 (这里需要调用设置锻刀村副本函数来刷怪)
                设置锻刀村副本(副本id)
                刷新队伍任务追踪(数字id)
            elseif 事件 == "能详细说说情况吗" then
                常规提示(数字id, "#G村长：上弦之伍·玉壶和上弦之肆·半天狗突然袭击了我们村子。玉壶能操控水流，而半天狗能分裂出多个情绪分身。他们的目标是摧毁我们的锻刀工坊，阻止日轮刀的制作！")
            elseif 事件 == "测试红色闪烁" then
                -- 发送红色闪烁效果
                发送数据(玩家数据[数字id].连接id, 6561, true)
                常规提示(数字id, "#Y已触发红色闪烁效果，请观察屏幕")

                -- 如果是队长，则向整个队伍发送红色闪烁
                if 玩家数据[数字id].队伍 > 0 and 玩家数据[数字id].队长 then
                    for _, 队员id in ipairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
                        if 队员id ~= 数字id then -- 不重复发送给队长
                            发送数据(玩家数据[队员id].连接id, 6561, true)
                            常规提示(队员id, "#Y队长触发了红色闪烁效果测试，请观察屏幕")
                        end
                    end
                end
            end
        end
    elseif 名称 == "玉壶召唤物" then
        if 进程 == STAGE.FIGHT_YUHUFISH then
            if 事件 == "斩杀使魔" then
                if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                    常规提示(数字id, "#Y/对方正在战斗中")
                    return
                end
                if 玩家数据[数字id].队伍 == 0 then
                    常规提示(数字id, "#Y必须组队才能触发该活动")
                    return
                end
                if 取队伍人数(数字id) < MIN_PLAYERS then
                    常规提示(数字id, string.format("#Y/挑战玉壶召唤物最少要有%d人", MIN_PLAYERS))
                    return
                end
                if 取等级要求(数字id, MIN_LEVEL) == false then
                    常规提示(数字id, string.format("#Y/挑战玉壶召唤物至少要达到%d级", MIN_LEVEL))
                    return
                end
                任务数据[玩家数据[数字id].地图单位.标识].战斗 = true
                战斗准备类:创建战斗(数字id + 0, 160000, 玩家数据[数字id].地图单位.标识)
            end
        end
    elseif 名称 == "可畏" then
        if 进程 == STAGE.FIGHT_HANTENGU_CLONES then
            if 事件 == "全集中·水之呼吸" then
                if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                    常规提示(数字id, "#Y/对方正在战斗中")
                    return
                end
                if 玩家数据[数字id].队伍 == 0 then
                    常规提示(数字id, "#Y必须组队才能触发该活动")
                    return
                end
                if 取队伍人数(数字id) < MIN_PLAYERS then
                    常规提示(数字id, string.format("#Y/挑战半天狗分身最少要有%d人", MIN_PLAYERS))
                    return
                end
                if 取等级要求(数字id, MIN_LEVEL) == false then
                    常规提示(数字id, string.format("#Y/挑战半天狗分身至少要达到%d级", MIN_LEVEL))
                    return
                end
                任务数据[玩家数据[数字id].地图单位.标识].战斗 = true
                战斗准备类:创建战斗(数字id + 0, 160004, 玩家数据[数字id].地图单位.标识)
            end
        end
    elseif 名称 == "积怒" then
        if 进程 == STAGE.FIGHT_HANTENGU_CLONES then
            if 事件 == "全集中·炎之呼吸" then
                if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                    常规提示(数字id, "#Y/对方正在战斗中")
                    return
                end
                if 玩家数据[数字id].队伍 == 0 then
                    常规提示(数字id, "#Y必须组队才能触发该活动")
                    return
                end
                if 取队伍人数(数字id) < MIN_PLAYERS then
                    常规提示(数字id, string.format("#Y/挑战半天狗分身最少要有%d人", MIN_PLAYERS))
                    return
                end
                if 取等级要求(数字id, MIN_LEVEL) == false then
                    常规提示(数字id, string.format("#Y/挑战半天狗分身至少要达到%d级", MIN_LEVEL))
                    return
                end
                任务数据[玩家数据[数字id].地图单位.标识].战斗 = true
                战斗准备类:创建战斗(数字id + 0, 160005, 玩家数据[数字id].地图单位.标识)
            end
        end
    elseif 名称 == "空喜" then
        if 进程 == STAGE.FIGHT_HANTENGU_CLONES then
            if 事件 == "全集中·雷之呼吸" then
                if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                    常规提示(数字id, "#Y/对方正在战斗中")
                    return
                end
                if 玩家数据[数字id].队伍 == 0 then
                    常规提示(数字id, "#Y必须组队才能触发该活动")
                    return
                end
                if 取队伍人数(数字id) < MIN_PLAYERS then
                    常规提示(数字id, string.format("#Y/挑战半天狗分身最少要有%d人", MIN_PLAYERS))
                    return
                end
                if 取等级要求(数字id, MIN_LEVEL) == false then
                    常规提示(数字id, string.format("#Y/挑战半天狗分身至少要达到%d级", MIN_LEVEL))
                    return
                end
                任务数据[玩家数据[数字id].地图单位.标识].战斗 = true
                战斗准备类:创建战斗(数字id + 0, 160006, 玩家数据[数字id].地图单位.标识)
            end
        end
    elseif 名称 == "哀绝" then
        if 进程 == STAGE.FIGHT_HANTENGU_CLONES then
            if 事件 == "全集中·风之呼吸" then
                if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                    常规提示(数字id, "#Y/对方正在战斗中")
                    return
                end
                if 玩家数据[数字id].队伍 == 0 then
                    常规提示(数字id, "#Y必须组队才能触发该活动")
                    return
                end
                if 取队伍人数(数字id) < MIN_PLAYERS then
                    常规提示(数字id, string.format("#Y/挑战半天狗分身最少要有%d人", MIN_PLAYERS))
                    return
                end
                if 取等级要求(数字id, MIN_LEVEL) == false then
                    常规提示(数字id, string.format("#Y/挑战半天狗分身至少要达到%d级", MIN_LEVEL))
                    return
                end
                任务数据[玩家数据[数字id].地图单位.标识].战斗 = true
                战斗准备类:创建战斗(数字id + 0, 160007, 玩家数据[数字id].地图单位.标识)
            end
        end
    elseif (名称 == "玉壶" or 名称 == "玉壶本体") then
        if 进程 == STAGE.BOSS_YUHUBODY then
            if 事件 == "拔刀迎战" then
                 if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                    常规提示(数字id, "#Y/对方正在战斗中")
                    return
                end
                if 玩家数据[数字id].队伍 == 0 and 调试模式 == false then
                    常规提示(数字id, "#Y必须组队才能触发该活动")
                    return
                end
                if 取队伍人数(数字id) < MIN_PLAYERS and 调试模式 == false then
                    常规提示(数字id, string.format("#Y/挑战玉壶本体最少要有%d人", MIN_PLAYERS))
                    return
                end
                if 取等级要求(数字id, MIN_LEVEL) == false and 调试模式 == false then
                    常规提示(数字id, string.format("#Y/挑战玉壶本体至少要达到%d级", MIN_LEVEL))
                    return
                end

                -- 战斗前的对话
                常规提示(数字id, "#R玉壶：#W来吧，人类！让我看看你们的血液沸腾的样子！")

                -- 设置战斗标记
                任务数据[玩家数据[数字id].地图单位.标识].战斗 = true

                -- 创建战斗
                战斗准备类:创建战斗(数字id + 0, 160001, 玩家数据[数字id].地图单位.标识)

                -- 清除玩家地图单位引用
                玩家数据[数字id].地图单位 = nil
            elseif 事件 == "暂避锋芒" then
                常规提示(数字id, "#G你决定暂时避开这场战斗，寻找更好的时机。")
            end
        end
    elseif 名称 == "憎珀天" then
        if 进程 == STAGE.BOSS_ZOHAKUTEN then
            if 事件 == "全集中·日之呼吸" then
                 if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                    常规提示(数字id, "#Y/对方正在战斗中")
                    return
                end
                if 玩家数据[数字id].队伍 == 0 then
                    常规提示(数字id, "#Y必须组队才能触发该活动")
                    return
                end
                if 取队伍人数(数字id) < MIN_PLAYERS then
                    常规提示(数字id, string.format("#Y/挑战憎珀天最少要有%d人", MIN_PLAYERS))
                    return
                end
                if 取等级要求(数字id, MIN_LEVEL) == false then
                    常规提示(数字id, string.format("#Y/挑战憎珀天至少要达到%d级", MIN_LEVEL))
                    return
                end
                任务数据[玩家数据[数字id].地图单位.标识].战斗 = true
                战斗准备类:创建战斗(数字id + 0, 160002, 玩家数据[数字id].地图单位.标识)
            end
        end
    elseif 名称 == "半天狗本体" then
        if 进程 == STAGE.FIND_HANTENGU_BODY then
            if 事件 == "斩首示众" then
                 if 任务数据[玩家数据[数字id].地图单位.标识].战斗 ~= nil then
                    常规提示(数字id, "#Y/对方正在战斗中")
                    return
                end
                if 玩家数据[数字id].队伍 == 0 then
                    常规提示(数字id, "#Y必须组队才能触发该活动")
                    return
                end
                 if 取队伍人数(数字id) < MIN_PLAYERS then
                    常规提示(数字id, string.format("#Y/挑战半天狗本体最少要有%d人", MIN_PLAYERS))
                    return
                end
                if 取等级要求(数字id, MIN_LEVEL) == false then
                    常规提示(数字id, string.format("#Y/挑战半天狗本体至少要达到%d级", MIN_LEVEL))
                    return
                end
                任务数据[玩家数据[数字id].地图单位.标识].战斗 = true
                战斗准备类:创建战斗(数字id + 0, 160003, 玩家数据[数字id].地图单位.标识)
            end
        end
    elseif 名称 == "副本结束NPC" then
        if 进程 == STAGE.COMPLETE then
            -- 不再处理"接受嘉奖"事件，玩家只能选择获取战魄或陨铁
        end
    end
end

-- 其他分身 (积怒、空喜、哀绝) 的定义类似，可以调整模型、门派、技能等
-- 示例：半天狗分身 - 积怒 (假设防御/辅助型)
function 怪物属性:锻刀村半天狗积怒(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
    local sx = self:取属性(等级, "化生寺", "辅助") -- 假设积怒是辅助
    local xiulian = math.floor((等级 - 20) / 5) + 6

    战斗单位[1] = {
        名称 = "积怒",
        模型 = "树怪", -- 暂时使用树怪模型
        等级 = 等级,
        变异 = true,
        气血 = qz(sx.属性.气血) * 20, -- 血量稍高
        伤害 = qz(sx.属性.伤害) * 0.8,
        法伤 = qz(sx.属性.法伤) * 1.2,
        速度 = qz(sx.属性.速度) * 1.5,
        防御 = 等级 * 12, -- 防御较高
        法防 = 等级 * 10,
        技能 = {"高级防御", "高级再生", "感知", "金刚护法"}, -- 示例技能
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian-2},
        门派 = sx.门派,
        AI战斗 = {AI = "辅助治疗"}
    }
    -- 可以添加几个小怪
    for i = 2, 5 do
         local sx_minion = self:取属性(等级)
         战斗单位[i] = {
              名称 = "积怒的随从",
              模型 = "护卫", -- 示例模型
              等级 = 等级,
              气血 = qz(sx_minion.属性.气血) * 10,
              伤害 = qz(sx_minion.属性.伤害) * 1.0,
              法伤 = qz(sx_minion.属性.法伤) * 1.0,
              速度 = qz(sx_minion.属性.速度) * 1.8,
              防御 = 等级 * 8,
              法防 = 等级 * 6,
              主动技能 = sx_minion.技能组
         }
    end
    return 战斗单位
end

-- 添加初始化方法，符合项目架构

-- [新增] 半天狗分身 - 空喜 (假设敏捷/法系型)
function 怪物属性:锻刀村半天狗空喜(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
    local sx = self:取属性(等级, "方寸山", "法系") -- 假设空喜是法系
    local xiulian = math.floor((等级 - 20) / 5) + 7

    战斗单位[1] = {
        名称 = "空喜",
        模型 = "树怪", -- 暂时使用树怪模型
        等级 = 等级,
        变异 = true,
        气血 = qz(sx.属性.气血) * 12, -- 血量较低
        伤害 = qz(sx.属性.伤害) * 0.7,
        法伤 = qz(sx.属性.法伤) * 2.0, -- 法伤较高
        速度 = qz(sx.属性.速度) * 4.0, -- 速度很高
        防御 = 等级 * 8,
        法防 = 等级 * 9,
        技能 = {"高级法术暴击", "高级敏捷", "高级法术波动", "高级魔之心"}, -- 示例技能
        修炼 = {物抗 = xiulian-2, 法抗 = xiulian, 攻修 = xiulian+2},
        门派 = sx.门派,
        AI战斗 = {AI = "法术输出"}
    }
    -- 添加几个小怪
    for i = 2, 4 do
         local sx_minion = self:取属性(等级)
         战斗单位[i] = {
              名称 = "空喜的随从",
              模型 = "巡逻妖", -- 示例模型
              等级 = 等级,
              气血 = qz(sx_minion.属性.气血) * 7,
              伤害 = qz(sx_minion.属性.伤害) * 0.9,
              法伤 = qz(sx_minion.属性.法伤) * 1.3,
              速度 = qz(sx_minion.属性.速度) * 3.0, -- 速度较高
              防御 = 等级 * 6,
              法防 = 等级 * 7,
              主动技能 = {"落岩", "飞沙走石"} -- 示例技能
         }
    end
    return 战斗单位
end

-- [新增] 半天狗分身 - 哀绝 (假设混合型)
function 怪物属性:锻刀村半天狗哀绝(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
    local sx = self:取属性(等级, "阴曹地府", "混合") -- 假设哀绝是混合型
    local xiulian = math.floor((等级 - 20) / 5) + 7

    战斗单位[1] = {
        名称 = "哀绝",
        模型 = "树怪", -- 暂时使用树怪模型
        等级 = 等级,
        变异 = true,
        气血 = qz(sx.属性.气血) * 18,
        伤害 = qz(sx.属性.伤害) * 1.3,
        法伤 = qz(sx.属性.法伤) * 1.3,
        速度 = qz(sx.属性.速度) * 2.5,
        防御 = 等级 * 11,
        法防 = 等级 * 11,
        技能 = {"高级连击", "高级法术连击", "高级吸血", "高级反击", "高级鬼魂术"}, -- 示例技能
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian},
        门派 = sx.门派,
        AI战斗 = {AI = "混合输出"}
    }
    -- 添加几个小怪
    for i = 2, 5 do
         local sx_minion = self:取属性(等级)
         战斗单位[i] = {
             名称 = "哀绝的随从",
             模型 = "鬼卒", -- 示例模型
             等级 = 等级,
             气血 = qz(sx_minion.属性.气血) * 9,
             伤害 = qz(sx_minion.属性.伤害) * 1.2,
             法伤 = qz(sx_minion.属性.法伤) * 1.2,
             速度 = qz(sx_minion.属性.速度) * 2.2,
             防御 = 等级 * 7,
             法防 = 等级 * 7,
             主动技能 = {"尸腐毒", "鬼火"} -- 示例技能
         }
    end
    return 战斗单位
end

-- [新增] 半天狗本体 (最终Boss)
function 怪物属性:锻刀村半天狗本体(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
    local sx = self:取属性(等级, "盘丝洞", "法系") -- 假设半天狗本体是法系
    local xiulian = math.floor((等级 - 20) / 5) + 5 -- 本体修炼较低，因为是弱点

    战斗单位[1] = {
        名称 = "半天狗本体",
        模型 = "树怪", -- 暂时使用树怪模型
        等级 = 等级,
        变异 = true,
        气血 = qz(sx.属性.气血) * 8, -- 本体血量较低
        伤害 = qz(sx.属性.伤害) * 0.5, -- 本体攻击力很低
        法伤 = qz(sx.属性.法伤) * 0.5,
        速度 = qz(sx.属性.速度) * 5.0, -- 但速度很高，容易逃跑
        防御 = 等级 * 5, -- 防御也低
        法防 = 等级 * 5,
        技能 = {"高级敏捷", "高级隐身", "高级幸运"}, -- 示例技能
        修炼 = {物抗 = xiulian-3, 法抗 = xiulian-3, 攻修 = xiulian-3},
        门派 = sx.门派,
        AI战斗 = {AI = "逃跑型"} -- 假设有逃跑型AI
    }
    return 战斗单位
end

-- 玉壶本体 (Boss)
function 怪物属性:锻刀村玉壶本体(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
    if 等级 < 40 then 等级 = 40 end -- 确保最低等级

    local sx = self:取属性(等级, "龙宫", "法系") -- 玉壶是强力法系
    local xiulian = math.floor((等级 - 20) / 5) + 8 -- Boss修炼更高

    -- 获取难度调整系数
    local 难度系数 = {血量倍率=1.0, 伤害倍率=1.0, 人数=1}
    if 任务数据[任务id] and 任务数据[任务id].难度系数 then
        难度系数 = 任务数据[任务id].难度系数
    end

    -- 如果是队伍，获取队伍人数
    if 玩家数据[玩家id].队伍 and 玩家数据[玩家id].队伍 > 0 and 队伍数据[玩家数据[玩家id].队伍] then
        难度系数.人数 = #队伍数据[玩家数据[玩家id].队伍].成员数据
    end

    战斗单位[1] = {
        名称 = "玉壶",
        模型 = "树怪", -- 使用树怪模型作为临时替代
        等级 = 等级,
        变异 = true,
        角色 = true,
        气血 = qz(sx.属性.气血) * 35 * 难度系数.血量倍率,
        伤害 = qz(sx.属性.伤害) * 难度系数.伤害倍率,
        法伤 = qz(sx.属性.法伤) * 2.8 * 难度系数.伤害倍率,
        速度 = qz(sx.属性.速度) * 3.0,
        防御 = 等级 * 15,
        法防 = 等级 * 18,
        技能 = {"高级法术暴击", "高级法术波动", "高级魔之心", "高级神佑复生", "水漫金山", "水攻", "龙腾", "龙卷雨击"}, -- 水系技能
        修炼 = {物抗 = xiulian, 法抗 = xiulian+2, 攻修 = xiulian+2},
        门派 = sx.门派,
        AI战斗 = {AI = "法术输出"}
    }

    -- 根据队伍人数动态调整小怪数量
    local 小怪数量 = math.min(5, math.max(2, math.floor(难度系数.人数 * 0.8)))
    for i = 2, 小怪数量+1 do
        local sx_minion = self:取属性(等级)
        战斗单位[i] = {
            名称 = "玉壶的使魔",
            模型 = "鲛人", -- 水系怪物模型
            等级 = 等级,
            气血 = qz(sx_minion.属性.气血) * 12,
            伤害 = qz(sx_minion.属性.伤害) * 0.8,
            法伤 = qz(sx_minion.属性.法伤) * 1.5,
            速度 = qz(sx_minion.属性.速度) * 2.5,
            防御 = 等级 * 8,
            法防 = 等级 * 10,
            主动技能 = {"水攻", "龙腾"} -- 水系技能
        }
    end

    return 战斗单位
end

-- 示例：憎珀天 (Boss)
function 怪物属性:锻刀村憎珀天(任务id, 玩家id, 序号)
    local 战斗单位 = {}
    local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
    local sx = self:取属性(等级, "魔王寨", "法系") -- 假设憎珀天是强力法系+物理
    local xiulian = math.floor((等级 - 20) / 5) + 10 -- Boss修炼更高

    战斗单位[1] = {
        名称 = "憎珀天",
        模型 = "树怪", -- 暂时使用树怪模型
        等级 = 等级,
        变异 = true,
        角色 = true, -- 标记为Boss
        气血 = qz(sx.属性.气血) * 45, -- Boss血量很高
        伤害 = qz(sx.属性.伤害) * 2.0, -- 也有物理伤害
        法伤 = qz(sx.属性.法伤) * 2.5,
        速度 = qz(sx.属性.速度) * 4.5,
        防御 = 等级 * 16,
        法防 = 等级 * 14,
        技能 = {"高级法术暴击", "高级必杀", "高级强力", "高级魔之心", "神佑复生", "飞沙走石", "三昧真火", "狂怒之雷"}, -- Boss技能 (狂怒之雷为假设的范围技能)
        修炼 = {物抗 = xiulian, 法抗 = xiulian, 攻修 = xiulian},
        门派 = sx.门派,
        AI战斗 = {AI = "混合强力输出"} -- 可能需要自定义AI
    }
    -- 添加一些代表分身能力的随从
    for i = 2, 5 do
         local sx_minion = self:取属性(等级)
         local minion_name = {"可畏之影", "积怒之影", "空喜之影", "哀绝之影"}
         local minion_model = {"树怪", "树怪", "树怪", "树怪"} -- 暂时使用树怪模型
         战斗单位[i] = {
              名称 = minion_name[i-1],
              模型 = minion_model[i-1],
              等级 = 等级,
              气血 = qz(sx_minion.属性.气血) * 15,
              伤害 = qz(sx_minion.属性.伤害) * 1.4,
              法伤 = qz(sx_minion.属性.法伤) * 1.4,
              速度 = qz(sx_minion.属性.速度) * 3,
              防御 = 等级 * 10,
              法防 = 等级 * 8,
              主动技能 = sx_minion.技能组 -- 可以根据分身特性设置不同技能
         }
    end
    return 战斗单位
end


-- [新增] 副本结束NPC对话处理函数
if not __GWdh111 then __GWdh111 = {} end
__GWdh111[FUBEN_ID + 60] = function(连接id, 数字id, 序列, 标识, 地图)
    local 对话数据 = {}
    if not 任务数据[标识] then return end
    对话数据.模型 = 任务数据[标识].模型
    对话数据.名称 = 任务数据[标识].名称

    -- 检查玩家是否有副本任务
    local 任务id = 玩家数据[数字id].角色:取任务(FUBEN_ID)
    if 任务id == 0 then
        对话数据.对话 = "你似乎不是参与这次副本的队员，无法领取奖励。"
        对话数据.选项 = {"我明白了"}
        return 对话数据
    end

    -- 检查副本是否完成
    local 副本id = 任务数据[任务id].副本id
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        对话数据.对话 = "副本数据异常，无法领取奖励。"
        对话数据.选项 = {"我明白了"}
        return 对话数据
    end

    local 副本实例 = 副本数据.锻刀村之战.进行[副本id]
    if 副本实例.进程 ~= STAGE.COMPLETE then
        对话数据.对话 = "副本尚未完成，请先击败所有敌人。"
        对话数据.选项 = {"我明白了"}
        return 对话数据
    end

    -- 检查是否已领取奖励
    if 副本实例.奖励已领取 and 副本实例.奖励已领取[数字id] then
        对话数据.对话 = "你已经领取过本次副本的奖励了。"
        对话数据.选项 = {"我明白了"}
        return 对话数据
    end

    对话数据.对话 = "了不起！你们成功击败了#R上弦之伍·玉壶#W和#R上弦之肆·半天狗#W！\n锻刀村得以保全，我们的工匠们可以继续锻造日轮刀，为鬼杀队提供对抗鬼舞辻无惨的武器。\n大人已经得知了这个好消息，他让我转达对你们的感谢，并赐予你们这些奖励。愿你们在今后的战斗中继续斩杀恶鬼，保护人类！"
    对话数据.选项 = {"选择获取战魄", "选择获取陨铁"}

    return 对话数据
end

-- [新增] 副本入口NPC对话处理函数
if not __GWdh111 then __GWdh111 = {} end
__GWdh111[FUBEN_ID] = function(连接id, 数字id, 序列, 标识, 地图)
    local 对话数据 = {}
    if not 任务数据[标识] then return end -- Added nil check for 任务数据[标识]
    对话数据.模型 = 任务数据[标识].模型
    对话数据.名称 = 任务数据[标识].名称

    -- 检查玩家是否已经完成过副本
    if 副本数据 and 副本数据.锻刀村之战 and 副本数据.锻刀村之战.完成 and 副本数据.锻刀村之战.完成[数字id] then
        local 完成时间 = 副本数据.锻刀村之战.完成[数字id]
        local 当前时间 = os.time()
        local 冷却时间 = 3600 * 6 -- 4小时冷却

        if 当前时间 - 完成时间 < 冷却时间 then
            local 剩余时间 = 冷却时间 - (当前时间 - 完成时间)
            local 剩余小时 = math.floor(剩余时间 / 3600)
            local 剩余分钟 = math.floor((剩余时间 % 3600) / 60)

            对话数据.对话 = string.format("勇士，你刚刚帮助过锻刀村了，请休息一下再来吧。（冷却时间还剩：#R%d#W小时#R%d#W分钟）", 剩余小时, 剩余分钟)
            对话数据.选项 = {"我明白了"}
            return 对话数据
        end
    end

    -- 检查玩家等级
    if 玩家数据[数字id] and 玩家数据[数字id].角色 and 玩家数据[数字id].角色.等级 < 40 then -- Added nil checks for 玩家数据 and 玩家数据[数字id].角色
        对话数据.对话 = "勇士，锻刀村的战斗非常危险，至少需要#R40级#W才能参与。请提升实力后再来吧！"
        对话数据.选项 = {"我会努力提升的"}
        return 对话数据
    end

    -- 检查玩家是否在队伍中
    if 玩家数据[数字id] and 玩家数据[数字id].队伍 == 0 then -- Added nil check for 玩家数据[数字id]
        对话数据.对话 = "勇士，锻刀村正在遭受上弦之鬼的袭击！这场战斗非常危险，建议你组队前往。你确定要独自前往吗？"
        对话数据.选项 = {"我愿意独自前往", "我去组队再来"}
    else
        -- 检查是否是队长
        if 玩家数据[数字id] and 玩家数据[数字id].队长 == false then -- Added nil check for 玩家数据[数字id]
            对话数据.对话 = "勇士，锻刀村正在遭受上弦之鬼的袭击！请让你们的队长来与我对话，我需要向他说明情况。"
            对话数据.选项 = {"我明白了"}
        else
            -- 是队长，可以开启副本
            对话数据.对话 = "#Y锻刀村#W是制作日轮刀的重要之地，现在遭到了#R上弦之伍·玉壶#W和#R上弦之肆·半天狗#W的袭击！\n\n鬼杀队急需勇士前去支援，保护锻刀村的工匠们！你们愿意接受这个任务吗？"
            对话数据.选项 = {"我们愿意前往支援", "让我们考虑一下"}
        end
    end

    return 对话数据
end

-- 添加开启副本方法（与开启锻刀村副本方法相同，用于兼容对话处理类的调用）
function 副本_锻刀村之战:开启副本(id)
    -- 直接调用开启锻刀村副本方法
    self:开启锻刀村副本(id)
end


-- [新增] 副本结束NPC选项处理函数
if not __GWdh222 then __GWdh222 = {} end
__GWdh222[FUBEN_ID + 60] = function(连接id, 数字id, 序号, 内容)
    local 事件 = 内容[1]
    local 标识 = 内容[2]
    local 名称 = 内容[3]

    -- 检查玩家是否有副本任务
    local 任务id = 玩家数据[数字id].角色:取任务(FUBEN_ID)
    if 任务id == 0 then
        常规提示(数字id, "#Y/你没有正在进行的锻刀村之战副本任务。")
        return
    end

    -- 检查副本是否完成
    local 副本id = 任务数据[任务id].副本id
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 or not 副本数据.锻刀村之战.进行[副本id] then
        常规提示(数字id, "#Y/副本数据异常，无法领取奖励。")
        return
    end

    local 副本实例 = 副本数据.锻刀村之战.进行[副本id]
    if 副本实例.进程 ~= STAGE.COMPLETE then
        常规提示(数字id, "#Y/副本尚未完成，请先击败所有敌人。")
        return
    end

    -- 处理副本结束NPC的对话选项
    if 名称 == "副本结束NPC" then
        if 事件 == "选择获取战魄" then
            -- 获取正确的副本ID
            local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
            if not 副本id then
                常规提示(数字id, "#R/无法获取副本ID，请联系管理员。")
                return
            end

            -- 调用发放奖励函数，选择获取战魄
            副本_锻刀村之战:发放奖励并结束(副本id, 数字id, "战魄")
            常规提示(数字id, "#G/恭喜你完成锻刀村之战副本，获得了战魄！")

            -- 删除副本结束NPC
            if 任务数据[标识] then
                地图处理类:删除单位(任务数据[标识].地图编号, 标识)
                任务数据[标识] = nil
            end
        elseif 事件 == "选择获取陨铁" then
            -- 获取正确的副本ID
            local 副本id = 任务处理类:取副本id(数字id, FUBEN_ID)
            if not 副本id then
                常规提示(数字id, "#R/无法获取副本ID，请联系管理员。")
                return
            end

            -- 调用发放奖励函数，选择获取陨铁
            副本_锻刀村之战:发放奖励并结束(副本id, 数字id, "陨铁")
            常规提示(数字id, "#G/恭喜你完成锻刀村之战副本，获得了陨铁！")

            -- 删除副本结束NPC
            if 任务数据[标识] then
                地图处理类:删除单位(任务数据[标识].地图编号, 标识)
                任务数据[标识] = nil
            end
        elseif 事件 == "稍后再来" then
            常规提示(数字id, "#Y/你可以稍后再来领取奖励。")
        end
    end
end

-- [新增] 副本入口NPC选项处理函数
if not __GWdh222 then __GWdh222 = {} end
__GWdh222[FUBEN_ID] = function(连接id, 数字id, 序号, 内容)
    local 事件 = 内容[1]
    local 标识 = 内容[2]
    local 名称 = 内容[3]

    -- 检查玩家等级
    if 玩家数据[数字id] and 玩家数据[数字id].角色 and 玩家数据[数字id].角色.等级 < 40 and 事件 ~= "我会努力提升的" then -- Added nil checks
        常规提示(数字id, "#Y/你的等级不足40级，无法参与锻刀村之战副本")
        return
    end

    -- 处理锻刀村使者的对话选项
    if 名称 == "锻刀村使者" then
        if 事件 == "进入锻刀村之战副本" then
            -- 调用锻刀村之战副本的开启函数
            副本_锻刀村之战:开启锻刀村副本(数字id)
        end
    end

    -- 确保副本数据结构已初始化
    if not 副本数据 or not 副本数据.锻刀村之战 then
        -- 初始化副本数据结构
        if not 副本数据 then 副本数据 = {} end
        if not 副本数据.锻刀村之战 then
            副本数据.锻刀村之战 = {
                进行 = {}, -- 存储进行中的副本
                完成 = {}  -- 存储已完成副本的玩家ID和完成时间
            }

        end
    end

    -- 检查冷却时间
    if 副本数据.锻刀村之战.完成 and 副本数据.锻刀村之战.完成[数字id] then
        local 完成时间 = 副本数据.锻刀村之战.完成[数字id]
        local 当前时间 = os.time()
        local 冷却时间 = 3600 * 4 -- 4小时冷却

        if 当前时间 - 完成时间 < 冷却时间 then
            local 剩余秒数 = 冷却时间 - (当前时间 - 完成时间)
            local 剩余小时 = math.floor(剩余秒数 / 3600)
            local 剩余分钟 = math.floor((剩余秒数 % 3600) / 60)

            常规提示(数字id, string.format("#Y/副本冷却中，还需等待%d小时%d分钟", 剩余小时, 剩余分钟))
            return
        end
    end

    if 事件 == "我们愿意前往支援" or 事件 == "我愿意独自前往" then
        -- 直接调用全局函数
        开启锻刀村副本(数字id)
        return
    elseif 事件 == "我去组队再来" then
        常规提示(数字id, "#G/组队后再来挑战副本，可以获得更好的体验和奖励！")
        return
    elseif 事件 == "我明白了" then
        -- 只是关闭对话框
        return
    elseif 事件 == "我会努力提升的" then
        常规提示(数字id, "#G/努力提升等级吧，锻刀村的工匠们等待你的保护！")
        return
    end
end

-- [新增] 副本传送函数（已废弃，使用任务处理类:副本传送代替）
function 副本_锻刀村之战:副本传送(玩家id)
    -- 调用任务处理类的副本传送函数
    任务处理类:副本传送(玩家id, 1005)
end

-- 创建副本入口NPC
function 副本_锻刀村之战:创建入口NPC()
    -- 在长安城创建副本入口NPC
    local 地图id = FUBEN_ENTRY_MAP -- 长安城
    local 任务id = "锻刀村之战_入口NPC_" .. os.time()

    任务数据[任务id] = {
        id = 任务id,
        起始 = os.time(),
        结束 = 99999999, -- 永久存在
        玩家id = 0,
        队伍组 = {},
        名称 = "锻刀村使者",
        模型 = "剑侠客", -- 可以根据需要修改模型
        x = FUBEN_ENTRY_X,
        y = FUBEN_ENTRY_Y,
        方向 = 4,
        地图编号 = 地图id,
        地图名称 = 取地图名称(地图id),
        小地图名称颜色 = 3,
        类型 = FUBEN_ID -- 使用副本ID作为NPC类型
    }

    地图处理类:添加单位(任务id)

end

-- 这段代码已被移除，避免重复初始化
-- 使用上面的初始化代码块（1304-1353行）

-- 添加战斗胜利回调函数
function 胜利MOB_锻刀村玉壶鱼怪(id组, 战斗类型, 任务id)
    -- 显示战斗胜利提示
    for _, 玩家id in ipairs(id组) do
        常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
    end

    -- 处理副本进度
    副本_锻刀村之战:处理战斗胜利(id组, 160000, 任务id)
end

-- 半天狗分身战斗胜利回调
function 胜利MOB_锻刀村半天狗可畏(id组, 战斗类型, 任务id)
    -- 显示战斗胜利提示
    for _, 玩家id in ipairs(id组) do
        常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
    end
    -- 处理副本进度
    副本_锻刀村之战:处理战斗胜利(id组, 160004, 任务id)
end

function 胜利MOB_锻刀村半天狗积怒(id组, 战斗类型, 任务id)
    -- 显示战斗胜利提示
    for _, 玩家id in ipairs(id组) do
        常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
    end

-------
    -- 处理副本进度
    副本_锻刀村之战:处理战斗胜利(id组, 160005, 任务id)
end

function 胜利MOB_锻刀村半天狗空喜(id组, 战斗类型, 任务id)
    -- 显示战斗胜利提示
    for _, 玩家id in ipairs(id组) do
        常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
    end
-------
    -- 处理副本进度
    副本_锻刀村之战:处理战斗胜利(id组, 160006, 任务id)
end

function 胜利MOB_锻刀村半天狗哀绝(id组, 战斗类型, 任务id)
    -- 显示战斗胜利提示
    for _, 玩家id in ipairs(id组) do
        常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
    end
-------
    -- 处理副本进度
    副本_锻刀村之战:处理战斗胜利(id组, 160007, 任务id)
end

-- Boss战斗胜利回调
function 胜利MOB_锻刀村玉壶本体(id组, 战斗类型, 任务id)
    -- 显示战斗胜利提示
    for _, 玩家id in ipairs(id组) do
        常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
    end

-------
    -- 处理副本进度
    副本_锻刀村之战:处理战斗胜利(id组, 160001, 任务id)
end

function 胜利MOB_锻刀村憎珀天(id组, 战斗类型, 任务id)
    -- 显示战斗胜利提示
    for _, 玩家id in ipairs(id组) do
        常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
    end

-------
    -- 处理副本进度
    副本_锻刀村之战:处理战斗胜利(id组, 160002, 任务id)
end

function 胜利MOB_锻刀村半天狗本体(id组, 战斗类型, 任务id)
    -- 显示战斗胜利提示
    for _, 玩家id in ipairs(id组) do
        常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
    end

-------
    -- 处理副本进度
    副本_锻刀村之战:处理战斗胜利(id组, 160003, 任务id)
end

-- 添加战斗序号对应的胜利处理函数
function 胜利MOB_160000(胜利id, 战斗数据, id组)
    -- 玉壶召唤物战斗胜利处理
    if 任务数据[战斗数据.任务id] == nil then return end

    -- 先保存必要的信息，用于后续处理
    local 副本id = 任务数据[战斗数据.任务id].副本id
    local monster_name = 任务数据[战斗数据.任务id].名称

    -- 发放奖励 - 在删除任务数据前调用
    -- 为每个玩家发放战斗奖励
    if id组 and #id组 > 0 then
        for _, 玩家id in ipairs(id组) do
            常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
        end
        完成锻刀村副本任务(id组, 160000)
    end

    -- 删除单位和任务数据
    地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号, 任务数据[战斗数据.任务id].单位编号)
    任务数据[战斗数据.任务id] = nil

    -- 更新副本进度
    if 副本数据.锻刀村之战.进行[副本id] then
        副本数据.锻刀村之战.进行[副本id].阶段数据 = 副本数据.锻刀村之战.进行[副本id].阶段数据 or {}
        副本数据.锻刀村之战.进行[副本id].阶段数据.鱼怪击杀数 = (副本数据.锻刀村之战.进行[副本id].阶段数据.鱼怪击杀数 or 0) + 1

        -- 检查是否击杀了所有鱼怪
        local 总数 = 副本数据.锻刀村之战.进行[副本id].阶段数据.鱼怪总数 or 10
        if 副本数据.锻刀村之战.进行[副本id].阶段数据.鱼怪击杀数 >= 总数 then
            -- 使用更新副本进度函数来推进到下一阶段，同时触发屏幕闪烁
            更新副本进度(副本id, STAGE.FIGHT_HANTENGU_CLONES)

            -- 通知玩家 (更新副本进度函数已经发送了红色闪烁效果)
            if 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组 then
                for _, player_id in ipairs(任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组) do
                    常规提示(player_id, "#G玉壶的召唤物已被击退！半天狗的分身出现了！")
                end
            end
        end

        刷新队伍任务追踪(战斗数据.进入战斗玩家id)
    end
end

function 胜利MOB_160001(胜利id, 战斗数据, id组)
    -- 玉壶本体战斗胜利处理
    if 任务数据[战斗数据.任务id] == nil then return end

    -- 先保存必要的信息，用于后续处理
    local 副本id = 任务数据[战斗数据.任务id].副本id

    -- 发放奖励 - 在删除任务数据前调用
    -- 为每个玩家发放战斗奖励
    if id组 and #id组 > 0 then
        for _, 玩家id in ipairs(id组) do
            常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
        end
        完成锻刀村副本任务(id组, 160001)
    end

    -- 删除单位和任务数据
    地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号, 任务数据[战斗数据.任务id].单位编号)
    任务数据[战斗数据.任务id] = nil

    -- 更新副本进度
    if 副本数据.锻刀村之战.进行[副本id] then
        -- 使用更新副本进度函数来推进到下一阶段，同时触发屏幕闪烁
        更新副本进度(副本id, STAGE.BOSS_ZOHAKUTEN)

        -- 通知玩家 (更新副本进度函数已经发送了红色闪烁效果)
        if 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组 then
            for _, player_id in ipairs(任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组) do
                常规提示(player_id, "#G玉壶已被击败！憎珀天出现了！")
            end
        end

        刷新队伍任务追踪(战斗数据.进入战斗玩家id)
    end
end

function 胜利MOB_160002(胜利id, 战斗数据, id组)
    -- 憎珀天战斗胜利处理
    if 任务数据[战斗数据.任务id] == nil then return end

    -- 先保存必要的信息，用于后续处理
    local 副本id = 任务数据[战斗数据.任务id].副本id

    -- 发放奖励 - 在删除任务数据前调用
    -- 为每个玩家发放战斗奖励
    if id组 and #id组 > 0 then
        for _, 玩家id in ipairs(id组) do
            常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
        end
        完成锻刀村副本任务(id组, 160002)
    end

    -- 删除单位和任务数据
    地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号, 任务数据[战斗数据.任务id].单位编号)
    任务数据[战斗数据.任务id] = nil

    -- 更新副本进度
    if 副本数据.锻刀村之战.进行[副本id] then
        -- 使用更新副本进度函数来推进到下一阶段，同时触发屏幕闪烁
        更新副本进度(副本id, STAGE.FIND_HANTENGU_BODY)

        -- 通知玩家 (更新副本进度函数已经发送了红色闪烁效果)
        if 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组 then
            for _, player_id in ipairs(任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组) do
                常规提示(player_id, "#G憎珀天已被击败！快找到隐藏的半天狗本体！")
            end
        end

        刷新队伍任务追踪(战斗数据.进入战斗玩家id)
    end
end

function 胜利MOB_160003(胜利id, 战斗数据, id组)
    -- 半天狗本体战斗胜利处理
    if 任务数据[战斗数据.任务id] == nil then return end

    -- 先保存必要的信息，用于后续处理
    local 副本id = 任务数据[战斗数据.任务id].副本id

    -- 发放奖励 - 在删除任务数据前调用
    -- 为每个玩家发放战斗奖励
    if id组 and #id组 > 0 then
        for _, 玩家id in ipairs(id组) do
            常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
        end
        完成锻刀村副本任务(id组, 160003)
    end

    -- 删除单位和任务数据
    地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号, 任务数据[战斗数据.任务id].单位编号)
    任务数据[战斗数据.任务id] = nil

    -- 更新副本进度
    if 副本数据.锻刀村之战.进行[副本id] then
        -- 使用更新副本进度函数来推进到完成阶段，同时触发屏幕闪烁
        更新副本进度(副本id, STAGE.COMPLETE)

        -- 向所有参与的玩家发送提示
        if 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组 then
            for _, player_id in ipairs(任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组) do
                常规提示(player_id, "#G恭喜你击败了半天狗，成功保卫了锻刀村！")
            end
        end

        刷新队伍任务追踪(战斗数据.进入战斗玩家id)

        -- 调用副本完成处理函数
        副本_锻刀村之战:处理副本完成(副本id)
    end
end

function 副本_锻刀村之战:处理胜利_160004(胜利id, 战斗数据, id组)
    -- 半天狗可畏分身战斗胜利处理
    if 任务数据[战斗数据.任务id] == nil then return end

    -- 先保存必要的信息，用于后续处理
    local 副本id = 任务数据[战斗数据.任务id].副本id

    -- 发放奖励 - 在删除任务数据前调用
    -- 为每个玩家发放战斗奖励
    if id组 and #id组 > 0 then
        for _, 玩家id in ipairs(id组) do
            常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
        end
        完成锻刀村副本任务(id组, 160004)
    end

    -- 删除单位和任务数据
    地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号, 任务数据[战斗数据.任务id].单位编号)
    任务数据[战斗数据.任务id] = nil

    -- 更新副本进度
    if 副本数据.锻刀村之战.进行[副本id] then
        副本数据.锻刀村之战.进行[副本id].阶段数据 = 副本数据.锻刀村之战.进行[副本id].阶段数据 or {}
        副本数据.锻刀村之战.进行[副本id].阶段数据.可畏已击败 = true

        -- 同步到任务数据
        if 副本数据.锻刀村之战.进行[副本id].主任务id and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] then
            任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据 = 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据 or {}
            任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据.可畏已击败 = true
        end

        -- 检查是否所有分身都被击败
        if 副本数据.锻刀村之战.进行[副本id].阶段数据.可畏已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.积怒已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.空喜已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.哀绝已击败 then

            -- 使用更新副本进度函数来推进到下一阶段，同时触发屏幕闪烁
            更新副本进度(副本id, STAGE.BOSS_YUHUBODY)

            -- 通知玩家 (更新副本进度函数已经发送了红色闪烁效果)
            if 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组 then
                for _, player_id in ipairs(任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组) do
                    常规提示(player_id, "#G半天狗的分身已被击败！玉壶本体出现了！")
                end
            end
        end

        -- 使用带时间限制的刷新函数，避免频繁刷新
        if not 副本数据.锻刀村之战.进行[副本id].上次刷新时间 then
            副本数据.锻刀村之战.进行[副本id].上次刷新时间 = os.time()
            刷新队伍任务追踪(战斗数据.进入战斗玩家id)
        elseif os.time() - 副本数据.锻刀村之战.进行[副本id].上次刷新时间 >= 5 then
            副本数据.锻刀村之战.进行[副本id].上次刷新时间 = os.time()
            刷新队伍任务追踪(战斗数据.进入战斗玩家id)
        end
    end
end

function 副本_锻刀村之战:处理胜利_160005(胜利id, 战斗数据, id组)
    -- 半天狗积怒分身战斗胜利处理
    if 任务数据[战斗数据.任务id] == nil then return end

    -- 先保存必要的信息，用于后续处理
    local 副本id = 任务数据[战斗数据.任务id].副本id

    -- 发放奖励 - 在删除任务数据前调用
    -- 为每个玩家发放战斗奖励
    if id组 and #id组 > 0 then
        for _, 玩家id in ipairs(id组) do
            常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
        end
        完成锻刀村副本任务(id组, 160005)
    end

    -- 删除单位和任务数据
    地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号, 任务数据[战斗数据.任务id].单位编号)
    任务数据[战斗数据.任务id] = nil

    -- 更新副本进度
    if 副本数据.锻刀村之战.进行[副本id] then
        副本数据.锻刀村之战.进行[副本id].阶段数据 = 副本数据.锻刀村之战.进行[副本id].阶段数据 or {}
        副本数据.锻刀村之战.进行[副本id].阶段数据.积怒已击败 = true

        -- 同步到任务数据
        if 副本数据.锻刀村之战.进行[副本id].主任务id and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] then
            任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据 = 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据 or {}
            任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据.积怒已击败 = true
        end

        -- 检查是否所有分身都被击败
        if 副本数据.锻刀村之战.进行[副本id].阶段数据.可畏已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.积怒已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.空喜已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.哀绝已击败 then

            -- 使用更新副本进度函数来推进到下一阶段，同时触发屏幕闪烁
            更新副本进度(副本id, STAGE.BOSS_YUHUBODY)

            -- 通知玩家 (更新副本进度函数已经发送了红色闪烁效果)
            if 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组 then
                for _, player_id in ipairs(任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组) do
                    常规提示(player_id, "#G半天狗的分身已被击败！玉壶本体出现了！")
                end
            end
        end

        -- 使用带时间限制的刷新函数，避免频繁刷新
        if not 副本数据.锻刀村之战.进行[副本id].上次刷新时间 then
            副本数据.锻刀村之战.进行[副本id].上次刷新时间 = os.time()
            刷新队伍任务追踪(战斗数据.进入战斗玩家id)
        elseif os.time() - 副本数据.锻刀村之战.进行[副本id].上次刷新时间 >= 5 then
            副本数据.锻刀村之战.进行[副本id].上次刷新时间 = os.time()
            刷新队伍任务追踪(战斗数据.进入战斗玩家id)
        end
    end
end

function 副本_锻刀村之战:处理胜利_160006(胜利id, 战斗数据, id组)
    -- 半天狗空喜分身战斗胜利处理
    if 任务数据[战斗数据.任务id] == nil then return end

    -- 先保存必要的信息，用于后续处理
    local 副本id = 任务数据[战斗数据.任务id].副本id

    -- 发放奖励 - 在删除任务数据前调用
    -- 为每个玩家发放战斗奖励
    if id组 and #id组 > 0 then
        for _, 玩家id in ipairs(id组) do
            常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
        end
        完成锻刀村副本任务(id组, 160006)
    end

    -- 删除单位和任务数据
    地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号, 任务数据[战斗数据.任务id].单位编号)
    任务数据[战斗数据.任务id] = nil

    -- 更新副本进度
    if 副本数据.锻刀村之战.进行[副本id] then
        副本数据.锻刀村之战.进行[副本id].阶段数据 = 副本数据.锻刀村之战.进行[副本id].阶段数据 or {}
        副本数据.锻刀村之战.进行[副本id].阶段数据.空喜已击败 = true

        -- 同步到任务数据
        if 副本数据.锻刀村之战.进行[副本id].主任务id and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] then
            任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据 = 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据 or {}
            任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据.空喜已击败 = true
        end

        -- 检查是否所有分身都被击败
        if 副本数据.锻刀村之战.进行[副本id].阶段数据.可畏已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.积怒已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.空喜已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.哀绝已击败 then

            -- 使用更新副本进度函数来推进到下一阶段，同时触发屏幕闪烁
            更新副本进度(副本id, STAGE.BOSS_YUHUBODY)

            -- 通知玩家 (更新副本进度函数已经发送了红色闪烁效果)
            if 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组 then
                for _, player_id in ipairs(任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组) do
                    常规提示(player_id, "#G半天狗的分身已被击败！玉壶本体出现了！")
                end
            end
        end

        -- 使用带时间限制的刷新函数，避免频繁刷新
        if not 副本数据.锻刀村之战.进行[副本id].上次刷新时间 then
            副本数据.锻刀村之战.进行[副本id].上次刷新时间 = os.time()
            刷新队伍任务追踪(战斗数据.进入战斗玩家id)
        elseif os.time() - 副本数据.锻刀村之战.进行[副本id].上次刷新时间 >= 5 then
            副本数据.锻刀村之战.进行[副本id].上次刷新时间 = os.time()
            刷新队伍任务追踪(战斗数据.进入战斗玩家id)
        end
    end
end

function 副本_锻刀村之战:处理胜利_160007(胜利id, 战斗数据, id组)
    -- 半天狗哀绝分身战斗胜利处理
    if 任务数据[战斗数据.任务id] == nil then return end

    -- 先保存必要的信息，用于后续处理
    local 副本id = 任务数据[战斗数据.任务id].副本id

    -- 发放奖励 - 在删除任务数据前调用
    -- 为每个玩家发放战斗奖励
    if id组 and #id组 > 0 then
        for _, 玩家id in ipairs(id组) do
            常规提示(玩家id, "#G战斗胜利！正在发放奖励...")
        end
        完成锻刀村副本任务(id组, 160007)
    end

    -- 删除单位和任务数据
    地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号, 任务数据[战斗数据.任务id].单位编号)
    任务数据[战斗数据.任务id] = nil

    -- 更新副本进度
    if 副本数据.锻刀村之战.进行[副本id] then
        副本数据.锻刀村之战.进行[副本id].阶段数据 = 副本数据.锻刀村之战.进行[副本id].阶段数据 or {}
        副本数据.锻刀村之战.进行[副本id].阶段数据.哀绝已击败 = true

        -- 同步到任务数据
        if 副本数据.锻刀村之战.进行[副本id].主任务id and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] then
            任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据 = 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据 or {}
            任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].阶段数据.哀绝已击败 = true
        end

        -- 检查是否所有分身都被击败
        if 副本数据.锻刀村之战.进行[副本id].阶段数据.可畏已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.积怒已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.空喜已击败 and
           副本数据.锻刀村之战.进行[副本id].阶段数据.哀绝已击败 then

            -- 使用更新副本进度函数来推进到下一阶段，同时触发屏幕闪烁
            更新副本进度(副本id, STAGE.BOSS_YUHUBODY)

            -- 通知玩家 (更新副本进度函数已经发送了红色闪烁效果)
            if 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id] and 任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组 then
                for _, player_id in ipairs(任务数据[副本数据.锻刀村之战.进行[副本id].主任务id].队伍组) do
                    常规提示(player_id, "#G半天狗的分身已被击败！玉壶本体出现了！")
                end
            end
        end

        -- 使用带时间限制的刷新函数，避免频繁刷新
        if not 副本数据.锻刀村之战.进行[副本id].上次刷新时间 then
            副本数据.锻刀村之战.进行[副本id].上次刷新时间 = os.time()
            刷新队伍任务追踪(战斗数据.进入战斗玩家id)
        elseif os.time() - 副本数据.锻刀村之战.进行[副本id].上次刷新时间 >= 5 then
            副本数据.锻刀村之战.进行[副本id].上次刷新时间 = os.time()
            刷新队伍任务追踪(战斗数据.进入战斗玩家id)
        end
    end
end

-- [新增] 集中处理战斗胜利的函数
function 副本_锻刀村之战:处理战斗胜利(id组, 战斗类型, 任务id)
    if 任务数据[任务id] == nil then return end
    任务数据[任务id].战斗 = nil

    local 副本id = 任务数据[任务id].副本id
    if not 副本数据 or not 副本数据.锻刀村之战 or not 副本数据.锻刀村之战.进行 then
        return
    end
    local 副本实例 = 副本数据.锻刀村之战.进行[副本id]
    if 副本实例 == nil then
        return
    end

    local 进程 = 副本实例.进程
    local 队长id = (任务数据[副本id] or {}).领取人id -- Use (table or {}) to safely access field
    if not 玩家数据[队长id] or not 玩家数据[队长id].角色 or not 玩家数据[队长id].角色.地图数据 then return end -- Added nil checks
    local 地图id = 玩家数据[队长id].角色.地图数据.编号 -- 获取当前地图ID

    local monster_name = (任务数据[任务id] or {}).名称 -- Safely access 名称

    -- 调用处理怪物击杀函数更新阶段数据
    self:处理怪物击杀(任务id, monster_name)

    -- 检查是否满足阶段推进条件
    local stage_changed = false
    if 进程 == STAGE.FIGHT_YUHUFISH then
        副本实例.阶段数据 = 副本实例.阶段数据 or {} -- Ensure阶段数据 exists
        local 总数 = 副本实例.阶段数据.鱼怪总数 or 10
        if 副本实例.阶段数据.鱼怪击杀数 >= 总数 then
            -- 使用更新副本进度函数来处理进度变化
            更新副本进度(副本id, STAGE.FIGHT_HANTENGU_CLONES)

            -- 清理上一阶段数据
            副本实例.阶段数据 = {}
            if 任务数据[副本实例.主任务id] then
                任务数据[副本实例.主任务id].阶段数据 = {}
            end

            常规提示(队长id, "#G玉壶的召唤物已被击退！半天狗的分身出现了！")
            stage_changed = true
        end
    elseif 进程 == STAGE.FIGHT_HANTENGU_CLONES then
        副本实例.阶段数据 = 副本实例.阶段数据 or {} -- Ensure阶段数据 exists
        -- 检查是否所有分身都被击败
        if 副本实例.阶段数据.可畏已击败 and 副本实例.阶段数据.积怒已击败 and 副本实例.阶段数据.空喜已击败 and 副本实例.阶段数据.哀绝已击败 then
            -- 使用更新副本进度函数来处理进度变化
            更新副本进度(副本id, STAGE.BOSS_YUHUBODY)

            -- 清理上一阶段数据
            副本实例.阶段数据 = {}
            if 任务数据[副本实例.主任务id] then
                任务数据[副本实例.主任务id].阶段数据 = {}
            end

            常规提示(队长id, "#G半天狗的分身已被击败！玉壶本体出现了！")
            stage_changed = true
        end
    elseif 进程 == STAGE.BOSS_YUHUBODY then
         if monster_name == "玉壶本体" then
            -- 使用更新副本进度函数来处理进度变化
            更新副本进度(副本id, STAGE.BOSS_ZOHAKUTEN)

            -- 清理上一阶段数据
            副本实例.阶段数据 = {}
            if 任务数据[副本实例.主任务id] then
                任务数据[副本实例.主任务id].阶段数据 = {}
            end

            常规提示(队长id, "#G玉壶已被击败！憎珀天出现了！")
            stage_changed = true
         end
    elseif 进程 == STAGE.BOSS_ZOHAKUTEN then
         if monster_name == "憎珀天" then
            -- 使用更新副本进度函数来处理进度变化
            更新副本进度(副本id, STAGE.FIND_HANTENGU_BODY)

            -- 清理上一阶段数据
            副本实例.阶段数据 = {}
            if 任务数据[副本实例.主任务id] then
                任务数据[副本实例.主任务id].阶段数据 = {}
            end

            常规提示(队长id, "#G憎珀天已被击败！快找到隐藏的半天狗本体！")
            stage_changed = true
         end
    elseif 进程 == STAGE.FIND_HANTENGU_BODY then
         if monster_name == "半天狗本体" then
            -- 使用更新副本进度函数来处理进度变化
            更新副本进度(副本id, STAGE.COMPLETE)

            -- 副本完成
            副本_锻刀村之战:处理副本完成(副本id) -- 调用副本完成函数，传入副本id而不是主任务id
            stage_changed = true -- 标记阶段变化以便刷新任务追踪
         end
    end

    -- 注意：奖励发放已经在各个战斗胜利处理函数中直接调用，这里不再重复调用

    -- 刷新任务追踪 (只在阶段变化时刷新，减少不必要的刷新)
    local 队伍组 = 任务数据[副本实例.主任务id] and 任务数据[副本实例.主任务id].队伍组
    if not 队伍组 then
        return true
    end

    if stage_changed then
        -- 阶段变化时刷新所有队员的任务追踪
        for _, player_id in ipairs(队伍组) do
            常规提示(player_id, "#Y/您的副本进度已经更新")
            -- 系统函数会自动检查玩家是否存在，无需重复检查
            玩家数据[player_id].角色:刷新任务跟踪()
        end
    elseif (进程 == STAGE.FIGHT_YUHUFISH or 进程 == STAGE.FIGHT_HANTENGU_CLONES) then
        -- 对于需要实时更新进度的阶段，添加时间限制避免频繁刷新
        if not 副本实例.上次战斗刷新时间 then
            副本实例.上次战斗刷新时间 = os.time()
            -- 刷新任务追踪
            for _, player_id in ipairs(队伍组) do
                玩家数据[player_id].角色:刷新任务跟踪()
            end
        elseif os.time() - 副本实例.上次战斗刷新时间 >= 10 then
            副本实例.上次战斗刷新时间 = os.time()
            -- 刷新任务追踪
            for _, player_id in ipairs(队伍组) do
                玩家数据[player_id].角色:刷新任务跟踪()
            end
        end
    end

    -- 注意：单位删除和任务数据清理已经在各个战斗胜利处理函数中完成，这里不再重复
end

-- [新增] 创建副本入口NPC

-- 注册任务说明函数到全局表
if not 任务说明 then
    任务说明 = {}

end

任务说明[FUBEN_ID] = function(玩家id, 任务id)
    return 副本_锻刀村之战:任务说明(玩家id, 任务id)
end

-- 地图初始化已移至初始化方法中

-- 添加结束锻刀村副本函数

-- 处理副本完成函数
function 副本_锻刀村之战:处理副本完成(副本id)
    -- 简化检查，直接获取副本实例
    local 副本实例 = 副本数据 and 副本数据.锻刀村之战 and 副本数据.锻刀村之战.进行 and 副本数据.锻刀村之战.进行[副本id]
    if not 副本实例 then
        return false
    end

    -- 获取主任务ID和队伍组
    local 主任务id = 副本实例.主任务id
    local 任务实例 = 主任务id and 任务数据[主任务id]
    local 队伍组 = 任务实例 and 任务实例.队伍组

    -- 更新副本进度为完成
    副本实例.进程 = STAGE.COMPLETE
    if 任务实例 then
        任务实例.进程 = STAGE.COMPLETE
    end

    -- 向所有参与的玩家发送提示（不发送红色闪烁效果）
    if 队伍组 then
        for _, player_id in ipairs(队伍组) do
            -- 不再发送红色闪烁指令
            常规提示(player_id, "#G恭喜你击败了上弦之肆·半天狗，成功保卫了锻刀村！")
        end
    end

    -- 记录副本完成状态
    if not 副本数据.锻刀村之战.完成 then
        副本数据.锻刀村之战.完成 = {}
    end

    -- 记录所有队伍成员的完成状态并刷新任务追踪
    if 队伍组 then
        for _, player_id in ipairs(队伍组) do
            -- 记录完成状态
            副本数据.锻刀村之战.完成[player_id] = os.time()

            -- 刷新任务追踪
            玩家数据[player_id].角色:刷新任务跟踪()
        end
    end

    -- 不再使用更新副本进度函数来发送红色闪烁效果
    -- 副本进度已经在前面更新为STAGE.COMPLETE

    return true
end

-- 使用处理副本完成函数来发放奖励并结束副本

-- 添加完成锻刀村副本任务函数 (仅处理奖励)
function 完成锻刀村副本任务(id组, 战斗类型)
  for i = 1, #id组 do
    local id = id组[i]
    if 玩家数据[id] then
      local 等级 = 玩家数据[id].角色.等级
      local 经验
      local 银子
      local 储备

      -- 根据战斗类型给予不同奖励
      if 战斗类型 == 160000 then -- 玉壶召唤物
        local 经验 = 等级 * 取随机数(100, 105)
        local 银子 = 等级 * 取随机数(90, 110)
        玩家数据[id].角色:添加经验(经验, "锻刀村副本")
        玩家数据[id].角色:添加银子(银子, "锻刀村副本", 1)
        -- 随机给予物品奖励
        if 取随机数() <= 15 then
          local 奖励参数 = 取随机数(1, 50)
          链接 = { 提示 = format("#S(副本-锻刀村之战)#R/%s#Y/在#R/锻刀村之战#Y/表现卓越，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 10 then
            local 名称 = "金柳露"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 30 then
            local 名称 = "魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 35 then
            local 名称 = "五宝盒"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 50 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          end
        end
      elseif 战斗类型 == 160004 or 战斗类型 == 160005 or 战斗类型 == 160006 or 战斗类型 == 160007 then -- 半天狗分身
         local 经验 = 等级 * 取随机数(500, 600)
         local 银子 = 等级 * 取随机数(100, 150)
         玩家数据[id].角色:添加经验(经验, "锻刀村副本")
         玩家数据[id].角色:添加银子(银子, "锻刀村副本", 1)
         -- 随机给予物品奖励
         if 取随机数() <= 25 then
           local 奖励参数 = 取随机数(1, 60)
           链接 = { 提示 = format("#S(副本-锻刀村之战)#R/%s#Y/在#R/锻刀村之战#Y/击败了半天狗分身，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
           if 奖励参数 <= 15 then
             local 名称 = "金柳露"
             玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
           elseif 奖励参数 <= 30 then
             local 名称 = "超级金柳露"
             玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
           elseif 奖励参数 <= 40 then
             local 名称 = "魔兽要诀"
             玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
           elseif 奖励参数 <= 50 then
             local 名称 = "五宝盒"
             玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
           elseif 奖励参数 <= 60 then
             local 名称 = 取强化石()
             玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
           end
         end
      elseif 战斗类型 == 160001 then -- 玉壶本体
        local 经验 = 等级 * 取随机数(5000, 6000)
        local 银子 = 等级 * 取随机数(500, 600)
        玩家数据[id].角色:添加经验(经验, "副本奖励")
        玩家数据[id].角色:添加银子(银子, "副本奖励", 1)

        -- 给予玉壶本体的特殊奖励
        if 取随机数() <= 40 then
          local 奖励参数 = 取随机数(1, 100)
          链接 = { 提示 = format("#S(副本-锻刀村之战)#R/%s#Y/在#R/锻刀村之战#Y/击败了上弦之伍·玉壶，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 20 then
            local 名称 = "超级金柳露"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 40 then
            local 名称 = "高级魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 60 then
            local 名称 = "高级召唤兽内丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 80 then
            local 名称 = "五宝盒"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 100 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          end
        end
      elseif 战斗类型 == 160002 then -- 憎珀天
        local 经验 = 等级 * 取随机数(8000, 10000)
        local 银子 = 等级 * 取随机数(800, 1000)
        玩家数据[id].角色:添加经验(经验, "副本奖励")
        玩家数据[id].角色:添加银子(银子, "副本奖励", 1)

        -- 给予憎珀天的特殊奖励
        if 取随机数() <= 50 then
          local 奖励参数 = 取随机数(1, 100)
          链接 = { 提示 = format("#S(副本-锻刀村之战)#R/%s#Y/在#R/锻刀村之战#Y/击败了半天狗合体·憎珀天，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 15 then
            local 名称 = "超级金柳露"
            玩家数据[id].道具:给予超链接道具(id, 名称, 2, nil, 链接)
          elseif 奖励参数 <= 30 then
            local 名称 = "高级魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 45 then
            local 名称 = "高级召唤兽内丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 60 then
            local 名称 = "五宝盒"
            玩家数据[id].道具:给予超链接道具(id, 名称, 2, nil, 链接)
          elseif 奖励参数 <= 75 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 2, nil, 链接)
          elseif 奖励参数 <= 90 then
            local 名称 = "高级魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 100 then
            local 名称 = "高级兽决碎片"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          end
        end
      elseif 战斗类型 == 160003 then -- 半天狗本体
        local 经验 = 等级 * 取随机数(15000, 20000)
        local 银子 = 等级 * 取随机数(1500, 2000)
        玩家数据[id].角色:添加经验(经验, "副本奖励")
        玩家数据[id].角色:添加银子(银子, "副本奖励", 1)

        -- 给予半天狗本体的特殊奖励
        if 取随机数() <= 60 then
          local 奖励参数 = 取随机数(1, 100)
          链接 = { 提示 = format("#S(副本-锻刀村之战)#R/%s#Y/在#R/锻刀村之战#Y/击败了上弦之肆·半天狗本体，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 10 then
            local 名称 = "超级金柳露"
            玩家数据[id].道具:给予超链接道具(id, 名称, 3, nil, 链接)
          elseif 奖励参数 <= 20 then
            local 名称 = "高级魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, 2, nil, 链接)
          elseif 奖励参数 <= 30 then
            local 名称 = "高级召唤兽内丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, 2, nil, 链接)
          elseif 奖励参数 <= 40 then
            local 名称 = "五宝盒"
            玩家数据[id].道具:给予超链接道具(id, 名称, 3, nil, 链接)
          elseif 奖励参数 <= 50 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 3, nil, 链接)
          elseif 奖励参数 <= 60 then
            local 名称 = "高级魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, 2, nil, 链接)
          elseif 奖励参数 <= 70 then
            local 名称 = "高级兽决碎片"
            玩家数据[id].道具:给予超链接道具(id, 名称, 2, nil, 链接)
          elseif 奖励参数 <= 80 then
            local 名称 = "高级魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, 3, nil, 链接)
          elseif 奖励参数 <= 100 then
            local 名称 = "高级兽决碎片"
            玩家数据[id].道具:给予超链接道具(id, 名称, 3, nil, 链接)
          end
        end
      elseif 战斗类型 == 160008 then -- 最终奖励 (通过结束NPC领取)
        local 经验 = 等级 * 取随机数(2850, 6100)
        local 银子 = 等级 * 取随机数(1150, 1200)
        玩家数据[id].角色:添加经验(经验, "锻刀村副本")
        玩家数据[id].角色:添加银子(银子, "锻刀村副本", 1)
        玩家数据[id].角色:添加积分(300, "副本积分")

        -- 给予最终物品奖励
        local 奖励 = {
          {名称 = "高级魔兽要诀", 数量 = 1},
          {名称 = "高级召唤兽内丹", 数量 = 1},
          {名称 = "五宝盒", 数量 = 2}
        }

        -- 添加随机额外奖励
        local 额外奖励参数 = 取随机数(1, 100)
        if 额外奖励参数 <= 30 then
          table.insert(奖励, {名称 = "高级魔兽要诀", 数量 = 1})
        elseif 额外奖励参数 <= 60 then
          table.insert(奖励, {名称 = "高级兽决碎片", 数量 = 2})
        elseif 额外奖励参数 <= 80 then
          table.insert(奖励, {名称 = "超级金柳露", 数量 = 2})
        elseif 额外奖励参数 <= 100 then
          table.insert(奖励, {名称 = 取强化石(), 数量 = 3})
        end

        -- 添加物品
        for _, 物品 in ipairs(奖励) do
          玩家数据[id].道具:给予道具(id, 物品.名称, 物品.数量)
        end
      end
    end
  end
end

-- 直接返回类，符合项目风格
return 副本_锻刀村之战
