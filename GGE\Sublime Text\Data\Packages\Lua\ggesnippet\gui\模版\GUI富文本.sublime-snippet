<snippet>
	<content><![CDATA[
--======================================================================--
${1:富文本} = ${2:对象}:创建富文本("${1:富文本}",0,0)
    function ${1:富文本}:初始化()
        self:添加元素("W",0xFFFFFFFF)--白
        self:添加元素("R",0xFFFF0000)--红
        self:添加元素("G",0xFF00FF00)--绿
        self:添加元素("B",0xFF0000FF)--蓝
        self:添加元素("Y",0xFFFFFF00)--黄
        self:添加元素("V",0xFFFF00FF)--紫
        self:添加元素("C",0xFF00FFFF)--青
    end

    function ${1:富文本}:消息事件(消息,a,b)
        if 消息 =="左键弹起" then

        end
    end
]]></content>
    <tabTrigger>guifwb_</tabTrigger>
    <scope>source.lua</scope>
    <description>丰富文本模版</description>
</snippet>
