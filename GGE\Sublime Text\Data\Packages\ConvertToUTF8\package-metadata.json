{"name": "ConvertToUTF8", "version": "1.2.14", "sublime_text": "*", "platforms": ["*"], "python_version": "3.8", "url": "https://github.com/seanliang/ConvertToUTF8", "issues": "https://github.com/seanliang/ConvertToUTF8/issues", "author": ["<PERSON><PERSON><PERSON><PERSON>"], "description": "A Sublime Text 2 & 3 plugin for editing and saving files encoded in GBK, BIG5, EUC-KR, EUC-JP, Shift_JIS, etc.", "labels": [], "libraries": [], "install_time": 1714996995.1889372, "release_time": "2021-05-25 10:02:36"}