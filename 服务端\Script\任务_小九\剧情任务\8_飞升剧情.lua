-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2025-02-12 19:50:46
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-29 16:35:14

function 怪物属性:黑熊精(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=144
	local 模型={"赌徒","强盗","山贼"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称="黑熊精",
	模型="黑熊精",
	气血=math.floor(sx.属性.气血*18),
	伤害=math.floor(sx.属性.伤害*1.5),
	法伤=math.floor(sx.属性.法伤*1.3),
	速度=math.floor(sx.属性.速度*1.2),
	防御=math.floor(等级*10),
	法防=math.floor(5+(等级/2)*20),
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,4  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*16),
		伤害=math.floor(sx.属性.伤害*1.4),
		法伤=math.floor(sx.属性.法伤*1.2),
		速度=math.floor(sx.属性.速度*1.1),
		防御=math.floor(等级*9),
		法防=math.floor(5+(等级/2)*20),
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110047(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		if 取随机数() < 50 then
			玩家数据[v].道具:给予任务道具(v,"修篁斧")
			玩家数据[v].角色.剧情={主线=8,编号 = 3,地图 = 1112,进度 = 19,附加={物品="避火诀"}}
			local wb =  {"黑熊精","黑熊精","修篁斧给你了。"}
			local xx = {}
			local wb2 = {}
			发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
		end
	end
end
function 怪物属性:山贼(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=144
	local 模型={"赌徒","强盗","山贼"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称="山贼",
	模型="山贼",
	气血=math.floor(sx.属性.气血*18),
	伤害=math.floor(sx.属性.伤害*1.5),
	法伤=math.floor(sx.属性.法伤*1.3),
	速度=math.floor(sx.属性.速度*1.2),
	防御=math.floor(等级*10),
	法防=math.floor(5+(等级/2)*20),
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,4  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*16),
		伤害=math.floor(sx.属性.伤害*1.4),
		法伤=math.floor(sx.属性.法伤*1.2),
		速度=math.floor(sx.属性.速度*1.1),
		防御=math.floor(等级*9),
		法防=math.floor(5+(等级/2)*20),
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110048(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色.剧情={主线=8,编号 = 1,地图 = 1054,进度 = 28,附加={}}
		local wb =  {"山贼","横行霸道的山贼",名称,"少侠真厉害，以后我再也不敢了。"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:鬼将(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id) or 144
	-- 确保等级不低于135（飞升最低要求）
	if 等级 < 135 then
		等级 = 135
	end
	local 喽啰模型={"僵尸","野鬼","骷髅怪"}
	local 选中模型=喽啰模型[取随机数(1,#喽啰模型)]
	local sx=self:取属性(等级,"大唐官府","物理")

	-- 主怪：鬼将
	战斗单位[1]={
		名称="鬼将",
		模型="鬼将",
		等级=等级,
		气血=math.floor(sx.属性.气血*18),
		伤害=math.floor(sx.属性.伤害*1.5),
		法伤=math.floor(sx.属性.法伤*1.3),
		速度=math.floor(sx.属性.速度*1.2),
		防御=math.floor(等级*11),
		法防=math.floor(5+(等级/2)*20),
		魔法=2000,
		修炼={物抗=20,法抗=20,攻修=20},
		技能={"感知","强力","高级反震"},
		主动技能={"横扫千军","破血狂攻","后发制人"},
		门派="大唐官府",
		AI战斗={AI="物理"},
		主怪=true
	}

	-- 小怪：喽啰
	for i=2,4 do
		local 小怪sx=self:取属性(等级-5)
		战斗单位[i]={
			名称=选中模型.."喽啰",
			模型=选中模型,
			等级=等级-5,
			气血=math.floor(小怪sx.属性.气血*15),
			伤害=math.floor(小怪sx.属性.伤害*1.2),
			法伤=math.floor(小怪sx.属性.法伤*1.0),
			速度=math.floor(小怪sx.属性.速度*1.0),
			防御=math.floor((等级-5)*8),
			法防=math.floor(5+((等级-5)/2)*15),
			魔法=1500,
			修炼={物抗=15,法抗=15,攻修=15},
			技能={"感知"},
			主动技能=小怪sx.技能组 or {"攻击"},
			门派=小怪sx.门派,
			AI战斗={AI=小怪sx.智能 or "物理"}
		}
	end
	return 战斗单位
end
function 胜利MOB_110049(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		if 取随机数() < 50 then
			玩家数据[v].道具:给予任务道具(v,"不死壤")
			玩家数据[v].角色.剧情={主线=8,编号 = 3,地图 = 1112,进度 = 53,附加={物品="不死壤"}}
			local wb =  {"鬼将","鬼将",名称,"。..wewwn.wn ……wwa--…-运气真不好"}
			local xx = {}
			local wb2 = {}
			发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
		end
	end
end
-- 飞升剧情五行法阵第一场：大唐官府主怪 + 参、觜小怪
function 怪物属性:取飞升五行法阵一(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id) or 144
	-- 确保等级不低于135（飞升最低要求）
	if 等级 < 135 then
		等级 = 135
	end

	local sx=self:取属性(等级,"大唐官府","物理")

	-- 主怪：大唐官府门派
	战斗单位[1]={
		名称="五行守护者",
		模型="剑侠客",
		等级=等级,
		气血=math.floor(sx.属性.气血*17.5),
		伤害=math.floor(sx.属性.伤害*1.8),
		法伤=math.floor(sx.属性.法伤*1.3),
		速度=math.floor(sx.属性.速度*1.2),
		防御=math.floor(等级*12),
		法防=math.floor(5+(等级/2)*22),
		魔法=2500,
		修炼={物抗=25,法抗=25,攻修=25},
		技能={"感知","强力","高级反震"},
		主动技能={"横扫千军","破血狂攻","后发制人"},
		门派="大唐官府",
		AI战斗={AI="物理"},
		主怪=true
	}

	-- 小怪：参（左边，攻击极高）
	战斗单位[2]={
		名称="参",
		模型="天兵",
		等级=等级,
		气血=math.floor(sx.属性.气血*8),
		伤害=math.floor(sx.属性.伤害*3.5), -- 一击必杀能力
		法伤=math.floor(sx.属性.法伤*1.0),
		速度=math.floor(sx.属性.速度*1.3),
		防御=math.floor(等级*8),
		法防=math.floor(5+(等级/2)*15),
		魔法=2000,
		修炼={物抗=20,法抗=20,攻修=30},
		技能={"感知","必杀","高级强力"},
		--主动技能={"破血狂攻","横扫千军"},
		门派="大唐官府",
		AI战斗={AI="物理"}
	}

	-- 小怪：觜（右边，攻击极高）
	战斗单位[3]={
		名称="觜",
		模型="天兵",
		等级=等级,
		气血=math.floor(sx.属性.气血*8),
		伤害=math.floor(sx.属性.伤害*3.5), -- 一击必杀能力
		法伤=math.floor(sx.属性.法伤*1.0),
		速度=math.floor(sx.属性.速度*1.3),
		防御=math.floor(等级*8),
		法防=math.floor(5+(等级/2)*15),
		魔法=2000,
		修炼={物抗=20,法抗=20,攻修=30},
		技能={"感知","必杀","高级强力"},
		主动技能={"破血狂攻","横扫千军"},
		门派="大唐官府",
		AI战斗={AI="物理"}
	}

	-- 其他小怪：随机门派
	local 星宿名称={"角","氐","房","尾","箕","斗","牛","女","虚","危","室","壁","奎","娄","胃","昴","毕","井","鬼","柳","星","张","翼","轸"}
	local 已用名称={"参","觜"} -- 记录已使用的名称
	for i=4,5 do
		local 随机门派={"化生寺","方寸山","女儿村","神木林"}
		local 选中门派=随机门派[取随机数(1,#随机门派)]
		local 小怪sx=self:取属性(等级-3,选中门派)
		-- 选择未使用的星宿名称
		local 可用名称={}
		for _,名称 in pairs(星宿名称) do
			local 已使用=false
			for _,已用 in pairs(已用名称) do
				if 名称==已用 then
					已使用=true
					break
				end
			end
			if not 已使用 then
				可用名称[#可用名称+1]=名称
			end
		end
		local 选中名称=可用名称[取随机数(1,#可用名称)]
		已用名称[#已用名称+1]=选中名称

		战斗单位[i]={
			名称=选中名称,
			模型="天兵",
			等级=等级-3,
			气血=math.floor(小怪sx.属性.气血*12),
			伤害=math.floor(小怪sx.属性.伤害*1.2),
			法伤=math.floor(小怪sx.属性.法伤*1.2),
			速度=math.floor(小怪sx.属性.速度*1.0),
			防御=math.floor((等级-3)*9),
			法防=math.floor(5+((等级-3)/2)*18),
			魔法=2000,
			修炼={物抗=18,法抗=18,攻修=18},
			技能={"感知"},
			主动技能=小怪sx.技能组 or {"攻击"},
			门派=选中门派,
			AI战斗={AI=小怪sx.智能 or "物理"}
		}
	end
	return 战斗单位
end
-- 飞升剧情五行法阵第二场：天宫主怪 + 心、亢小怪
function 怪物属性:取飞升五行法阵二(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id) or 144
	if 等级 < 135 then
		等级 = 135
	end

	local sx=self:取属性(等级,"天宫","法系")

	-- 主怪：天宫门派
	战斗单位[1]={
		名称="五行法师",
		模型="神天兵",
		等级=等级,
		气血=math.floor(sx.属性.气血*17),
		伤害=math.floor(sx.属性.伤害*1.3),
		法伤=math.floor(sx.属性.法伤*1.8),
		速度=math.floor(sx.属性.速度*1.2),
		防御=math.floor(等级*10),
		法防=math.floor(5+(等级/2)*25),
		魔法=3000,
		修炼={物抗=25,法抗=25,攻修=25},
		技能={"感知","法术连击","法术暴击"},
		主动技能={"雷击","奔雷咒","天雷斩"},
		门派="天宫",
		AI战斗={AI="法系"},
		主怪=true
	}

	-- 小怪：心（必须先杀）
	战斗单位[2]={
		名称="心",
		模型="天兵",
		等级=等级,
		气血=math.floor(sx.属性.气血*7.75),
		伤害=math.floor(sx.属性.伤害*1.2),
		法伤=math.floor(sx.属性.法伤*1.5),
		速度=math.floor(sx.属性.速度*1.1),
		防御=math.floor(等级*8),
		法防=math.floor(5+(等级/2)*20),
		魔法=2500,
		修炼={物抗=20,法抗=20,攻修=20},
		技能={"感知","神佑复生"},
		主动技能={"雷击","奔雷咒"},
		门派="天宫",
		AI战斗={AI="法系"},
		特殊机制="必杀目标" -- 标记为必须先杀的怪物
	}

	-- 小怪：亢（必须先杀）
	战斗单位[3]={
		名称="亢",
		模型="天兵",
		等级=等级,
		气血=math.floor(sx.属性.气血*7.75),
		伤害=math.floor(sx.属性.伤害*1.2),
		法伤=math.floor(sx.属性.法伤*1.5),
		速度=math.floor(sx.属性.速度*1.1),
		防御=math.floor(等级*8),
		法防=math.floor(5+(等级/2)*20),
		魔法=2500,
		修炼={物抗=20,法抗=20,攻修=20},
		技能={"感知","神佑复生"},
		主动技能={"雷击","奔雷咒"},
		门派="天宫",
		AI战斗={AI="法系"},
		特殊机制="必杀目标" -- 标记为必须先杀的怪物
	}

	-- 其他小怪：随机门派
	local 星宿名称={"角","氐","房","尾","箕","斗","牛","女","虚","危","室","壁","奎","娄","胃","昴","毕","觜","参","井","鬼","柳","星","张","翼","轸"}
	local 已用名称={"心","亢"} -- 记录已使用的名称
	for i=4,5 do
		local 随机门派={"普陀山","龙宫","魔王寨","凌波城"}
		local 选中门派=随机门派[取随机数(1,#随机门派)]
		local 小怪sx=self:取属性(等级-3,选中门派)
		-- 选择未使用的星宿名称
		local 可用名称={}
		for _,名称 in pairs(星宿名称) do
			local 已使用=false
			for _,已用 in pairs(已用名称) do
				if 名称==已用 then
					已使用=true
					break
				end
			end
			if not 已使用 then
				可用名称[#可用名称+1]=名称
			end
		end
		local 选中名称=可用名称[取随机数(1,#可用名称)]
		已用名称[#已用名称+1]=选中名称

		战斗单位[i]={
			名称=选中名称,
			模型="天兵",
			等级=等级-3,
			气血=math.floor(小怪sx.属性.气血*12),
			伤害=math.floor(小怪sx.属性.伤害*1.2),
			法伤=math.floor(小怪sx.属性.法伤*1.2),
			速度=math.floor(小怪sx.属性.速度*1.0),
			防御=math.floor((等级-3)*9),
			法防=math.floor(5+((等级-3)/2)*18),
			魔法=2000,
			修炼={物抗=18,法抗=18,攻修=18},
			技能={"感知"},
			主动技能=小怪sx.技能组 or {"攻击"},
			门派=选中门派,
			AI战斗={AI=小怪sx.智能 or "法系"}
		}
	end
	return 战斗单位
end
-- 飞升剧情五行法阵第三场：魔王寨主怪
function 怪物属性:取飞升五行法阵三(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id) or 144
	if 等级 < 135 then
		等级 = 135
	end

	local sx=self:取属性(等级,"魔王寨","法系")

	-- 主怪：魔王寨门派（法术攻击极强）
	战斗单位[1]={
		名称="五行魔王",
		模型="巨魔王",
		等级=等级,
		气血=math.floor(sx.属性.气血*18),
		伤害=math.floor(sx.属性.伤害*1.3),
		法伤=math.floor(sx.属性.法伤*2.2), -- 法术攻击极强
		速度=math.floor(sx.属性.速度*1.2),
		防御=math.floor(等级*11),
		法防=math.floor(5+(等级/2)*23),
		魔法=3500,
		修炼={物抗=25,法抗=25,攻修=30},
		技能={"感知","法术连击","法术暴击","高级法术波动"},
		主动技能={"三昧真火","地狱烈火","飞砂走石"},
		门派="魔王寨",
		AI战斗={AI="法系"},
		主怪=true
	}

	-- 其他小怪：随机门派
	local 星宿名称={"角","亢","氐","房","心","尾","箕","斗","牛","女","虚","危","室","壁","奎","娄","胃","昴","毕","觜","参","井","鬼","柳","星","张","翼","轸"}
	local 已用名称={} -- 记录已使用的名称
	for i=2,5 do
		local 随机门派={"狮驼岭","阴曹地府","盘丝洞","无底洞"}
		local 选中门派=随机门派[取随机数(1,#随机门派)]
		local 小怪sx=self:取属性(等级-5,选中门派)
		-- 选择未使用的星宿名称
		local 可用名称={}
		for _,名称 in pairs(星宿名称) do
			local 已使用=false
			for _,已用 in pairs(已用名称) do
				if 名称==已用 then
					已使用=true
					break
				end
			end
			if not 已使用 then
				可用名称[#可用名称+1]=名称
			end
		end
		local 选中名称=可用名称[取随机数(1,#可用名称)]
		已用名称[#已用名称+1]=选中名称

		战斗单位[i]={
			名称=选中名称,
			模型="天兵",
			等级=等级-5,
			气血=math.floor(小怪sx.属性.气血*9.7),
			伤害=math.floor(小怪sx.属性.伤害*1.1),
			法伤=math.floor(小怪sx.属性.法伤*1.3),
			速度=math.floor(小怪sx.属性.速度*1.0),
			防御=math.floor((等级-5)*8),
			法防=math.floor(5+((等级-5)/2)*16),
			魔法=2000,
			修炼={物抗=15,法抗=15,攻修=15},
			技能={"感知"},
			主动技能=小怪sx.技能组 or {"攻击"},
			门派=选中门派,
			AI战斗={AI=小怪sx.智能 or "物理"}
		}
	end
	return 战斗单位
end
-- 飞升剧情五行法阵第四场：龙宫主怪
function 怪物属性:取飞升五行法阵四(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id) or 144
	if 等级 < 135 then
		等级 = 135
	end

	local sx=self:取属性(等级,"龙宫","法系")

	-- 主怪：龙宫门派（法术攻击很强）
	战斗单位[1]={
		名称="五行龙王",
		模型="龙太子",
		等级=等级,
		气血=math.floor(sx.属性.气血*18.5),
		伤害=math.floor(sx.属性.伤害*1.3),
		法伤=math.floor(sx.属性.法伤*2.0), -- 法术攻击很强
		速度=math.floor(sx.属性.速度*1.2),
		防御=math.floor(等级*11),
		法防=math.floor(5+(等级/2)*24),
		魔法=3500,
		修炼={物抗=25,法抗=25,攻修=28},
		技能={"感知","法术连击","法术暴击"},
		主动技能={"龙卷雨击","龙腾","龙吟"},
		门派="龙宫",
		AI战斗={AI="法系"},
		主怪=true
	}

	-- 其他小怪：随机门派
	local 星宿名称={"角","亢","氐","房","心","尾","箕","斗","牛","女","虚","危","室","壁","奎","娄","胃","昴","毕","觜","参","井","鬼","柳","星","张","翼","轸"}
	local 已用名称={} -- 记录已使用的名称
	for i=2,5 do
		local 随机门派={"五庄观","普陀山","天宫","化生寺"}
		local 选中门派=随机门派[取随机数(1,#随机门派)]
		local 小怪sx=self:取属性(等级-3,选中门派)
		-- 选择未使用的星宿名称
		local 可用名称={}
		for _,名称 in pairs(星宿名称) do
			local 已使用=false
			for _,已用 in pairs(已用名称) do
				if 名称==已用 then
					已使用=true
					break
				end
			end
			if not 已使用 then
				可用名称[#可用名称+1]=名称
			end
		end
		local 选中名称=可用名称[取随机数(1,#可用名称)]
		已用名称[#已用名称+1]=选中名称

		战斗单位[i]={
			名称=选中名称,
			模型="天兵",
			等级=等级-3,
			气血=math.floor(小怪sx.属性.气血*10),
			伤害=math.floor(小怪sx.属性.伤害*1.2),
			法伤=math.floor(小怪sx.属性.法伤*1.3),
			速度=math.floor(小怪sx.属性.速度*1.0),
			防御=math.floor((等级-3)*9),
			法防=math.floor(5+((等级-3)/2)*18),
			魔法=2200,
			修炼={物抗=18,法抗=18,攻修=18},
			技能={"感知"},
			主动技能=小怪sx.技能组 or {"攻击"},
			门派=选中门派,
			AI战斗={AI=小怪sx.智能 or "法系"}
		}
	end
	return 战斗单位
end

function 怪物属性:取飞升剧情怪物信息(任务id,玩家id)
	local 战斗单位={}
	local 等级=144
	战斗单位[1]={名称="稷神",模型="天兵",伤害=等级*18,气血=等级*100,灵力=等级*9,速度=等级*4.5,防御=等级*11,法防=等级*9,法伤=等级*13,躲闪=等级*4,魔法=2000,等级=等级,技能={},主动技能=Q_门派法术[Q_门派编号[10]]}
	战斗单位[2]={名称="稷神",模型="天兵",伤害=等级*18,气血=等级*100,灵力=等级*9,速度=等级*4.5,防御=等级*11,法防=等级*9,法伤=等级*13,躲闪=等级*4,魔法=2000,等级=等级,技能={},主动技能=Q_门派法术[Q_门派编号[10]]}
	战斗单位[3]={名称="稷神",模型="天兵",伤害=等级*18,气血=等级*100,灵力=等级*9,速度=等级*4.5,防御=等级*11,法防=等级*9,法伤=等级*13,躲闪=等级*4,魔法=2000,等级=等级,技能={},主动技能=Q_门派法术[Q_门派编号[10]]}
	战斗单位[4]={名称="稷神",模型="天兵",伤害=等级*18,气血=等级*100,灵力=等级*9,速度=等级*4.5,防御=等级*11,法防=等级*9,法伤=等级*13,躲闪=等级*4,魔法=2000,等级=等级,技能={},主动技能=Q_门派法术[Q_门派编号[10]]}
	战斗单位[5]={名称="稷神",模型="天兵",伤害=等级*18,气血=等级*100,灵力=等级*9,速度=等级*4.5,防御=等级*11,法防=等级*9,法伤=等级*13,躲闪=等级*4,魔法=2000,等级=等级,技能={},主动技能=Q_门派法术[Q_门派编号[10]]}
	战斗单位[6]={名称="社神",模型="天兵",伤害=等级*18,气血=等级*100,灵力=等级*9,速度=等级*4.5,防御=等级*11,法防=等级*9,法伤=等级*13,躲闪=等级*4,魔法=2000,等级=等级,技能={},主动技能=Q_门派法术[Q_门派编号[10]]}
	战斗单位[7]={名称="社神",模型="天兵",伤害=等级*18,气血=等级*100,灵力=等级*9,速度=等级*4.5,防御=等级*11,法防=等级*9,法伤=等级*13,躲闪=等级*4,魔法=2000,等级=等级,技能={},主动技能=Q_门派法术[Q_门派编号[10]]}
	战斗单位[8]={名称="社神",模型="天兵",伤害=等级*18,气血=等级*100,灵力=等级*9,速度=等级*4.5,防御=等级*11,法防=等级*9,法伤=等级*13,躲闪=等级*4,魔法=2000,等级=等级,技能={},主动技能=Q_门派法术[Q_门派编号[10]]}
	战斗单位[9]={名称="社神",模型="天兵",伤害=等级*18,气血=等级*100,灵力=等级*9,速度=等级*4.5,防御=等级*11,法防=等级*9,法伤=等级*13,躲闪=等级*4,魔法=2000,等级=等级,技能={},主动技能=Q_门派法术[Q_门派编号[10]]}
	战斗单位[10]={名称="社神",模型="天兵",伤害=等级*18,气血=等级*100,灵力=等级*9,速度=等级*4.5,防御=等级*11,法防=等级*9,法伤=等级*13,躲闪=等级*4,魔法=2000,等级=等级,技能={},主动技能=Q_门派法术[Q_门派编号[10]]}
	return 战斗单位
end
-- 飞升剧情牛魔王战斗胜利处理
function 胜利MOB_110046(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"飞升剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=8,编号 = 2,地图 = 1144,进度 = 49,附加={}}
		local wb =  {"牛魔王","牛魔王","精彩精彩彩!（鼓蹄子）心情好多了，趁俺高兴，同意了。"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
-- 飞升剧情五行法阵第一场胜利处理
function 胜利MOB_110052(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	-- for k,v in pairs(id组) do
	-- 	常规提示(v,"#G金之试炼通过！准备迎接木之试炼！")
	-- end
end
-- 飞升剧情五行法阵第二场胜利处理
function 胜利MOB_110053(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	-- for k,v in pairs(id组) do
	-- 	常规提示(v,"#G木之试炼通过！准备迎接水之试炼！")
	-- end
end
-- 飞升剧情五行法阵第三场胜利处理
function 胜利MOB_110054(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	-- for k,v in pairs(id组) do
	-- 	常规提示(v,"#G水之试炼通过！准备迎接火之试炼！")
	-- end
end
-- 飞升剧情五行法阵第四场胜利处理
function 胜利MOB_110055(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	-- for k,v in pairs(id组) do
	-- 	常规提示(v,"#G火之试炼通过！准备迎接最终的土之试炼！")
	-- end
end

function 胜利MOB_110004(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色.历劫.飞升 = true
		玩家数据[v].角色:添加经验(math.floor(1000000),"五行法阵") -- 100万经验
		玩家数据[v].角色:增加剧情点(5) -- 5剧情点
		玩家数据[v].角色.善恶点 = 玩家数据[v].角色.善恶点 + 100 -- 100善恶点
		常规提示(v, "#G/完成五行法阵试炼，获得了#Y/100点#G/善恶点！")
		玩家数据[v].角色.剧情={主线=8,编号 = 1,地图 = 1114,进度 = 54,附加={}}
		local wb =  {"玉帝","玉皇大帝","想不到你居然一口气闯过了五行阵法，他们输的心服口服。看来你已经入于化境，朕授予你三界贤者的称号。"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
-- 心魔挑战胜利处理
function 胜利MOB_110051(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		-- 给予经验和银子奖励
		玩家数据[v].角色:添加经验(500000,"心魔挑战")
		玩家数据[v].角色:添加银子(100000,"心魔挑战")
		玩家数据[v].角色:增加剧情点(1)
		
		-- 心魔挑战完成后，回到菩提老祖处，保持进度41但清除分支状态
		玩家数据[v].角色.剧情={主线=8,编号 = 1,地图 = 1137,进度 = 41,附加={心魔已除=true}}
		
		local wb = {"心魔","心魔","你战胜了心中的执念！快回去找菩提老祖吧。"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end