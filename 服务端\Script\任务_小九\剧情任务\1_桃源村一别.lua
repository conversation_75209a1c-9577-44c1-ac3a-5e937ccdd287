-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2025-05-19 20:43:06
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-01 23:04:53

function 怪物属性:桃源村一别野猪(任务id,玩家id)
	local 战斗单位={}
	local 等级=1
	local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	战斗单位[1]={名称=剧情名称.名称,模型=剧情名称.模型,主怪=true,伤害=80,气血=500,法伤=等级*8,速度=等级*4,防御=等级*4,法防=等级*2,躲闪=等级*2,魔法=200,等级=等级,技能={},主动技能={}}
	return 战斗单位
end
function 胜利MOB_110000(胜利id,战斗数据)
	local 数字id=战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(200),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=1,编号=13,地图=1003,进度=5,附加={}}
		发送数据(玩家数据[v].连接id,227,{剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:桃源村一别狸(任务id,玩家id)
	local 战斗单位={}
	local 等级=1
	local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	战斗单位[1]={名称=剧情名称.名称,模型=剧情名称.模型,开场发言="点击捕捉按钮，将我带回家吧#119",可捕捉=true,伤害=等级*12,气血=等级*300,法伤=等级*8,速度=等级*4,防御=等级*4,法防=等级*2,躲闪=等级*2,魔法=200,等级=等级,技能={},主动技能={}}
	return 战斗单位
end
function 胜利MOB_110001(胜利id,战斗数据)
	local 数字id=战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(210),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=1,编号=11,地图=1003,进度=8,附加={}}
		发送数据(玩家数据[v].连接id,227,{剧情=玩家数据[v].角色.剧情})
	end
end