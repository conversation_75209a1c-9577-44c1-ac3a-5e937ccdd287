-- @Author: baidwwy
-- @Date:   2024-06-07 18:12:58
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-12 19:50:29

function 怪物属性:康太尉(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=85
	local 模型={"天兵","天将","地狱战神"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(85*100 + 2000),
	伤害=math.floor(85*6 + 200),
	法伤=math.floor(85*4 + 150),
	速度=math.floor(85*3 + 150),
	防御=math.floor(85*4.5),
	法防=math.floor(85*3.2),
	等级=等级,
	技能={"高级连击","高级必杀"},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(85*60 + 1500),
		伤害=math.floor(85*4 + 180),
		法伤=math.floor(sx.属性.法伤),
		速度=math.floor(85*2 + 100),
		防御=math.floor(85*3.8),
		法防=math.floor(5+(等级/2)*25.5),
		等级=等级,
		技能={},
		主动技能={"横扫千军","破血狂攻"},
		}
	end
	return 战斗单位
end
function 胜利MOB_110039(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(85000),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=7,编号 = 2,地图 = 1114,进度 = 3,附加={}}
		local wb =  {"天兵","康太尉","果然有些功夫门道......小的们，打不过我们跑啊!"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:家丁(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=85
	local 模型={"赌徒","强盗","山贼"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称=剧情名称.名称,
	模型="赌徒",
	气血=math.floor(85*120 + 2500),
	伤害=math.floor(85*8 + 350),
	法伤=math.floor(85*6 + 280),
	速度=math.floor(85*3 + 150),
	防御=math.floor(85*4.5),
	法防=math.floor(85*3.2),
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(85*80 + 1800),
		伤害=math.floor(85*6 + 250),
		法伤=math.floor(sx.属性.法伤),
		速度=math.floor(85*2 + 100),
		防御=math.floor(85*3.8),
		法防=math.floor(5+(等级/2)*25.5),
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110040(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(85000),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=7,编号 = 30,地图 = 1173,进度 = 12,附加={}}
		local wb =  {"小桃红","高翠兰","你阻我姻缘，我，我我恨死你了!呜!"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:冤魂(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=85
	local 模型={"僵尸","野鬼","骷髅怪"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(85*120 + 2500),
	伤害=math.floor(85*8 + 350),
	法伤=math.floor(85*6 + 280),
	速度=math.floor(85*4 + 300),
	防御=math.floor(85*4.5),
	法防=math.floor(85*3.2),
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(85*80 + 1800),
		伤害=math.floor(85*6 + 250),
		法伤=math.floor(sx.属性.法伤),
		速度=math.floor(85*3 + 200),
		防御=math.floor(85*3.8),
		法防=math.floor(5+(等级/2)*25.5),
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110041(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(85000),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v,"鬼牙攫魂")
		玩家数据[v].角色.剧情={主线=7,编号 = 27,地图 = 1173,进度 = 16,附加={}}
		local wb =  {"僵尸","冤魂","我们再也不敢去捣利了~内风来w"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:哮天犬(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=85
	local 模型={"天兵","天将","地狱战神"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称="哮天犬",
	模型="狼",
	气血=math.floor(85*110 + 2500),
	伤害=math.floor(85*7 + 220),
	法伤=math.floor(sx.属性.法伤),
	速度=math.floor(85*3.5 + 200),
	防御=math.floor(85*4.5),
	法防=math.floor(85*3.2),
	等级=等级,
	技能={"高级连击","高级必杀","高级偷袭"},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(85*80 + 1800),
		伤害=math.floor(85*6 + 250),
		法伤=math.floor(sx.属性.法伤),
		速度=math.floor(85*2 + 100),
		防御=math.floor(85*3.8),
		法防=math.floor(5+(等级/2)*25.5),
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110042(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(85000),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v,"龙骨甲")
		玩家数据[v].道具:给予道具(v,"五龙丹",150)
		玩家数据[v].角色.剧情={主线=7,编号 = 20,地图 = 1173,进度 = 39,附加={物品="蛇蝎美人"}}
		local wb =  {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"哈哈，终于取得五龙丹了!!"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:吊死鬼(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=85
	local 模型={"僵尸"}
	local 模型=模型[取随机数(1,#模型)]
	local sx = self:取属性(等级)
	战斗单位[1]={
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(85*180 + 5000),
	伤害=math.floor(85*8 + 300),
	法伤=math.floor(sx.属性.法伤),
	速度=math.floor(85*4 + 300),
	防御=math.floor(5+(85/2)*25.5),
	法防=math.floor(5+(85/2)*25.5),
	固定伤害=85*10,
	等级=等级,
	技能={"高级夜战","高级偷袭","高级强力","高级必杀","高级敏捷"},
	主动技能={"判官令","阎罗令"},
	}

	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(85*150 + 3000),
		伤害=math.floor(85*6 + 1000),
		法伤=math.floor(sx.属性.法伤),
		速度=math.floor(85*3 + 200),
		防御=math.floor(5+(85/2)*25.5),
		法防=math.floor(5+(85/2)*25.5),
		等级=85,
		固定伤害=85*10,
		技能={"高级夜战","高级偷袭","高级强力","高级敏捷"},
		主动技能={"判官令","阎罗令"},
		AI战斗 = { AI = sx.智能 }
		}
	end
	return 战斗单位
end
function 胜利MOB_110044(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(85000),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v,"乾坤帽")
		玩家数据[v].角色.剧情={主线=7,编号 = 1,地图 = 1130,进度 = 50,附加={}}
		local wb =  {"野鬼","吊死鬼","哎哟，别打了别打了，你们到地狱迷宫四层就能见到她。"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:青灵玄女(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=85
	local 模型={"凤凰"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级,龙宫,"法系")
	战斗单位[1]={
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(85*18*18),
	伤害=math.floor(85*8 + 350),
	法伤=math.floor(85*6 + 280),
	速度=math.floor(85*3.2 + 250),
	防御=math.floor(5+(85/2)*25.5),
	法防=math.floor(5+(85/2)*25.5),
	等级=等级,
	技能={"高级法术波动","魔之心","高级防御","高级幸运"},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(85*15*15),
		伤害=math.floor(85*8 + 350),
		法伤=math.floor(85*6 + 280),
		速度=math.floor(85*2.5 + 180),
		防御=math.floor(5+(85/2)*25.5),
		法防=math.floor(5+(85/2)*25.5),
		等级=85,
		技能={"魔之心","法术暴击","高级神佑复生","高级幸运"},
		主动技能=sx.技能组,
		}
	end
	return 战斗单位
end
function 胜利MOB_110045(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(85000),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予任务道具(v,"定海珠")
		玩家数据[v].道具:给予道具(v,"金柳露")
		玩家数据[v].角色.剧情={主线=7,编号 = 1,地图 = 1192,进度 = 54,附加={物品="定海珠"}}
		local wb =  {"如意仙子","青灵玄女","我认输，珠子给你。"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:牛妖(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=85
	local 模型={"牛妖"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(85*120 + 2500),
	伤害=math.floor(85*8 + 350),
	法伤=math.floor(85*6 + 280),
	速度=math.floor(85*3 + 150),
	防御=math.floor(85*4.5),
	法防=math.floor(85*3.2),
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(85*80 + 1800),
		伤害=math.floor(85*6 + 250),
		法伤=math.floor(sx.属性.法伤),
		速度=math.floor(85*2 + 100),
		防御=math.floor(85*3.8),
		法防=math.floor(5+(85/2)*25.5),
		等级=85,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
-- function 胜利MOB_110046(胜利id,战斗数据)
-- 	local 数字id = 战斗数据.进入战斗玩家id
-- 	local id组={数字id}
-- 	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
-- 		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
-- 			if v~=数字id then
-- 				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
-- 					id组[#id组+1]=v
-- 				end
-- 			end
-- 		end
-- 	end
-- 	for k,v in pairs(id组) do
-- 		-- 检查是否为无名鬼城任务（主线6分支2进度8）
-- 		if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线 == 6 and 玩家数据[v].角色.剧情.分支 == 2 and 玩家数据[v].角色.剧情.进度 == 8 then
-- 			-- 无名鬼城任务完成，设置完成标记
-- 			玩家数据[v].角色.历劫.无名鬼城 = true
-- 			玩家数据[v].角色:添加经验(337500,"无名鬼城剧情")
-- 			玩家数据[v].角色:增加剧情点(1)
-- 			玩家数据[v].角色.剧情={主线=6,编号 = 2,地图 = 1130,进度 = 9,分支=2,附加={}}
-- 			local wb =  {"千年怨鬼","千年怨鬼","不可能...我的怨念...竟然被你化解了..."}
-- 			local xx = {}
-- 			local wb2 = {}
-- 			发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
-- 		else
-- 			-- 原有的主线7剧情处理
-- 			玩家数据[v].角色:添加经验(math.floor(85000),"主线剧情")
-- 			玩家数据[v].角色:增加剧情点(1)
-- 			玩家数据[v].角色.剧情={主线=8,编号 = 2,地图 = 1144,进度 = 49,附加={}}
-- 			local wb =  {"牛魔王","牛魔王","精彩精彩彩!（鼓蹄子）心情好多了，趁俺高兴，同意了。"}
-- 			local xx = {}
-- 			local wb2 = {}
-- 			发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
-- 		end
-- 	end
-- end

-- -- 无名鬼城千年怨鬼胜利函数
-- function 胜利MOB_110047(胜利id,战斗数据)
-- 	local 数字id = 战斗数据.进入战斗玩家id
-- 	local id组={数字id}
-- 	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
-- 		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
-- 			if v~=数字id then
-- 				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
-- 					id组[#id组+1]=v
-- 				end
-- 			end
-- 		end
-- 	end
-- 	for k,v in pairs(id组) do
-- 		-- 无名鬼城任务完成，设置完成标记
-- 		玩家数据[v].角色.历劫.无名鬼城 = true
-- 		玩家数据[v].角色:添加经验(337500,"无名鬼城剧情")
-- 		玩家数据[v].角色:增加剧情点(1)
-- 		玩家数据[v].角色.剧情={主线=6,编号 = 2,地图 = 1130,进度 = 9,分支=2,附加={}}
-- 		local wb =  {"千年怨鬼","千年怨鬼","不可能...我的怨念...竟然被你化解了..."}
-- 		local xx = {}
-- 		local wb2 = {}
-- 		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
-- 	end
-- end



