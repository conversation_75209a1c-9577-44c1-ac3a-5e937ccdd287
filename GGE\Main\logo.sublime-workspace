{"auto_complete": {"selected_items": []}, "buffers": [{"file": "main.lua", "settings": {"buffer_size": 1175, "line_ending": "Windows"}}], "build_system": "Packages/Lua/ggegame.sublime-build", "build_system_choices": [[[["Packages/Lua/ggegame.sublime-build", "Run"], ["Packages/Lua/ggeserver.sublime-build", "Run"]], ["Packages/Lua/ggegame.sublime-build", "Run"]]], "build_varint": "", "command_palette": {"height": 313.0, "last_filter": "", "selected_items": [["Package Control: ", "Package Control: Install Package→安装程序包"]], "width": 632.0}, "console": {"height": 0.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/D/梦幻服务端/GGE/Main"], "file_history": ["/I/我的项目/GGELUA/Core/Server/ggemain.lua", "/I/我的项目/GGELUA/Core/Game/ggemain.lua", "/D/Desktop/GGELUA/extend/Fmod类.lua", "/D/Desktop/GGELUA/lua/Fmod类.lua", "/D/Desktop/GGELUA/samples/logo/main.lua", "/D/Desktop/GGELUA/samples/logo/logo.sublime-project", "/D/Desktop/GGELUA/main.lua", "/D/Desktop/GGELUA/samples/logo.sublime-project"], "find": {"height": 23.0}, "find_in_files": {"height": 0.0, "where_history": []}, "find_state": {"case_sensitive": false, "find_history": ["false", "logo", ".创建"], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": [], "reverse": false, "show_context": true, "use_buffer2": true, "whole_word": false, "wrap": true}, "groups": [{"selected": 0, "sheets": [{"buffer": 0, "file": "main.lua", "semi_transient": false, "settings": {"buffer_size": 1175, "regions": {}, "selection": [[381, 381]], "settings": {"BracketHighlighterBusy": false, "bh_regions": ["bh_unmatched", "bh_unmatched_center", "bh_unmatched_open", "bh_unmatched_close", "bh_unmatched_content", "bh_angle", "bh_angle_center", "bh_angle_open", "bh_angle_close", "bh_angle_content", "bh_default", "bh_default_center", "bh_default_open", "bh_default_close", "bh_default_content", "bh_round", "bh_round_center", "bh_round_open", "bh_round_close", "bh_round_content", "bh_tag", "bh_tag_center", "bh_tag_open", "bh_tag_close", "bh_tag_content", "bh_double_quote", "bh_double_quote_center", "bh_double_quote_open", "bh_double_quote_close", "bh_double_quote_content", "bh_square", "bh_square_center", "bh_square_open", "bh_square_close", "bh_square_content", "bh_curly", "bh_curly_center", "bh_curly_open", "bh_curly_close", "bh_curly_content", "bh_single_quote", "bh_single_quote_center", "bh_single_quote_open", "bh_single_quote_close", "bh_single_quote_content", "bh_regex", "bh_regex_center", "bh_regex_open", "bh_regex_close", "bh_regex_content", "bh_c_define", "bh_c_define_center", "bh_c_define_open", "bh_c_define_close", "bh_c_define_content"], "c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 223, 1, 27, 3, 51, 36, 0, 0, 0, 113, 1, 133, 113, 2, 82, 113, 3, 46], "color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme", "encoding_helper_encoding": "UTF-8", "function_name_status_row": 18, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.tmLanguage", "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 0, "type": "text"}]}], "incremental_find": {"height": 23.0}, "input": {"height": 30.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.exec": {"height": 173.0}, "output.find_results": {"height": 0.0}, "pinned_build_system": "Packages/Lua/ggegame.sublime-build", "project": "logo.sublime-project", "replace": {"height": 42.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_project": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 150.0, "status_bar_visible": true, "template_settings": {}}