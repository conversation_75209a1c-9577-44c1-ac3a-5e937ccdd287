[
	// Basic drag select
	{
		"button": "button1", "count": 1,
		"press_command": "drag_select"
	},
	{
		"button": "button1", "count": 1, "modifiers": ["ctrl"],
		"press_command": "drag_select",
		"press_args": {"additive": true}
	},
	{
		"button": "button1", "count": 1, "modifiers": ["alt"],
		"press_command": "drag_select",
		"press_args": {"subtractive": true}
	},

	// Select between selection and click location
	{
		"button": "button1", "modifiers": ["shift"],
		"press_command": "drag_select",
		"press_args": {"extend": true}
	},
	{
		"button": "button1", "modifiers": ["shift", "ctrl"],
		"press_command": "drag_select",
		"press_args": {"additive": true, "extend": true}
	},

	// Drag select by words
	{
		"button": "button1", "count": 2,
		"press_command": "drag_select",
		"press_args": {"by": "words"}
	},
	{
		"button": "button1", "count": 2, "modifiers": ["ctrl"],
		"press_command": "drag_select",
		"press_args": {"by": "words", "additive": true}
	},
	{
		"button": "button1", "count": 2, "modifiers": ["alt"],
		"press_command": "drag_select",
		"press_args": {"by": "words", "subtractive": true}
	},

	// Drag select by lines
	{
		"button": "button1", "count": 3,
		"press_command": "drag_select",
		"press_args": {"by": "lines"}
	},
	{
		"button": "button1", "count": 3, "modifiers": ["ctrl"],
		"press_command": "drag_select",
		"press_args": {"by": "lines", "additive": true}
	},
	{
		"button": "button1", "count": 3, "modifiers": ["alt"],
		"press_command": "drag_select",
		"press_args": {"by": "lines", "subtractive": true}
	},

	// Alt + Shift + Mouse 1 column select
	{
		"button": "button1", "modifiers": ["alt", "shift"],
		"press_command": "drag_select",
		"press_args": {"by": "columns"}
	},
	{
		"button": "button1", "modifiers": ["ctrl", "alt", "shift"],
		"press_command": "drag_select",
		"press_args": {"by": "columns", "additive": true}
	},

	// Shift + Mouse 2 Column select
	{
		"button": "button2", "modifiers": ["shift"],
		"press_command": "drag_select",
		"press_args": {"by": "columns"}
	},
	{
		"button": "button2", "modifiers": ["shift", "ctrl"],
		"press_command": "drag_select",
		"press_args": {"by": "columns", "additive": true}
	},
	{
		"button": "button2", "modifiers": ["shift", "alt"],
		"press_command": "drag_select",
		"press_args": {"by": "columns", "subtractive": true}
	},

	// Mouse 3 column select
	{
		"button": "button3",
		"press_command": "drag_select",
		"press_args": {"by": "columns"}
	},
	{
		"button": "button3", "modifiers": ["ctrl"],
		"press_command": "drag_select",
		"press_args": {"by": "columns", "additive": true}
	},
	{
		"button": "button3", "modifiers": ["alt"],
		"press_command": "drag_select",
		"press_args": {"by": "columns", "subtractive": true}
	},

	// Simple chording: hold down mouse 2, and click mouse 1
	{
		"button": "button1", "count": 1, "modifiers": ["button2"],
		"command": "expand_selection", "args": {"to": "line"},
		"press_command": "drag_select"
	},
	{
		"button": "button1", "count": 2, "modifiers": ["button2"],
		"command": "expand_selection_to_paragraph"
	},
	{
		"button": "button1", "count": 3, "modifiers": ["button2"],
		"command": "select_all"
	},

	// Switch files with buttons 4 and 5
	{ "button": "button4", "modifiers": [], "command": "prev_view" },
	{ "button": "button5", "modifiers": [], "command": "next_view" },

	// Switch files by holding down button 2, and using the scroll wheel
	{ "button": "scroll_down", "modifiers": ["button2"], "command": "next_view" },
	{ "button": "scroll_up", "modifiers": ["button2"], "command": "prev_view" },

	// Change font size with ctrl+scroll wheel
	{ "button": "scroll_down", "modifiers": ["ctrl"], "command": "decrease_font_size" },
	{ "button": "scroll_up", "modifiers": ["ctrl"], "command": "increase_font_size" },

	{ "button": "button2", "modifiers": [], "command": "context_menu" }
]
