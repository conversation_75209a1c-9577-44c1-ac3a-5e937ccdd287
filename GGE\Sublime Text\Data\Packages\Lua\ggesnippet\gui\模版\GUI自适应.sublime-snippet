<snippet>
	<content><![CDATA[
--======================================================================--
${1:背景} = ${2:对象}:创建自适应("${1:背景}",0,0)
    function ${1:背景}:初始化()
        self:置左上纹理(${3:纹理})
        self:置中上纹理(${4:纹理})
        self:置右上纹理(${5:纹理})
        self:置左边纹理(${6:纹理})
        self:置中间纹理(${7:纹理})
        self:置右边纹理(${8:纹理})
        self:置左下纹理(${9:纹理})
        self:置中下纹理(${10:纹理})
        self:置右下纹理(${11:纹理})
        self:置宽高(w,h)
    end
]]></content>
    <tabTrigger>guizsy_</tabTrigger>
    <scope>source.lua</scope>
    <description>自适应模版</description>
</snippet>
