-- @Author: baidwwy
-- @Date:   2024-06-07 18:12:58
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-02-15 14:35:02

function 怪物属性:小白龙2(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"蛟龙"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称=剧情名称.名称,
	模型=模型,
	气血=math.floor(sx.属性.气血*18),
	伤害=math.floor(sx.属性.伤害*1.5),
	法伤=math.floor(sx.属性.法伤*1.3),
	速度=math.floor(sx.属性.速度*1.2),
	防御=math.floor(等级*3),
	法防=math.floor(5+(等级/2)*30),
	等级=等级,
	技能={"高级神佑","高级连击","高级必杀","高级偷袭"},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*13),
		伤害=math.floor(sx.属性.伤害*1.2),
		法伤=math.floor(sx.属性.法伤*1.2),
		速度=math.floor(sx.属性.速度*1.1),
		防御=math.floor(等级*2.5),
		法防=math.floor(5+(等级/2)*28),
		等级=等级,
		技能={"高级神佑","高级连击"},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110027(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v,"龙鳞") 
		玩家数据[v].角色.剧情={主线=5,编号 = 1,地图 = 1009,进度 = 7,附加={物品="龙鳞"}}
		local wb =  {"小白龙","小白龙","天庭走狗!我斗你不过，凭你将我头颅拿去领赏吧!"}
		local xx = {}
		local wb2 = {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"如果我真的是天庭走狗刚才已经将你杀了，但是我没有这么做。小白龙，现在你可以相信我了?告诉我究竟发生了什么事情?",{},
		{"小白龙","小白龙","这......好吧。我娶亲那天晚上撞破新娘万圣公主私会情郎，为了龙族脸面我不好声张，只得借酒消愁。一直喝得烂醉如泥，人事不知，醒来后发现自己竟然成了纵火烧毁明珠的罪犯!为了保全九族，老父含泪将我告上天庭，定了死罪。我不甘心，我一定要找到真正的凶手!",{},
		{玩家数据[v].角色.模型,玩家数据[v].角色.名称,"此事的确蹊跷，但你现在被天庭通缉，怎能轻易抛头露面?我愿意为你查清此事!",{},
		{"小白龙","小白龙","大侠，此事就拜托你了，请受我一拜!长安大雁塔的镇塔之神和我交情不浅，拿着我的信物去吧，他一定会帮忙的。",{},
		nil}}}}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:梦之魅(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"僵尸","野鬼","骷髅怪"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(sx.属性.气血*12) + 10,
	伤害=math.floor(sx.属性.伤害) + 10,
	法伤=math.floor(sx.属性.法伤) + 10,
	速度=math.floor(sx.属性.速度) + 10,
	防御=math.floor(等级*2) + 10,
	法防=math.floor(5+(等级/2)*25.5) + 10,
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*10) + 10,
		伤害=math.floor(sx.属性.伤害) + 10,
		法伤=math.floor(sx.属性.法伤) + 10,
		速度=math.floor(sx.属性.速度) + 10,
		防御=math.floor(等级*2) + 10,
		法防=math.floor(5+(等级/2)*25.5) + 10,
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110028(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=5,编号 = 1,地图 = 1007,进度 = 9,附加={战斗=1}}
		local wb =  {"僵尸","梦之魅","呜，为什么魔魅就不能同心爱的人在一起。"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:血之魅(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"僵尸","野鬼","骷髅怪"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(sx.属性.气血*12) + 20,
	伤害=math.floor(sx.属性.伤害) + 20,
	法伤=math.floor(sx.属性.法伤) + 20,
	速度=math.floor(sx.属性.速度) + 20,
	防御=math.floor(等级*2) + 20,
	法防=math.floor(5+(等级/2)*25.5) + 20,
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*10) + 20,
		伤害=math.floor(sx.属性.伤害) + 20,
		法伤=math.floor(sx.属性.法伤) + 20,
		速度=math.floor(sx.属性.速度) + 20,
		防御=math.floor(等级*2) + 20,
		法防=math.floor(5+(等级/2)*25.5) + 20,
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110029(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=5,编号 = 1,地图 = 1008,进度 = 10,附加={战斗=1}}
		local wb =  {"僵尸","血之魅","血……我的血都流光了……"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:森之魅(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"僵尸","野鬼","骷髅怪"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(sx.属性.气血*12) + 30,
	伤害=math.floor(sx.属性.伤害) + 30,
	法伤=math.floor(sx.属性.法伤) + 30,
	速度=math.floor(sx.属性.速度) + 30,
	防御=math.floor(等级*2) + 30,
	法防=math.floor(5+(等级/2)*25.5) + 30,
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*10) + 30,
		伤害=math.floor(sx.属性.伤害) + 30,
		法伤=math.floor(sx.属性.法伤) + 30,
		速度=math.floor(sx.属性.速度) + 30,
		防御=math.floor(等级*2) + 30,
		法防=math.floor(5+(等级/2)*25.5) + 30,
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110030(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=5,编号 = 1,地图 = 1009,进度 = 11,附加={战斗=1}}
		local wb =  {"僵尸","森之魅","呜……我不甘心!"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:奔波儿灞(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"虾兵","蟹将","蛤蟆精"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(sx.属性.气血*12) + 40,
	伤害=math.floor(sx.属性.伤害) + 40,
	法伤=math.floor(sx.属性.法伤) + 40,
	速度=math.floor(sx.属性.速度) + 40,
	防御=math.floor(等级*2) + 40,
	法防=math.floor(5+(等级/2)*25.5) + 40,
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*10) + 40,
		伤害=math.floor(sx.属性.伤害) + 40,
		法伤=math.floor(sx.属性.法伤) + 40,
		速度=math.floor(sx.属性.速度) + 40,
		防御=math.floor(等级*2) + 40,
		法防=math.floor(5+(等级/2)*25.5) + 40,
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110031(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v,"兽王腰带")
		玩家数据[v].道具:给予任务道具(v,"白剑")
		玩家数据[v].角色.剧情={主线=5,编号 = 2,地图 = 1116,进度 = 13,附加={物品="白剑"}}
		local wb =  {"蛤蟆精","奔波儿灞","哎呀英雄饶命，偷宝的不是我啊，是我家万圣公主的夫君九头精怪！"}
		local xx = {}
		local wb2 = {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"到底是怎么回事，从实招来饶你不死！",{},
		{"蛤蟆精","奔波儿灞","我叫做奔波儿灞。是乱石山碧波潭万圣龙王手下的蛤蟆精。我家万圣公主招得一个驸马唤做九头精怪，神通广大。日前显法力下了一阵血雨，借机偷了镇塔之宝。驸马偷宝之后怕人发觉，故差我在此看守，这是公主赐给我的白剑。",{},
		{玩家数据[v].角色.模型,玩家数据[v].角色.名称,"万圣公主不是小白龙的未婚妻么?看来她的情郎就是偷宝贼九头精怪，我先去找万圣公主问个明白!",{},
		nil}}}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:蟹将军(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"虾兵","蟹将","蛤蟆精"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={ 
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(sx.属性.气血*12) + 10,  -- 增加10
	伤害=math.floor(sx.属性.伤害) + 10,  -- 增加10
	法伤=math.floor(sx.属性.法伤) + 10,  -- 增加10
	速度=math.floor(sx.属性.速度) + 10,  -- 增加10
	防御=math.floor(等级*2) + 10,  -- 增加10
	法防=math.floor(5+(等级/2)*25.5) + 10,  -- 增加10
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={ 
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*10) + 10,  -- 增加10
		伤害=math.floor(sx.属性.伤害) + 10,  -- 增加10
		法伤=math.floor(sx.属性.法伤) + 10,  -- 增加10
		速度=math.floor(sx.属性.速度) + 10,  -- 增加10
		防御=math.floor(等级*2) + 10,  -- 增加10
		法防=math.floor(5+(等级/2)*25.5) + 10,  -- 增加10
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110032(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=5,编号 = 14,地图 = 1116,进度 = 14,附加={战斗=1}}
		local wb =  {"蟹将","蟹将军","来人啊!有人擅自闯入龙宫啦!虾将军把这个人拦下来!"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:虾将军(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"虾兵","蟹将","蛤蟆精"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={ 
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(sx.属性.气血*12) + 20,  -- 增加20
	伤害=math.floor(sx.属性.伤害) + 20,  -- 增加20
	法伤=math.floor(sx.属性.法伤) + 20,  -- 增加20
	速度=math.floor(sx.属性.速度) + 20,  -- 增加20
	防御=math.floor(等级*2) + 20,  -- 增加20
	法防=math.floor(5+(等级/2)*25.5) + 20,  -- 增加20
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={ 
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*10) + 20,  -- 增加20
		伤害=math.floor(sx.属性.伤害) + 20,  -- 增加20
		法伤=math.floor(sx.属性.法伤) + 20,  -- 增加20
		速度=math.floor(sx.属性.速度) + 20,  -- 增加20
		防御=math.floor(等级*2) + 20,  -- 增加20
		法防=math.floor(5+(等级/2)*25.5) + 20,  -- 增加20
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110033(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色.剧情={主线=5,编号 = 16,地图 = 1116,进度 = 15,附加={}}
		local wb =  {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"里里，龙宫里没人拦得住我!"}
		local xx = {}
		local wb2 = {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"到底是怎么回事，从实招来饶你不死！",{},
		{"蛤蟆精","奔波儿灞","我叫做奔波儿灞。是乱石山碧波潭万圣龙王手下的蛤蟆精。我家万圣公主招得一个驸马唤做九头精怪，神通广大。日前显法力下了一阵血雨，借机偷了镇塔之宝。驸马偷宝之后怕人发觉，故差我在此看守，这是公主赐给我的白剑。",{},
		{玩家数据[v].角色.模型,玩家数据[v].角色.名称,"万圣公主不是小白龙的未婚妻么?看来她的情郎就是偷宝贼九头精怪，我先去找万圣公主问个明白!",{},
		nil}}}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:天庭侍卫(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"天兵","天将","地狱战神"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={ 
	名称=剧情名称.名称,
	模型="凤凰",
	气血=math.floor(sx.属性.气血*12) + 30,  -- 增加30
	伤害=math.floor(sx.属性.伤害) + 30,  -- 增加30
	法伤=math.floor(sx.属性.法伤) + 30,  -- 增加30
	速度=math.floor(sx.属性.速度) + 30,  -- 增加30
	防御=math.floor(等级*2) + 30,  -- 增加30
	变异=true,
	法防=math.floor(5+(等级/2)*25.5) + 30,  -- 增加30
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={ 
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*10) + 30,  -- 增加30
		伤害=math.floor(sx.属性.伤害) + 30,  -- 增加30
		法伤=math.floor(sx.属性.法伤) + 30,  -- 增加30
		速度=math.floor(sx.属性.速度) + 30,  -- 增加30
		防御=math.floor(等级*2) + 30,  -- 增加30
		法防=math.floor(5+(等级/2)*25.5) + 30,  -- 增加30
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110034(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v,"龙须草")
		玩家数据[v].道具:给予道具(v,"天眼通符")
		玩家数据[v].道具:给予道具(v,"血色茶花",10)
		玩家数据[v].角色.剧情={主线=5,编号 = 16,地图 = 1116,进度 = 17,附加={物品="龙须草",战斗=1}}
		local wb =  {"王母","王母娘娘","有人抢走了龙须草!"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:万圣公主(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"小龙女"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={ 
	名称=剧情名称.名称,
	模型="蛟龙",
	气血=math.floor(sx.属性.气血*12) + 50,  -- 增加50
	伤害=math.floor(sx.属性.伤害) + 50,  -- 增加50
	法伤=math.floor(sx.属性.法伤) + 50,  -- 增加50
	速度=math.floor(sx.属性.速度) + 50,  -- 增加50
	防御=math.floor(等级*2) + 50,  -- 增加50
	法防=math.floor(5+(等级/2)*25.5) + 50,  -- 增加50
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={ 
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*10) + 50,  -- 增加50
		伤害=math.floor(sx.属性.伤害) + 50,  -- 增加50
		法伤=math.floor(sx.属性.法伤) + 50,  -- 增加50
		速度=math.floor(sx.属性.速度) + 50,  -- 增加50
		防御=math.floor(等级*2) + 50,  -- 增加50
		法防=math.floor(5+(等级/2)*25.5) + 50,  -- 增加50
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110035(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v,"龙须草")
		玩家数据[v].道具:给予道具(v,"天眼通符")
		玩家数据[v].道具:给予道具(v,"血色茶花",10)
		玩家数据[v].角色.剧情={主线=5,编号 = 4,地图 = 1112,进度 = 18,附加={物品="龙须草"}}
		local wb =  {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"哼，这就是你欺骗我的下场!快说九头精怪在那里?!"}
		local xx = {}
		local wb2 = {"万圣公主","万圣公主","呜呜，他去魔王寨赴宴了。",{},
		nil}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:牛妖亲兵(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"牛妖"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={ 
	名称="牛妖亲兵",
	模型=模型,
	气血=math.floor(sx.属性.气血*12) + 40,  -- 增加40
	伤害=math.floor(sx.属性.伤害) + 40,  -- 增加40
	法伤=math.floor(sx.属性.法伤) + 40,  -- 增加40
	速度=math.floor(sx.属性.速度) + 40,  -- 增加40
	防御=math.floor(等级*2) + 40,  -- 增加40
	法防=math.floor(5+(等级/2)*25.5) + 40,  -- 增加40
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={ 
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*10) + 40,  -- 增加40
		伤害=math.floor(sx.属性.伤害) + 40,  -- 增加40
		法伤=math.floor(sx.属性.法伤) + 40,  -- 增加40
		速度=math.floor(sx.属性.速度) + 40,  -- 增加40
		防御=math.floor(等级*2) + 40,  -- 增加40
		法防=math.floor(5+(等级/2)*25.5) + 40,  -- 增加40
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110036(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=5,编号 = 2,地图 = 1145,进度 = 20,附加={战斗=1}}
		local wb =  {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"九头精怪，现在轮到你了!!"}
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:九头精怪(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"牛妖"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={ 
	名称=剧情名称.名称,
	模型=剧情名称.模型,
	气血=math.floor(sx.属性.气血*20) + 50,  -- 增加50
	伤害=math.floor(sx.属性.伤害)*2 + 50,  -- 增加50
	法伤=math.floor(sx.属性.法伤) + 50,  -- 增加50
	速度=math.floor(sx.属性.速度)*5 + 50,  -- 增加50
	防御=math.floor(5+(等级/2)*25.5) + 50,  -- 增加50
	法防=math.floor(5+(等级/2)*25.5) + 50,  -- 增加50
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={ 
		名称=模型.."喽啰",
		模型=模型,
		气血=math.floor(sx.属性.气血*10) + 50,  -- 增加50
		伤害=math.floor(sx.属性.伤害*1.5) + 50,  -- 增加50
		法伤=math.floor(sx.属性.法伤) + 50,  -- 增加50
		速度=math.floor(sx.属性.速度)*5 + 50,  -- 增加50
		防御=math.floor(等级*2) + 50,  -- 增加50
		法防=math.floor(5+(等级/2)*25.5) + 50,  -- 增加50
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110037(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=5,编号 = 3,地图 = 1112,进度 = 21,附加={战斗=1}}
		local wb =  {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"快将镇塔之宝交出来！"}
		local xx = {}
		local wb2 = {"九头精怪","九头精怪","哼，我早就把镇塔之宝进献给玉帝了!为什么小白龙就是天命取经人，取个经书就可以修成正果，我就要辛辛苦苦修炼几千年!我不甘心，我也可以去取经，我也要修成正果，所以小白龙一定要死!",{},
		{玩家数据[v].角色.模型,玩家数据[v].角色.名称,"你做了这么多坏事，就算取得真经也无法修成正果的!",{},
		nil}}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end
function 怪物属性:天庭侍卫2(任务id,玩家id,序号)
	local 战斗单位={}
	local 剧情名称=取假人表(玩家数据[玩家id].角色.剧情.地图,玩家数据[玩家id].角色.剧情.编号)
	local 等级=70
	local 模型={"天兵","天将","地狱战神"}
	local 模型=模型[取随机数(1,#模型)]
	local sx=self:取属性(等级)
	战斗单位[1]={
	名称="天兵",
	模型="天兵",
	气血=math.floor(sx.属性.气血*12) + 30,  -- 增加30
	伤害=math.floor(sx.属性.伤害*1.5) + 30,  -- 增加30
	法伤=math.floor(sx.属性.法伤) + 30,  -- 增加30
	速度=math.floor(sx.属性.速度) + 30,  -- 增加30
	防御=math.floor(等级*2) + 30,  -- 增加30
	法防=math.floor(5+(等级/2)*25.5) + 30,  -- 增加30
	等级=等级,
	技能={},
	主动技能=sx.技能组,
	}
	for i=2,5  do
		战斗单位[i]={ 
		名称=模型.."天将",
		模型=模型,
		气血=math.floor(sx.属性.气血*10) + 30,  -- 增加30
		伤害=math.floor(sx.属性.伤害*1.5) + 30,  -- 增加30
		法伤=math.floor(sx.属性.法伤) + 30,  -- 增加30
		速度=math.floor(sx.属性.速度) + 30,  -- 增加30
		防御=math.floor(等级*2) + 30,  -- 增加30
		法防=math.floor(5+(等级/2)*25.5) + 30,  -- 增加30
		等级=等级,
		技能={},
		主动技能=sx.技能组
		}
	end
	return 战斗单位
end
function 胜利MOB_110038(胜利id,战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组={数字id}
	if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长 then
		for k,v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v~=数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线==玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度==玩家数据[数字id].角色.剧情.进度 then
					id组[#id组+1]=v
				end
			end
		end
	end
	for k,v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(109119),"主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情={主线=5,编号 = 2,地图 = 1141,进度 = 22,附加={}}
		local wb =  {"玉帝","玉皇大帝","快去请观音姐姐和如来佛祖!!!"}
		local xx = {}
		local wb2 = {玩家数据[v].角色.模型,玩家数据[v].角色.名称,"哼，不用你请，我自去找观音姐姐问个明白!",{},
		nil}
		发送数据(玩家数据[v].连接id, 1501, {模型= wb[1],名称 = wb[2],对话 =wb[3] ,选项 = xx,下一页=wb2,剧情=玩家数据[v].角色.剧情})
	end
end