<snippet>
	<content><![CDATA[
--======================================================================--
${1:输入框} = ${2:对象}:创建输入("${1:输入框}",0,0)
    function ${1:输入框}:初始化()
        self:置限制字数(16)
        self:置编辑模式(true)
    end
    function ${1:输入框}:消息事件(消息,a,b)
        if 消息 == "输入字符" and a == '\t' then

            return true
        end
    end
]]></content>
    <tabTrigger>guisr_</tabTrigger>
    <scope>source.lua</scope>
    <description>输入框模版</description>
</snippet>
