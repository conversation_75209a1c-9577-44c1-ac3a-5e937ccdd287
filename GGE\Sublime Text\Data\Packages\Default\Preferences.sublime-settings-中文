{
    //将您的设置放在 "Packages/User/Preferences.sublime-settings" 文件中，这将覆盖此处的设置。
    //设置也可以放在特定语法的设置文件中，例如，针对 python 文件的Packages/User/Python.sublime-settings。
    
    {
        //设置文本区域内使用的颜色。
        //“auto” 值将根据操作系统的外观在 "light_color_scheme" 和
        //"dark_color_scheme" 之间切换。
        "color_scheme": "Mariana.sublime-color-scheme",

        //控制当设置为 "auto" 时的 "color_scheme"。
        "light_color_scheme": "Breakers.sublime-color-scheme",
        "dark_color_scheme": "Mariana.sublime-color-scheme",

        //注意，font_face 和 font_size 被特定于平台的设置文件覆盖了，
        //例如 "Preferences (Linux).sublime-settings"。
        //因此，在这里设置它们将没有效果：您必须在用户文件首选项中设置它们。
        
        "font_face": "",
        "font_size": 10,

        // 有效的选项在所有平台上都是：
        //  - "no_bold": 禁用粗体文本
        //  - "no_italic": 禁用斜体文本
        // 抗锯齿选项：
        //  - "no_antialias": 禁用抗锯齿
        //  - "gray_antialias": 使用灰阶抗锯齿而不是次像素
        // 连字选项：
        //  - "no_liga": 禁用标准连字 (OpenType liga 特性)
        //  - "no_clig": 禁用上下文连字 (OpenType clig 特性)
        //  - "no_calt": 禁用上下文替代 (OpenType calt 特性)
        //  - "dlig": 启用可选连字 (OpenType dlig 特性)
        //  - "ss01": 启用 OpenType 样式集 1。可以通过更改最后两位数字启用集 1 到 10。
        //  仅限 Windows 选项：
        //  - "directwrite": (默认) 使用 DirectWrite 进行字体渲染
        //  - "gdi": 使用 GDI 进行字体渲染
        //  - "dwrite_cleartype_classic": 仅适用于 "directwrite" 模式，
        //        应该呈现类似于传统 GDI 的字体
        //  - "dwrite_cleartype_natural": 仅适用于 "directwrite" 模式，
        //        应该呈现类似于 ClearType "自然质量" GDI 的字体
        //  - "subpixel_antialias": 在系统级别禁用时强制 ClearType 抗锯齿
        //  仅限 Mac 选项：
        //  - "no_round": 当 "font_size" 很小时，不要对单行距字体进行圆整。
        "font_options": [],
        // 主题文本的字体选项。有效的选项与 "font_options" 相同，除了不支持 "no_bold" 和 "no_italic"
        "theme_font_options": [],

        // 视为分隔单词的字符
        "word_separators": "./\\()\"'-:,.;<>~!@#$%^&*|+=[]{}`~?",

        // 视为分隔子词的字符
        "sub_word_separators": "_",

        // 设置为 false 以防止在 gutter 中绘制行号
        "line_numbers": true,

        // 设置为 false 以完全隐藏 gutter
        "gutter": true,

        // gutter 和文本之间的间距
        "margin": 4,

        // 折叠按钮是在 gutter 中显示的三角形，用于折叠文本区域
        "fold_buttons": true,

        // 仅在鼠标悬停在 gutter 上时才显示折叠按钮
        "fade_fold_buttons": true,

        // 要使用的代码折叠风格
        // - "auto": 使用当前语法的默认设置
        // - "force_indentation": 始终使用基于缩进的代码折叠
        // - "scope_only": 仅允许基于语法的代码折叠
        "fold_style": "auto",

        // 在 gutter 中指示修改过的行
        // - true: 对所有文件启用修改行指示器
        // - "auto": 仅在 Git 中跟踪的文件上启用修改行指示器
        // - false: 禁用修改行指示器
        "mini_diff": true,

        // 要显示垂直标尺的列。每个条目也可以是列、标尺样式和可选的标尺宽度的列表，例如：[[80, "solid", 2], 78, [90, "dotted"]]
        "rulers": [],

        // 控制如何绘制标尺线
        // - "dotted": 绘制为点状线
        // - "stippled": 绘制为虚线
        // - "solid": 绘制为实线
        "ruler_style": "dotted",

        // 控制绘制多宽的标尺线
        "ruler_width": 1.0,

        // 设置为 true 以默认启用拼写检查
        "spell_check": false,

        // 制表符等于多少空格
        "tab_size": 4,

        // 设置为 true 以在按下 tab 时插入空格
        "translate_tabs_to_spaces": false,

        // 如果 translate_tabs_to_spaces 为 true，则 use_tab_stops 会使 tab 和 backspace 以及 delete 插入/删除到下一个制表位
        "use_tab_stops": true,

        // 设置为 false 以禁用在加载时检测制表符与空格
        "detect_indentation": true,

        // 自动计算按下 enter 时的缩进
        "auto_indent": true,

        // 使自动缩进更智能，例如，在 C 中的 if 语句后缩进下一行。需要启用 auto_indent。
        "smart_indent": true,

        // 添加空白直到第一个开放的括号，当缩进。需要启用 auto_indent。
        "indent_to_bracket": false,

        // 当移动光标离开行时，修剪自动缩进添加的空白
        "trim_automatic_white_space": true,

        // 禁用水平滚动，如果启用。
        // 可以设置为 true、false 或 "auto"，在源代码中禁用，否则启用。
        "word_wrap": "auto",

        // 设置为非 0 值以强制在该列而不是窗口宽度处换行。查看 "wrap_width_style" 以获取额外选项。
        "wrap_width": 0,

        // 控制如何应用 "wrap_width" 设置。以下是可用选项：
        // - "constant": 总是在换行宽度处换行。
        // - "min": 如果可用空间较少，则在换行宽度或更少处换行。
        "wrap_width_style": "constant",

        // 设置为 false 以防止对折行进行缩进到同一级别
        "indent_subsequent_lines": true,

        // 将文本在窗口中居中绘制，而不是左对齐
        "draw_centered": false,

        // 控制自动配对引号、括号等
        "auto_match_enabled": true,

        // 自动关闭 HTML 和 XML 标签当输入 </ 时。
        "auto_close_tags": true,

        // 用于拼写检查的单词列表。也可以是字典列表。
        "dictionary": "Packages/Language - English/en_US.dic",

        // 设置要检查拼写错误的范围
        "spelling_selector": "markup.raw, source string.quoted - punctuation - meta.preprocessor.include, source comment - source comment.block.preprocessor, -(source, constant, keyword, storage, support, variable, markup.underline.link, meta.tag)",

        // 设置为 true 以在 minimap 上绘制可见矩形周围的边框。
        // 边框的颜色将由颜色方案中的 "minimap_border" 键决定
        "draw_minimap_border": false,

        // 总是可视化 minimap 上的视口，而不是仅在鼠标悬停时显示
        "always_show_minimap_viewport": false,

        // 是否使 minimap 应该水平滚动以匹配视口。
        "minimap_horizontal_scrolling": false,

        // 如果启用，将突出显示任何带有光标的行
        "highlight_line": false,

        // 如果启用，将突出显示任何带有光标的行的 gutter
        "highlight_gutter": true,

        // 如果启用，将根据颜色方案突出显示 gutter 中带有光标的行的行号
        "highlight_line_number": true,

        // 有效的值为 "smooth", "phase", "blink" and "solid". Previous
        // 光标样式
        // 以前的版本 Sublime Text 默认使用 "smooth"。
        "caret_style": "solid",

        // 这些设置控制光标的大小
        "caret_extra_top": 4,
        "caret_extra_bottom": 4,
        "caret_extra_width": 1,

        // 当启用时，光标将被绘制为一个矩形，使用当前字符的宽度
        "block_caret": false,

        // 设置为 false 以禁用下划线标记包围光标的括号
        "match_brackets": true,

        // 设置为 false 如果您宁愿只在光标旁边有一个括号时才突出显示它
        "match_brackets_content": true,

        // 设置为 false 以不突出显示方括号。这只在 match_brackets 为 true 时有效
        "match_brackets_square": true,

        // 设置为 false 以不突出显示花括号。这只在 match_brackets 为 true 时有效
        "match_brackets_braces": true,

        // 设置为 false 以不突出显示尖括号。这只在 match_brackets 为 true 时有效
        "match_brackets_angle": false,

        // 启用可视化 HTML 和 XML 中匹配的标签
        "match_tags": true,

        // 突出显示当前选中文本的其他出现位置
        "match_selection": true,

        // 每行顶部的额外间距，单位为像素
        "line_padding_top": 0,

        // 每行底部的额外间距，单位为像素
        "line_padding_bottom": 0,

        // 设置为 false 以禁用滚动到缓冲区末尾之外。
        // 在 Mac 上，这个值会在特定平台的设置中被覆盖，因此您需要在用户设置中放置此行以覆盖它。
        //
        // 这个设置也可以设置为 0 到 1 之间的数字来指定允许滚动到缓冲区末尾之外的程度。0.5 会滚动一半，0.0 和 false 相同。
        "scroll_past_end": true,

        // 设置在滚动时显示的上下文行数。这会影响所有选择更改，比如选择拖动、页上/页下和移动光标。
        "scroll_context_lines": 0,

        // 控制当在第一行或最后一行按下上或下键时会发生什么。
        // 在 Mac 上，这个值会在特定平台的设置中被覆盖，因此您需要在用户设置中放置此行以覆盖它。
        "move_to_limit_on_up_down": false,

        // 控制何时绘制空白。以下选项可以组合使用：
        // - "selection": 在当前选择下绘制空白。
        // - "leading": 绘制从行开始到第一个字符之间的任何空白。
        // - "enclosed": 绘制被其他字符包围的空白。
        // - "trailing": 绘制行尾的空白。
        // - "isolated": 绘制只包含空白的行。
        // - "all": 上述所有，即始终绘制空白。
        //
        // 这些选项可以通过在其后面添加下划线分隔的任何以下选项进一步细化：
        // - "none": 不绘制这种空白。
        // - "tabs": 只绘制制表符。
        // - "spaces": 只绘制空格。
        // - "mixed": 只绘制与缩进样式不匹配的空白。例如，如果 "translate_tabs_to_spaces" 为 true，则只绘制制表符。
        // - "mixed_tabs": 类似于 "mixed"，但只绘制制表符。
        // - "mixed_spaces": 类似于 "mixed"，但只绘制空格。
        // - "all": 绘制制表符和空格。这是默认设置。
        //
        // 注意，选项按顺序应用。因此，后面的选项可能会覆盖前面的选项。
        //
        // 示例：
        // - ["selection", "trailing", "isolated"]:
        //     在行尾和当前选择下绘制空白。
        //
        // - ["all_tabs", "selection"]:
        //     绘制任何地方的制表符和当前选择下的任何空白。
        //
        // - ["all_mixed"]:
        //     绘制任何与缩进样式不匹配的空白。
        //
        // - ["leading_mixed", "isolated_mixed"]:
        //     绘制任何不匹配缩进样式的缩进。
        //
        // - ["selection_mixed_tabs"]:
        //     如果缩进样式是空格，则仅在当前选择下绘制制表符。
        //
        // - ["all_tabs", "selection"]:
        //     绘制所有制表符和当前选择下的任何空白。
        //
        // - ["all", "selection_none"]:
        //     默认的反面。除了当前选择下的空白外，到处都绘制空白。
        "draw_white_space": ["selection"],

        // 控制如何绘制非 ASCII 空白。
        // - "none": 按原样绘制 Unicode 空白，例如隐藏零宽度空格。
        // - "punctuation": 绘制定义为标点的 Unicode 空白字符。这包括 NBSP，但不包括 CJK 表意文字空格。
        // - "all": 绘制所有非 ASCII 空格字符。
        "draw_unicode_white_space": "punctuation",

        // 控制是否将 Unicode 双向字符绘制为代码点。
        "draw_unicode_bidi": true,

        // 控制字符（或空白）可以绘制为十六进制或使用它们的缩写：
        // - "hex": 使用它们的十六进制编码绘制字符，即 <0xA0>
        // - "names": 使用它们的缩写绘制字符，即 <NBSP>
        "control_character_style": "hex",

        // 控制当在状态栏中显示列选择描述（"Line X, Column Y"）时使用哪种类型的列：
        // - "virtual": 计算虚拟列。制表符计算为它们在空格中的宽度。
        // - "real": 计算真实的列。制表符计算为一列。
        "selection_description_column_type": "virtual",

        // 设置为 false 以关闭缩进指南。
        // 可以通过编辑相应的 .tmTheme 文件并指定颜色 "guide"、"activeGuide" 和 "stackGuide" 来自定义缩进指南的颜色和宽度
        "draw_indent_guides": true,

        // 控制如何绘制缩进指南。选项可以组合，有效选项为：
        //  - "draw_normal" - 将为每个缩进组绘制普通的缩进指南
        //  - "draw_active" - 将为包含光标的组绘制不同颜色的缩进指南
        //  - "draw_active_single" - 将为包含光标的组中右侧的缩进指南绘制不同颜色
        //  - "solid" - 可以与任何 draw_* 选项组合使用，以绘制实线而不是点线
        "indent_guide_options": ["draw_normal"],

        // 控制保存时从何处删除尾随空白。
        // - "none": 在保存时不删除任何尾随空白。
        // - "all": 在保存时删除所有尾随空白。
        // - "not_on_caret": 仅删除不影响光标的空白。当与 "save_on_focus_lost" 和某些桌面环境结合使用时，
        //   如果应用程序频繁失去焦点，这可以避免光标大幅跳动。
        "trim_trailing_white_space_on_save": "none",

        // 仅删除您修改过的文件部分中的尾随空白。如果文件的其他部分有尾随空白，它们将保持不变。
        "trim_only_modified_white_space": true,

        // 设置为 true 以确保在保存时文件的最后一行以换行符结束
        "ensure_newline_at_eof_on_save": false,

        // 设置为 true 以在切换到不同文件或应用程序时自动保存文件
        "save_on_focus_lost": false,

        // 当无法自动确定编码时使用的编码。
        // ASCII、UTF-8 和 UTF-16 编码将被自动检测。
        "fallback_encoding": "Western (Windows 1252)",

        // 保存新文件时使用的编码，以及打开未定义编码的文件时使用的编码（例如，纯 ASCII 文件）。
        // 如果以特定编码打开文件（要么检测到要么显式给出），此设置将被忽略，文件将保存为打开时使用的编码。
        "default_encoding": "UTF-8",

        // 包含 null 字节的文件默认以十六进制打开
        "enable_hexadecimal_encoding": true,

        // 确定新文件中每行结尾使用哪些字符。
        // 有效值为 'system'（操作系统使用的任何内容）、'windows'（CR LF）、以及 'unix'（仅 LF）。
        "default_line_ending": "system",

        // 当启用时，悬停单词上会显示一个弹出窗口，列出所有可能的定义符号位置。需要 index_files。
        "show_definitions": true,

        // 当启用时，按下 tab 会插入最匹配的补全。当禁用时，tab 只会触发片段或插入一个 tab。
        // 按下 shift+tab 可以在启用了 tab_completion 的情况下插入一个明确的 tab。
        "tab_completion": true,

        // 启用自动补全在输入时自动触发。
        "auto_complete": true,

        // 自动补全自动触发的最大文件大小。
        "auto_complete_size_limit": 4194304,

        // 输入后显示自动完成窗口之前的延迟，单位为毫秒。
        "auto_complete_delay": 50,

        // 控制自动补全触发的语法作用域
        "auto_complete_selector": "meta.tag, source - comment - string.quoted.double.block - string.quoted.single.block - string.unquoted.heredoc",

        // 额外的触发自动补全的情况
        "auto_complete_triggers": [
            {
                "selector": "text.html, text.xml",
                "characters": "<"
            },
            {
                "selector": "punctuation.accessor",
                "rhs_empty": true
            }
        ],

        // 默认情况下，按下 enter 会提交当前补全。此设置可以使其改为在 tab 上完成。
        // 在 tab 上完成通常是更好的选择，因为它消除了提交补全和插入新行之间的歧义。
        "auto_complete_commit_on_tab": false,

        // 控制当自动补全字段处于活动状态时是否显示自动补全。
        // 仅在 auto_complete_commit_on_tab 为 true 时相关。
        "auto_complete_with_fields": false,

        // 控制当按下上键时，如果自动补全窗口中的第一项被选中，会发生什么：如果为 false，则隐藏窗口，
        // 否则选择窗口中的最后一项。对于按下下键时，如果最后一项被选中，情况也类似。
        "auto_complete_cycle": false,

        // 当启用时，自动补全会使用索引数据来提供来自其他文件的补全。
        "auto_complete_use_index": true,

        // 如果之前选择的补全应该自动被选中
        "auto_complete_use_history": false,

        // 控制在输入时如何重新排序自动补全结果：
        // - "none" 将根据补全与输入文本的匹配程度完全重新排序结果。
        // - "some" 将部分重新排序结果，同时考虑到补全与输入文本的匹配程度和补全的可能性。
        // - "strict" 永远不会重新排序结果。
        "auto_complete_preserve_order": "some",

        // 如果自动补全引擎认为足够可能，添加尾随符号（例如 '.', '()'）
        "auto_complete_trailing_symbols": false,

        // 如果自动补全引擎认为足够可能，在补全后面添加一个空格
        "auto_complete_trailing_spaces": true,

        // 当为 false 时，片段不会出现在自动补全中。它们仍然可以通过输入它们的 tab 触发器并在自动补全不显示时按下 tab 来触发。
        "auto_complete_include_snippets": true,

        // 当此设置为 false 时，当输入时，片段不会显示在自动补全对话框中，而是只有在明确触发自动补全对话框时才会显示。
        "auto_complete_include_snippets_when_typing": true,

        // 对于大于这个大小的文件，将不进行语法检测。这会显著加快加载大型文件的速度。设置为 0 将始终执行语法检测。默认值为 16MiB。
        "syntax_detection_size_limit": 16777216,

        // 指定要忽略的片段文件的通配模式列表。
        // 例如，要忽略所有默认的 C++ 片段，可以将此设置为 ["C++/*"]
        "ignored_snippets": [],

        // 这些设置在输入时自动隐藏 UI 的各个部分。移动鼠标将再次显示它们。
        "auto_hide_menu": false,
        "auto_hide_tabs": false,
        "auto_hide_status_bar": false,

        // 与 auto_hide_tabs 相关，如果启用，当切换文件时，标签页将暂时显示。标签页会在恢复输入或经过固定时间后自动隐藏。
        "reveal_tabs_with_timeout": false,

        // 当在 Linux 和 Windows 上隐藏菜单时，当按下 alt 键时，如果此设置为 false，则禁用显示菜单。这不会影响自动隐藏或通过命令面板切换菜单。
        "reveal_menu": true,

        // 如果为 true，则在输入时隐藏鼠标指针/光标。移动指针将使其重新显示。此设置在 macOS 上无效。
        "hide_pointer_while_typing": true,

        // 默认情况下，shift+tab 只有在选择跨越多行时才会进行缩进。在其他时候按下 shift+tab，它将插入一个 tab 字符 -
        // 这允许在启用了 tab_completion 的情况下插入制表符。将此设置为 true 以使 shift+tab 总是进行缩进，而不是插入制表符。
        "shift_tab_unindent": false,

        // 如果为 true，则当选择为空时，复制和剪切命令将操作当前行，而不是什么都不做。
        "copy_with_empty_selection": true,

        // 如果为 true，则在选择文本时，将所选文本复制到查找面板中。
        // 在 Mac 上，这个值会在特定平台的设置中被覆盖为 true。
        "find_selected_text": true,

        // 如果为 true，则在选择文本时，从所选文本复制到查找面板的模式将自动转义，当进行正则表达式搜索时。
        "regex_auto_escape": true,

        // 当 auto_find_in_selection 启用时，当选择多行文本时，"在所选文本中查找" 标志将自动启用。
        // "find_only" 或 "replace_only" 也可以使用，以仅启用此行为的查找或替换面板。
        "auto_find_in_selection": false,

        // 这决定了当按下 "查找全部" 或 "替换全部" 按钮时，查找面板是否关闭。注意这不会改变按键绑定行为。
        "close_find_after_find_all": true,
        "close_find_after_replace_all": true,

        // 在滚动条中突出显示查找结果
        "highlight_find_results_in_scrollbar": true,

        // 要在滚动条中显示的查找结果的最大数量。如果这个数量被超过则不显示任何结果。使用 0 去除此限制。
        "find_scroll_highlights_limit": 8192,

        // 最大文件大小，以使用 "突出显示匹配项" 选项。当输入搜索查询时，需要搜索整个文件，这可能会很快变慢。使用 0 去除此限制。
        "find_highlight_matches_max_size": 16777216, // 16MiB

        // 启用正则表达式时，与 "find_highlight_matches_max_size" 相同，但使用 1MiB。
        "find_regex_highlight_matches_max_size": 1048576, // 1MiB

        // 查找文件中的查找的最大输出大小。防止搜索结果过多时使用太多内存。使用 0 去除此限制。
        "find_in_files_max_result_size": 16777216, // 16MiB

        // 跳过超过此大小的文件。这可以防止在存在大型文件时搜索使用所有可用内存。请注意，在多核计算机上，一次可能会加载多个文件。使用 0 去除此限制。
        "find_in_files_max_file_size": 104857600, // 100MiB

        // 从查找文件缓冲区打开搜索结果。
        "find_in_files_side_by_side": false,

        // 要显示的上下文行数
        "find_in_files_context_lines": 2,

        // 显示每个结果周围行中的字符数，超出此范围的文本将被省略。值为 0 将去除任何限制。
        "find_in_files_context_characters": 300,

        // 从结果中抑制无法打开和搜索文件的错误消息
        "find_in_files_suppress_errors": false,

        // 当拖动文本时，通过点击所选文本开始拖放操作。
        "drag_text": true,

        // 当文件被拖放到 Sublime Text 上时获得焦点。在 Windows 上，这个值将被覆盖为 true。
        "focus_on_file_drop": false,

        //
        // 用户界面设置
        //

        // 主题
        // 主题控制 Sublime Text 的 UI 外观（按钮、标签页、滚动条等）
        // "auto" 值将根据操作系统的外观在 "light_theme" 和 "dark_theme" 之间切换。
        "theme": "auto",

        // 控制当设置为 "auto" 时的 "theme"。
        "light_theme": "Default.sublime-theme",
        "dark_theme": "Default Dark.sublime-theme",

        // 自适应主题特有的：控制是否使用自定义或默认标题栏。
        // 注意，在 Windows 上，启用此功能并且使用自适应主题时，将使用汉堡菜单。
        "themed_title_bar": true,

        // 控制 Default、Default Dark 和 Adaptive 主题的文件标签样式。
        // 选项："rounded", "square"
        //   "angled" 提供了 Sublime Text 3 中的标签样式，但只与 Default 和 Adaptive 主题兼容，并且不支持新特性，如非活动窗格调暗。
        "file_tab_style": "rounded",

        // 如果非活动工作表的背景略微修改，使输入焦点更明显。
        "inactive_sheet_dimming": true,

        // 设置为 0 以禁用平滑滚动。设置为 0 到 1 之间的值以滚动得更慢，或设置为大于 1 以滚动得更快
        "scroll_speed": 1.0,

        // 控制展开或折叠文件夹时的侧边栏动画
        "tree_animation_enabled": true,

        // 控制整个应用程序的动画
        "animation_enabled": true,

        // 使修改过的标签更明显
        "highlight_modified_tabs": false,

        // 如果为 false，则隐藏标签关闭按钮
        "show_tab_close_buttons": true,

        // 确定关闭按钮在哪一边
        "show_tab_close_buttons_on_left": false,

        // 在侧边栏中以粗体显示文件夹
        "bold_folder_labels": false,

        // 在使用自适应主题时，在 UI 的各个部分之间绘制分隔线
        "adaptive_dividers": false,

        // 在弹出窗口下绘制阴影
        "popup_shadows": true,

        // 仅限 Mac。控制在 10.12 及更高版本中使用 macOS 的本地标签。每个本地标签包含一个完整的项目，从而允许在一个单独的窗口中打开多个项目。有效值为 "system"、"preferred" 和 "disabled"。
        "native_tabs": "system",

        // 是否新标签页应该在当前标签页之后打开，还是在标签页列表的末尾打开。
        "open_tabs_after_current": true,

        // 有效值为 "system"、"enabled" 和 "disabled"
        "overlay_scroll_bars": "system",

        // 允许标签页左右滚动，而不是仅仅缩小
        "enable_tab_scrolling": true,

        // 隐藏滚动标签页的按钮，需要使用触摸板或鼠标滚轮
        "hide_tab_scrolling_buttons": false,

        // 隐藏新建标签页按钮
        "hide_new_tab_button": false,

        // 在状态栏中显示切换侧边栏按钮
        "show_sidebar_button": true,

        // 在状态栏中显示 Git 状态，需要启用 show_git_status 设置
        "show_git_status_in_status_bar": true,

        // 在状态栏中显示文件编码
        "show_encoding": false,

        // 在状态栏中显示行尾样式
        "show_line_endings": false,

        // 在状态栏中显示缩进单位
        "show_indentation": true,

        // 在状态栏中显示语法
        "show_syntax": true,

        // 在状态栏中以完整长度、紧凑或完全隐藏的方式显示行号、列号。有效值为 "enabled"、"compact" 和 "disabled"
        "show_line_column": "enabled",

        // 在状态栏中显示拼写错误
        "show_spelling_errors": true,

        // 放大整个用户界面。Sublime Text 必须重启后才能生效。1.0 是正常比例，0.0 允许根据旧版 Linux 配置自动检测文本比例，这些配置不完全支持 GTK 显示缩放。
        "ui_scale": 0.0,

        // 启用硬件加速渲染。这将渲染移动到您的 GPU，允许在更高分辨率下更快地渲染。更改此设置需要重启应用程序才能生效。
        // - "none": 执行 CPU 渲染。
        // - "opengl": 使用 OpenGL 进行渲染。最低要求版本为 4.1
        //
        // 在 Mac 上，这个值会在特定平台的设置中被覆盖。
        "hardware_acceleration": "none",

        //
        // 应用程序行为设置
        //

        // 使用 hot_exit 退出应用程序时，它将立即关闭而不提示。未保存的修改和打开的文件将被保留，并在下次启动时恢复。
        //
        // Hot exit 有不同的模式可供选择：
        // - "always": 应用程序退出时始终执行热退出。这包括在相关平台上关闭最后一个窗口时。
        // - "only_on_quit": 仅在请求应用程序退出时执行热退出，而不是在关闭最后一个窗口时。此设置仅在 Windows 和 Linux 上使用。
        // - "disabled": 禁用热退出。
        "hot_exit": "always",

        // 启用 hot_exit_projects 时，关闭项目将立即执行而不提示。未保存的修改和打开的文件将保留在工作区文件中，并在重新打开时恢复。
        "hot_exit_projects": true,

        // remember_full_screen 允许 Sublime Text 在退出时如果是全屏模式，则启动时也以全屏模式启动。当设置为 false 时，Sublime Text 永远不会以全屏模式启动。
        "remember_full_screen": false,

        // remember_workspace 使 Sublime Text 记住每个窗口上次所在的工作区。当设置为 false 时，操作系统将决定在哪个工作区打开窗口。这在 Wayland 上不起作用。
        "remember_workspace": true,

        // remember_layout 使 Sublime Text 启动时使用上次关闭时的布局。仅在禁用 hot_exit 时适用。
        "remember_layout": false,

        // 是否应通过 Sublime Text 更新系统的最新文件/文件夹列表。
        "update_system_recent_files": true,

        // 仅限 Mac。如果应调用用户的默认 shell 来获取用户自定义的环境变量。可以是布尔值，或者是要调用的 shell 的路径字符串。Sublime Text 必须重启后才能生效。
        "shell_environment": true,

        // 如果文件在磁盘上已更改，则重新加载该文件。
        "reload_file_on_change": true,

        // 始终在重新加载文件前提示，即使文件没有被修改。如果文件有未保存的更改，将始终显示提示。
        // 只有当 "reload_file_on_change" 为 true 时才会显示。
        "always_prompt_for_file_reload": false,

        // 在重新打开 Sublime Text 时关闭已保存但已从文件系统删除的文件（未保存的文件不会被关闭）。如果此设置为 false，则不会关闭任何文件，相反，它们将作为空文件恢复。
        //
        // 这在您从不稳定的网络文件系统工作时非常有用，因为如果连接不活跃，选项卡将会丢失。
        "close_deleted_files": true,

        // 当从文件资源管理器或命令行打开文件时，控制是否创建新窗口。
        // - "never": 除非没有窗口打开，否则永远不会打开新窗口。
        // - "always": 总是在新窗口中打开文件。
        // - "finder_only": (仅限 macOS) 仅在从 Finder 或拖放到 dock 图标上时，在新窗口中打开文件。
        //
        // 在 Mac 上，这个值会在特定平台的设置中被覆盖。
        "open_files_in_new_window": "never",

        // 仅限 Mac: 这控制是否在启动时创建一个空窗口。
        "create_window_at_startup": true,

        // 仅限 Mac: 在 Touch Bar 上显示最近的文件。
        "show_navigation_bar": true,

        // 仅限 Mac: 使用全局查找剪贴板。
        "use_find_clipboard": true,

        // 设置为 true 以在关闭最后一个文件时关闭窗口，除非窗口中有一个打开的文件夹。
        // 在 Mac 上，这个值会在特定平台的设置中被覆盖，因此您需要在用户设置中放置此行以覆盖它。
        "close_windows_when_empty": false,

        // 在标题栏中显示文件的完整路径。在 Mac 上，这个值会在特定平台的设置中被覆盖，默认值为 false。
        "show_full_path": true,

		// 在标题栏中显示文件的相对路径。这将覆盖 sidebar 中列出的文件的 show_full_path，但是其他文件仍然使用 show_full_path。
        "show_rel_path": false,

        // 在标题栏中显示 "项目 - 文件" 或 "文件 - 项目"。
        "show_project_first": false,

        // 构建时显示构建结果面板。如果设置为 false，则可以通过 "工具/构建结果" 菜单显示构建结果。
        "show_panel_on_build": true,

        // 在发生错误的地方下方显示错误。
        "show_errors_inline": true,

        // 在侧边栏和状态栏中显示文件的 Git 仓库信息。需要重启 Sublime Text 才能生效。
        "show_git_status": true,

        // 启用显示 Git 状态，允许在用户主目录中的 Git 仓库显示状态。默认禁用，因为这通常是偶然的，可能会导致与文件系统监视相关的高 CPU 使用率。
        "allow_git_home_dir": false,

        // 确定是否应该与索引或 HEAD 对比跟踪的 Git 文件。
        // 有效值为 "index" 或 "head"
        "git_diff_target": "index",

        // 到 Sublime Merge 可执行文件的路径。如果 Sublime Merge 安装在非标准位置，例如当使用便携式安装时，应设置此路径。设置为 'null' 以隐藏 Sublime Merge 集成。
        "sublime_merge_path": "",

        // 点击侧边栏中的文件时预览文件内容。双击或编辑预览将打开文件并为其分配一个标签页。
        // - true: 总是点击预览，包括右键点击
        // - false: 从不预览
        // - "only_left": 仅在左键点击时预览，右键点击将更改选择但不会预览文件。
        "preview_on_click": true,

        // 控制点击侧边栏中已经打开的单个文件时的行为。
        // - true: 如果文件已在任何组中打开，它将被选中。
        // - false: 只有在文件在焦点组中打开时，它才会被选中，否则将打开文件的新视图。
        "select_across_groups": false,

        // 控制控制台历史记录中保留的最大行数。注意，较大的限制将增加内存使用量。使用 0 去除任何限制。
        "console_max_history_lines": 3000,

        // folder_exclude_patterns 和 file_exclude_patterns 控制哪些文件在侧边栏中的文件夹中列出。这些也可以在每个项目的基础上设置。
        "folder_exclude_patterns": [".svn", ".git", ".hg", "CVS", ".Trash", ".Trash-*"],
        "file_exclude_patterns": ["*.pyc", "*.pyo", "*.exe", "*.dll", "*.obj", "*.o", "*.a", "*.lib", "*.so", "*.dylib", "*.ncb", "*.sdf", "*.suo", "*.pdb", "*.idb", ".DS_Store", ".directory", "desktop.ini", "*.class", "*.psd", "*.db", "*.sublime-workspace"],
        // 这些文件仍然会在侧边栏中显示，但不会包含在 "转到任何地方" 或 "在文件中查找"
        "binary_file_patterns": ["*.jpg", "*.jpeg", "*.png", "*.gif", "*.ttf", "*.tga", "*.dds", "*.ico", "*.eot", "*.pdf", "*.swf", "*.jar", "*.zip"],

        // 文件索引解析侧边栏中的所有文件，并构建它们的符号索引。这是 Goto Definition 工作所必需的。
        "index_files": true,

        // 设置用于索引的线程数。值为 0 将使 Sublime Text 根据核心数进行猜测。使用 index_files 设置禁用所有工作器。这需要重启才能生效。
        "index_workers": 0,

        // 设置索引器是否应该排除被 git 忽略的文件。
        "index_exclude_gitignore": true,

        // 是否应该跳过未知文件扩展名的索引，或者完全跳过它们。如果一个文件扩展名没有被识别，我们建议设置/更改该扩展名的默认语法，而不是关闭这个设置。
        "index_skip_unknown_extensions": true,

        // index_exclude_patterns 表示哪些文件不会被索引。
        "index_exclude_patterns": ["*.log"],

        // 确定 Goto Anything 是否应该排除被 git 忽略的文件。
        "goto_anything_exclude_gitignore": false,

        // 当启用 Vintage（vi 仿真包）时
        // （见下面的 ignored_packages），此设置控制文件默认是以命令模式还是插入模式打开
        "vintage_start_in_command_mode": false,

        // Yank 命令可以将文本放置在内部寄存器中，或在系统剪贴板中。
        "vintage_use_clipboard": false,

        // 默认情况下，Vintage 会将所有 ctrl+<key> 按键绑定解释为常规的 Sublime Text 按键绑定。将此设置为 true 以使 ctrl 键像在 vim 中那样起作用，而不是 (例如，ctrl+f 来向前移动一页)。
        "vintage_ctrl_keys": false,

        // relative_line_numbers 将每个行号绘制为与当前行的距离。与 Vintage 一起使用很有用。
        "relative_line_numbers": false,

        // 在这里列出要忽略的包。当从这个列表中删除条目时，可能需要重新启动才能生效，如果该包包含插件。
        "ignored_packages": ["Vintage"]
    }
}
