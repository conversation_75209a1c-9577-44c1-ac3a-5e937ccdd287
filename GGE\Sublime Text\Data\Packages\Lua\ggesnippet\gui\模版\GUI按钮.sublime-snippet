<snippet>
	<content><![CDATA[
--======================================================================--
${1:按钮} = ${2:对象}:创建按钮("${1:按钮}",0,0)
    function ${1:按钮}:初始化()
    	self:置正常纹理(${3:纹理})
    	self:置按下纹理(${5:纹理})
        self:置编辑模式(true)
    end

    function ${1:按钮}:消息事件(消息,a,b)
        if 消息 =="左键弹起" then

        end
    end
]]></content>
    <tabTrigger>guian_</tabTrigger>
    <scope>source.lua</scope>
    <description>按钮模版</description>
</snippet>
