-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2024-05-12 22:27:43
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-05-31 16:02:02
local 副本_齐天大圣 = class()
function 副本_齐天大圣:初始化()
  local 地图列表 = { 6043, 6044, 6045, 6046 }
  local 初始坐标列表 = { 1514, 1122, 1111, 1009 }
  for i, 地图编号 in ipairs(地图列表) do
    地图处理类.地图数据[地图编号] = { npc = {}, 单位 = {}, 传送圈 = {} }
    地图处理类.地图玩家[地图编号] = {}
    地图处理类.地图坐标[地图编号] = 地图处理类.地图坐标[初始坐标列表[i]]
    地图处理类.地图单位[地图编号] = {}
    地图处理类.单位编号[地图编号] = 1000
  end
end

function 副本_齐天大圣:取齐天大圣次数限制(id, 次数)
  if 副本数据.齐天大圣.完成[id] then
    添加最后对话(id, "你今日已经完成过该副本了")
    return true
  elseif 玩家数据[id].队伍 ~= 0 then
    for i = 1, #队伍数据[玩家数据[id].队伍].成员数据 do
      local lsid = 队伍数据[玩家数据[id].队伍].成员数据[i]
      if 副本数据.齐天大圣.完成[lsid] then
        添加最后对话(id, 玩家数据[lsid].角色.名称 .. "今日已经完成过该副本了")
        return true
      end
    end
  end
  return false
end

function 副本_齐天大圣:开启齐天大圣副本(id)
  if 玩家数据[id].队伍 == 0 or 玩家数据[id].队长 == false then
    常规提示(id, "#Y/该任务必须组队完成且由队长领取")
    return
  elseif 取队伍人数(id) < 5 and 调试模式 == false then
    常规提示(id, "#Y此副本要求队伍人数不低于5人")
    return
  elseif not 调试模式 and 取等级要求(id, 80) == false then
    常规提示(id, "#Y此副本要求角色等级不能低于80级")
    return
  elseif self:取齐天大圣次数限制(id) then
    return
  elseif 取队员任务存在(玩家数据[id].队伍,581) then
    常规提示(id,"#Y/队伍中已有队员正在进行该副本任,无法领取新的副本")
    return
  end
  local 任务id = id .. "_581_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 999)
  副本数据.齐天大圣.进行[id] = { 进程 = 1, 主任务id = 任务id }
  设置齐天大圣副本(id)
  任务数据[任务id] = {
    id = 任务id,
    起始 = os.time(),
    结束 = 7200,
    玩家id = {},
    队伍组 = {},
    副本id = id,
    类型 = 581
  }
  local 队伍id = 玩家数据[id].队伍
  for n = 1, #队伍数据[队伍id].成员数据 do
    local 临时id = 队伍数据[队伍id].成员数据[n]
    任务数据[任务id].队伍组[#任务数据[任务id].队伍组 + 1] = 临时id
    玩家数据[临时id].角色:添加任务(任务id)
    副本数据.齐天大圣.完成[临时id] = true
    常规提示(临时id, "#Y你开启了齐天大圣副本")
  end
  地图处理类:跳转地图(id, 6043, 12, 108)
end

function 副本_齐天大圣:更改模型(id, 模型)
  玩家数据[id].角色.变身数据 = 模型
  发送数据(玩家数据[id].连接id, 37, 玩家数据[id].角色.变身数据)
  设置任务1(id, 5, 玩家数据[id].角色.变身数据, 7200)
  地图处理类:更改模型(id, 玩家数据[id].角色.变身数据, 1)
end

function 设置齐天大圣副本(id)
  local 随机名称 = {}
  local 随机模型 = {}
  local 随机序列 = os.time() % 1000
  玩家数据[id].战斗 = 0
  local 队伍id = 玩家数据[id].队伍
  if 副本数据.齐天大圣.进行[id].进程 == 1 then
    local text1 = { "齐天大圣 始" }
    for n = 1, #队伍数据[队伍id].成员数据 do
      local 临时id = 队伍数据[队伍id].成员数据[n]
      发送数据(玩家数据[临时id].连接id, 6557, { 文本 = text1, 类型 = "齐天大圣序", 字体 = nil, 音乐 = nil, 背景 = nil, 横排显示 = true, 动画调用 = { 581, 1 } })
    end
    随机名称 = { "伤心的小猴", "临死的老猴" }
    随机模型 = { "超级金猴", "长眉灵猴" }
    副本数据.齐天大圣.进行[id].小猴老猴 = { 伤心的小猴 = 5, 临死的老猴 = 5 }
    for i = 1, 2 do
      for n = 1, 8 do
        地图 = 6043
        local xy = 地图处理类.地图坐标[地图]:取随机点()
        local 任务id = "_582_" .. os.time() .. "_" .. 取随机数(1, 999) .. n .. i
        任务数据[任务id] = {
          id = 任务id,
          起始 = os.time(),
          结束 = 1200,
          玩家id = id,
          队伍组 = {},
          名称 = 随机名称[i],
          模型 = 随机模型[i],
          x = xy.x,
          y = xy.y,
          副本id = id,
          地图编号 = 地图,
          小地图名称颜色 = 3,
          地图名称 = 取地图名称(地图),
          类型 = 582
        }
        地图处理类:添加单位(任务id)
      end
    end
  elseif 副本数据.齐天大圣.进行[id].进程 == 2 then
    副本数据.齐天大圣.进行[id].无常 = { 黑无常 = 0, 白无常 = 0 }
    local 任务id = id .. "_583_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6043
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7200,
      玩家id = id,
      队伍组 = {},
      名称 = "白无常",
      模型 = "白无常",
      变异 = true,
      x = 114,
      y = 106,
      方向 = 0,
      副本id = id,
      地图编号 = 地图,
      地图名称 = 取地图名称(地图),
      类型 = 583,
    }
    地图处理类:添加单位(任务id)
    任务id = id .. "_584_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6043
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7200,
      玩家id = id,
      队伍组 = {},
      名称 = "黑无常",
      模型 = "黑无常",
      变异 = true,
      x = 31,
      y = 20,
      方向 = 0,
      副本id = id,
      地图编号 = 地图,
      地图名称 = 取地图名称(地图),
      类型 = 584,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.齐天大圣.进行[id].进程 == 3 then
    local text1 = { "齐天大圣之 大闹地府" }
    for n = 1, #队伍数据[队伍id].成员数据 do
      local 临时id = 队伍数据[队伍id].成员数据[n]
      发送数据(玩家数据[临时id].连接id, 6557, { 文本 = text1, 类型 = "齐天大圣2", 字体 = nil, 音乐 = nil, 背景 = nil, 横排显示 = true, 动画调用 = { 581, 2 } })
    end
    local 任务id = id .. "_585_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6044
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7200,
      玩家id = id,
      队伍组 = {},
      名称 = "判官",
      模型 = "判官",
      x = 53,
      y = 66,
      方向 = 0,
      副本id = id,
      地图编号 = 地图,
      地图名称 = 取地图名称(地图),
      类型 = 585,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.齐天大圣.进行[id].进程 == 4 then
    local 任务id = id .. "_586_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6044
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7200,
      玩家id = id,
      队伍组 = {},
      名称 = "阎王",
      模型 = "阎罗王",
      x = 33,
      y = 54,
      方向 = 0,
      副本id = id,
      地图编号 = 地图,
      地图名称 = 取地图名称(地图),
      类型 = 586,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.齐天大圣.进行[id].进程 == 5 then
    发送数据(玩家数据[id].连接id, 6558, { 调用 = { 581, 3 } })
    local 任务id = id .. "_587_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6043
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7200,
      玩家id = id,
      队伍组 = {},
      名称 = "太白金星",
      模型 = "太白金星",
      x = 114,
      y = 106,
      方向 = 方向,
      副本id = id,
      地图编号 = 地图,
      地图名称 = 取地图名称(地图),
      类型 = 587,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.齐天大圣.进行[id].进程 == 6 then
    副本数据.齐天大圣.进行[id].展示实力 = { 玉皇大帝 = 0, 四大天王 = 0 }
    local 任务id = id .. "_588_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6045
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7200,
      玩家id = id,
      队伍组 = {},
      名称 = "玉皇大帝",
      模型 = "玉帝",
      x = 152,
      y = 108,
      方向 = 0,
      副本id = id,
      地图编号 = 地图,
      地图名称 = 取地图名称(地图),
      类型 = 588,
    }
    地图处理类:添加单位(任务id)
    任务id = id .. "_589_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6045
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7200,
      玩家id = id,
      队伍组 = {},
      名称 = "天王",
      模型 = "大力金刚",
      x = 167,
      y = 111,
      方向 = 0,
      副本id = id,
      地图编号 = 地图,
      地图名称 = 取地图名称(地图),
      类型 = 589,
    }
    地图处理类:添加单位(任务id)
    任务id = id .. "_589.1_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6045
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 1200,
      玩家id = id,
      队伍组 = {},
      名称 = "齐天大圣",
      模型 = "孙悟空",
      x = 208,
      y = 137,
      方向 = 0,
      副本id = id,
      地图编号 = 地图,
      地图名称 = 取地图名称(地图),
      类型 = 589.1,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.齐天大圣.进行[id].进程 == 7 then
    local 任务id = id .. "_590_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6045
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7200,
      玩家id = id,
      队伍组 = {},
      名称 = "玉皇大帝",
      模型 = "玉帝",
      x = 152,
      y = 108,
      方向 = 0,
      副本id = id,
      地图编号 = 地图,
      地图名称 = 取地图名称(地图),
      类型 = 590,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.齐天大圣.进行[id].进程 == 8 then
    地图 = 6045
    随机名称 = { "盗马贼" }
    随机模型 = { "强盗" }
    副本数据.齐天大圣.进行[id].盗马贼 = 10
    for i = 1, 10 do
      local xy = 地图处理类.地图坐标[地图]:取随机点()
      local 任务id = id .. "_591_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(88, 99999999) .. i
      任务数据[任务id] = {
        id = 任务id,
        起始 = os.time(),
        结束 = 7586,
        玩家id = id,
        队伍组 = {},
        名称 = "盗马贼",
        模型 = "强盗",
        x = xy.x,
        y = xy.y,
        方向 = 方向,
        副本id = id,
        地图编号 = 地图,
        小地图名称颜色 = 3,
        地图名称 = 取地图名称(地图),
        类型 = 591,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.齐天大圣.进行[id].进程 == 9 then
    副本数据.齐天大圣.进行[id].盗马贼 = nil
    local 任务id = id .. "_592_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6045
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7586,
      玩家id = id,
      队伍组 = {},
      名称 = "调皮的小马",
      模型 = "超级神马",
      x = xy.x,
      y = xy.y,
      方向 = 方向,
      副本id = id,
      地图编号 = 地图,
      小地图名称颜色 = 3,
      地图名称 = 取地图名称(地图),
      类型 = 592,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.齐天大圣.进行[id].进程 == 10 then
    local 任务id = id .. "_593_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6045
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7586,
      玩家id = id,
      队伍组 = {},
      名称 = "天蓬元帅",
      模型 = "猪八戒",
      x = 246,
      y = 105,
      方向 = 1,
      副本id = id,
      地图编号 = 地图,
      地图名称 = 取地图名称(地图),
      类型 = 593,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.齐天大圣.进行[id].进程 == 11 then
    副本数据.齐天大圣.进行[id].百万天兵 = { 百万天兵 = 0, 巨灵神 = 0 }
    -- 百万天兵
    local 任务id = id .. "_594_1_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 9999)
    地图 = 6043
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7586,
      玩家id = id,
      队伍组 = {},
      名称 = "百万天兵",
      模型 = "天兵",
      x = xy.x,
      y = xy.y,
      方向 = 方向,
      副本id = id,
      地图编号 = 地图,
      小地图名称颜色 = 3,
      地图名称 = 取地图名称(地图),
      类型 = 594,
    }
    地图处理类:添加单位(任务id)
    
    -- 巨灵神
    local 任务id = id .. "_594_2_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 9999)
    地图 = 6043
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7586,
      玩家id = id,
      队伍组 = {},
      名称 = "巨灵神",
      模型 = "大力金刚",
      x = xy.x,
      y = xy.y,
      方向 = 方向,
      副本id = id,
      地图编号 = 地图,
      小地图名称颜色 = 3,
      地图名称 = 取地图名称(地图),
      类型 = 594,
    }
    地图处理类:添加单位(任务id)
    
    -- 李靖
    local 任务id = id .. "_594_3_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 9999)
    地图 = 6043
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7586,
      玩家id = id,
      队伍组 = {},
      名称 = "李靖",
      模型 = "李靖",
      饰品显示 = true,
      x = xy.x,
      y = xy.y,
      方向 = 方向,
      副本id = id,
      地图编号 = 地图,
      小地图名称颜色 = 3,
      地图名称 = 取地图名称(地图),
      类型 = 594,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.齐天大圣.进行[id].进程 == 12 then
    local 任务id = id .. "_595_" .. os.time() .. "_" .. 随机序列 .. "_" .. 取随机数(1, 99)
    地图 = 6046
    local xy = 地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id] = {
      id = 任务id,
      起始 = os.time(),
      结束 = 7586,
      玩家id = id,
      队伍组 = {},
      名称 = "镇塔之神",
      模型 = "将军",
      x = 31,
      y = 25,
      方向 = 0,
      副本id = id,
      地图编号 = 地图,
      地图名称 = 取地图名称(地图),
      类型 = 595,
    }
    地图处理类:添加单位(任务id)
  end
end

__GWdh111[582] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    if 任务数据[标识].名称 == "伤心的小猴" and 副本数据.齐天大圣.进行[副本id].进程 == 1 then
      对话数据.对话 = "猴爷爷快要不行了,呜呜呜。"
      对话数据.选项 = { "置之不理！", "救救我！" }
    elseif 任务数据[标识].名称 == "临死的老猴" and 副本数据.齐天大圣.进行[副本id].进程 == 1 then
      对话数据.对话 = "大王,我不行了,以后无法再陪伴大王了。"
      对话数据.选项 = { "不闻不问！", "哎，可怜" }
    end
  end
  return 对话数据
end
__GWdh222[582] = function(连接id, id, 序号, 内容)
  local 事件 = 内容[1]
  local 名称 = 内容[3]
  if 事件 == "救救我！" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    完成齐天大圣任务({ id }, 玩家数据[id].地图单位.标识, 582)
    玩家数据[id].地图单位 = nil
    return
  elseif 事件 == "哎，可怜" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    完成齐天大圣任务({ id }, 玩家数据[id].地图单位.标识, 582.1)
    玩家数据[id].地图单位 = nil
    return
  elseif 事件 == "置之不理！" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    完成齐天大圣任务({ id }, 玩家数据[id].地图单位.标识, 582.2)
    玩家数据[id].地图单位 = nil
    return
  elseif 事件 == "不闻不问！" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    完成齐天大圣任务({ id }, 玩家数据[id].地图单位.标识, 582.3)
    玩家数据[id].地图单位 = nil
    return
  end
end
__GWdh111[583] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    对话数据.对话 = "我等要收走死亡老猴的魂魄,尔等不要挡路！"
    对话数据.选项 = { "我看谁敢", "我只是路过" }
  else
    对话数据.对话 = "我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[583] = function(连接id, id, 序号, 内容)
  local 事件 = 内容[1]
  local 名称 = 内容[3]
  if 事件 == "我看谁敢" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    if 任务数据[玩家数据[id].地图单位.标识].战斗 ~= nil then
      常规提示(id, "#Y/对方正在战斗中")
      return
    end
    if 玩家数据[id].队伍 == 0 then
      常规提示(id, "#Y必须组队才能触发该活动")
      return
    end
    if 取队伍人数(id) < 5 and 调试模式 == false then
      常规提示(id, "#Y/挑战齐天大圣最少要有五人")
      return
    end
    if 取等级要求(id, 60) == false then
      常规提示(id, "#Y/挑战齐天大圣至少要达到60级")
      return
    end
    战斗准备类:创建战斗(id + 0, 155600, 玩家数据[id].地图单位.标识)
    任务数据[玩家数据[id].地图单位.标识].战斗 = true
    玩家数据[id].地图单位 = nil
    return
  end
end
__GWdh111[584] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    对话数据.对话 = "我等要收走死亡老猴的魂魄,尔等不要挡路！"
    对话数据.选项 = { "我看谁敢", "我只是路过" }
  else
    对话数据.对话 = "我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[584] = function(连接id, id, 序号, 内容)
  local 事件 = 内容[1]
  local 名称 = 内容[3]
  if 事件 == "我看谁敢" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    if 任务数据[玩家数据[id].地图单位.标识].战斗 ~= nil then
      常规提示(id, "#Y/对方正在战斗中")
      return
    end
    if 玩家数据[id].队伍 == 0 then
      常规提示(id, "#Y必须组队才能触发该活动")
      return
    end
    if 取队伍人数(id) < 5 and 调试模式 == false then
      常规提示(id, "#Y/挑战齐天大圣最少要有五人")
      return
    end
    if 取等级要求(id, 60) == false then
      常规提示(id, "#Y/挑战齐天大圣至少要达到60级")
      return
    end
    战斗准备类:创建战斗(id + 0, 155601, 玩家数据[id].地图单位.标识)
    任务数据[玩家数据[id].地图单位.标识].战斗 = true
    玩家数据[id].地图单位 = nil
    return
  end
end
__GWdh111[585] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    对话数据.对话 = "大仙,生死簿存放在阎王手中,你找小仙也没有用啊！"
    完成齐天大圣任务({ id }, 玩家数据[id].地图单位.标识, 585)
  end
  return 对话数据
end
__GWdh111[586] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    对话数据.对话 = "生死簿乃是地府重宝,岂是尔等可以索要的！"
    对话数据.选项 = { "那就别怪我不客气", "我再想办法" }
  else
    对话数据.对话 = "我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[586] = function(连接id, id, 序号, 内容)
  local 事件 = 内容[1]
  local 名称 = 内容[3]
  if 事件 == "那就别怪我不客气" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    if 任务数据[玩家数据[id].地图单位.标识].战斗 ~= nil then
      常规提示(id, "#Y/对方正在战斗中")
      return
    end
    if 玩家数据[id].队伍 == 0 then
      常规提示(id, "#Y必须组队才能触发该活动")
      return
    end
    if 取队伍人数(id) < 5 and 调试模式 == false then
      常规提示(id, "#Y/挑战齐天大圣最少要有五人")
      return
    end
    if 取等级要求(id, 60) == false then
      常规提示(id, "#Y/挑战齐天大圣至少要达到60级")
      return
    end
    战斗准备类:创建战斗(id + 0, 155602, 玩家数据[id].地图单位.标识)
    任务数据[玩家数据[id].地图单位.标识].战斗 = true
    玩家数据[id].地图单位 = nil
    return
  end
end
__GWdh111[587] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    对话数据.对话 = "玉帝天恩浩荡,赦免了您大闹地府之罪,还要授予你官职,且与我一同前往天宫授封吧。"
    完成齐天大圣任务({ id }, 玩家数据[id].地图单位.标识, 587)
  end
  return 对话数据
end
__GWdh111[588] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    if 副本数据.齐天大圣.进行[id].展示实力.四大天王 == 0 then
      添加最后对话(id, "请先挑战天王！！")
      return
    end
    if 副本数据.齐天大圣.进行[id].进程 == 6 then
      对话数据.对话 = "要想做官得让朕先看看你的本事！"
      对话数据.选项 = { "那老孙就露两手", "我再想办法" }
    else
      对话数据.对话 = "你已完成任务，请勿打扰。"
    end
  end
  return 对话数据
end
__GWdh222[588] = function(连接id, id, 序号, 内容)
  local 事件 = 内容[1]
  local 名称 = 内容[3]
  if 事件 == "那老孙就露两手" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    if 任务数据[玩家数据[id].地图单位.标识].战斗 ~= nil then
      常规提示(id, "#Y/对方正在战斗中")
      return
    end
    if 玩家数据[id].队伍 == 0 then
      常规提示(id, "#Y必须组队才能触发该活动")
      return
    end
    if 取队伍人数(id) < 5 and 调试模式 == false then
      常规提示(id, "#Y/挑战齐天大圣最少要有五人")
      return
    end
    if 取等级要求(id, 60) == false then
      常规提示(id, "#Y/挑战齐天大圣至少要达到60级")
      return
    end
    战斗准备类:创建战斗(id + 0, 155603, 玩家数据[id].地图单位.标识)
    任务数据[玩家数据[id].地图单位.标识].战斗 = true
    玩家数据[id].地图单位 = nil
    return
  end
end
__GWdh111[589] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    if 副本数据.齐天大圣.进行[副本id].进程 == 6 then
      对话数据.对话 = "不是什么人，都能在天庭做官的！"
      对话数据.选项 = { "你算什么东西！", "我再想办法" }
    else
      对话数据.对话 = "你已完成任务，请勿打扰。"
    end
  end
  return 对话数据
end
__GWdh222[589] = function(连接id, id, 序号, 内容)
  local 事件 = 内容[1]
  local 名称 = 内容[3]
  if 事件 == "你算什么东西！" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    if 任务数据[玩家数据[id].地图单位.标识].战斗 ~= nil then
      常规提示(id, "#Y/对方正在战斗中")
      return
    end
    if 玩家数据[id].队伍 == 0 then
      常规提示(id, "#Y必须组队才能触发该活动")
      return
    end
    if 取队伍人数(id) < 5 and 调试模式 == false then
      常规提示(id, "#Y/挑战齐天大圣最少要有五人")
      return
    end
    if 取等级要求(id, 60) == false then
      常规提示(id, "#Y/挑战齐天大圣至少要达到60级")
      return
    end
    战斗准备类:创建战斗(id + 0, 155608, 玩家数据[id].地图单位.标识)
    任务数据[玩家数据[id].地图单位.标识].战斗 = true
    玩家数据[id].地图单位 = nil
    return
  end
end
__GWdh111[589.1] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    对话数据.对话 = "玉帝老儿，你们有什么招法尽管使出来吧！"
    对话数据.选项 = { "俺老孙教你变身术！", "我再想办法" }
  else
    对话数据.对话 = "你已经学会变身术了"
  end
  return 对话数据
end
__GWdh222[589.1] = function(连接id, id, 序号, 内容)
  local 事件 = 内容[1]
  local 名称 = 内容[3]
  if 事件 == "俺老孙教你变身术！" then
    if 玩家数据[id].角色.体力 < 10 then
      添加最后对话(id, "你当前的体力不够，无法变化")
      return
    end
    if 玩家数据[id].角色.变身数据 ~= nil then
      玩家数据[id].角色.变身数据 = nil
      玩家数据[id].角色.变异 = nil
      local 任务id = 玩家数据[id].角色:取任务(1)
      任务数据[任务id] = nil
      玩家数据[id].角色:取消任务(任务id)
    end
    local 造型 = 齐天大圣变身卡范围[取随机数(1, #齐天大圣变身卡范围)]
    玩家数据[id].角色.体力 = 玩家数据[id].角色.体力 - 10
    玩家数据[id].角色.变身数据 = 造型
    玩家数据[id].角色.变异 = true
    玩家数据[id].角色:刷新信息()
    发送数据(玩家数据[id].连接id, 37, 玩家数据[id].角色.变身数据)
    常规提示(id, "变化成功")
    发送数据(玩家数据[id].连接id, 5506, { 玩家数据[id].角色:取气血数据() })
    发送数据(玩家数据[id].连接id, 12)
    设置任务1(id, 1, 玩家数据[id].角色.变身数据, 3600)
    地图处理类:更改模型(id, 玩家数据[id].角色.变身数据, 1)
  end
end
__GWdh111[590] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    对话数据.对话 = "你能穿上朝服，管理天上所有的骏马,还能拥有好听的官名“弼马温”怎么样！"
    完成齐天大圣任务({ id }, 玩家数据[id].地图单位.标识, 590)
  end
  return 对话数据
end
__GWdh111[591] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    对话数据.对话 = "天庭的马儿真是好啊！"
    对话数据.选项 = { "找死", "我只是路过" }
  else
    对话数据.对话 = "我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[591] = function(连接id, id, 序号, 内容)
  local 事件 = 内容[1]
  local 名称 = 内容[3]
  if 事件 == "找死" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    if 任务数据[玩家数据[id].地图单位.标识].战斗 ~= nil then
      常规提示(id, "#Y/对方正在战斗中")
      return
    end
    if 玩家数据[id].队伍 == 0 then
      常规提示(id, "#Y必须组队才能触发该活动")
      return
    end
    if 取队伍人数(id) < 5 and 调试模式 == false then
      常规提示(id, "#Y/挑战齐天大圣最少要有五人")
      return
    end
    if 取等级要求(id, 60) == false then
      常规提示(id, "#Y/挑战齐天大圣至少要达到60级")
      return
    end
    战斗准备类:创建战斗(id + 0, 155604, 玩家数据[id].地图单位.标识)
    任务数据[玩家数据[id].地图单位.标识].战斗 = true
    玩家数据[id].地图单位 = nil
    return
  end
end
__GWdh111[592] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    对话数据.对话 = "调皮的小马,快点跟着我回去吧！"
    完成齐天大圣任务({ id }, 玩家数据[id].地图单位.标识, 592)
  end
  return 对话数据
end
__GWdh111[593] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    对话数据.对话 = "你就是一个不入流的弼马温,专门看马的而已,哈哈哈,知道我是谁么,我乃天庭十万水军元帅,天蓬元帅是也！"
    完成齐天大圣任务({ id }, 玩家数据[id].地图单位.标识, 593)
  end
  return 对话数据
end
__GWdh111[594] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    if 任务数据[标识].名称 == "百万天兵" and 副本数据.齐天大圣.进行[副本id].进程 == 11 then
      对话数据.对话 = "大胆泼猴,竟敢违逆天命,还不拿命来！"
      对话数据.选项 = { "口出狂言", "我只是路过" }
    elseif 任务数据[标识].名称 == "巨灵神" and 副本数据.齐天大圣.进行[副本id].进程 == 11 then
      对话数据.对话 = "大胆泼猴,竟敢违逆天命,还不拿命来！"
      对话数据.选项 = { "比划比划", "我只是路过" }
    elseif 任务数据[标识].名称 == "李靖" then
      local 副本id=任务数据[标识].副本id
      if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
        添加最后对话(id, "我好像不认识你吧？？？")
        return
      end
      if 副本数据.齐天大圣.进行[副本id].百万天兵.百万天兵 == 1 and 副本数据.齐天大圣.进行[副本id].百万天兵.巨灵神 == 1 then
        完成齐天大圣任务({ id }, 玩家数据[id].地图单位.标识, 594)
      else
        对话数据.对话 = "哼,连我的手下都没有消灭,还想来战我！"
      end
    end
  end
  return 对话数据
end
__GWdh222[594] = function(连接id, id, 序号, 内容)
  local 事件 = 内容[1]
  local 名称 = 内容[3]
  if 事件 == "口出狂言" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    if 任务数据[玩家数据[id].地图单位.标识].战斗 ~= nil then
      常规提示(id, "#Y/对方正在战斗中")
      return
    end
    if 玩家数据[id].队伍 == 0 then
      常规提示(id, "#Y必须组队才能触发该活动")
      return
    end
    if 取队伍人数(id) < 5 and 调试模式 == false then
      常规提示(id, "#Y/挑战齐天大圣最少要有五人")
      return
    end
    if 取等级要求(id, 60) == false then
      常规提示(id, "#Y/挑战齐天大圣至少要达到60级")
      return
    end
    任务数据[玩家数据[id].地图单位.标识].战斗类型 = "百万天兵"
    战斗准备类:创建战斗(id + 0, 155605, 玩家数据[id].地图单位.标识)
    任务数据[玩家数据[id].地图单位.标识].战斗 = true
    玩家数据[id].地图单位 = nil
    return
  elseif 事件 == "比划比划" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    if 任务数据[玩家数据[id].地图单位.标识].战斗 ~= nil then
      常规提示(id, "#Y/对方正在战斗中")
      return
    end
    if 玩家数据[id].队伍 == 0 then
      常规提示(id, "#Y必须组队才能触发该活动")
      return
    end
    if 取队伍人数(id) < 5 and 调试模式 == false then
      常规提示(id, "#Y/挑战齐天大圣最少要有五人")
      return
    end
    if 取等级要求(id, 60) == false then
      常规提示(id, "#Y/挑战齐天大圣至少要达到60级")
      return
    end
    任务数据[玩家数据[id].地图单位.标识].战斗类型 = "巨灵神"
    战斗准备类:创建战斗(id + 0, 155606, 玩家数据[id].地图单位.标识)
    任务数据[玩家数据[id].地图单位.标识].战斗 = true
    玩家数据[id].地图单位 = nil
    return
  end
end
__GWdh111[595] = function(连接id, id, 序列, 标识, 地图)
  local 对话数据 = {}
  if 任务数据[标识].战斗 == nil then
    local 副本id = 任务数据[标识].副本id
    if (任务数据[玩家数据[id].角色:取任务(581)] or {}).副本id ~= 副本id then
      添加最后对话(id, "我好像不认识你吧？？？")
      return
    end
    对话数据.对话 = "少侠还是走到这一步了！"
    对话数据.选项 = { "开打吧！", "我只是路过" }
  else
    对话数据.对话 = "我正在战斗中，请勿打扰。"
  end
  return 对话数据
end
__GWdh222[595] = function(连接id, id, 序号, 内容)
  local 事件 = 内容[1]
  local 名称 = 内容[3]
  if 事件 == "开打吧！" then
    if 任务数据[玩家数据[id].地图单位.标识] == nil then return end
    if 任务数据[玩家数据[id].地图单位.标识].战斗 ~= nil then
      常规提示(id, "#Y/对方正在战斗中")
      return
    end
    if 玩家数据[id].队伍 == 0 then
      常规提示(id, "#Y必须组队才能触发该活动")
      return
    end
    if 取队伍人数(id) < 5 and 调试模式 == false then
      常规提示(id, "#Y/挑战齐天大圣最少要有五人")
      return
    end
    if 取等级要求(id, 60) == false then
      常规提示(id, "#Y/挑战齐天大圣至少要达到60级")
      return
    end
    战斗准备类:创建战斗(id + 0, 155607, 玩家数据[id].地图单位.标识)
    任务数据[玩家数据[id].地图单位.标识].战斗 = true
    玩家数据[id].地图单位 = nil
    return
  end
end
function 副本_齐天大圣:任务说明(玩家id, 任务id)
  local 说明 = {}
  local 副本id = 任务数据[任务id].副本id
  if 副本数据.齐天大圣.进行[副本id] == nil then
    说明 = { "齐天大圣", "#L您的副本已经完成" }
  else
    local 进程 = 副本数据.齐天大圣.进行[副本id].进程
    if 进程 == 1 then
      说明 = { "齐天大圣", format(
      "#L通过傲来国#R/xx/金毛猴#W/前往花果山前山，四处安抚下猴群吧！\n#R伤心的小猴#L当前剩余: %s\n#R临死的老猴#L当前剩余: %s\n距离副本结束剩余:#R/%s#W分钟",
        副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴, 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴, 取分((任务数据[任务id].结束 - (os.time() - 任务数据[任务id].起始)))) }
    elseif 进程 == 2 then
      说明 = { "齐天大圣", format(
      "#R黑白无常#L前来拘魂濒死的老猴,前往(110,103),(31,20)附近与其交涉。\n白无常(#G/%s#W//1)\n黑无常(#G/%s#W//1)\n距离副本结束剩余:#R/%s#W分钟",
        副本数据.齐天大圣.进行[副本id].无常.白无常, 副本数据.齐天大圣.进行[副本id].无常.黑无常, 取分((任务数据[任务id].结束 - (os.time() - 任务数据[任务id].起始)))) }
    elseif 进程 == 3 then
      说明 = { "齐天大圣", format("#L得悉生死簿之秘,前往地府寻找崔判官。") }
    elseif 进程 == 4 then
      说明 = { "齐天大圣", format("#L获知生死簿保存在阎王手中，向阎王索取生死簿！") }
    elseif 进程 == 5 then
      说明 = { "齐天大圣", format("#L拿来生死簿,我改改改！天庭获悉后,派遣太白金星(110,103)前来招安！") }
    elseif 进程 == 6 then
      说明 = { "齐天大圣", format("#L向玉皇大帝展示本领，在继续挑战四大天王之一！\n玉皇大帝(#G/%s#W//1)\n四大天王(#G/%s#W//1)\n距离副本结束剩余:#R/%s#W分钟",
        副本数据.齐天大圣.进行[副本id].展示实力.玉皇大帝, 副本数据.齐天大圣.进行[副本id].展示实力.四大天王, 取分((任务数据[任务id].结束 - (os.time() - 任务数据[任务id].起始)))) }
    elseif 进程 == 7 then
      说明 = { "齐天大圣", format("#L实力展示完毕,找玉帝授封吧！") }
    elseif 进程 == 8 then
      说明 = { "齐天大圣", format("#L发现盗马贼,前往驱逐盗马贼吧！剩余盗马贼:#R%s", 副本数据.齐天大圣.进行[副本id].盗马贼) }
    elseif 进程 == 9 then
      说明 = { "齐天大圣", format("#L一匹调皮的小马跑了出去,抓紧快去巡回吧。") }
    elseif 进程 == 10 then
      说明 = { "齐天大圣", format("#L近日闲来无事,初来天庭四处逛逛吧!(对话#R/天蓬元帅#W/)(243.105)。") }
    elseif 进程 == 11 then
      说明 = { "齐天大圣", format(
      "#L玉帝大怒，命托塔天王率巨灵神下界降服你，百万天兵闯入花果山,喝退百万天兵,打退巨灵神后对话李靖。\n百万天兵(#G/%s#W//1)\n巨灵神(#G/%s#W//1)\n距离副本结束剩余:#R/%s#W分钟",
        副本数据.齐天大圣.进行[副本id].百万天兵.百万天兵, 副本数据.齐天大圣.进行[副本id].百万天兵.巨灵神, 取分((任务数据[任务id].结束 - (os.time() - 任务数据[任务id].起始)))) }
    elseif 进程 == 12 then
      说明 = { "齐天大圣", format("#L托塔天王使出七宝玲珑塔，进入塔中挑战镇塔神灵。") }
    end
  end
  return 说明
end

function 怪物属性:齐天大圣黑无常(任务id, 玩家id, 序号)
  local 战斗单位 = {}
  local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
  local sx = self:取属性(等级, 门派)
  local 任务属性 = 任务数据[任务id]
  local 门派 = 任务属性.门派
  local xiulian = math.floor((等级 - 20) / 5)
  if sx.智能 == "物理" or sx.智能 == "法系" or sx.智能 == "辅助" then
    主怪是否为输出 = true
  end
  sx = self:取属性(等级, "阴曹地府", "固伤")
  战斗单位[1] = {
    名称 = "黑无常"
    ,
    模型 = "野鬼"
    ,
    等级 = 等级
    ,
    变异 = true
    ,
    气血 = qz(sx.属性.气血) * 18
    ,
    伤害 = qz(sx.属性.伤害)
    ,
    法伤 = qz(sx.属性.法伤)
    ,
    速度 = qz(sx.属性.速度)
    ,
    固定伤害 = math.floor(等级 * 5)
    ,
    防御 = math.floor(等级 * 5)
    ,
    法防 = 等级
    ,
    技能 = { "感知" }
    ,
    门派 = sx.门派
    ,
    AI战斗 = { AI = sx.智能 }
    ,
    主动技能 = sx.技能组
  }
  for i = 2, 3 do
    sx = self:取属性(等级, "狮驼岭", "物理")
    战斗单位[i] = {
      名称 = "斩妖"
      ,
      模型 = "巡游天神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 18
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 4, 5 do
    sx = self:取属性(等级, "大唐官府", "物理")
    战斗单位[i] = {
      名称 = "灾"
      ,
      模型 = "夜罗刹"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 18
      ,
      伤害 = qz(sx.属性.伤害) * 1.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 6, 6 do
    sx = self:取属性(等级, "魔王寨", "法系")
    战斗单位[i] = {
      名称 = "天罚"
      ,
      模型 = "混沌兽"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 12
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤) * 1.5
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = 等级
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 7, 8 do
    sx = self:取属性(等级, "神木林", "法系")
    战斗单位[i] = {
      名称 = "鬼兵"
      ,
      模型 = "吸血鬼"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 11
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤) * 1.2
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  return 战斗单位
end

function 怪物属性:齐天大圣白无常(任务id, 玩家id, 序号)
  local 战斗单位 = {}
  local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
  local sx = self:取属性(等级, 门派)
  local 任务属性 = 任务数据[任务id]
  local 门派 = 任务属性.门派
  local xiulian = math.floor((等级 - 20) / 5)
  if sx.智能 == "物理" or sx.智能 == "法系" or sx.智能 == "辅助" then
    主怪是否为输出 = true
  end
  sx = self:取属性(等级, "阴曹地府", "固伤")
  战斗单位[1] = {
    名称 = "白无常"
    ,
    模型 = "僵尸"
    ,
    等级 = 等级
    ,
    变异 = true
    ,
    气血 = qz(sx.属性.气血) * 18
    ,
    伤害 = qz(sx.属性.伤害)
    ,
    法伤 = qz(sx.属性.法伤)
    ,
    速度 = qz(sx.属性.速度)
    ,
    固定伤害 = math.floor(等级 * 5)
    ,
    防御 = math.floor(等级 * 5)
    ,
    法防 = 等级
    ,
    修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
    ,
    技能 = { "感知" }
    ,
    门派 = sx.门派
    ,
    AI战斗 = { AI = sx.智能 }
    ,
    主动技能 = sx.技能组
  }
  for i = 2, 3 do
    sx = self:取属性(等级, "凌波城", "物理")
    战斗单位[i] = {
      名称 = "斩妖"
      ,
      模型 = "巡游天神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 4, 5 do
    sx = self:取属性(等级, "阴曹地府", "物理")
    战斗单位[i] = {
      名称 = "灾"
      ,
      模型 = "夜罗刹"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 1.3
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      主动技能 = { "百爪狂杀", "六道无量" }
    }
  end
  for i = 6, 6 do
    sx = self:取属性(等级, "魔王寨", "法系")
    战斗单位[i] = {
      名称 = "天罚"
      ,
      模型 = "混沌兽"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 12
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤) * 1.5
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = 等级
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 7, 8 do
    sx = self:取属性(等级, "龙宫", "法系")
    战斗单位[i] = {
      名称 = "鬼兵"
      ,
      模型 = "吸血鬼"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 12
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤) * 1.3
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  return 战斗单位
end

function 怪物属性:齐天大圣阎王(任务id, 玩家id, 序号)
  local 战斗单位 = {}
  local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
  local sx = self:取属性(等级, 门派)
  local 任务属性 = 任务数据[任务id]
  local 门派 = 任务属性.门派
  local xiulian = math.floor((等级 - 20) / 5)
  if sx.智能 == "物理" or sx.智能 == "法系" or sx.智能 == "辅助" then
    主怪是否为输出 = true
  end
  sx = self:取属性(等级, "阴曹地府", "固伤")
  战斗单位[1] = {
    名称 = "阎王"
    ,
    模型 = "阎罗王"
    ,
    等级 = 等级
    ,
    气血 = qz(sx.属性.气血) * 20
    ,
    伤害 = qz(sx.属性.伤害)
    ,
    法伤 = qz(sx.属性.法伤)
    ,
    速度 = qz(sx.属性.速度)
    ,
    固定伤害 = math.floor(等级 * 6)
    ,
    防御 = math.floor(等级 * 6)
    ,
    法防 = 等级
    ,
    修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
    ,
    技能 = { "感知", "高级夜战", "幽冥鬼眼" }
    ,
    AI战斗 = { AI = sx.智能 }
    ,
    门派 = sx.门派
    ,
    主动技能 = sx.技能组
  }
  for i = 2, 5 do
    sx = self:取属性(等级)
    战斗单位[i] = {
      名称 = "小鬼"
      ,
      模型 = "骷髅怪"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 11
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      固定伤害 = math.floor(等级 * 4)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 6, 6 do
    sx = self:取属性(等级, "大唐官府", "物理")
    战斗单位[i] = {
      名称 = "天罚"
      ,
      模型 = "混沌兽"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 1.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = 等级
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 7, 8 do
    sx = self:取属性(等级, "魔王寨", "法系")
    战斗单位[i] = {
      名称 = "小鬼"
      ,
      模型 = "骷髅怪"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 12
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤) * 1.3
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  return 战斗单位
end

function 怪物属性:齐天大圣天王(任务id, 玩家id, 序号)
  local 战斗单位 = {}
  local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
  local sx = self:取属性(等级, 门派)
  local 任务属性 = 任务数据[任务id]
  local 门派 = 任务属性.门派
  local xiulian = math.floor((等级 - 20) / 5)
  if sx.智能 == "物理" or sx.智能 == "法系" or sx.智能 == "辅助" then
    主怪是否为输出 = true
  end
  sx = self:取属性(等级, "大唐官府", "物理")
  战斗单位[1] = {
    名称 = "天王"
    ,
    模型 = "大力金刚"
    ,
    等级 = 等级
    ,
    气血 = qz(sx.属性.气血) * 20
    ,
    伤害 = qz(sx.属性.伤害) * 1.2
    ,
    法伤 = qz(sx.属性.法伤)
    ,
    速度 = qz(sx.属性.速度)
    ,
    防御 = math.floor(等级 * 6)
    ,
    法防 = math.floor(等级 * 4)
    ,
    技能 = { "感知" }
    ,
    修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
    ,
    门派 = sx.门派
    ,
    AI战斗 = { AI = sx.智能 }
    ,
    主动技能 = sx.技能组
  }
  for i = 2, 2 do
    sx = self:取属性(等级, "狮驼岭", "物理")
    战斗单位[i] = {
      名称 = "斩妖"
      ,
      模型 = "巡游天神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 1.2
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 3, 3 do
    sx = self:取属性(等级, "天宫", "封系")
    战斗单位[i] = {
      名称 = "镇妖"
      ,
      模型 = "地狱战神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 12
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 4, 4 do
    sx = self:取属性(等级, "凌波城", "物理")
    战斗单位[i] = {
      名称 = "天灾"
      ,
      模型 = "夜罗刹"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 1.1
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 5, 5 do
    sx = self:取属性(等级)
    战斗单位[i] = {
      名称 = "天灾"
      ,
      模型 = "噬天虎"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 20
      ,
      伤害 = qz(sx.属性.伤害) * 1.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 4)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 6, 6 do
    sx = self:取属性(等级, "龙宫", "法系")
    战斗单位[i] = {
      名称 = "天罚"
      ,
      模型 = "混沌兽"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 13
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤) * 1.2
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 4)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 7, 8 do
    sx = self:取属性(等级)
    战斗单位[i] = {
      名称 = "天兵"
      ,
      模型 = "天兵"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 3.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 4)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
    }
  end
  return 战斗单位
end

function 怪物属性:齐天大圣展示实力(任务id, 玩家id, 序号)
  local 战斗单位 = {}
  local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
  local sx = self:取属性(等级, 门派)
  local 任务属性 = 任务数据[任务id]
  local 门派 = 任务属性.门派
  local xiulian = math.floor((等级 - 20) / 5)
  if sx.智能 == "物理" or sx.智能 == "法系" or sx.智能 == "辅助" then
    主怪是否为输出 = true
  end
  sx = self:取属性(等级, "大唐官府", "物理")
  战斗单位[1] = {
    名称 = "天王"
    ,
    模型 = "大力金刚"
    ,
    变异 = true
    ,
    等级 = 等级
    ,
    气血 = qz(sx.属性.气血) * 20
    ,
    伤害 = qz(sx.属性.伤害) * 1.2
    ,
    法伤 = qz(sx.属性.法伤)
    ,
    速度 = qz(sx.属性.速度)
    ,
    防御 = math.floor(等级 * 6)
    ,
    法防 = math.floor(等级 * 4)
    ,
    技能 = { "感知" }
    ,
    招式特效 = 取招式特效("大唐官府")
    ,
    修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
    ,
    门派 = sx.门派
    ,
    AI战斗 = { AI = sx.智能 }
    ,
    主动技能 = sx.技能组
  }
  for i = 2, 2 do
    sx = self:取属性(等级, "狮驼岭", "物理")
    战斗单位[i] = {
      名称 = "斩妖"
      ,
      模型 = "巡游天神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 1.2
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      招式特效 = 取招式特效("狮驼岭")
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 3, 3 do
    sx = self:取属性(等级, "天宫", "封系")
    战斗单位[i] = {
      名称 = "镇妖"
      ,
      模型 = "地狱战神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 12
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      招式特效 = 取招式特效("天宫")
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 4, 4 do
    sx = self:取属性(等级, "凌波城", "物理")
    战斗单位[i] = {
      名称 = "天灾"
      ,
      模型 = "鬼将"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 1.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      招式特效 = 取招式特效("凌波城")
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 5, 5 do
    sx = self:取属性(等级)
    战斗单位[i] = {
      名称 = "天灾"
      ,
      模型 = "噬天虎"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 20
      ,
      伤害 = qz(sx.属性.伤害) * 1.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 4)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 6, 6 do
    sx = self:取属性(等级, "龙宫", "法系")
    战斗单位[i] = {
      名称 = "天罚"
      ,
      模型 = "混沌兽"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 13
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤) * 1.2
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 4)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 7, 8 do
    sx = self:取属性(等级)
    战斗单位[i] = {
      名称 = "天兵"
      ,
      模型 = "天兵"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 10
      ,
      伤害 = qz(sx.属性.伤害) * 3.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 4)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      AI战斗 = { AI = sx.智能 }
    }
  end
  return 战斗单位
end

function 怪物属性:齐天大圣盗马贼(任务id, 玩家id, 序号)
  local 战斗单位 = {}
  local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
  local sx = self:取属性(等级, "无底洞", "固伤")
  战斗单位[1] = {
    名称 = 任务数据[任务id].名称
    ,
    模型 = 任务数据[任务id].模型
    ,
    变异 = true
    ,
    等级 = 等级
    ,
    气血 = qz(sx.属性.气血)
    ,
    伤害 = qz(sx.属性.伤害)
    ,
    法伤 = qz(sx.属性.法伤)
    ,
    速度 = qz(sx.属性.速度)
    ,
    技能 = { "感知" }
    ,
    主动技能 = sx.技能组
  }
  for i = 2, 5 do
    sx = self:取属性(等级)
    战斗单位[i] = {
      名称 = "强盗"
      ,
      模型 = "强盗"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血)
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      技能 = { "感知" }
      ,
      主动技能 = sx.技能组
    }
  end
  return 战斗单位
end

function 怪物属性:齐天大圣百万天兵(任务id, 玩家id, 序号)
  local 战斗单位 = {}
  local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
  local sx = self:取属性(等级, 门派)
  local 任务属性 = 任务数据[任务id]
  local 门派 = 任务属性.门派
  local xiulian = math.floor((等级 - 20) / 5)
  if sx.智能 == "物理" or sx.智能 == "法系" or sx.智能 == "辅助" then
    主怪是否为输出 = true
  end
  sx = self:取属性(等级, "天宫", "物理")
  战斗单位[1] = {
    名称 = "巨灵神"
    ,
    模型 = "进阶天将"
    ,
    变异 = true
    ,
    等级 = 等级
    ,
    气血 = qz(sx.属性.气血) * 18
    ,
    伤害 = qz(sx.属性.伤害) * 1.8
    ,
    法伤 = qz(sx.属性.法伤)
    ,
    速度 = qz(sx.属性.速度)
    ,
    防御 = math.floor(等级 * 6)
    ,
    法防 = 等级
    ,
    修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
    ,
    技能 = { "感知" }
    ,
    门派 = sx.门派
    ,
    AI战斗 = { AI = sx.智能 }
    ,
    主动技能 = sx.技能组
  }
  for i = 2, 2 do
    sx = self:取属性(等级, "狮驼岭", "物理")
    战斗单位[i] = {
      名称 = "斩妖"
      ,
      模型 = "进阶巡游天神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 18
      ,
      伤害 = qz(sx.属性.伤害) * 1.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = 等级
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 3, 3 do
    sx = self:取属性(等级, "天宫", "封系")
    战斗单位[i] = {
      名称 = "镇妖"
      ,
      模型 = "进阶地狱战神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 12
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 4, 4 do
    sx = self:取属性(等级, "大唐官府", "物理")
    战斗单位[i] = {
      名称 = "天灾"
      ,
      模型 = "进阶夜罗刹"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 20
      ,
      伤害 = qz(sx.属性.伤害) * 1.2
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 5, 5 do
    sx = self:取属性(等级, "普陀山", "辅助")
    战斗单位[i] = {
      名称 = "天恩"
      ,
      模型 = "进阶葫芦宝贝"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 6, 6 do
    sx = self:取属性(等级, "龙宫", "法系")
    战斗单位[i] = {
      名称 = "天罚"
      ,
      模型 = "进阶混沌兽"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤) * 1.3
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 7, 7 do
    sx = self:取属性(等级, "女儿村", "封系")
    战斗单位[i] = {
      名称 = "天弄"
      ,
      模型 = "阴阳伞"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 1.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知", "高级连击" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 8, 10 do
    sx = self:取属性(等级)
    战斗单位[i] = {
      名称 = "天兵"
      ,
      模型 = "进阶天兵"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 1.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知", "高级连击" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  return 战斗单位
end

function 怪物属性:齐天大圣巨灵神(任务id, 玩家id, 序号)
  local 战斗单位 = {}
  local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
  local sx = self:取属性(等级, 门派)
  local 任务属性 = 任务数据[任务id]
  local 门派 = 任务属性.门派
  local xiulian = math.floor((等级 - 20) / 5)
  if sx.智能 == "物理" or sx.智能 == "法系" or sx.智能 == "辅助" then
    主怪是否为输出 = true
  end
  sx = self:取属性(等级, "天宫", "物理")
  战斗单位[1] = {
    名称 = "巨灵神"
    ,
    模型 = "进阶野猪精"
    ,
    变异 = true
    ,
    等级 = 等级
    ,
    气血 = qz(sx.属性.气血) * 18
    ,
    伤害 = qz(sx.属性.伤害) * 1.8
    ,
    法伤 = qz(sx.属性.法伤)
    ,
    速度 = qz(sx.属性.速度)
    ,
    防御 = math.floor(等级 * 6)
    ,
    法防 = 等级
    ,
    修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
    ,
    技能 = { "感知" }
    ,
    门派 = sx.门派
    ,
    AI战斗 = { AI = sx.智能 }
    ,
    主动技能 = sx.技能组
  }
  for i = 2, 2 do
    sx = self:取属性(等级, "狮驼岭", "物理")
    战斗单位[i] = {
      名称 = "斩妖"
      ,
      模型 = "进阶巡游天神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 18
      ,
      伤害 = qz(sx.属性.伤害) * 1.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = 等级
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 3, 3 do
    sx = self:取属性(等级, "天宫", "封系")
    战斗单位[i] = {
      名称 = "镇妖"
      ,
      模型 = "进阶地狱战神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 12
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 4, 4 do
    sx = self:取属性(等级, "大唐官府", "物理")
    战斗单位[i] = {
      名称 = "天灾"
      ,
      模型 = "进阶夜罗刹"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 20
      ,
      伤害 = qz(sx.属性.伤害) * 1.2
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 5, 5 do
    sx = self:取属性(等级, "普陀山", "辅助")
    战斗单位[i] = {
      名称 = "天恩"
      ,
      模型 = "进阶葫芦宝贝"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 6, 6 do
    sx = self:取属性(等级, "龙宫", "法系")
    战斗单位[i] = {
      名称 = "天罚"
      ,
      模型 = "进阶混沌兽"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤) * 1.3
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 7, 7 do
    sx = self:取属性(等级, "女儿村", "封系")
    战斗单位[i] = {
      名称 = "天弄"
      ,
      模型 = "阴阳伞"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 1.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知", "高级连击" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 8, 10 do
    sx = self:取属性(等级)
    战斗单位[i] = {
      名称 = "天兵"
      ,
      模型 = "进阶天兵"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 1.5
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知", "高级连击" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  return 战斗单位
end

function 怪物属性:齐天大圣镇塔之神(任务id, 玩家id, 序号)
  local 战斗单位 = {}
  local 等级 = 取队伍最高等级数(玩家数据[玩家id].队伍, 玩家id)
  local sx = self:取属性(等级, 门派)
  local 任务属性 = 任务数据[任务id]
  local 门派 = 任务属性.门派
  local xiulian = math.floor((等级 - 20) / 5)
  if sx.智能 == "物理" or sx.智能 == "法系" or sx.智能 == "辅助" then
    主怪是否为输出 = true
  end
  sx = self:取属性(等级, "天宫", "法系")
  战斗单位[1] = {
    名称 = "塔灵"
    ,
    模型 = "进阶龙龟"
    ,
    变异 = true
    ,
    等级 = 等级
    ,
    气血 = qz(sx.属性.气血) * 18
    ,
    伤害 = qz(sx.属性.伤害)
    ,
    法伤 = qz(sx.属性.法伤) * 1.2
    ,
    速度 = qz(sx.属性.速度)
    ,
    防御 = math.floor(等级 * 4)
    ,
    法防 = 等级
    ,
    修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
    ,
    技能 = { "感知" }
    ,
    主动技能 = { "五雷轰顶", "雷霆万钧" }
  }
  for i = 2, 2 do
    sx = self:取属性(等级, "狮驼岭", "物理")
    战斗单位[i] = {
      名称 = "斩妖"
      ,
      模型 = "巡游天神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 18
      ,
      伤害 = qz(sx.属性.伤害) * 1.2
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = 等级
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      技能 = { "感知" }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 3, 3 do
    sx = self:取属性(等级, "天宫", "封系")
    战斗单位[i] = {
      名称 = "镇妖"
      ,
      模型 = "地狱战神"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤) * 4
      ,
      速度 = qz(sx.属性.速度)
      ,
      技能 = { "感知" }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 4, 4 do
    sx = self:取属性(等级, "大唐官府", "物理")
    战斗单位[i] = {
      名称 = "天灾"
      ,
      模型 = "夜罗刹"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 20
      ,
      伤害 = qz(sx.属性.伤害) * 1.2
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 6)
      ,
      法防 = math.floor(等级 * 4)
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 5, 5 do
    sx = self:取属性(等级, "无底洞", "辅助")
    战斗单位[i] = {
      名称 = "天恩"
      ,
      模型 = "葫芦宝贝"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 6, 6 do
    sx = self:取属性(等级, "神木林", "法系")
    战斗单位[i] = {
      名称 = "天罚"
      ,
      模型 = "混沌兽"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤) * 1.2
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 7, 7 do
    sx = self:取属性(等级, "无底洞", "封系")
    战斗单位[i] = {
      名称 = "天弄"
      ,
      模型 = "阴阳伞"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 8, 8 do
    sx = self:取属性(等级)
    战斗单位[i] = {
      名称 = "降兽"
      ,
      模型 = "踏云兽"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 25
      ,
      伤害 = qz(sx.属性.伤害)
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
      ,
      主动技能 = sx.技能组
    }
  end
  for i = 9, 10 do
    sx = self:取属性(等级)
    战斗单位[i] = {
      名称 = "天兵"
      ,
      模型 = "天兵"
      ,
      等级 = 等级
      ,
      气血 = qz(sx.属性.气血) * 15
      ,
      伤害 = qz(sx.属性.伤害) * 1.2
      ,
      法伤 = qz(sx.属性.法伤)
      ,
      速度 = qz(sx.属性.速度)
      ,
      防御 = math.floor(等级 * 5)
      ,
      法防 = 等级
      ,
      技能 = { "感知", "高级连击", "嗜血追击", "乘胜追击" }
      ,
      修炼 = { 物抗 = xiulian, 法抗 = 0, 攻修 = xiulian }
      ,
      门派 = sx.门派
      ,
      AI战斗 = { AI = sx.智能 }
    }
  end
  return 战斗单位
end

function 齐天大圣副本失败(任务id)
  if os.time() - 任务数据[任务id].起始 >= 任务数据[任务id].结束 and 任务数据[任务id].结束 ~= 99999999 then
    if 任务数据[任务id].战斗 ~= true then
      if 任务数据[任务id].类型 == 581 then
        for i = 1, #任务数据[任务id].队伍组 do
          if 玩家数据[任务数据[任务id].队伍组[i]] ~= nil then
            玩家数据[任务数据[任务id].队伍组[i]].角色:取消任务(玩家数据[任务数据[任务id].队伍组[i]].角色:取任务(581))
          end
        end
        任务数据[任务id] = nil
      else
        任务处理类:删除单位(任务id)
      end
    end
  end
end

function 完成齐天大圣任务(id组, 任务id, 战斗类型)
  local 奖励
  if 任务数据[任务id] then
    local 副本id = 任务数据[任务id].副本id
    local 进度刷新 = false
    for i = 1, #id组 do
      local id = id组[i]
      local 等级 = 玩家数据[id].角色.等级
      local 经验
      local 银子
      local 储备
      if 战斗类型 == 582 then
        local 等级 = 玩家数据[id].角色.等级
        local 经验 = 等级 * 取随机数(78, 80)
        local 银子 = 等级 * 取随机数(30, 33)
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        玩家数据[id].战斗 = 0
      elseif 战斗类型 == 582.1 then
        local 等级 = 玩家数据[id].角色.等级
        local 经验 = 等级 * 取随机数(78, 80)
        local 银子 = 等级 * 取随机数(30, 33)
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        玩家数据[id].战斗 = 0
      elseif 战斗类型 == 582.2 then
        local 等级 = 玩家数据[id].角色.等级
        local 银子 = 等级 * 取随机数(20, 22)
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        --玩家数据[id].角色.人气 = 玩家数据[id].角色.人气 - 50
        玩家数据[id].角色:扣除人气(50)
        常规提示(id, "你的人气降低了")
        玩家数据[id].战斗 = 0
      elseif 战斗类型 == 155600 then
        local 等级 = 玩家数据[id].角色.等级
        local 经验 = 等级 * 取随机数(1000, 1200)
        local 银子 = 等级 * 取随机数(90, 120)
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        玩家数据[id].战斗 = 0
        if 取随机数() <= 25 then
          local 奖励参数 = 取随机数(1, 60)
          链接 = { 提示 = format("#S(副本-齐天大圣)#R/%s#Y/在#R/齐天大圣#Y/表现卓越，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 15 then
            local 名称 = "高级魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 16 then
            local 名称 = "召唤兽内丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 35 then
            local 名称 = "五宝盒"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 50 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 3, nil, 链接)
          elseif 奖励参数 <= 55 then
            local 名称 = "九转金丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(100, 150), nil, 链接)
          elseif 奖励参数 <= 60 then
            local 名称 = 取宝石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(1, 5), nil, 链接)
          end
        end
      elseif 战斗类型 == 155601 then
        local 等级 = 玩家数据[id].角色.等级
        local 经验 = 等级 * 取随机数(1000, 1200)
        local 银子 = 等级 * 取随机数(90, 120)
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        玩家数据[id].战斗 = 0
        if 取随机数() <= 35 then
          local 奖励参数 = 取随机数(1, 75)
          链接 = { 提示 = format("#S(副本-齐天大圣)#R/%s#Y/在#R/齐天大圣#Y/表现卓越，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 10 then
            local 名称 = "特赦令牌"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 15 then
            local 名称 = "超级金柳露"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 16 then
            local 名称 = "召唤兽内丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 30 then
            local 名称 = "魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 35 then
            local 名称 = "五宝盒"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 50 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 10, nil, 链接)
          elseif 奖励参数 <= 55 then
            local 名称 = "九转金丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(100, 150), nil, 链接)
          elseif 奖励参数 <= 60 then
            local 名称 = 取宝石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(4, 5), nil, 链接)
          elseif 奖励参数 <= 75 then
            local 名称 = 取宝宝装备()
            local lv = math.min(qz(等级 / 10), 13)
            玩家数据[id].道具:给予超链接道具(id, 名称, { lv + 1, lv + 2 }, nil, 链接)
          end
        end
      elseif 战斗类型 == 155602 then
        local 等级 = 玩家数据[id].角色.等级
        local 经验 = 等级 * 取随机数(1200, 1500)
        local 银子 = 等级 * 取随机数(90, 120)
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        玩家数据[id].战斗 = 0
        if 取随机数() <= 55 then
          local 奖励参数 = 取随机数(1, 75)
          链接 = { 提示 = format("#S(副本-齐天大圣)#R/%s#Y/在#R/齐天大圣#Y/表现卓越，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 10 then
            local 名称 = "特赦令牌"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 15 then
            local 名称 = "超级金柳露"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 16 then
            local 名称 = "召唤兽内丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 30 then
            local 名称 = "魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 35 then
            local 名称 = "五宝盒"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 50 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 10, nil, 链接)
          elseif 奖励参数 <= 60 then
            local 名称 = "九转金丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(100, 150), nil, 链接)
          elseif 奖励参数 <= 75 then
            local 名称 = 取宝宝装备()
            local lv = math.min(qz(等级 / 10), 13)
            玩家数据[id].道具:给予超链接道具(id, 名称, { lv + 1, lv + 2 }, nil, 链接)
          end
        end
      elseif 战斗类型 == 155603 then
        local 等级 = 玩家数据[id].角色.等级
        local 经验 = 等级 * 取随机数(1400, 1600)
        local 银子 = 等级 * 取随机数(90, 120)
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        玩家数据[id].战斗 = 0
        -- local stid = 取随机数(1, #id组)
        -- stid = id组[stid]
        -- local 链接 = { 提示 = format("#S(副本-齐天大圣)#G/%s#R在天宫挑战四大天王时，趁乱得到了珍贵的", 玩家数据[stid].角色.名称), 频道 = "xt", 结尾 = "#R！#80" }
        -- 玩家数据[stid].道具:给予超链接道具(stid, "特殊兽决·碎片", 1, nil, 链接)
        if 取随机数() <= 45 then
          local 奖励参数 = 取随机数(1, 75)
          链接 = { 提示 = format("#S(副本-齐天大圣)#R/%s#Y/在#R/齐天大圣#Y/表现卓越，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 10 then
            local 名称 = "特赦令牌"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 15 then
            local 名称 = "超级金柳露"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 16 then
            local 名称 = "召唤兽内丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 30 then
            local 名称 = "摇钱树苗"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 35 then
            local 名称 = "五宝盒"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 50 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 10, nil, 链接)
          elseif 奖励参数 <= 55 then
            local 名称 = "九转金丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(100, 150), nil, 链接)
          elseif 奖励参数 <= 75 then
            local 名称 = 取宝石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(1, 5), nil, 链接)

          end
        end
      elseif 战斗类型 == 155605 then
        local 等级 = 玩家数据[id].角色.等级
        local 经验 = 等级 * 取随机数(1400, 1600)
        local 银子 = 等级 * 取随机数(90, 120)
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        玩家数据[id].战斗 = 0
        if 取随机数() <= 45 then
          local 奖励参数 = 取随机数(1, 100)
          链接 = { 提示 = format("#S(副本-齐天大圣)#R/%s#Y/在#R/齐天大圣#Y/表现卓越，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 10 then
            local 名称 = "摇钱树苗"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 15 then
            local 名称 = "超级金柳露"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 16 then
            local 名称 = "召唤兽内丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 30 then
            local 名称 = "魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 35 then
            local 名称 = "五宝盒"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 50 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 100 then
            local 名称 = 取宝石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(1, 6), nil, 链接)
          end
        end
      elseif 战斗类型 == 155606 then
        local 等级 = 玩家数据[id].角色.等级
        local 经验 = 等级 * 取随机数(1400, 1600)
        local 银子 = 等级 * 取随机数(90, 120)
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        玩家数据[id].战斗 = 0
        if 取随机数() <= 40 then
          local 奖励参数 = 取随机数(1, 75)
          链接 = { 提示 = format("#S(副本-齐天大圣)#R/%s#Y/在#R/齐天大圣#Y/表现卓越，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }

          if 奖励参数 <= 1 then
            local 名称 = "愤怒符"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 16 then
            local 名称 = "摇钱树苗"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 30 then
            local 名称 = "圣兽丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 35 then
            local 名称 = "玉葫灵髓"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 40 then
            local 名称 = "清灵净瓶"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 50 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 10, nil, 链接)
          elseif 奖励参数 <= 60 then
            local 名称 = 取宝石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(1, 6), nil, 链接)
          elseif 奖励参数 <= 75 then
            local 名称 = "未激活的符石"
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(1, 3), nil, 链接)
          end
        end
      elseif 战斗类型 == 155607 then
        local 等级 = 玩家数据[id].角色.等级
        local 经验 = 等级 * 取随机数(1400, 1600)
        local 银子 = 等级 * 取随机数(90, 120) + 4000
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        玩家数据[id].战斗 = 0
        if 取随机数() <= 40 then
          local 奖励参数 = 取随机数(1, 75)
          链接 = { 提示 = format("#S(副本-齐天大圣)#R/%s#Y/在#R/齐天大圣#Y/表现卓越，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 1 then
            local 名称 = "愤怒符"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 16 then
            local 名称 = "摇钱树苗"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 30 then
            local 名称 = "圣兽丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 35 then
            local 名称 = "玉葫灵髓"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 40 then
            local 名称 = "清灵净瓶"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 50 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 10, nil, 链接)
          elseif 奖励参数 <= 75 then
            local 名称 = 取宝石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(3, 5), nil, 链接)
          end
        end
      elseif 战斗类型 == 155608 then
        local 等级 = 玩家数据[id].角色.等级
        local 经验 = 等级 * 取随机数(1600, 1800)
        local 银子 = 等级 * 取随机数(90, 120) + 8000
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        玩家数据[id].战斗 = 0
        if 取随机数() <= 35 then
          local 奖励参数 = 取随机数(1, 75)
          链接 = { 提示 = format("#S(副本-齐天大圣)#R/%s#Y/在#R/齐天大圣#Y/表现卓越，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 15 then
            local 名称 = "超级金柳露"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 16 then
            local 名称 = "召唤兽内丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 30 then
            local 名称 = "魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 35 then
            local 名称 = "五宝盒"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 50 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 10, nil, 链接)
          elseif 奖励参数 <= 55 then
            local 名称 = "九转金丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(100, 150), nil, 链接)
          elseif 奖励参数 <= 60 then
            local 名称 = 取宝石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(4, 6), nil, 链接)
          elseif 奖励参数 <= 75 then
            local 名称 = 取宝宝装备()
            local lv = math.min(qz(等级 / 10), 13)
            玩家数据[id].道具:给予超链接道具(id, 名称, { lv + 1, lv + 2 }, nil, 链接)
          end
        end
      elseif 战斗类型 == 155604 then
        local 等级 = 玩家数据[id].角色.等级
        local 经验 = 等级 * 取随机数(78, 80)
        local 银子 = 等级 * 取随机数(58, 60)
        玩家数据[id].角色:添加经验(经验, "齐天大圣")
        玩家数据[id].角色:添加银子(银子, "齐天大圣", 1)
        玩家数据[id].战斗 = 0
        if 取随机数() <= 25 then
          local 奖励参数 = 取随机数(1, 75)
          链接 = { 提示 = format("#S(副本-齐天大圣)#R/%s#Y/在#R/齐天大圣#Y/表现卓越，额外获得了#G", 玩家数据[id].角色.名称), 频道 = "xt", 结尾 = "#Y。" }
          if 奖励参数 <= 1 then
            local 名称 = "附魔宝珠"
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(10, 16) * 10, nil, 链接)
          elseif 奖励参数 <= 15 then
            local 名称 = "超级金柳露"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 16 then
            local 名称 = "召唤兽内丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 30 then
            local 名称 = "魔兽要诀"
            玩家数据[id].道具:给予超链接道具(id, 名称, nil, nil, 链接)
          elseif 奖励参数 <= 35 then
            local 名称 = "五宝盒"
            玩家数据[id].道具:给予超链接道具(id, 名称, 1, nil, 链接)
          elseif 奖励参数 <= 50 then
            local 名称 = 取强化石()
            玩家数据[id].道具:给予超链接道具(id, 名称, 5, nil, 链接)
          elseif 奖励参数 <= 75 then
            local 名称 = "九转金丹"
            玩家数据[id].道具:给予超链接道具(id, 名称, 取随机数(100, 150), nil, 链接)
          end
        end
      end
    end
    if 战斗类型 == 582 then
      if 玩家数据[id组[1]].角色:取任务(581) and 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 > 0 then
        副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 = 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 - 1
        if 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 <= 0 and 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 <= 0 then
          副本数据.齐天大圣.进行[副本id].进程 = 2
          设置齐天大圣副本(副本id)
          进度刷新 = true
        end
      end
    elseif 战斗类型 == 582.1 then
      if 玩家数据[id组[1]].角色:取任务(581) and 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 > 0 then
        副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 = 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 - 1
        if 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 <= 0 and 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 <= 0 then
          副本数据.齐天大圣.进行[副本id].进程 = 2
          设置齐天大圣副本(副本id)
          进度刷新 = true
        end
      end
    elseif 战斗类型 == 582.3 then
      if 玩家数据[id组[1]].角色:取任务(581) and 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 > 0 then
        副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 = 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 - 1
        if 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 <= 0 and 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 <= 0 then
          副本数据.齐天大圣.进行[副本id].进程 = 2
          设置齐天大圣副本(副本id)
          进度刷新 = true
        end
      end
    elseif 战斗类型 == 582.2 then
      if 玩家数据[id组[1]].角色:取任务(581) and 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 > 0 then
        副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 = 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 - 1
        if 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 <= 0 and 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 <= 0 then
          副本数据.齐天大圣.进行[副本id].进程 = 2
          设置齐天大圣副本(副本id)
          进度刷新 = true
        end
      end
    elseif 战斗类型 == 155600 then
      if 玩家数据[id组[1]].角色:取任务(581) and 副本数据.齐天大圣.进行[副本id].无常.白无常 < 1 then
        副本数据.齐天大圣.进行[副本id].无常.白无常 = 副本数据.齐天大圣.进行[副本id].无常.白无常 + 1
        if 副本数据.齐天大圣.进行[副本id].无常.白无常 >= 1 and 副本数据.齐天大圣.进行[副本id].无常.黑无常 >= 1 then
          副本数据.齐天大圣.进行[副本id].进程 = 3
          设置齐天大圣副本(副本id)
          进度刷新 = true
          任务处理类:副本传送(id组[1], 11)
        end
      end
    elseif 战斗类型 == 155601 then
      if 玩家数据[id组[1]].角色:取任务(581) and 副本数据.齐天大圣.进行[副本id].无常.黑无常 < 1 then
        副本数据.齐天大圣.进行[副本id].无常.黑无常 = 副本数据.齐天大圣.进行[副本id].无常.黑无常 + 1
        if 副本数据.齐天大圣.进行[副本id].无常.白无常 >= 1 and 副本数据.齐天大圣.进行[副本id].无常.黑无常 >= 1 then
          副本数据.齐天大圣.进行[副本id].进程 = 3
          设置齐天大圣副本(副本id)
          进度刷新 = true
          任务处理类:副本传送(id组[1], 11)
        end
      end
    elseif 战斗类型 == 585 then
      local 副本id = 任务数据[任务id].副本id
      副本数据.齐天大圣.进行[副本id].进程 = 4
      设置齐天大圣副本(副本id)
      进度刷新 = true
    elseif 战斗类型 == 155602 then
      local 副本id = 任务数据[任务id].副本id
      副本数据.齐天大圣.进行[副本id].进程 = 5
      设置齐天大圣副本(副本id)
      进度刷新 = true
      任务处理类:副本传送(id组[1], 11)
    elseif 战斗类型 == 587 then
      local 副本id = 任务数据[任务id].副本id
      副本数据.齐天大圣.进行[副本id].进程 = 6
      设置齐天大圣副本(副本id)
      进度刷新 = true
      任务处理类:副本传送(id组[1], 11)
    elseif 战斗类型 == 155603 then
      if 玩家数据[id组[1]].角色:取任务(581) and 副本数据.齐天大圣.进行[副本id].展示实力.玉皇大帝 < 1 then
        副本数据.齐天大圣.进行[副本id].展示实力.玉皇大帝 = 副本数据.齐天大圣.进行[副本id].展示实力.玉皇大帝 + 1
        if 副本数据.齐天大圣.进行[副本id].展示实力.玉皇大帝 >= 1 and 副本数据.齐天大圣.进行[副本id].展示实力.四大天王 >= 1 then
          local 副本id = 任务数据[任务id].副本id
          副本数据.齐天大圣.进行[副本id].进程 = 7
          设置齐天大圣副本(副本id)
          进度刷新 = true
        else
          local id组 = 任务数据[副本数据.齐天大圣.进行[副本id].主任务id].队伍组
          for i = 1, #id组 do
            if 玩家数据[id组[i]] then
              玩家数据[id组[i]].角色:刷新任务跟踪()
            end
          end
          return
        end
      end
    elseif 战斗类型 == 155608 then
      if 玩家数据[id组[1]].角色:取任务(581) and 副本数据.齐天大圣.进行[副本id].展示实力.四大天王 < 1 then
        副本数据.齐天大圣.进行[副本id].展示实力.四大天王 = 副本数据.齐天大圣.进行[副本id].展示实力.四大天王 + 1
        if 副本数据.齐天大圣.进行[副本id].展示实力.玉皇大帝 >= 1 and 副本数据.齐天大圣.进行[副本id].展示实力.四大天王 >= 1 then
          local 副本id = 任务数据[任务id].副本id
          副本数据.齐天大圣.进行[副本id].进程 = 7
          设置齐天大圣副本(副本id)
          进度刷新 = true
        else
          local id组 = 任务数据[副本数据.齐天大圣.进行[副本id].主任务id].队伍组
          for i = 1, #id组 do
            if 玩家数据[id组[i]] then
              玩家数据[id组[i]].角色:刷新任务跟踪()
            end
          end
          return
        end
      end
    elseif 战斗类型 == 590 then
      副本数据.齐天大圣.进行[副本id].进程 = 8
      设置齐天大圣副本(副本id)
      进度刷新 = true
    elseif 战斗类型 == 155604 then
      副本数据.齐天大圣.进行[副本id].盗马贼 = 副本数据.齐天大圣.进行[副本id].盗马贼 - 1
      if 副本数据.齐天大圣.进行[副本id].盗马贼 <= 0 then
        副本数据.齐天大圣.进行[副本id].进程 = 9
        设置齐天大圣副本(副本id)
        进度刷新 = true
      end
    elseif 战斗类型 == 592 then
      副本数据.齐天大圣.进行[副本id].进程 = 10
      设置齐天大圣副本(副本id)
      进度刷新 = true
    elseif 战斗类型 == 593 then
      副本数据.齐天大圣.进行[副本id].进程 = 11
      设置齐天大圣副本(副本id)
      进度刷新 = true
      任务处理类:副本传送(id组[1], 11)
    elseif 战斗类型 == 155605 then
      if 玩家数据[id组[1]].角色:取任务(581) and 副本数据.齐天大圣.进行[副本id].百万天兵.百万天兵 < 1 then
        副本数据.齐天大圣.进行[副本id].百万天兵.百万天兵 = 副本数据.齐天大圣.进行[副本id].百万天兵.百万天兵 + 1
      end
    elseif 战斗类型 == 155606 then
      if 玩家数据[id组[1]].角色:取任务(581) and 副本数据.齐天大圣.进行[副本id].百万天兵.巨灵神 < 1 then
        副本数据.齐天大圣.进行[副本id].百万天兵.巨灵神 = 副本数据.齐天大圣.进行[副本id].百万天兵.巨灵神 + 1
      end
    elseif 战斗类型 == 594 then
      if 副本数据.齐天大圣.进行[副本id].百万天兵.百万天兵 == 1 and 副本数据.齐天大圣.进行[副本id].百万天兵.巨灵神 == 1 then
        副本数据.齐天大圣.进行[副本id].进程 = 12
        设置齐天大圣副本(副本id)
        进度刷新 = true
        任务处理类:副本传送(id组[1], 11)
      end
    elseif 战斗类型 == 155607 then
      local id组 = 任务数据[副本数据.齐天大圣.进行[副本id].主任务id].队伍组
      for i = 1, #id组 do
        if 玩家数据[id组[i]] then
          地图处理类:跳转地图(id组[i], 1001, 358, 35)
          更新玩家每日(id组[i], "副本任务", "齐天大圣")
          玩家数据[id组[i]].角色:添加积分(200, "副本积分")
          常规提示(id组[i], "#Y/恭喜你们完成齐天大圣副本")
        end
      end
      结束齐天大圣副本(任务id)
      return
    end
    local id组 = 任务数据[副本数据.齐天大圣.进行[副本id].主任务id].队伍组
    for i = 1, #id组 do
      if 玩家数据[id组[i]] then
        玩家数据[id组[i]].角色:刷新任务跟踪()
        if 进度刷新 then
          常规提示(id组[i], "#Y/您的副本进度已经更新")
        end
      end
    end
    if 任务数据[任务id] and 任务数据[任务id].地图编号 and 任务数据[任务id].单位编号 then
      地图处理类:删除单位(任务数据[任务id].地图编号, 任务数据[任务id].单位编号)
    end
    任务数据[任务id] = nil
  end
end

function 结束齐天大圣副本(任务id)
  local 副本id = 任务数据[任务id].副本id
  local 主任务id = 副本数据.齐天大圣.进行[副本id].主任务id
  副本数据.齐天大圣.进行[副本id] = nil
  local id组 = 任务数据[主任务id].队伍组
  任务数据[主任务id] = nil
  for i = 1, #id组 do
    if 玩家数据[id组[i]] then
      玩家数据[id组[i]].角色:取消任务(581)
      玩家数据[id组[i]].角色:刷新任务跟踪()
      if 玩家数据[id组[i]].战斗 ~= 0 and ((战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 155600 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 155601 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 155602 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 155603 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 155604 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 155605 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 155606 or (战斗准备类.战斗盒子[玩家数据[id组[i]].战斗] or {}).战斗类型 == 155607) then
        战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]:结束战斗(0, 0, 1)
      end
    end
  end
  for i in pairs(任务数据) do
    if 任务数据[i].类型 >= 581 and 任务数据[i].类型 <= 595 and 任务数据[i].副本id == 副本id then
      if 任务数据[i].地图编号 and 任务数据[i].单位编号 then
        地图处理类:删除单位(任务数据[i].地图编号, 任务数据[i].单位编号)
      end
      任务数据[i] = nil
    end
  end
end

return 副本_齐天大圣
