{"file_history": ["/D/Server/main.lua", "/D/Server/Script/角色处理类/道具处理.lua", "/D/Server/Script/数据库/全局函数.lua", "/D/Server/Script/角色处理类/召唤兽处理.lua", "/D/Server/Script/角色处理类/装备处理.lua", "/D/Server/Script/角色处理类/角色共用.lua", "/D/Server/Script/角色处理类/角色通用.lua", "/D/Server/Script/角色处理类/使用道具.lua", "/D/Server/Script/数据库/宠物数据.lua", "/D/Server/Script/任务/分类任务/端午.lua", "/D/Server/Script/任务/分类任务/大闹天宫.lua", "/D/Server/Script/任务/分类任务/乌鸡国.lua", "/D/Server/Script/角色处理类/非任务奖励.lua", "/D/梦幻服务端/客户端/script/显示类/道具详情.lua", "/D/梦幻服务端/客户端/script/系统类/人物框.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗类.lua", "/D/梦幻服务端/客户端/Script/资源类/加载类.lua", "/D/梦幻服务端/服务端/main.lua", "/D/梦幻服务端/服务端/Script/对话处理类/NPC对话内容.lua", "/D/梦幻服务端/服务端/Script/角色处理类/召唤兽处理类.lua", "/D/梦幻服务端/服务端/Script/假人处理类/假人玩家.lua", "/D/梦幻服务端/服务端/Script/数据中心/物品数据.lua", "/D/梦幻服务端/服务端/Script/角色处理类/道具处理类.lua", "/D/梦幻服务端/服务端/Script/角色处理类/装备处理类.lua", "/D/梦幻服务端/客户端/Script/数据中心/场景NPC.lua", "/D/梦幻服务端/客户端/script/全局/假人.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗命令类.lua", "/D/梦幻服务端/客户端/Script/数据中心/物品库.lua", "/D/梦幻服务端/GGE/Extend/文件类.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/红孩儿.lua", "/D/梦幻服务端/服务端/Script/角色处理类/道具仓库.lua", "/D/2024.07.04团/服务端/main.lua", "/D/2024.07.04团/服务端/Script/角色处理类/装备处理类.lua", "/D/2024.07.04团/服务端/Script/战斗处理类/战斗计算/战斗执行类.lua", "/D/2024.07.04团/服务端/Script/角色处理类/成就类.lua", "/D/2024.07.04团/服务端/Script/战斗处理类/战斗处理.lua", "/D/2024.07.04团/服务端/Script/属性控制/宠物.lua", "/D/2024.07.04团/服务端/Script/初始化脚本.lua", "/D/2024.07.04团/服务端/Script/角色处理类/ScriptInit.lua", "/D/梦幻服务端/新建文件夹/服务端/Script/对话处理类/NPC对话内容.lua", "/D/梦幻服务端/服务端/Script/任务_小九/神器任务/墨魂笔之踪.lua", "/D/梦幻服务端/服务端/Script/神器任务/墨魂笔之踪.lua", "/D/2024.07.04团/服务端/Script/角色处理类/道具处理类.lua", "/D/梦幻服务端/服务端/Script/对话处理类/对话调用/1002.lua", "/D/梦幻服务端/服务端/Script/神器任务/神归昆仑镜.lua", "/D/2024.07.04团/服务端/Script/任务活动类/首席争霸赛.lua", "/D/2024.07.04团/服务端/Script/神器任务/墨魂笔之踪.lua", "/D/2024.07.04团/服务端/Script/对话处理类/NPC对话内容.lua", "/D/2024.07.04团/服务端/Script/对话处理类/对话调用/1002.lua", "/D/2024.07.04团/服务端/Script/任务处理类/任务处理类.lua", "/D/2024.07.04团/服务端/Script/对话处理类/对话调用/1016.lua", "/D/2024.07.04团/服务端/Script/对话处理类/对话调用/1070.lua", "/D/梦幻服务端/shenqi/服务端/Script/对话处理类/NPC对话内容.lua", "/D/梦幻服务端/shenqi/服务端/Script/任务_小九/神器任务/神归昆仑镜.lua", "/D/梦幻服务端/shenqi/客户端/Script/网络/数据交换.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa196940.39752/墨魂笔之踪.lua", "/D/梦幻服务端/服务端/Script/对话处理类/怪物对话处理.lua", "/D/梦幻服务端/服务端/Script/对话处理类/怪物对话内容.lua", "/D/梦幻服务端/服务端/Script/任务_小九/天罡星.lua", "/D/梦幻服务端/服务端/Script/任务_小九/中秋任务.lua", "/D/梦幻服务端/服务端/Script/任务_小九/周末玩法/降妖伏魔.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa115300.41135/怪物对话内容.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/怪物调用/结算处理.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/创建战斗.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/怪物属性.lua", "/D/梦幻服务端/服务端/Script/任务处理类/任务处理类.lua", "/D/梦幻服务端/服务端/Script/任务_小九/常规任务/香飘飘任务.lua", "/D/梦幻服务端/服务端/Script/任务_小九/常规任务/师门任务.lua", "/D/梦幻服务端/服务端/Script/任务_小九/常规任务/人物修任务.lua", "/D/梦幻服务端/服务端/Script/任务_小九/天降辰星.lua", "/D/梦幻服务端/客户端/main.lua", "/G/三花修复版-低调使用/服务端/main.lua", "/G/三花修复版-低调使用/服务端/Script/任务处理类/周杰伦.lua", "/G/三花修复版-低调使用/服务端/Script/任务处理类/长安保卫战.lua", "/G/三花修复版-低调使用/服务端/Script/角色处理类/道具处理类.lua", "/G/三花修复版-低调使用/服务端/Script/战斗处理类/创建战斗.lua", "/G/三花修复版-低调使用/服务端/Script/战斗处理类/怪物属性.lua", "/G/三花修复版-低调使用/服务端/Script/全局函数类/队伍函数类.lua", "/G/三花修复版-低调使用/服务端/Script/系统处理类/GM工具类.lua", "/G/三花修复版-低调使用/服务端/Script/初始化脚本.lua", "/D/梦幻服务端/服务端/Script/全局函数类/队伍函数类.lua", "/D/梦幻服务端/服务端/Script/任务_小九/周末玩法/门派闯关.lua", "/D/梦幻服务端/服务端/Script/全局函数类/全局函数.lua", "/D/梦幻服务端/服务端/Script/任务_小九/副本任务/齐天大圣.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/战斗处理.lua", "/D/梦幻服务端/客户端/Script/更新类/打字机.lua", "/D/梦幻服务端/客户端/Script/场景类/第二场景.lua", "/D/梦幻服务端/客户端/Script/网络/数据交换.lua", "/D/梦幻服务端/客户端/Script/资源类/地图类.lua", "/D/梦幻服务端/客户端/Script/全局/主控.lua", "/D/梦幻服务端/客户端/Script/系统类/按钮.lua", "/D/梦幻服务端/客户端/Script/功能界面/聊天框外部.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗状态栏.lua", "/D/梦幻服务端/客户端/Script/场景类/剧情处理器.lua", "/D/梦幻服务端/客户端/Script/全局/假人.lua", "/D/梦幻服务端/服务端/Script/战斗处理类/AI战斗.lua", "/D/梦幻服务端/客户端/Script/小九UI/角色/人物状态栏.lua", "/D/梦幻服务端/客户端/Script/初系统/游戏更新说明.lua", "/D/梦幻服务端/服务端/Script/角色处理类/角色处理类.lua", "/D/梦幻服务端/服务端/Script/系统处理类/神兵异兽榜.lua", "/D/梦幻服务端/客户端/Script/资源类/FSB.lua", "/D/梦幻服务端/客户端/Script/资源类/缓存资源.lua", "/D/梦幻服务端/客户端/Script/资源类/WDF.lua", "/D/梦幻服务端/客户端/Script/全局/变量1.lua", "/D/梦幻服务端/客户端/script/战斗类/战斗单位类.lua", "/D/梦幻服务端/客户端/script/小九UI/系统设置.lua", "/D/梦幻服务端/客户端/Script/小九UI/系统设置.lua", "/D/梦幻服务端/客户端/script/全局/变量1.lua", "/D/梦幻服务端/客户端/Script/功能界面/底图框.lua", "/D/梦幻服务端/客户端/Script/小九UI/助战系统.lua", "/D/梦幻服务端/服务端/Script/助战处理类/MateControl.lua", "/D/梦幻服务端/客户端/Script/全局/主显.lua", "/D/2024.07.04团/PC客户端/main.lua", "/D/2024.07.04团/PC客户端/Script/全局/主控.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗单位类.lua", "/D/梦幻服务端/客户端/Script/资源类/gge资源类.lua", "/D/梦幻服务端/客户端/Script/数据中心/音效库.lua", "/D/梦幻服务端/客户端/Script/全局/人物.lua", "/D/梦幻服务端/客户端/Script/数据中心/特效库.lua", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script/网络/数据交换.lua", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script/全局/主控.lua", "/D/紫禁之巅九黎/梦回长安/Script/网络/数据交换.lua", "/D/紫禁之巅九黎/梦回长安/Script/全局/主控.lua", "/D/紫禁之巅九黎/梦回长安/Script/数据中心/特效库.lua", "/D/三花修复版-低调使用/客户端/Script/数据中心/特效库.lua", "/D/梦幻服务端/客户端/Script/小九UI/钓鱼.lua", "/D/梦幻服务端/客户端/Script/小九UI/召唤兽属性栏.lua", "/D/梦幻服务端/客户端/Script/数据中心/自定义库.lua"], "folder_history": ["/D/梦幻服务端/新建文件夹/服务端", "/D/2024.07.04团/服务端/Script", "/D/梦幻服务端/shenqi/客户端/Script", "/D/梦幻服务端/shenqi/服务端/Script", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script", "/D/my2/客户端/script", "/D/数据库版本/客户端早期源码/script", "/D/三花修复版-低调使用/客户端/Script", "/D/紫禁之巅九黎/梦回长安/Script", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script/全局", "/D/梦幻服务端/源码备份/0.49/客户端/<PERSON><PERSON>t", "/D/2024.07.04团/PC客户端/Script", "/D/梦幻服务端/源码备份/0.543/客户端/<PERSON>ript", "/D/my2/客户端", "/D/my2/服务端/<PERSON><PERSON>t", "/D/08源码/服务端/Script", "/D/08源码/客户端", "/D/梦幻服务端/源码备份/0.49/客户端", "/D/紫禁之巅九黎/服务端/Script", "/D/三花修复版-低调使用/服务端/Script", "/D/梦幻服务端/源码备份/0.49/服务端/<PERSON><PERSON>t", "/D/梦神3/服务端/<PERSON><PERSON>t"], "last_version": 4180, "last_window_id": 157, "log_indexing": false, "next_update_check": 1733187713, "settings": {"new_window_full_screen": false, "new_window_height": 892.0, "new_window_maximized": false, "new_window_position": [432.0, 66.0], "new_window_settings": {"auto_complete": {"selected_items": []}, "build_system_choices": [[[["Packages/Lua/ggebc.sublime-build", "RunInCommand"], ["Packages/Lua/ggegame.sublime-build", "RunInCommand"], ["Packages/Lua/ggeobj.sublime-build", "RunInCommand"], ["Packages/Lua/ggeserver.sublime-build", "RunInCommand"]], ["Packages/Lua/ggegame.sublime-build", "RunInCommand"]]], "build_varint": "", "command_palette": {"height": 129.0, "last_filter": "", "selected_items": [["phpf", "File: Copy Path From Project"], ["in", "Package Control: Install Package"], ["require", "Snippet: require(module)"], ["references to f", "Preferences: Package Control Settings – <PERSON><PERSON>ult"]], "width": 464.0}, "console": {"height": 152.0, "history": ["import urllib.request,os; pf = 'Package Control.sublime-package'; ipp = sublime.installed_packages_path(); urllib.request.install_opener( urllib.request.build_opener( urllib.request.ProxyHandler()) ); open(os.path.join(ipp, pf), 'wb').write(urllib.request.urlopen( 'http://sublime.wbond.net/' + pf.replace(' ','%20')).read())"]}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "file_history": ["/D/梦幻服务端/客户端/main.lua", "/D/梦幻服务端/客户端/script/显示类/道具详情.lua", "/D/梦幻服务端/客户端/script/系统类/人物框.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗类.lua", "/D/梦幻服务端/客户端/Script/资源类/加载类.lua", "/D/梦幻服务端/客户端/Script/数据中心/场景NPC.lua", "/D/梦幻服务端/客户端/script/全局/假人.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗命令类.lua", "/D/梦幻服务端/客户端/Script/数据中心/物品库.lua", "/D/梦幻服务端/GGE/Extend/文件类.lua", "/D/梦幻服务端/客户端/Script/更新类/打字机.lua", "/D/梦幻服务端/客户端/Script/场景类/第二场景.lua", "/D/梦幻服务端/客户端/Script/网络/数据交换.lua", "/D/梦幻服务端/客户端/Script/资源类/地图类.lua", "/D/梦幻服务端/客户端/Script/全局/主控.lua", "/D/梦幻服务端/客户端/Script/系统类/按钮.lua", "/D/梦幻服务端/客户端/Script/功能界面/聊天框外部.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗状态栏.lua", "/D/梦幻服务端/客户端/Script/场景类/剧情处理器.lua", "/D/梦幻服务端/客户端/Script/全局/假人.lua", "/D/梦幻服务端/客户端/Script/小九UI/角色/人物状态栏.lua", "/D/梦幻服务端/客户端/Script/初系统/游戏更新说明.lua", "/D/梦幻服务端/客户端/Script/资源类/FSB.lua", "/D/梦幻服务端/客户端/Script/资源类/缓存资源.lua", "/D/梦幻服务端/客户端/Script/资源类/WDF.lua", "/D/梦幻服务端/客户端/Script/全局/变量1.lua", "/D/梦幻服务端/客户端/script/战斗类/战斗单位类.lua", "/D/梦幻服务端/客户端/script/小九UI/系统设置.lua", "/D/梦幻服务端/客户端/Script/小九UI/系统设置.lua", "/D/梦幻服务端/客户端/script/全局/变量1.lua", "/D/梦幻服务端/客户端/Script/功能界面/底图框.lua", "/D/梦幻服务端/客户端/Script/小九UI/助战系统.lua", "/D/梦幻服务端/客户端/Script/全局/主显.lua", "/D/梦幻服务端/客户端/Script/资源类/gge资源类.lua", "/D/梦幻服务端/客户端/Script/数据中心/音效库.lua", "/D/梦幻服务端/客户端/Script/全局/人物.lua", "/D/梦幻服务端/客户端/Script/数据中心/特效库.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗单位类.lua", "/D/梦幻服务端/客户端/Script/小九UI/钓鱼.lua", "/D/梦幻服务端/客户端/Script/小九UI/召唤兽属性栏.lua", "/D/梦幻服务端/客户端/Script/数据中心/自定义库.lua", "/D/梦幻服务端/客户端/Script/数据中心/战斗模型库.lua", "/D/梦幻服务端/源码备份/0.40", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script/数据中心/2024.3.2/客户端/Script/数据中心/战斗模型库.lua", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script/战斗类/战斗单位类.lua", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script/战斗类/2024.3.2/客户端/Script/战斗类/战斗单位类.lua", "/D/梦幻服务端/源码备份/客户端/Script/战斗类/战斗单位类.lua", "/D/梦幻服务端/客户端/Script/全局/变量2.lua", "/D/梦幻服务端/GGE/Core/Game/ggedebug.lua", "/D/梦幻服务端/客户端/Script/小九UI/人物状态栏.lua", "/D/梦幻服务端/GGE/Extend/Fmod类.lua", "/D/梦幻服务端/客户端/Script/更新类/星盘.lua", "/D/梦幻服务端/客户端/script/全局/主控.lua", "/D/梦幻服务端/源码备份/0.543/客户端/Script/全局/人物.lua", "/D/梦幻服务端/客户端/Script/资源类/gge精灵类.lua", "/D/梦幻服务端/客户端/Script/小九函数/图片按钮.lua", "/D/梦幻服务端/客户端/Script/小九UI/战备系统.lua", "/D/梦幻服务端/客户端/Script/功能界面/时辰.lua", "/D/梦幻服务端/客户端/Script/小九UI/交易.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗召唤栏.lua", "/D/梦幻服务端/客户端/Script/资源类/动画类.lua", "/D/梦幻服务端/客户端/Script/资源类/专用动画类2.lua", "/D/梦幻服务端/客户端/Script/聊天框系统/聊天框丰富文本.lua", "/D/梦幻服务端/客户端/Script/更新类/唱戏交票界面.lua", "/D/梦幻服务端/客户端/Script/初系统/标题.lua", "/D/梦幻服务端/客户端/Script/系统类/人物框.lua", "/D/梦幻服务端/客户端/Script/系统类/自适应.lua", "/D/梦幻服务端/客户端/Script/小九UI/奇经八脉.lua", "/D/梦幻服务端/客户端/Script/更新类/转盘.lua", "/C/Users/<USER>/Documents/WeChat Files/wxid_yy7zc4ux2ghf22/FileStorage/File/2024-10/星盘.lua", "/D/梦幻服务端/客户端/Script/小九UI/成就系统.lua", "/D/梦幻服务端/客户端/Script/功能界面/世界地图分类小地图.lua", "/D/梦幻服务端/客户端/Script/数据中心/场景.lua", "/D/梦幻服务端/客户端/script/系统类/按钮.lua", "/D/梦幻服务端/客户端/script/功能界面/世界地图分类小地图.lua", "/D/梦幻服务端/客户端/script/更新类/转盘.lua", "/D/梦幻服务端/客户端/Script/神器类/合成灵犀玉.lua", "/C/Users/<USER>/Documents/WeChat Files/wxid_yy7zc4ux2ghf22/FileStorage/File/2024-10/天罡星.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa19308.39655/全局函数.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa16724.46328/天罡星.lua", "/D/梦幻服务端/客户端/Script/小九UI/星盘.lua", "/D/梦幻服务端/客户端/Script/小九UI/道具行囊.lua", "/D/梦幻服务端/客户端/Script/全局/玩家.lua", "/D/梦幻服务端/客户端/Script/小九UI/队伍栏.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗动画类.lua", "/D/梦幻服务端/客户端/Script/小九UI/摊位出售.lua", "/D/梦幻服务端/客户端/Script/小九UI/商城宝宝查看.lua", "/D/梦幻服务端/客户端/Script/小九UI/商城类.lua", "/D/梦幻服务端/客户端/Script/数据中心/技能库.lua", "/D/梦幻服务端/客户端/Script/资源类/动画类_X9.lua", "/D/梦幻服务端/客户端/script/战斗类/战斗类.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa13784.2416/加载类.lua", "/C/Users/<USER>/Desktop/新建文件夹 (2)/0.544/客户端/Script/资源类/地图类.lua", "/C/Users/<USER>/Desktop/新建文件夹 (2)/0.544/客户端/Script/战斗类/战斗类.lua", "/C/Users/<USER>/Desktop/新建文件夹 (2)/0.544/客户端/Script/资源类/加载类.lua", "/D/梦幻服务端/客户端/__GGE", "/D/梦幻服务端/客户端/Script/剑会/剑会天下.lua", "/D/梦幻服务端/客户端/Script/剑会/剑会排行榜.lua", "/D/梦幻服务端/客户端/Script/功能界面/聊天框.lua", "/D/梦幻服务端/客户端/Script/数据中心/经脉库.lua", "/D/梦幻服务端/客户端/Script/数据中心/普通模型库.lua", "/D/梦幻服务端/客户端/Script/小九UI/成长礼包.lua", "/D/梦幻服务端/客户端/Script/小九UI/符石镶嵌.lua", "/D/梦幻服务端/客户端/Script/剑会/剑会匹配.lua", "/D/梦幻服务端/客户端/Script/神器类/修复神器.lua", "/D/梦幻服务端/客户端/Script/神器类/神器更换五行.lua", "/D/梦幻服务端/客户端/Script/小九UI/法宝.lua", "/D/梦幻服务端/客户端/Script/显示类/技能_格子.lua", "/D/梦幻服务端/客户端/Script/全局/自己_专用.LUA", "/D/梦幻服务端/客户端/Script/显示类/道具详情.lua", "/D/梦幻服务端/客户端/Script/小九UI/召唤兽资质栏.lua", "/D/梦幻服务端/客户端/Script/显示类/技能.lua", "/D/梦幻服务端/客户端/Script/小九UI/祥瑞界面.lua", "/D/梦幻服务端/客户端/Script/更新类/神秘宝箱.lua", "/D/梦幻服务端/客户端/Script/网络/hp.lua", "/D/梦幻服务端/客户端/Script/初系统/登陆.lua", "/D/梦幻服务端/客户端/Script/初系统/分区.lua", "/D/梦幻服务端/GGE/Core/Game/gge包围盒.lua", "/D/梦幻服务端/客户端/script/显示类/游戏公告类.lua", "/D/梦幻服务端/客户端/Script/神器类/神器查看.lua", "/D/梦幻服务端/GGE/Core/Game/gge图像类1.lua", "/D/梦幻服务端/源码备份/0.49/客户端/<PERSON>rip<PERSON>/小九UI/奇经八脉.lua", "/D/梦幻服务端/客户端/Script/显示类/物品.lua", "/D/梦幻服务端/客户端/Script/小九UI/属性切换.lua", "/D/梦幻服务端/客户端/script/小九UI/成就系统.lua", "/D/梦幻服务端/客户端/Script/属性控制/队伍.lua", "/D/梦幻服务端/客户端/Script/小九UI/好友列表.lua", "/D/梦幻服务端/客户端/Script/小九UI/好友查找.lua"], "find": {"height": 32.0}, "find_in_files": {"height": 116.0, "where_history": ["D:\\梦幻服务端\\客户端\\Script", "D:\\梦幻服务端\\服务端\\Script,D:\\梦幻服务端\\客户端", "D:\\梦幻服务端\\服务端\\Script", "D:\\梦幻服务端", "D:\\梦幻服务端\\客户端\\Script", "D:\\梦幻服务端\\GGE", "D:\\梦幻服务端\\客户端\\Script", "全局界面风格", "D:\\梦幻服务端\\客户端\\Script", "D:\\梦幻服务端", "D:\\梦幻服务端\\客户端\\Script", "D:\\梦幻服务端\\服务端\\Script", ""]}, "find_state": {"case_sensitive": false, "find_history": ["品质", "灵气", "品质", "九眼天珠", "老胡", "天眼", "渐变", "黑屏渐变", "渐变", "翻页", "6557", "鼠标按下", "点击", "第二场景", "6557", "剧情动画", "[23]", "推荐", "人物状态", "tp.tp.", "游戏音效", "游戏音效>0", "游戏音效", "音效", "游戏音乐", "声音设置", "进程", "战斗音效", "战斗", "进程", "音乐", "self.音乐", "进程", "进程=1", "进程==1", "系统设置", "战斗音效类", "无叠加", "击飞", "self:音效类", "无叠加", "self:音效类", "tp:音效类", "self:音效类", "音效开启", "游戏音效", "全局资源地址", "资源缓存", "全局资源缓存", "全局资源地址", "全局资源缓存", "资源缓存", "__fsyz", "全局dt", "随机序列", "config.ini", "音乐开启", "[11]", "游戏音乐", "tp", "音量", "设置", "发送数据(7)", "7", "1501", "助战", "主站", "引擎创建完成", "全局时辰微秒", "重建tp", "垂直同步", "按钮音效", "ALT+A", "清理所有音效", "音效", "场景", "self.游戏音效", "播放音效类", "yxs", "游戏音效", "按钮", "\"升级\"", "self.特效缓存 = {}", "fc:置根(self)", "音效类", "音效开启", "tp", "钓鱼", "舞天姬", "[16]", "[15]", "推荐加点", "巫蛮儿", "四方向", "击退", "同步气血", "四方向", "ZhandouModel[\"羽灵神_弓弩\"]", "ZhandouModel", "弓弩", "雷神", "角色信息", "self.单位类型 == \"角色\"", "self.武器子类 == 14", "14", "武器子类==", "武器子类== 14", "移动事件", "击退", "14", "八方向", "八方想", "14", "逍遥生", "超级泡泡", "玄彩娥", "击退处理", "超级泡泡", "大力", "剑侠客", "内存", "显示(dt,x,y)", "场景音乐类", "战斗音效序列", "战斗单位", "升级", "音效", "消音"], "highlight": false, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["tp.游戏音效>0", "游戏音效类", "tp:战斗音效类", "tp:音效类", "星盘", "字体", "btcsmszt", "终结帧", "攻击2", "wzife.wd1", "magic.wd1", "wzife.wd1", "Resource.ft", "wzife.wd1", "Resource.ft", "wzife.wd1", "tp.提示:经脉技能", "tp.提示:经脉技能(x, y, self.资源组[11][n].技能)", "elseif", "", "\\n#S", "\"\\n#S穴位", "\\n#S穴位", "\\n#S", "\"\\n#S", "\"\\n", "\"#S\\n", "", "：", "", " ", "", "穴位：【", "\"\\n#S", "", "\"\\n#S", "self.技能.新介绍", "self.新介绍", "经脉", "", "#S", "根", "攻击2", "暴击", "self.战斗单位[ljcs][self.战斗流程[1]", "self.战斗单位[ljcs]", "self.x + 13", "self.x + 10", "jiu资源", "self:音效类", "if 游戏音效>0 and 临时音乐~=nil then\n      self:音效类(临时音乐.文件,临时音乐.资源,'1')\n    end", "self:音效类", "self:音效类(临时音乐.文件,临时音乐.资源,'1')", "self:音效类", "self:音效类(临时音乐[动作],临时音乐.资源,动作)", "self:音效类", "self:音效类(临时音乐[动作],临时音乐.资源,动作)", "self.战斗单位[ljcs][self.战斗流程[1].攻击方]:音效类(临时音乐.文件,临时音乐.资源,名称)", "tp:游戏音效类1", "yxs", "self:游戏音效类", "donghua", "ziyuan", "界面风格", "输入背景", "", "全局界面风格", "", "uigy.wdf", "", "偏移值", "41", "22", "18", "40", "97", "50", "12", "67", "60", "table.remove(self.战斗流程[1].挨打方, n)", "自动数据", "桀骜自恃", "超级三昧真火", "桀骜自恃", "超级三昧真火", "self.战斗单位[ljcs][self.战斗流程[1].挨打方[1]", "self.战斗单位[ljcs][self.战斗流程[1].攻击方]", "self.战斗单位[ljcs]", "},", "", "49.235.140.123"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "incremental_find": {"height": 32.0}, "input": {"height": 45.0}, "menu_visible": true, "output.exec": {"height": 273.0}, "output.find_results": {"height": 0.0}, "output.mdpopups": {"height": 0.0}, "output.unsaved_changes": {"height": 381.0}, "pinned_build_system": "Packages/Lua/ggegame.sublime-build", "replace": {"height": 60.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["", "客户端\\Script\\初系统\\创建.lua"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [["", "D:\\梦幻服务端\\客户端\\开发客户端.sublime-project"]], "width": 380.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 178.0, "status_bar_visible": true, "template_settings": {}}, "new_window_width": 1350.0}, "windows": [{"auto_complete": {"selected_items": []}, "buffers": [{"file": "main.lua", "settings": {"buffer_size": 4856, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[1, 1, "revert", null, "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", "DwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAHgHAAAAAAAAeAcAAAAAAAAAAAAAAADwvw"], [4, 1, "insert", {"characters": "q"}, "AgAAADkCAAAAAAAAOgIAAAAAAAAAAAAAOgIAAAAAAAA6AgAAAAAAAAMAAAA4MDA", "DwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAADkCAAAAAAAAPAIAAAAAAAAAAAAAAADwvw"], [5, 1, "left_delete", null, "AQAAADkCAAAAAAAAOQIAAAAAAAABAAAAcQ", "DwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAADoCAAAAAAAAOgIAAAAAAAAAAAAAAADwvw"], [24, 1, "sequence", {"commands": []}, "EwAAADkCAAAAAAAAOgIAAAAAAAAAAAAAOQIAAAAAAAA7AgAAAAAAAAEAAABxOQIAAAAAAAA9AgAAAAAAAAIAAABxdTkCAAAAAAAAPQIAAAAAAAAEAAAAcXUnYTkCAAAAAAAAPwIAAAAAAAAEAAAAcXVhbjkCAAAAAAAAQAIAAAAAAAAGAAAAcXVhbidqOQIAAAAAAABCAgAAAAAAAAcAAABxdWFuJ2p1OQIAAAAAAABDAgAAAAAAAAkAAABxdWFuJ2p1J3k5AgAAAAAAAEQCAAAAAAAACgAAAHF1YW4nanUneW85AgAAAAAAAEYCAAAAAAAACwAAAHF1YW4nanUneW91OQIAAAAAAABHAgAAAAAAAA0AAABxdWFuJ2p1J3lvdSd4OQIAAAAAAABJAgAAAAAAAA4AAABxdWFuJ2p1J3lvdSd4aTkCAAAAAAAASgIAAAAAAAAQAAAAcXVhbidqdSd5b3UneGknazkCAAAAAAAASwIAAAAAAAARAAAAcXVhbidqdSd5b3UneGkna3U5AgAAAAAAAEwCAAAAAAAAEgAAAHF1YW4nanUneW91J3hpJ2t1YTkCAAAAAAAATgIAAAAAAAATAAAAcXVhbidqdSd5b3UneGkna3VhbjkCAAAAAAAATwIAAAAAAAAVAAAAcXVhbidqdSd5b3UneGkna3VhbidkOQIAAAAAAAA/AgAAAAAAABYAAABxdWFuJ2p1J3lvdSd4aSdrdWFuJ2R1OQIAAAAAAAA/AgAAAAAAABIAAADlhajlsYDmuLjmiI/lrr3luqY", "DwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAADkCAAAAAAAAOQIAAAAAAAAAAAAAAADwvw"], [44, 1, "sequence", {"commands": []}, "DwAAAEACAAAAAAAAQQIAAAAAAAADAAAANjAwQAIAAAAAAABCAgAAAAAAAAEAAABxQAIAAAAAAABEAgAAAAAAAAIAAABxdUACAAAAAAAARAIAAAAAAAAEAAAAcXUnYUACAAAAAAAARgIAAAAAAAAEAAAAcXVhbkACAAAAAAAASAIAAAAAAAAGAAAAcXVhbidrQAIAAAAAAABGAgAAAAAAAAgAAABxdWFuJ2sneUACAAAAAAAARwIAAAAAAAAGAAAAcXVhbidrQAIAAAAAAABJAgAAAAAAAAcAAABxdWFuJ2t1QAIAAAAAAABKAgAAAAAAAAkAAABxdWFuJ2t1J3lAAgAAAAAAAEsCAAAAAAAACgAAAHF1YW4na3UneW9AAgAAAAAAAE0CAAAAAAAACwAAAHF1YW4na3UneW91QAIAAAAAAABOAgAAAAAAAA0AAABxdWFuJ2t1J3lvdSd4QAIAAAAAAABEAgAAAAAAAA4AAABxdWFuJ2t1J3lvdSd4aUACAAAAAAAARAIAAAAAAAAMAAAA5YWo5bqT5ri45oiP", "DwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAEACAAAAAAAAQwIAAAAAAAAAAAAAAADwvw"], [51, 1, "sequence", {"commands": []}, "BwAAAEQCAAAAAAAARQIAAAAAAAAAAAAARAIAAAAAAABGAgAAAAAAAAEAAABnRAIAAAAAAABHAgAAAAAAAAIAAABnYUQCAAAAAAAASQIAAAAAAAADAAAAZ2FvRAIAAAAAAABKAgAAAAAAAAUAAABnYW8nZEQCAAAAAAAARgIAAAAAAAAGAAAAZ2FvJ2R1RAIAAAAAAABGAgAAAAAAAAYAAADpq5jluqY", "DwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAEQCAAAAAAAARAIAAAAAAAAAAAAAAADwvw"], [52, 5, "left_delete", null, "BQAAAEUCAAAAAAAARQIAAAAAAAADAAAA5bqmRAIAAAAAAABEAgAAAAAAAAMAAADpq5hDAgAAAAAAAEMCAAAAAAAAAwAAAOaIj0ICAAAAAAAAQgIAAAAAAAADAAAA5ri4QQIAAAAAAABBAgAAAAAAAAMAAADlupM", "DwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAEYCAAAAAAAARgIAAAAAAAAAAAAAAADwvw"], [56, 1, "sequence", {"commands": []}, "BAAAAEECAAAAAAAAQgIAAAAAAAAAAAAAQQIAAAAAAABDAgAAAAAAAAEAAABqQQIAAAAAAABCAgAAAAAAAAIAAABqdUECAAAAAAAAQgIAAAAAAAADAAAA5bGA", "DwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAEECAAAAAAAAQQIAAAAAAAAAAAAAAADwvw"], [68, 1, "sequence", {"commands": []}, "DAAAAEICAAAAAAAAQwIAAAAAAAAAAAAAQgIAAAAAAABEAgAAAAAAAAEAAAB5QgIAAAAAAABFAgAAAAAAAAIAAAB5b0ICAAAAAAAARwIAAAAAAAADAAAAeW91QgIAAAAAAABIAgAAAAAAAAUAAAB5b3UneEICAAAAAAAASgIAAAAAAAAGAAAAeW91J3hpQgIAAAAAAABLAgAAAAAAAAgAAAB5b3UneGknZ0ICAAAAAAAATAIAAAAAAAAJAAAAeW91J3hpJ2dhQgIAAAAAAABOAgAAAAAAAAoAAAB5b3UneGknZ2FvQgIAAAAAAABPAgAAAAAAAAwAAAB5b3UneGknZ2FvJ2RCAgAAAAAAAEYCAAAAAAAADQAAAHlvdSd4aSdnYW8nZHVCAgAAAAAAAEYCAAAAAAAADAAAAOa4uOaIj+mrmOW6pg", "DwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAEICAAAAAAAAQgIAAAAAAAAAAAAAAADwvw"], [72, 1, "file_header_replace", {"a": 117, "b": 137, "strings": " 2024-10-28 08:55:50"}, "AQAAAHUAAAAAAAAAiQAAAAAAAAAUAAAAIDIwMjQtMTAtMjYgMDg6MTA6NTA", "DwAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAE8CAAAAAAAATwIAAAAAAAAAAAAAAADwvw"], [3, 1, "toggle_comment", {"block": false}, "AwAAAEoIAAAAAAAATQgAAAAAAAAAAAAAKAgAAAAAAAArCAAAAAAAAAAAAAALCAAAAAAAAA4IAAAAAAAAAAAAAA", "CgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAATQgAAAAAAAALCAAAAAAAAAAAAAAAAPC/"], [5, 1, "file_header_replace", {"a": 107, "b": 127, "strings": " 2024-11-27 15:08:14"}, "AQAAAGsAAAAAAAAAfwAAAAAAAAAUAAAAIDIwMjQtMTEtMTkgMjI6NDU6NTg", "CgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAVggAAAAAAAAOCAAAAAAAAAAAAAAAAPC/"], [2, 1, "file_header_replace", {"a": 107, "b": 127, "strings": " 2024-11-29 11:14:29"}, "AQAAAGsAAAAAAAAAfwAAAAAAAAAUAAAAIDIwMjQtMTEtMjcgMjE6Mjg6MTE", "BQAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"]]}], "build_system": "Packages/Lua/ggegame.sublime-build", "build_system_choices": [[[["Packages/Lua/ggebc.sublime-build", "RunInCommand"], ["Packages/Lua/ggegame.sublime-build", "RunInCommand"], ["Packages/Lua/ggeobj.sublime-build", "RunInCommand"], ["Packages/Lua/ggeserver.sublime-build", "RunInCommand"]], ["Packages/Lua/ggegame.sublime-build", "RunInCommand"]]], "build_varint": "", "command_palette": {"height": 129.0, "last_filter": "", "selected_items": [["phpf", "File: Copy Path From Project"], ["in", "Package Control: Install Package"], ["require", "Snippet: require(module)"], ["references to f", "Preferences: Package Control Settings – <PERSON><PERSON>ult"]], "width": 464.0}, "console": {"height": 152.0, "history": ["import urllib.request,os; pf = 'Package Control.sublime-package'; ipp = sublime.installed_packages_path(); urllib.request.install_opener( urllib.request.build_opener( urllib.request.ProxyHandler()) ); open(os.path.join(ipp, pf), 'wb').write(urllib.request.urlopen( 'http://sublime.wbond.net/' + pf.replace(' ','%20')).read())"]}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/D/梦幻服务端/客户端", "/D/梦幻服务端/客户端/Script", "/D/梦幻服务端/客户端/Script/全局", "/D/梦幻服务端/客户端/Script/初系统", "/D/梦幻服务端/客户端/Script/功能界面", "/D/梦幻服务端/客户端/Script/场景类", "/D/梦幻服务端/客户端/Script/小九UI", "/D/梦幻服务端/客户端/Script/战斗类", "/D/梦幻服务端/客户端/Script/数据中心", "/D/梦幻服务端/客户端/Script/显示类", "/D/梦幻服务端/客户端/Script/更新类", "/D/梦幻服务端/客户端/Script/神器类", "/D/梦幻服务端/客户端/Script/系统类", "/D/梦幻服务端/客户端/Script/网络", "/D/梦幻服务端/客户端/Script/资源类"], "file_history": ["/D/梦幻服务端/客户端/script/显示类/道具详情.lua", "/D/梦幻服务端/客户端/script/系统类/人物框.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗类.lua", "/D/梦幻服务端/客户端/Script/资源类/加载类.lua", "/D/梦幻服务端/客户端/Script/数据中心/场景NPC.lua", "/D/梦幻服务端/客户端/script/全局/假人.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗命令类.lua", "/D/梦幻服务端/客户端/Script/数据中心/物品库.lua", "/D/梦幻服务端/GGE/Extend/文件类.lua", "/D/梦幻服务端/客户端/Script/更新类/打字机.lua", "/D/梦幻服务端/客户端/Script/场景类/第二场景.lua", "/D/梦幻服务端/客户端/Script/网络/数据交换.lua", "/D/梦幻服务端/客户端/Script/资源类/地图类.lua", "/D/梦幻服务端/客户端/Script/全局/主控.lua", "/D/梦幻服务端/客户端/Script/系统类/按钮.lua", "/D/梦幻服务端/客户端/Script/功能界面/聊天框外部.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗状态栏.lua", "/D/梦幻服务端/客户端/Script/场景类/剧情处理器.lua", "/D/梦幻服务端/客户端/Script/全局/假人.lua", "/D/梦幻服务端/客户端/Script/小九UI/角色/人物状态栏.lua", "/D/梦幻服务端/客户端/Script/初系统/游戏更新说明.lua", "/D/梦幻服务端/客户端/Script/资源类/FSB.lua", "/D/梦幻服务端/客户端/Script/资源类/缓存资源.lua", "/D/梦幻服务端/客户端/Script/资源类/WDF.lua", "/D/梦幻服务端/客户端/Script/全局/变量1.lua", "/D/梦幻服务端/客户端/script/战斗类/战斗单位类.lua", "/D/梦幻服务端/客户端/script/小九UI/系统设置.lua", "/D/梦幻服务端/客户端/Script/小九UI/系统设置.lua", "/D/梦幻服务端/客户端/script/全局/变量1.lua", "/D/梦幻服务端/客户端/Script/功能界面/底图框.lua", "/D/梦幻服务端/客户端/Script/小九UI/助战系统.lua", "/D/梦幻服务端/客户端/Script/全局/主显.lua", "/D/梦幻服务端/客户端/Script/资源类/gge资源类.lua", "/D/梦幻服务端/客户端/Script/数据中心/音效库.lua", "/D/梦幻服务端/客户端/Script/全局/人物.lua", "/D/梦幻服务端/客户端/Script/数据中心/特效库.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗单位类.lua", "/D/梦幻服务端/客户端/Script/小九UI/钓鱼.lua", "/D/梦幻服务端/客户端/Script/小九UI/召唤兽属性栏.lua", "/D/梦幻服务端/客户端/Script/数据中心/自定义库.lua", "/D/梦幻服务端/客户端/Script/数据中心/战斗模型库.lua", "/D/梦幻服务端/源码备份/0.40", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script/数据中心/2024.3.2/客户端/Script/数据中心/战斗模型库.lua", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script/战斗类/战斗单位类.lua", "/D/梦幻服务端/源码备份/0.40 2024.3.2/客户端/Script/战斗类/2024.3.2/客户端/Script/战斗类/战斗单位类.lua", "/D/梦幻服务端/源码备份/客户端/Script/战斗类/战斗单位类.lua", "/D/梦幻服务端/客户端/Script/全局/变量2.lua", "/D/梦幻服务端/GGE/Core/Game/ggedebug.lua", "/D/梦幻服务端/客户端/Script/小九UI/人物状态栏.lua", "/D/梦幻服务端/GGE/Extend/Fmod类.lua", "/D/梦幻服务端/客户端/Script/更新类/星盘.lua", "/D/梦幻服务端/客户端/script/全局/主控.lua", "/D/梦幻服务端/源码备份/0.543/客户端/Script/全局/人物.lua", "/D/梦幻服务端/客户端/Script/资源类/gge精灵类.lua", "/D/梦幻服务端/客户端/Script/小九函数/图片按钮.lua", "/D/梦幻服务端/客户端/Script/小九UI/战备系统.lua", "/D/梦幻服务端/客户端/Script/功能界面/时辰.lua", "/D/梦幻服务端/客户端/Script/小九UI/交易.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗召唤栏.lua", "/D/梦幻服务端/客户端/Script/资源类/动画类.lua", "/D/梦幻服务端/客户端/Script/资源类/专用动画类2.lua", "/D/梦幻服务端/客户端/Script/聊天框系统/聊天框丰富文本.lua", "/D/梦幻服务端/客户端/Script/更新类/唱戏交票界面.lua", "/D/梦幻服务端/客户端/Script/初系统/标题.lua", "/D/梦幻服务端/客户端/Script/系统类/人物框.lua", "/D/梦幻服务端/客户端/Script/系统类/自适应.lua", "/D/梦幻服务端/客户端/Script/小九UI/奇经八脉.lua", "/D/梦幻服务端/客户端/Script/更新类/转盘.lua", "/C/Users/<USER>/Documents/WeChat Files/wxid_yy7zc4ux2ghf22/FileStorage/File/2024-10/星盘.lua", "/D/梦幻服务端/客户端/Script/小九UI/成就系统.lua", "/D/梦幻服务端/客户端/Script/功能界面/世界地图分类小地图.lua", "/D/梦幻服务端/客户端/Script/数据中心/场景.lua", "/D/梦幻服务端/客户端/script/系统类/按钮.lua", "/D/梦幻服务端/客户端/script/功能界面/世界地图分类小地图.lua", "/D/梦幻服务端/客户端/script/更新类/转盘.lua", "/D/梦幻服务端/客户端/Script/神器类/合成灵犀玉.lua", "/C/Users/<USER>/Documents/WeChat Files/wxid_yy7zc4ux2ghf22/FileStorage/File/2024-10/天罡星.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa19308.39655/全局函数.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa16724.46328/天罡星.lua", "/D/梦幻服务端/客户端/Script/小九UI/星盘.lua", "/D/梦幻服务端/客户端/Script/小九UI/道具行囊.lua", "/D/梦幻服务端/客户端/Script/全局/玩家.lua", "/D/梦幻服务端/客户端/Script/小九UI/队伍栏.lua", "/D/梦幻服务端/客户端/Script/战斗类/战斗动画类.lua", "/D/梦幻服务端/客户端/Script/小九UI/摊位出售.lua", "/D/梦幻服务端/客户端/Script/小九UI/商城宝宝查看.lua", "/D/梦幻服务端/客户端/Script/小九UI/商城类.lua", "/D/梦幻服务端/客户端/Script/数据中心/技能库.lua", "/D/梦幻服务端/客户端/Script/资源类/动画类_X9.lua", "/D/梦幻服务端/客户端/script/战斗类/战斗类.lua", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa13784.2416/加载类.lua", "/C/Users/<USER>/Desktop/新建文件夹 (2)/0.544/客户端/Script/资源类/地图类.lua", "/C/Users/<USER>/Desktop/新建文件夹 (2)/0.544/客户端/Script/战斗类/战斗类.lua", "/C/Users/<USER>/Desktop/新建文件夹 (2)/0.544/客户端/Script/资源类/加载类.lua", "/D/梦幻服务端/客户端/__GGE", "/D/梦幻服务端/客户端/Script/剑会/剑会天下.lua", "/D/梦幻服务端/客户端/Script/剑会/剑会排行榜.lua", "/D/梦幻服务端/客户端/Script/功能界面/聊天框.lua", "/D/梦幻服务端/客户端/Script/数据中心/经脉库.lua", "/D/梦幻服务端/客户端/Script/数据中心/普通模型库.lua", "/D/梦幻服务端/客户端/Script/小九UI/成长礼包.lua", "/D/梦幻服务端/客户端/Script/小九UI/符石镶嵌.lua", "/D/梦幻服务端/客户端/Script/剑会/剑会匹配.lua", "/D/梦幻服务端/客户端/Script/神器类/修复神器.lua", "/D/梦幻服务端/客户端/Script/神器类/神器更换五行.lua", "/D/梦幻服务端/客户端/Script/小九UI/法宝.lua", "/D/梦幻服务端/客户端/Script/显示类/技能_格子.lua", "/D/梦幻服务端/客户端/Script/全局/自己_专用.LUA", "/D/梦幻服务端/客户端/Script/显示类/道具详情.lua", "/D/梦幻服务端/客户端/Script/小九UI/召唤兽资质栏.lua", "/D/梦幻服务端/客户端/Script/显示类/技能.lua", "/D/梦幻服务端/客户端/Script/小九UI/祥瑞界面.lua", "/D/梦幻服务端/客户端/Script/更新类/神秘宝箱.lua", "/D/梦幻服务端/客户端/Script/网络/hp.lua", "/D/梦幻服务端/客户端/Script/初系统/登陆.lua", "/D/梦幻服务端/客户端/Script/初系统/分区.lua", "/D/梦幻服务端/GGE/Core/Game/gge包围盒.lua", "/D/梦幻服务端/客户端/script/显示类/游戏公告类.lua", "/D/梦幻服务端/客户端/Script/神器类/神器查看.lua", "/D/梦幻服务端/GGE/Core/Game/gge图像类1.lua", "/D/梦幻服务端/源码备份/0.49/客户端/<PERSON>rip<PERSON>/小九UI/奇经八脉.lua", "/D/梦幻服务端/客户端/Script/显示类/物品.lua", "/D/梦幻服务端/客户端/Script/小九UI/属性切换.lua", "/D/梦幻服务端/客户端/script/小九UI/成就系统.lua", "/D/梦幻服务端/客户端/Script/属性控制/队伍.lua", "/D/梦幻服务端/客户端/Script/小九UI/好友列表.lua", "/D/梦幻服务端/客户端/Script/小九UI/好友查找.lua", "/D/梦幻服务端/客户端/Script/资源类/路径类.lua"], "find": {"height": 32.0}, "find_in_files": {"height": 116.0, "where_history": ["D:\\梦幻服务端\\客户端\\Script", "D:\\梦幻服务端\\服务端\\Script,D:\\梦幻服务端\\客户端", "D:\\梦幻服务端\\服务端\\Script", "D:\\梦幻服务端", "D:\\梦幻服务端\\客户端\\Script", "D:\\梦幻服务端\\GGE", "D:\\梦幻服务端\\客户端\\Script", "全局界面风格", "D:\\梦幻服务端\\客户端\\Script", "D:\\梦幻服务端", "D:\\梦幻服务端\\客户端\\Script", "D:\\梦幻服务端\\服务端\\Script", ""]}, "find_state": {"case_sensitive": false, "find_history": ["品质", "灵气", "品质", "九眼天珠", "老胡", "天眼", "渐变", "黑屏渐变", "渐变", "翻页", "6557", "鼠标按下", "点击", "第二场景", "6557", "剧情动画", "[23]", "推荐", "人物状态", "tp.tp.", "游戏音效", "游戏音效>0", "游戏音效", "音效", "游戏音乐", "声音设置", "进程", "战斗音效", "战斗", "进程", "音乐", "self.音乐", "进程", "进程=1", "进程==1", "系统设置", "战斗音效类", "无叠加", "击飞", "self:音效类", "无叠加", "self:音效类", "tp:音效类", "self:音效类", "音效开启", "游戏音效", "全局资源地址", "资源缓存", "全局资源缓存", "全局资源地址", "全局资源缓存", "资源缓存", "__fsyz", "全局dt", "随机序列", "config.ini", "音乐开启", "[11]", "游戏音乐", "tp", "音量", "设置", "发送数据(7)", "7", "1501", "助战", "主站", "引擎创建完成", "全局时辰微秒", "重建tp", "垂直同步", "按钮音效", "ALT+A", "清理所有音效", "音效", "场景", "self.游戏音效", "播放音效类", "yxs", "游戏音效", "按钮", "\"升级\"", "self.特效缓存 = {}", "fc:置根(self)", "音效类", "音效开启", "tp", "钓鱼", "舞天姬", "[16]", "[15]", "推荐加点", "巫蛮儿", "四方向", "击退", "同步气血", "四方向", "ZhandouModel[\"羽灵神_弓弩\"]", "ZhandouModel", "弓弩", "雷神", "角色信息", "self.单位类型 == \"角色\"", "self.武器子类 == 14", "14", "武器子类==", "武器子类== 14", "移动事件", "击退", "14", "八方向", "八方想", "14", "逍遥生", "超级泡泡", "玄彩娥", "击退处理", "超级泡泡", "大力", "剑侠客", "内存", "显示(dt,x,y)", "场景音乐类", "战斗音效序列", "战斗单位", "升级", "音效", "消音"], "highlight": false, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["tp.游戏音效>0", "游戏音效类", "tp:战斗音效类", "tp:音效类", "星盘", "字体", "btcsmszt", "终结帧", "攻击2", "wzife.wd1", "magic.wd1", "wzife.wd1", "Resource.ft", "wzife.wd1", "Resource.ft", "wzife.wd1", "tp.提示:经脉技能", "tp.提示:经脉技能(x, y, self.资源组[11][n].技能)", "elseif", "", "\\n#S", "\"\\n#S穴位", "\\n#S穴位", "\\n#S", "\"\\n#S", "\"\\n", "\"#S\\n", "", "：", "", " ", "", "穴位：【", "\"\\n#S", "", "\"\\n#S", "self.技能.新介绍", "self.新介绍", "经脉", "", "#S", "根", "攻击2", "暴击", "self.战斗单位[ljcs][self.战斗流程[1]", "self.战斗单位[ljcs]", "self.x + 13", "self.x + 10", "jiu资源", "self:音效类", "if 游戏音效>0 and 临时音乐~=nil then\n      self:音效类(临时音乐.文件,临时音乐.资源,'1')\n    end", "self:音效类", "self:音效类(临时音乐.文件,临时音乐.资源,'1')", "self:音效类", "self:音效类(临时音乐[动作],临时音乐.资源,动作)", "self:音效类", "self:音效类(临时音乐[动作],临时音乐.资源,动作)", "self.战斗单位[ljcs][self.战斗流程[1].攻击方]:音效类(临时音乐.文件,临时音乐.资源,名称)", "tp:游戏音效类1", "yxs", "self:游戏音效类", "donghua", "ziyuan", "界面风格", "输入背景", "", "全局界面风格", "", "uigy.wdf", "", "偏移值", "41", "22", "18", "40", "97", "50", "12", "67", "60", "table.remove(self.战斗流程[1].挨打方, n)", "自动数据", "桀骜自恃", "超级三昧真火", "桀骜自恃", "超级三昧真火", "self.战斗单位[ljcs][self.战斗流程[1].挨打方[1]", "self.战斗单位[ljcs][self.战斗流程[1].攻击方]", "self.战斗单位[ljcs]", "},", "", "49.235.140.123"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "main.lua", "selected": true, "semi_transient": false, "settings": {"buffer_size": 4856, "regions": {}, "selection": [[1802, 1802]], "settings": {"BracketHighlighterBusy": false, "bh_regions": ["bh_curly", "bh_curly_center", "bh_curly_open", "bh_curly_close", "bh_curly_content", "bh_c_define", "bh_c_define_center", "bh_c_define_open", "bh_c_define_close", "bh_c_define_content", "bh_round", "bh_round_center", "bh_round_open", "bh_round_close", "bh_round_content", "bh_default", "bh_default_center", "bh_default_open", "bh_default_close", "bh_default_content", "bh_double_quote", "bh_double_quote_center", "bh_double_quote_open", "bh_double_quote_close", "bh_double_quote_content", "bh_single_quote", "bh_single_quote_center", "bh_single_quote_open", "bh_single_quote_close", "bh_single_quote_content", "bh_angle", "bh_angle_center", "bh_angle_open", "bh_angle_close", "bh_angle_content", "bh_square", "bh_square_center", "bh_square_open", "bh_square_close", "bh_square_content", "bh_unmatched", "bh_unmatched_center", "bh_unmatched_open", "bh_unmatched_close", "bh_unmatched_content", "bh_tag", "bh_tag_center", "bh_tag_open", "bh_tag_close", "bh_tag_content", "bh_regex", "bh_regex_center", "bh_regex_open", "bh_regex_close", "bh_regex_content"], "bracket_highlighter.busy": false, "bracket_highlighter.clone": -1, "bracket_highlighter.clone_locations": {"close": {}, "icon": {}, "open": {}, "unmatched": {}}, "bracket_highlighter.clone_regions": ["bh_angle", "bh_angle_center", "bh_angle_open", "bh_angle_close", "bh_angle_content", "bh_curly", "bh_curly_center", "bh_curly_open", "bh_curly_close", "bh_curly_content", "bh_tag", "bh_tag_center", "bh_tag_open", "bh_tag_close", "bh_tag_content", "bh_double_quote", "bh_double_quote_center", "bh_double_quote_open", "bh_double_quote_close", "bh_double_quote_content", "bh_single_quote", "bh_single_quote_center", "bh_single_quote_open", "bh_single_quote_close", "bh_single_quote_content", "bh_unmatched", "bh_unmatched_center", "bh_unmatched_open", "bh_unmatched_close", "bh_unmatched_content", "bh_c_define", "bh_c_define_center", "bh_c_define_open", "bh_c_define_close", "bh_c_define_content", "bh_default", "bh_default_center", "bh_default_open", "bh_default_close", "bh_default_content", "bh_round", "bh_round_center", "bh_round_open", "bh_round_close", "bh_round_content", "bh_square", "bh_square_center", "bh_square_open", "bh_square_close", "bh_square_content", "bh_regex", "bh_regex_center", "bh_regex_open", "bh_regex_close", "bh_regex_content"], "bracket_highlighter.locations": {"close": {}, "icon": {}, "open": {}, "unmatched": {}}, "bracket_highlighter.regions": ["bh_default", "bh_default_center", "bh_default_open", "bh_default_close", "bh_default_content", "bh_round", "bh_round_center", "bh_round_open", "bh_round_close", "bh_round_content", "bh_curly", "bh_curly_center", "bh_curly_open", "bh_curly_close", "bh_curly_content", "bh_single_quote", "bh_single_quote_center", "bh_single_quote_open", "bh_single_quote_close", "bh_single_quote_content", "bh_regex", "bh_regex_center", "bh_regex_open", "bh_regex_close", "bh_regex_content", "bh_tag", "bh_tag_center", "bh_tag_open", "bh_tag_close", "bh_tag_content", "bh_double_quote", "bh_double_quote_center", "bh_double_quote_open", "bh_double_quote_close", "bh_double_quote_content", "bh_unmatched", "bh_unmatched_center", "bh_unmatched_open", "bh_unmatched_close", "bh_unmatched_content", "bh_square", "bh_square_center", "bh_square_open", "bh_square_close", "bh_square_content", "bh_c_define", "bh_c_define_center", "bh_c_define_open", "bh_c_define_close", "bh_c_define_content", "bh_angle", "bh_angle_center", "bh_angle_open", "bh_angle_close", "bh_angle_content"], "c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 232, 6, 17, 15, 50, 44, 10, 177, 25, 113, 1, 133, 113, 2, 82, 113, 3, 46], "color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme", "function_name_status_row": 89, "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 32.0}, "input": {"height": 45.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.exec": {"height": 273.0}, "output.find_results": {"height": 0.0}, "output.mdpopups": {"height": 0.0}, "output.unsaved_changes": {"height": 381.0}, "pinned_build_system": "Packages/Lua/ggegame.sublime-build", "position": "0,0,1,-1,-1,-1,-1,966,424,15,1790,8514bb22fc34f34285266bfa43046084", "project": "开发客户端.sublime-project", "replace": {"height": 60.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["", "客户端\\Script\\初系统\\创建.lua"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [["", "D:\\梦幻服务端\\客户端\\开发客户端.sublime-project"]], "width": 380.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 178.0, "status_bar_visible": true, "template_settings": {}, "window_id": 136, "workspace_name": "/D/梦幻服务端/客户端/开发客户端.sublime-workspace"}, {"auto_complete": {"selected_items": [["s", "self"], ["t", "table"], ["els", "elseif"], ["lo", "loadstring"], ["小", "小精灵"], ["进阶", "进阶二郎神"], ["三", "三昧真火"], ["飞", "飞砂走石"], ["os", "os.exit\t([code])"], ["PR", "print"], ["美美", "美美二版\t(对话处理类.lua)"], ["精魄灵石", "精魄灵石·速"], ["钟灵石", "钟灵石（未鉴定）\t(道具处理类.lua)"], ["精魄", "精魄灵石（未鉴定）\t(道具处理类.lua)"], ["三人比武", "三人比武大会"], ["特殊", "特殊魔兽要诀一本\t(任务处理类.lua)"], ["VIP5", "VIP5状态\t(管理工具类.lua)"], ["一寸阳光三", "一寸阳光三寸暖附"], ["超级神狗", "超级神狗_物理\t(main.lua)"], ["取随机", "取随机书铁"], ["高级", "高级召唤兽内丹"], ["elseif", "elseif"], ["添加称谓", "添加称谓时间\t(任务处理类.lua)"], ["for", "for\tfor i=1,10"], ["召唤兽加锁", "召唤兽加锁\t(召唤兽处理类.lua)"], ["帮战", "帮战奖励领取"], ["tr", "true"], ["梦云幻", "梦云幻甲限制"], ["sto", "sort"], ["rt", "return\treturn 返回"], ["ret", "return\treturn 返回"], ["else", "elseif"], ["超级金", "超级金柳露"], ["黑山", "黑山BOSS"], ["弱点", "弱点击破\t(易语言dll.lua)"], ["无名", "无名秘籍技能\t(易语言dll.lua)"], ["fun", "function\tfunction"], ["D", "<PERSON><PERSON>"], ["el", "elseif"], ["超级灵", "超级灵鹿_物理\t(main.lua)"], ["超级青", "超级青鸾_物理\t(main.lua)"], ["高级法术", "高级法术暴击"], ["loc", "local"], ["esle", "elseif"], ["re", "return\treturn 返回"], ["经脉", "经脉判断"], ["测试", "测试时间"]]}, "buffers": [{"file": "Script/商会处理类/商会处理类.lua", "settings": {"buffer_size": 10254, "encoding": "UTF-8", "line_ending": "Windows"}}, {"file": "Script/任务处理类/任务处理类.lua", "settings": {"buffer_size": 549058, "encoding": "UTF-8", "line_ending": "Windows"}}], "build_system": "Packages/Lua/ggeserver.sublime-build", "build_system_choices": [[[["Packages/Lua/Lua.sublime-build", ""], ["Packages/Lua/ggegame.sublime-build", ""], ["Packages/Lua/ggegame.sublime-build", "Run"], ["Packages/Lua/ggegame.sublime-build", "RunInCommand"], ["Packages/Lua/ggegame.sublime-build", "SetGGE"], ["Packages/Lua/ggegame.sublime-build", "Stop"], ["Packages/Lua/ggegame.sublime-build", "AboutGGE"], ["Packages/Lua/ggeobj.sublime-build", ""], ["Packages/Lua/ggeobj.sublime-build", "Run"], ["Packages/Lua/ggeobj.sublime-build", "RunInCommand"], ["Packages/Lua/ggeobj.sublime-build", "SetGGE"], ["Packages/Lua/ggeobj.sublime-build", "Stop"], ["Packages/Lua/ggeobj.sublime-build", "AboutGGE"], ["Packages/Lua/ggeserver.sublime-build", ""], ["Packages/Lua/ggeserver.sublime-build", "Run"], ["Packages/Lua/ggeserver.sublime-build", "RunInCommand"], ["Packages/Lua/ggeserver.sublime-build", "SetGGE"], ["Packages/Lua/ggeserver.sublime-build", "Stop"], ["Packages/Lua/ggeserver.sublime-build", "AboutGGE"]], ["Packages/Lua/ggeserver.sublime-build", ""]], [[["Packages/Lua/ggegame.sublime-build", "Run"], ["Packages/Lua/ggeobj.sublime-build", "Run"], ["Packages/Lua/ggeserver.sublime-build", "Run"]], ["Packages/Lua/ggeserver.sublime-build", "Run"]], [[["Packages/Lua/ggegame.sublime-build", "RunInCommand"], ["Packages/Lua/ggeobj.sublime-build", "RunInCommand"], ["Packages/Lua/ggeserver.sublime-build", "RunInCommand"]], ["Packages/Lua/ggeserver.sublime-build", "RunInCommand"]], [[["Packages/Lua/ggegame.sublime-build", "Stop"], ["Packages/Lua/ggeobj.sublime-build", "Stop"], ["Packages/Lua/ggeserver.sublime-build", "Stop"]], ["Packages/Lua/ggeserver.sublime-build", "Stop"]]], "build_varint": "", "command_palette": {"height": 392.0, "last_filter": "", "selected_items": [], "width": 464.0}, "console": {"height": 0.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/D/08正版钓鱼/NewServer/Script", "/D/08正版钓鱼/NewServer/Script/任务处理类"], "file_history": ["/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/main.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/tysj/Bot/dd.txt", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/tysj/Bot/BBTable.txt", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/BotManager.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/ItemTableBiz.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/BBTableBiz.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/ChatCtrl.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/ChatDb.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/BoothBotBiz.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/BoothCtrl.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/json.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/tysj/Bot/ttt.txt", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/BotDb.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/BotCtrl.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/TeamBotBiz.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/tysj/找朋友数据.txt", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/任务处理类/任务处理类.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/tysj/Bot/ItemTable.txt", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/BoothDb.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/Bot/BotBiz.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/tysj/Bot/ChatBot.txt", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/tysj/Bot/BoothBot.txt", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/地图处理类/地图处理类.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/商店处理类/摆摊处理类.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/配置文件.ini", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/main", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/综合处理类/VIP处理类.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/对话处理/对话处理类.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/data/9309032/道具.txt", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/data/9309032/账号.txt", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/data/9309032/角色.txt", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/data/9309032/召唤兽.txt", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/商店处理类/商店处理类.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/对话处理/对话处理类111 - 副本.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/玩家角色类/道具处理类.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/玩家角色类/装备处理类.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/游戏活动类/游戏活动类.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/地图处理类/地图数据类.lua", "/E/BaiduNetdiskDownload/08正版钓鱼/NewServer/Script/战斗处理类/战斗准备类.lua", "/D/完梦西游文件大全/服务端源码/script/任务处理类/任务处理类", "/D/完梦西游文件大全/服务端源码/Script/网络处理类/网络处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/系统处理类/易语言dll.lua", "/D/完梦西游文件大全/服务端源码/Script/系统处理类/管理工具类.lua", "/D/完梦西游文件大全/服务端源码/script/玩家角色类/道具处理类", "/D/完梦西游文件大全/服务端源码/script/综合处理类/vip处理类", "/D/完梦西游文件大全/服务端源码/script/游戏活动类/游戏活动类", "/D/完梦西游文件大全/服务端源码/Script/玩家角色类/装备处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/系统处理类/物品库.lua", "/D/完梦西游文件大全/服务端源码/main.lua", "/D/完梦西游文件大全/服务端源码/Script/战斗处理类/战斗处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/战斗处理类/战斗准备类.lua", "/D/完梦西游文件大全/服务端源码/Script/任务处理类/任务处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/玩家角色类/角色处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/对话处理/对话处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/地图处理类/地图数据类.lua", "/D/完梦西游文件大全/服务端源码/Script/综合处理类/称号系统处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/综合处理类/钓鱼处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/地图处理类/地图处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/玩家角色类/道具处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/商店处理类/商店处理类.lua", "/D/完梦西游文件大全/服务端源码/main", "/D/完梦西游文件大全/完梦客户端/Script/全_主控.lua", "/D/完梦西游文件大全/完梦客户端/Script/base_聊天窗口.lua", "/D/完梦西游文件大全/完梦客户端/Script/登录系统.lua", "/D/完梦西游文件大全/服务端源码/script/玩家角色类/装备处理类", "/D/完梦西游文件大全/服务端源码/Script/对话处理/对话处理类111 - 副本.lua", "/D/完梦西游文件大全/服务端源码/Script/商店处理类/摆摊处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/Bot/BotManager.lua", "/D/完梦西游文件大全/服务端源码/script/对话处理/对话处理类", "/D/完梦西游文件大全/服务端源码/script/地图处理类/地图数据类", "/D/完梦西游文件大全/服务端源码/Script/Bot/ChatDb.lua", "/D/完梦西游文件大全/服务端源码/Script/Bot/BotCtrl.lua", "/D/完梦西游文件大全/服务端源码/Script/Bot/BotDb.lua", "/D/完梦西游文件大全/服务端源码/Script/Bot/BoothCtrl.lua", "/D/完梦西游文件大全/服务端源码/Script/Bot/BoothDb.lua", "/D/完梦西游文件大全/服务端源码/Script/Bot/BotBiz.lua", "/D/完梦西游文件大全/服务端源码/Script/Bot/BBTableBiz.lua", "/D/完梦西游文件大全/服务端源码/Script/Bot/BoothBotBiz.lua", "/D/完梦西游文件大全/服务端源码/Script/Bot/ChatCtrl.lua", "/D/完梦西游文件大全/服务端源码/Script/Bot/ItemTableBiz.lua", "/D/完梦西游文件大全/服务端源码/Script/Bot/TeamBotBiz.lua", "/D/完梦西游文件大全/服务端源码/Script/综合处理类/锦衣处理类.lua", "/D/完梦西游文件大全/服务端源码/Script/地图处理类/地图坐标类.lua", "/D/NewServer/Script/任务处理类/娱乐类.lua", "/D/NewServer/Script/综合处理类/VIP处理类.lua", "/D/服务端备份/NewServer/Script/网络处理类/网络处理类.lua", "/D/NewServer/Script/网络处理类/网络处理类.lua", "/D/NewServer/Script/系统处理类/物品库.lua", "/D/NewServer/Script/系统处理类/易语言dll.lua", "/D/NewServer/Script/玩家角色类/装备处理类.lua", "/D/NewServer/Script/玩家角色类/角色处理类.lua", "/D/NewServer/Script/战斗处理类/战斗处理类.lua", "/D/NewServer/Script/系统处理类/管理工具类.lua", "/D/NewServer/tysj/Bot/ttt.txt", "/D/NewServer/Script/商店处理类/商店处理类.lua", "/D/NewServer/Script/玩家角色类/道具处理类.lua", "/D/NewServer/tysj/Bot/ChatBot.txt", "/D/NewServer/Script/对话处理/对话处理类.lua", "/D/NewServer/data/111111/账号.txt", "/D/NewServer/data/111111/角色.txt", "/D/NewServer/data/111111/道具.txt", "/D/NewServer/Script/json.lua", "/D/NewServer/Script/Bot/ChatDb.lua", "/D/NewServer/tysj/Bot/FightBot.txt", "/D/NewServer/Script/Bot/BotDb.lua", "/D/NewServer/Script/Bot/BotCtrl.lua", "/D/NewServer/Script/Bot/BotBiz.lua", "/D/NewServer/Script/Bot/ChatCtrl.lua", "/D/NewServer/data/1111111/角色.txt", "/D/NewServer/tysj/Bot/ItemTable.txt", "/D/NewServer/tysj/Bot/BBTable.txt", "/D/NewServer/tysj/Bot/dd.txt", "/D/NewServer/Script/Bot/BotManager.lua", "/D/NewServer/Script/Bot/BoothDb.lua", "/D/NewServer/Script/Bot/BoothCtrl.lua", "/D/NewServer/Script/Bot/BoothBotBiz.lua", "/D/NewServer/Script/Bot/BBTableBiz.lua", "/D/NewServer/Script/Bot/ItemTableBiz.lua", "/D/NewServer/Script/Bot/TeamBotBiz.lua", "/D/NewServer/tysj/Bot/BoothBot.txt", "/D/NewServer/data/1111111/道具.txt", "/D/NewServer/main.lua", "/D/QQ/573577020/FileRecv/main.lua", "/D/NewServer/data/1111111/账号.txt", "/D/QQ/573577020/FileRecv/商店处理类(1).lua", "/D/QQ/573577020/FileRecv/商店处理类.lua", "/D/NewServer/Script/任务处理类/任务处理类.lua", "/D/NewServer/data/123123111/道具.txt"], "find": {"height": 32.0}, "find_in_files": {"height": 90.0, "where_history": ["E:\\BaiduNetdiskDownload\\08正版钓鱼\\NewServer\\Script", "E:\\BaiduNetdiskDownload\\08正版钓鱼\\NewServer", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码\\Script\\任务处理类", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码\\Script\\任务处理类\\任务处理类.lua", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码\\Script\\对话处理", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码\\Script\\网络处理类\\网络处理类.lua", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码\\Script\\玩家角色类\\装备处理类.lua", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码\\Script\\战斗处理类\\战斗准备类.lua", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码\\Script\\任务处理类\\任务处理类.lua", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码\\Script\\任务处理类\\任务处理类.lua", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码\\Script\\对话处理\\对话处理类.lua", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码\\NewServer", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\完梦西游文件大全\\服务端源码", "D:\\完梦西游文件大全\\服务端源码\\Script", "D:\\被卡NewServer\\Script", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer\\Script\\任务处理类", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer\\tysj\\Bot\\FightBot.txt", "D:\\NewServer\\tysj\\Bot\\BoothBot.txt", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer\\Script\\战斗处理类", "D:\\NewServer\\Script", "D:\\NewServer\\Script\\任务处理类", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer\\", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer\\Script\\战斗处理类\\战斗处理类.lua", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script\\任务处理类", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\map", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer\\Script\\任务处理类\\任务处理类.lua", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script", "D:\\NewServer", "D:\\NewServer\\Script"]}, "find_state": {"case_sensitive": true, "find_history": ["ttt.txt", "dd.txt", "srcArr", "Bot", "BoT", "<PERSON>", "srcArr", "addBoothBot", "假人系统", "BotManager", "整秒处理", "bot", "************", "103.", "程序目录=[[D:\\NewServer\\]]", "************", "**************", "**************", "127.0.0.1", "E:\\BaiduNetdiskDownload\\08正版钓鱼", "127.0.0.1", "D:\\NewServer", "=61", "=75", "=50", "D:\\NewServer", "积分商场", "副本积分", "副本官员", "该积分达到", "邪灵", "书铁", "邪灵书铁", "邪灵", "书铁", "邪灵", "书铁", "洗练", "出售道具", "出售商城", "=10", "特效范围", "观照万象", "关照万象", "水果积分", "抽奖使者", "宝象积分", "宝象积分抽奖使者", "宝象积分", "积分", "妖魔积分", "普通", "妖魔积分", "水果精灵", "妖魔抽奖", "积分抽奖", "积分", "积分抽奖", "妖魔积分", "ip", "8083", "武器", "衣服", "临时数据[5]", "请检查", "请检查专用", "请检查", "双加", "取120级装备礼包", "VIP5", "刷出十殿阎罗", "取随机书铁", "邪灵", "刷出十殿阎罗", "刷出地", "装备", "单加属性[1]", "劳动节", "发送数据", "合成", "宝石", "合成", "镶嵌", "宝石", "取随机书铁", "352-2", "镶嵌", "宝石", "谛听", "self.发送序号 = 121", "邪灵", "符石", "符石点化", "符石组合", "点化星位", "星位点化", "邪灵", "取随机书铁()", "随机防具组", "取随机书铁()", "随机书铁", "取随机书铁", "任务链", "任务链等级", "取完成地妖星", "取完成地妖星100", "取地妖星100", "世界boos", "世界boss", "帮派竞赛报名", "帮派报名", "首席", "帮派竞赛", "兰虎", "打造", "--", "修改", "这里", "强化", "装备", "打造技巧", "五行", "VIP金", "vip金", "五行金", "vip", "VIP", "界面数据45"], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["127.0.0.1", "程序目录=lfs.currentdir()..[[\\]]", "************", "127.0.0.1", "D:", "E:\\BaiduNetdiskDownload\\08正版钓鱼\\NewServer", "=100000000", "=175", "E:\\BaiduNetdiskDownload\\08正版钓鱼\\NewServer", "1501", "发送数据1", "连接did", "碎星诀", "碎星决", "印度神油", "上古凶兽", "超级腾蛇", "进阶腾蛇", "善恶有报", "or 一寸阳光三寸暖 == true or 一寸阳光三寸暖附 == true", "奖励目标", "一寸阳光三寸暖", "超级锦毛鼠"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "Script/商会处理类/商会处理类.lua", "semi_transient": false, "settings": {"buffer_size": 10254, "regions": {}, "selection": [[608, 608]], "settings": {"BracketHighlighterBusy": false, "bh_regions": ["bh_c_define", "bh_c_define_center", "bh_c_define_open", "bh_c_define_close", "bh_c_define_content", "bh_round", "bh_round_center", "bh_round_open", "bh_round_close", "bh_round_content", "bh_regex", "bh_regex_center", "bh_regex_open", "bh_regex_close", "bh_regex_content", "bh_curly", "bh_curly_center", "bh_curly_open", "bh_curly_close", "bh_curly_content", "bh_double_quote", "bh_double_quote_center", "bh_double_quote_open", "bh_double_quote_close", "bh_double_quote_content", "bh_tag", "bh_tag_center", "bh_tag_open", "bh_tag_close", "bh_tag_content", "bh_unmatched", "bh_unmatched_center", "bh_unmatched_open", "bh_unmatched_close", "bh_unmatched_content", "bh_square", "bh_square_center", "bh_square_open", "bh_square_close", "bh_square_content", "bh_angle", "bh_angle_center", "bh_angle_open", "bh_angle_close", "bh_angle_content", "bh_single_quote", "bh_single_quote_center", "bh_single_quote_open", "bh_single_quote_close", "bh_single_quote_content", "bh_default", "bh_default_center", "bh_default_open", "bh_default_close", "bh_default_content"], "c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 228, 8, 30, 16, 35, 22, 0, 0, 0, 113, 1, 133, 113, 2, 82, 113, 3, 46], "color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme", "function_name_status_row": 31, "is_init_dirty_state": false, "origin_encoding": "UTF-8", "syntax": "Packages/Lua/Lua.sublime-syntax", "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 360.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "file": "Script/任务处理类/任务处理类.lua", "selected": true, "semi_transient": true, "settings": {"buffer_size": 549058, "regions": {}, "selection": [[0, 0]], "settings": {"c_time": [128, 3, 99, 100, 97, 116, 101, 116, 105, 109, 101, 10, 100, 97, 116, 101, 116, 105, 109, 101, 10, 113, 0, 67, 10, 7, 232, 12, 2, 11, 27, 26, 0, 17, 224, 113, 1, 133, 113, 2, 82, 113, 3, 46], "syntax": "Packages/Lua/Lua.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 19694.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 32.0}, "input": {"height": 37.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.exec": {"height": 161.0}, "output.find_results": {"height": 0.0}, "pinned_build_system": "", "position": "0,0,1,-1,-1,-1,-1,1013,62,62,1428,8514bb22fc34f34285266bfa43046084", "project": "服务端模板.sublime-project", "replace": {"height": 60.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_project": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 212.0, "status_bar_visible": true, "template_settings": {}, "window_id": 157, "workspace_name": "/D/08正版钓鱼/NewServer/服务端模板.sublime-workspace"}], "workspaces": {"recent_workspaces": ["/D/08正版钓鱼/NewServer/服务端模板.sublime-workspace", "/D/Server/服务端.sublime-workspace", "/C/Users/<USER>/AppData/Local/Temp/Rar$DIa20048.31156/服务端模板.sublime-workspace", "/D/梦幻服务端/服务端/开发服务端.sublime-workspace", "/D/2024.07.04团/服务端/开发服务端.sublime-workspace", "/D/梦幻服务端/客户端/开发客户端.sublime-workspace", "/G/三花修复版-低调使用/服务端/开发服务端.sublime-workspace", "/D/2024.07.04团/PC客户端/开发梦幻西游.sublime-workspace", "/D/BaiduNetdiskDownload/2008/客户端/开发服务端.sublime-workspace", "/D/BaiduNetdiskDownload/2008/GGE/开发服务端.sublime-workspace", "/D/BaiduNetdiskDownload/2008/服务端/开发服务端.sublime-workspace"]}}