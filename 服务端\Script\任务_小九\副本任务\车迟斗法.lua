-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2025-02-12 19:50:46
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-16 18:17:21
local 副本_车迟斗法 = class()
function 副本_车迟斗法:初始化() end
function 设置任务610(id)
	if 玩家数据[id].队伍==0 or 玩家数据[id].队长==false  then
		常规提示(id,"#Y/该任务必须组队完成且由队长领取")
		return
	elseif 取队伍人数(id)<5 and 调试模式==false then
		常规提示(id,"#Y此副本要求队伍人数不低于5人")
		return
	elseif not 调试模式 and  取等级要求(id,70)==false then
		常规提示(id,"#Y此副本要求角色等级不能低于70级")
		return
	end
	local 队伍id=玩家数据[id].队伍
	for n=1,#队伍数据[队伍id].成员数据 do
		local 临时id=队伍数据[队伍id].成员数据[n]
		if 副本数据.车迟斗法.完成[临时id]~=nil then
			常规提示(id,"#Y"..玩家数据[临时id].角色.名称.."本日已经完成过此副本了")
			return
		elseif 玩家数据[临时id].角色:取任务(610)~=0 then
			常规提示(id,"#Y"..玩家数据[临时id].角色.名称.."正在进行副本任务，无法领取新的副本")
			return
		end
	end
	副本数据.车迟斗法.进行[id]={进程=1,数量=0,进度=0}
	GetUpMOB610(id)
	local 任务id=id.."_610_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999)
	任务数据[任务id]={
	id=任务id,
	起始=os.time(),
	结束=7200,
	玩家id=0,
	队伍组=table.loadstring(table.tostring(队伍数据[玩家数据[id].队伍].成员数据)),
	副本id=id,
	类型=610
	}
	任务处理类:添加队伍任务(id,任务id,"#Y你开启了车迟斗法副本")
end
function GetUpMOB610(id)
	if 副本数据.车迟斗法.进行[id]==nil then
		return
	end
	if 副本数据.车迟斗法.进行[id].进程==1 then
		local 任务id=id.."_611_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=6005
		local xy=地图处理类.地图坐标[地图]:取随机点()
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="有个和尚",
		模型="和尚3",
		x=95,
		y=140,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=611
		}
		地图处理类:添加单位(任务id)
	elseif 副本数据.车迟斗法.进行[id].进程==2 then
		local 地图=6005
		local x待发送数据 = {[地图]={}}
		for n=1,20 do
			local 任务id=id.."_612_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"..n
			local xy=地图处理类.地图坐标[地图]:取随机点()
			任务数据[任务id]={
			id=任务id,
			起始=os.time(),
			结束=3600,
			玩家id=0,
			队伍组={},
			名称="有个道士",
			模型="道士",
			x=xy.x,
			y=xy.y,
			副本id=id,
			地图编号=地图,
			地图名称=取地图名称(地图),
			小地图名称颜色=3,
			类型=612
			}
			table.insert(x待发送数据[地图], 地图处理类:批量添加单位(任务id))
		end
		local 任务id=id.."_613_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="道观",
		模型="道观",
		x=84,
		y=27,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		事件="明雷活动",
		类型=613
		}
		table.insert(x待发送数据[地图], 地图处理类:批量添加单位(任务id))
		for n, v in pairs(地图处理类.地图玩家[地图]) do
			if 地图处理类:取同一地图(地图,id,n,1)  then
				发送数据(玩家数据[n].连接id,1021,x待发送数据[地图])
			end
		end
		x待发送数据={}
	elseif 副本数据.车迟斗法.进行[id].进程==3 then
		local 地图=6005
		local x待发送数据 = {[地图]={}}
		local 任务id=id.."_614_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local xy=地图处理类.地图坐标[地图]:取随机点()
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="有个和尚",
		模型="和尚3",
		x=95,
		y=140,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=614
		}
		table.insert(x待发送数据[地图], 地图处理类:批量添加单位(任务id))
		for n=1,20 do
			local 任务id=id.."_615_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"..n
			local xy=地图处理类.地图坐标[地图]:取随机点()
			任务数据[任务id]={
			id=任务id,
			起始=os.time(),
			结束=3600,
			玩家id=0,
			队伍组={},
			名称="供品",
			模型="泡泡",
			x=xy.x,
			y=xy.y,
			副本id=id,
			地图编号=地图,
			地图名称=取地图名称(地图),
			小地图名称颜色=3,
			类型=615
			}
			table.insert(x待发送数据[地图], 地图处理类:批量添加单位(任务id))
		end
		for n, v in pairs(地图处理类.地图玩家[地图]) do
			if 地图处理类:取同一地图(地图,id,n,1)  then
				发送数据(玩家数据[n].连接id,1021,x待发送数据[地图])
			end
		end
		x待发送数据={}
	elseif 副本数据.车迟斗法.进行[id].进程==4 then
		local 地图=6006
		local x待发送数据 = {[地图]={}}
		for n=1,3 do
			local 任务id=id.."_616_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"..n
			local 名称列表 = {"太上老君","元始天尊","灵宝道君",}
			local xy=地图处理类.地图坐标[地图]:取随机点()
			任务数据[任务id]={
			id=任务id,
			起始=os.time(),
			结束=3600,
			玩家id=0,
			队伍组={},
			名称=名称列表[n],
			模型="进阶大力金刚",
			x=xy.x,
			y=xy.y,
			副本id=id,
			地图编号=地图,
			地图名称=取地图名称(地图),
			小地图名称颜色=3,
			类型=616
			}
			table.insert(x待发送数据[地图], 地图处理类:批量添加单位(任务id))
		end
		local 任务id=id.."_617_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local xy=地图处理类.地图坐标[地图]:取随机点()
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
			名称="道童",
			模型="道童",
			x=19,
			y=37,
			副本id=id,
			地图编号=地图,
			地图名称=取地图名称(地图),
			小地图名称颜色=3,
			类型=617
		}
		table.insert(x待发送数据[地图], 地图处理类:批量添加单位(任务id))
		for n, v in pairs(地图处理类.地图玩家[地图]) do
			if 地图处理类:取同一地图(地图,id,n,1)  then
				发送数据(玩家数据[n].连接id,1021,x待发送数据[地图])
			end
		end
		x待发送数据={}
	elseif 副本数据.车迟斗法.进行[id].进程==5 then
		local 地图=6007
		local x待发送数据 = {[地图]={}}
		for i=1,3 do
			local 任务id=id.."_618_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"..i
			local 名称列表 = {"雷公","电母","雨师"}
			local 模型列表 = {"雷鸟人","星灵仙子","雨师"}
			local xy=地图处理类.地图坐标[地图]:取随机点()
			任务数据[任务id]={
			id=任务id,
			起始=os.time(),
			结束=3600,
			玩家id=0,
			队伍组={},
			名称=名称列表[i],
			模型=模型列表[i],
			x=xy.x,
			y=xy.y,
			副本id=id,
			地图编号=地图,
			地图名称=取地图名称(地图),
			小地图名称颜色=3,
			类型=618
			}
			table.insert(x待发送数据[地图], 地图处理类:批量添加单位(任务id))
		end
		for n, v in pairs(地图处理类.地图玩家[地图]) do
			if 地图处理类:取同一地图(地图,id,n,1)  then
				发送数据(玩家数据[n].连接id,1021,x待发送数据[地图])
			end
		end
		x待发送数据={}
	elseif 副本数据.车迟斗法.进行[id].进程==6 then
		local 任务id=id.."_619_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=6007
		local 名称列表 = {"羊力大仙","鹿力大仙","虎力大仙"}
		local 模型列表 = {"羊头怪","吸血鬼","噬天虎"}
		local sjs = 取随机数(1,3)
		local xy=地图处理类.地图坐标[地图]:取随机点()
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称=名称列表[sjs],
		模型=模型列表[sjs],
		x=xy.x,
		y=xy.y,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=619
		}
		地图处理类:添加单位(任务id)
	elseif 副本数据.车迟斗法.进行[id].进程==7 then
		local 地图=6007
		local x待发送数据 = {[地图]={}}
		local 队伍id=玩家数据[id].队伍
		for n=1,#队伍数据[队伍id].成员数据 do
			local 队员id=队伍数据[队伍id].成员数据[n]
			local 任务id=id.."_620_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"..n
			local 地图=6007
			local xy=地图处理类.地图坐标[地图]:取随机点()
			任务数据[任务id]={
			id=任务id,
			起始=os.time(),
			结束=3600,
			玩家id=队员id,
			队伍组={},
			名称= 玩家数据[队员id].角色.名称.."的宝箱",
			模型="宝箱",
			x=xy.x,
			y=xy.y,
			副本id=id,
			地图编号=地图,
			地图名称=取地图名称(地图),
			小地图名称颜色=3,
			事件="明雷活动",
			类型=620
			}
			table.insert(x待发送数据[地图], 地图处理类:批量添加单位(任务id))
		end
		for n=1,10 do
			local 任务id=id.."_621_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"..n
			local 地图=6007
			local xy=地图处理类.地图坐标[地图]:取随机点()
			任务数据[任务id]={
			id=任务id,
			起始=os.time(),
			结束=3600,
			玩家id=0,
			队伍组={},
			名称="小宝箱",
			模型="宝箱",
			x=xy.x,
			y=xy.y,
			副本id=id,
			地图编号=地图,
			地图名称=取地图名称(地图),
			小地图名称颜色=3,
			事件="明雷活动",
			类型=621
			}
			table.insert(x待发送数据[地图], 地图处理类:批量添加单位(任务id))
		end
		for n, v in pairs(地图处理类.地图玩家[地图]) do
			if 地图处理类:取同一地图(地图,id,n,1)  then
				发送数据(玩家数据[n].连接id,1021,x待发送数据[地图])
			end
		end
		x待发送数据={}
	end
end
__GWdh111[611]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	local 副本id = 任务处理类:取副本id(数字id,610)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
	end
	if 副本数据.车迟斗法.进行[数字id].进程==1 then
		对话数据.对话="那车迟国内不知从哪来了三个道士,会抟砂炼汞,打坐存神,点水为油,点石成金。更精通呼风唤雨之术,解决车迟国一地干早多年的麻烦。深得国王和皇后龙信,而国王命我等求雨,我等法カ不济,无法完成,か王则视我等无能。这不如今三道士主张兴盖三清观宇,祈君王万年不老,更将君心惑动。国王直接令我寺中无论老少全体前来兴建三清殿。"
		地图处理类:删除单位(任务数据[标识].地图编号,任务数据[标识].单位编号)
		任务数据[标识]=nil
		副本数据.车迟斗法.进行[数字id]={进程=2,数量=0,进度=0}
		GetUpMOB610(数字id)
		刷新队伍任务追踪(数字id)
	end
	return 对话数据
end
__GWdh111[612]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,610) then
		对话数据.对话="副本任务数据错误"
		return 对话数据
	end
	local 副本id = 任务处理类:取副本id(数字id,610)
	if 副本id == 0 then
		对话数据.对话="副本任务数据错误"
		return 对话数据
	end
	if 副本数据.车迟斗法.进行[副本id].进程==2 then
		local 题目随机 = 取随机数()
		if 题目随机 < 5 then
			对话数据.对话="在“淝水之战”中的大败的君主是"
			对话数据.选项={"苻坚","曹操","苻融","拓跋宏"}
		elseif 题目随机 < 10 then
			对话数据.对话="词语“同气”是指什么关系"
			对话数据.选项={"姐妹","兄弟","夫君","娘子"}
		elseif 题目随机 < 15 then
			对话数据.对话="下列哪种中药对眼睛有好处？"
			对话数据.选项={"决白子","鱼眼","决明子","黄芩"}
		elseif 题目随机 < 20 then
			对话数据.对话="苏州评弹是哪两种表演形式的统称？"
			对话数据.选项={"评话和说词","评话和弹词","评剧和说词","说唱"}
		elseif 题目随机 < 25 then
			对话数据.对话="智者乐水的后一句是"
			对话数据.选项={"仁者见仁","仁者音山","仁者见山","仁者乐山"}
		elseif 题目随机 < 30 then
			对话数据.对话="等级达到多少级可以在长安城的副本官员发起副本？"
			对话数据.选项={"50级","60级","70级","80级"}
		elseif 题目随机 < 35 then
			对话数据.对话="书店和铁匠铺，裁缝店打工赚钱的区别是？"
			对话数据.选项={"铁匠铺不消耗气血","书店不消耗气血","裁缝店不消耗气血","不消耗气血"}
		elseif 题目随机 < 40 then
			对话数据.对话="时事通处可以购买西游时讯，请问这份报纸不包括哪条内容"
			对话数据.选项={"西游简报","游山玩水","玩家守则","明月楼"}
		elseif 题目随机 < 45 then
			对话数据.对话="红娘是哪部作品中的人物？"
			对话数据.选项={"东厢记","西游记","金瓶梅","西厢记"}
		elseif 题目随机 < 50 then
			对话数据.对话="华夏民族在哪里诞生？"
			对话数据.选项={"黄河流域","两河流域","长江流域","中原流域"}
		elseif 题目随机 < 55 then
			对话数据.对话="杜甫诗句故人入我梦，明我长相忆，君今在罗网，何以有羽翼中故人是指"
			对话数据.选项={"王昭君","李白","苏轼","李世民"}
		elseif 题目随机 < 60 then
			对话数据.对话="与木兰辞并称乐府双璧的是？"
			对话数据.选项={"双剑合璧","男女","孔雀东南飞","夫妻"}
		elseif 题目随机 < 65 then
			对话数据.对话="古诗名句忽见陌头杨柳色下句是？"
			对话数据.选项={"悔教夫媚觅封相","悔教夫媚觅封官","悔教夫媚觅封将","悔教夫媚觅封候"}
		elseif 题目随机 < 70 then
			对话数据.对话="最古老的绘画形式是"
			对话数据.选项={"壁画","石画","手画","形体"}
		elseif 题目随机 < 75 then
			对话数据.对话="名不正则言不顺是哪家的思想？"
			对话数据.选项={"墨家","儒家","道家","法家"}
		elseif 题目随机 < 80 then
			对话数据.对话="山西洪桐县的明代监狱，因为谁的故事而著名？"
			对话数据.选项={"苏四","苏二","苏三","苏大强"}
		elseif 题目随机 < 85 then
			对话数据.对话="茅台酒以什么原料制成？"
			对话数据.选项={"玉米","高梁米","糯米","高梁"}
		elseif 题目随机 < 90 then
			对话数据.对话="古人常用松鹤延年图祝寿，鹤是生长在？"
			对话数据.选项={"沼泽","平原","草原","天上"}
		elseif 题目随机 < 95 then
			对话数据.对话="黄庭坚的欲问江南近消息，喜君贻我一枝春中的一枝春指的是"
			对话数据.选项={"兰花","梅花","桂花","桃花"}
		elseif 题目随机 < 100 then
			对话数据.对话="古代诗句中都有折柳二字，这是什么情况下的习俗？"
			对话数据.选项={"相交","相思","送别","送行"}
		end
	end
	return 对话数据
end
__GWdh222[612]=function (连接id,数字id,序号,内容)
	local 副本id = 任务处理类:取副本id(数字id,610)
	if 副本id == 0 then
		return
	end
	local 事件=内容[1]
	local 名称=内容[3]
	local 正确答案 = {
		["苻坚"]=true,
		["兄弟"]=true,
		["决明子"]=true,
		["评话和弹词"]=true,
		["仁者乐山"]=true,
		["书店不消耗气血"]=true,
		["玩家守则"]=true,
		["西厢记"]=true,
		["黄河流域"]=true,
		["李白"]=true,
		["孔雀东南飞"]=true,
		["悔教夫媚觅封候"]=true,
		["壁画"]=true,
		["儒家"]=true,
		["苏三"]=true,
		["高梁"]=true,
		["沼泽"]=true,
		["梅花"]=true,
		["送别"]=true,
		["60级"]=true
	}

	if 正确答案[事件] then
		-- 回答正确，给予队伍成员木材奖励
		local 队伍id = 玩家数据[数字id].队伍
		if 队伍id ~= 0 then
			for n=1,#队伍数据[队伍id].成员数据 do
				local 成员id = 队伍数据[队伍id].成员数据[n]
				local 奖励随机 = 取随机数()
				if 奖励随机 <= 15 then -- 15%概率获得高级木材
					玩家数据[成员id].道具:给予道具(成员id,"高级木材",1)
					--常规提示(成员id,"#Y/回答正确！获得了#G/高级木材#Y/！")
				else -- 85%概率获得普通木材
					玩家数据[成员id].道具:给予道具(成员id,"普通木材",1)
					--常规提示(成员id,"#Y/回答正确！获得了#G/普通木材#Y/！")
				end
			end
		else
			-- 单人情况
			local 奖励随机 = 取随机数()
			if 奖励随机 <= 15 then
				玩家数据[数字id].道具:给予道具(数字id,"高级木材",1)
				常规提示(数字id,"#Y/回答正确！获得了#G/高级木材#Y/！")
			else
				玩家数据[数字id].道具:给予道具(数字id,"普通木材",1)
				常规提示(数字id,"#Y/回答正确！获得了#G/普通木材#Y/！")
			end
		end

		地图处理类:删除单位(任务数据[玩家数据[数字id].地图单位.标识].地图编号,任务数据[玩家数据[数字id].地图单位.标识].单位编号)
		任务数据[玩家数据[数字id].地图单位.标识]=nil
		刷新队伍任务追踪(数字id)
	else
		-- 回答错误，给予提示
		常规提示(数字id,"#R/回答错误！请仔细思考后再试。")
	end
end
__GWdh111[613]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,610) then
		对话数据.对话="副本任务数据错误"
		return 对话数据
	end
	local 副本id = 任务处理类:取副本id(数字id,610)
	if 副本id == 0 then
		对话数据.对话="副本任务数据错误"
		return 对话数据
	end
	if 副本数据.车迟斗法.进行[副本id].进程==2 then
		-- 确保进度字段存在
		if 副本数据.车迟斗法.进行[副本id].进度 == nil then
			副本数据.车迟斗法.进行[副本id].进度 = 0
		end
		local 当前进度 = 副本数据.车迟斗法.进行[副本id].进度
		if 当前进度 >= 100 then
			-- 只有队长可以推进进程
			if 副本id == 数字id then
				对话数据.对话="道观建设已经完成！感谢各位少侠的帮助，现在可以继续下一步了。"
				副本数据.车迟斗法.进行[副本id]={进程=3,数量=0,进度=0}
				GetUpMOB610(副本id)
				刷新队伍任务追踪(副本id)
				任务处理类:删除副本单位(副本id,610,612)
				地图处理类:删除单位(任务数据[标识].地图编号,任务数据[标识].单位编号)
				任务数据[标识]=nil
			else
				对话数据.对话="道观建设已经完成！请队长来推进下一步。"
			end
		else
			--对话数据.对话="道观建设需要木材，当前进度："..当前进度.."/100。队伍成员可以递交普通木材（+8进度）或高级木材（+20进度）。"
			对话数据.对话=""
			对话数据.选项={"递交普通木材","递交高级木材","查看进度"}
		end
	else
		对话数据.对话="现在不是建设道观的时候。进程："..副本数据.车迟斗法.进行[副本id].进程
	end
	return 对话数据
end
__GWdh222[613]=function (连接id,数字id,序号,内容)
	local 副本id = 任务处理类:取副本id(数字id,610)
	if 副本id == 0 then
		return
	end
	local 事件=内容[1]
	local 名称=内容[3]

	if 事件=="递交普通木材" then
		-- 检查玩家是否有普通木材
		if not 玩家数据[数字id].道具:判定背包道具(数字id,"普通木材",1) then
			常规提示(数字id,"#Y/你没有普通木材可以递交。")
			return
		end

		-- 扣除普通木材
		玩家数据[数字id].道具:消耗背包道具(连接id,数字id,"普通木材",1)

		-- 增加进度
		副本数据.车迟斗法.进行[副本id].进度 = (副本数据.车迟斗法.进行[副本id].进度 or 0) + 8

		-- 提示玩家
		常规提示(数字id,"#Y/成功递交普通木材，道观建设进度增加8点！当前进度："..副本数据.车迟斗法.进行[副本id].进度.."/100")

		-- 刷新任务追踪
		刷新队伍任务追踪(数字id)

	elseif 事件=="递交高级木材" then
		-- 检查玩家是否有高级木材
		if not 玩家数据[数字id].道具:判定背包道具(数字id,"高级木材",1) then
			常规提示(数字id,"#Y/你没有高级木材可以递交。")
			return
		end

		-- 扣除高级木材
		玩家数据[数字id].道具:消耗背包道具(连接id,数字id,"高级木材",1)

		-- 增加进度
		副本数据.车迟斗法.进行[副本id].进度 = (副本数据.车迟斗法.进行[副本id].进度 or 0) + 20

		-- 提示玩家
		常规提示(数字id,"#Y/成功递交高级木材，道观建设进度增加20点！当前进度："..副本数据.车迟斗法.进行[副本id].进度.."/100")

		-- 刷新任务追踪
		刷新队伍任务追踪(数字id)

	elseif 事件=="查看进度" then
		local 当前进度 = 副本数据.车迟斗法.进行[副本id].进度 or 0
		常规提示(数字id,"#Y/道观建设当前进度："..当前进度.."/100")
	end
end
__GWdh111[614]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	local 副本id = 任务处理类:取副本id(数字id,610)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 副本数据.车迟斗法.进行[数字id].进程==3  then
		if 副本数据.车迟斗法.进行[数字id].数量 >= 3 then
			对话数据.对话="阿弥陀佛,善哉善哉!少侠真是顽皮,顽皮就好,可不要造酿祸端!老衲这就引你们前往大殿,了解情况即可,切记不可冲动啊!"
			副本数据.车迟斗法.进行[数字id]={进程=4,数量=0,进度=0}
			地图处理类:删除单位(任务数据[标识].地图编号,任务数据[标识].单位编号)
			任务数据[标识]=nil
			任务处理类:删除副本单位(数字id,610,615)
			GetUpMOB610(数字id)
			刷新队伍任务追踪(数字id)
			地图处理类:跳转地图(数字id,6006,24,23)
			玩家数据[数字id].地图单位=nil
		else
			对话数据.对话="还有供品没有消灭"
		end
	end
	return 对话数据
end
__GWdh111[615]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,610)then
		对话数据.对话="副本任务数据错误"
		return 对话数据
	end
	local 副本id = 任务处理类:取副本id(数字id,610)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 任务数据[标识].战斗==nil then
		if 副本数据.车迟斗法.进行[数字id].进程==3  then
			对话数据.对话="我是贡品你想做什么？"
			对话数据.选项={"我来吃掉你","我很饱了，放你一马"}
		end
	end
	return 对话数据
end
__GWdh222[615]=function (连接id,数字id,序号,内容)
	local 事件=内容[1]
	local 名称=内容[3]
	if 事件=="我来吃掉你" then
		if 任务数据[玩家数据[数字id].地图单位.标识].战斗~=nil then 常规提示(数字id,"#Y/对方正在战斗中") return  end
		if 取队伍人数(数字id)<1  and 调试模式==false then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
		任务数据[玩家数据[数字id].地图单位.标识].战斗=true
		战斗准备类:创建战斗(数字id+0,100101,玩家数据[数字id].地图单位.标识)
		玩家数据[数字id].地图单位=nil
		return
	end
end
__GWdh111[616]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,610)then
		对话数据.对话="副本任务数据错误"
		return 对话数据
	end
	local 副本id = 任务处理类:取副本id(数字id,610)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 任务数据[标识].战斗==nil then
		if 副本数据.车迟斗法.进行[数字id].进程==4  then
			对话数据.对话="尔等，何人"
			对话数据.选项={"神像，得罪了","点错了"}
		end
	end
	return 对话数据
end
__GWdh222[616]=function (连接id,数字id,序号,内容)
	local 事件=内容[1]
	local 名称=内容[3]
	if 事件=="神像，得罪了" then
		if 任务数据[玩家数据[数字id].地图单位.标识].战斗~=nil then 常规提示(数字id,"#Y/对方正在战斗中") return  end
		if 取队伍人数(数字id)<1  and 调试模式==false then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
		任务数据[玩家数据[数字id].地图单位.标识].战斗=true
		战斗准备类:创建战斗(数字id+0,100102,玩家数据[数字id].地图单位.标识)
		玩家数据[数字id].地图单位=nil
		return
	end
end
__GWdh111[617]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,610)then
		对话数据.对话="副本任务数据错误"
		return 对话数据
	end
	local 副本id = 任务处理类:取副本id(数字id,610)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 副本数据.车迟斗法.进行[数字id].进程==4  then
		if 副本数据.车迟斗法.进行[数字id].数量 == 3 then
			对话数据.对话="大胆狂徒!胆敢在三清大殿撒野,待我师傅前来收拾尔等!"
			副本数据.车迟斗法.进行[数字id]={进程=5,数量=0,进度=0}
			地图处理类:删除单位(任务数据[标识].地图编号,任务数据[标识].单位编号)
			任务数据[标识]=nil
			GetUpMOB610(数字id)
			刷新队伍任务追踪(数字id)
			地图处理类:跳转地图(数字id,6007,35,151)
			玩家数据[数字id].地图单位=nil
		else
			对话数据.对话="还有神像没有消灭"
		end
	end
	return 对话数据
end
__GWdh111[618]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,610)then
		return
	end
	local 副本id = 任务处理类:取副本id(数字id,610)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 任务数据[标识].战斗==nil then
		if 副本数据.车迟斗法.进行[数字id].进程==5  then
			if 取队伍人数(数字id)<1  and 调试模式==false then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[标识].战斗=true
			战斗准备类:创建战斗(数字id+0,100103,标识)
			玩家数据[数字id].地图单位=nil
		end
	end
end
__GWdh111[619]=function (连接id,数字id,序列,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if not 任务处理类:判定队伍组(数字id,610)then
		对话数据.对话="副本任务数据错误"
		return 对话数据
	end
	local 副本id = 任务处理类:取副本id(数字id,610)
	if 副本id == 0 or 副本id ~= 数字id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
		return 对话数据
	end
	if 任务数据[标识].战斗==nil then
		if 副本数据.车迟斗法.进行[数字id].进程==6  then
			对话数据.对话="原来是你们这群乳臭未干的小子坏我们好事,这就把你们收拾了"
			对话数据.选项={"看谁收拾谁","点错了"}
		end
	end
	return 对话数据
end
__GWdh222[619]=function (连接id,数字id,序号,内容)
	local 事件=内容[1]
	local 名称=内容[3]
	if 事件=="看谁收拾谁" then
		if 任务数据[玩家数据[数字id].地图单位.标识].战斗~=nil then 常规提示(数字id,"#Y/对方正在战斗中") return  end
		if 取队伍人数(数字id)<1  and 调试模式==false then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
		任务数据[玩家数据[数字id].地图单位.标识].战斗=true
		战斗准备类:创建战斗(数字id+0,100104,玩家数据[数字id].地图单位.标识)
		玩家数据[数字id].地图单位=nil
		return
	end
end
__GWdh111[620]=function (连接id,数字id,序列,标识,地图)
    if 任务数据[标识].玩家id == 数字id then
        local 等级 = 玩家数据[数字id].角色.等级
        -- 基于69级200万经验计算经验倍率
        local 基础经验 = 2000000  -- 69级基础经验200万
        local 经验倍率 = math.max(1, (等级/69)^1.5)  -- 使用指数增长
        local 银子倍率 = math.max(1, (等级/69)^1.3)  -- 银子增长稍缓和一些

        -- 修改基础奖励
        玩家数据[数字id].角色:添加经验(基础经验 * 经验倍率,"副本-车迟副本")
        玩家数据[数字id].角色:添加银子(200000 * 银子倍率,"副本-车迟副本",1)

        -- 清理相关数据
        地图处理类:删除单位(任务数据[标识].地图编号,任务数据[标识].单位编号)
        任务数据[标识]=nil
        玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(610))
        副本数据.车迟斗法.完成[数字id]=true

        local 链接 = {提示=format("#S(车迟国副本)#G/%s#Y/车迟国归来，功德无量，获得",玩家数据[数字id].角色.名称),频道="xt",结尾="#Y/一个。恭喜恭喜！#85"}
        local 奖励参数=取随机数(1,60)
        if 取随机数()<=100 then
            if 奖励参数<=10 then
                local 名称="金柳露"
                玩家数据[数字id].道具:给予超链接道具(数字id,名称,1,nil,链接)
            elseif 奖励参数<=15 then
                local 名称="超级金柳露"
                玩家数据[数字id].道具:给予超链接道具(数字id,名称,1,nil,链接)
            elseif 奖励参数<=25 then
                local 名称="召唤兽内丹"
                玩家数据[数字id].道具:给予超链接道具(数字id,名称,nil,nil,链接)
            elseif 奖励参数<=35 then
                local 名称="魔兽要诀"
                玩家数据[数字id].道具:给予超链接道具(数字id,名称,1,nil,链接)
            elseif 奖励参数<=45 then
                local 名称="未激活的符石"
                玩家数据[数字id].道具:给予超链接道具(数字id,名称,取随机数(1,2),nil,链接)

            else
                local 名称=取宝石()
                玩家数据[数字id].道具:给予超链接道具(数字id,名称,1,nil,链接)
            end
        end
    end
end
__GWdh111[621]=function (连接id,数字id,序列,标识,地图)
    local 等级 = 玩家数据[数字id].角色.等级
    -- 基于69级20万经验计算经验倍率
    local 基础经验 = 30000  -- 69级基础经验20万
    local 经验倍率 = math.max(1, (等级/69)^1.5)
    local 银子倍率 = math.max(1, (等级/69)^1.3)

    玩家数据[数字id].角色:添加经验(基础经验 * 经验倍率,"副本-车迟副本")
    玩家数据[数字id].角色:添加银子(20000 * 银子倍率,"副本-车迟副本",1)

    local 链接 = {提示=format("#S(车迟国副本)#G/%s#Y/车迟国归来，功德无量，获得",玩家数据[数字id].角色.名称),频道="xt",结尾="#Y/一个。恭喜恭喜！#85"}
    local 奖励参数=取随机数(1,60)
    if 奖励参数<=15 then
        local 名称="超级金柳露"
        玩家数据[数字id].道具:给予超链接道具(数字id,名称,1,nil,链接)
    elseif 奖励参数<=25 then
        local 名称="召唤兽内丹"
        玩家数据[数字id].道具:给予超链接道具(数字id,名称,nil,nil,链接)
    elseif 奖励参数<=35 then
        local 名称="魔兽要诀"
        玩家数据[数字id].道具:给予超链接道具(数字id,名称,1,nil,链接)
    elseif 奖励参数<=45 then
        local 名称="未激活的符石"
        玩家数据[数字id].道具:给予超链接道具(数字id,名称,取随机数(1,2),nil,链接)
    elseif 奖励参数<=50 then
        玩家数据[数字id].道具:给予超链接书铁(数字id,{6,9},nil,链接)
    elseif 奖励参数<=51 then
        local 名称="金柳露"
        玩家数据[数字id].道具:给予超链接道具(数字id,名称,1,nil,链接)
    else
        local 名称=取宝石()
        玩家数据[数字id].道具:给予超链接道具(数字id,名称,1,nil,链接)
    end
    地图处理类:删除单位(任务数据[标识].地图编号,任务数据[标识].单位编号)
    任务数据[标识]=nil
end
function rwgx610(任务id)
	if os.time()-任务数据[任务id].起始>=任务数据[任务id].结束 and 任务数据[任务id].结束 ~= 99999999 then
		if 任务数据[任务id].战斗~=true then
			if 任务数据[任务id].类型== 610 then
				local 副本id = 任务数据[任务id].副本id

				-- 清除所有副本相关NPC
				任务处理类:删除副本单位(副本id,610,611) -- 和尚
				任务处理类:删除副本单位(副本id,610,612) -- 道士
				任务处理类:删除副本单位(副本id,610,613) -- 道观
				任务处理类:删除副本单位(副本id,610,614) -- 和尚
				任务处理类:删除副本单位(副本id,610,615) -- 供品
				任务处理类:删除副本单位(副本id,610,616) -- 三清
				任务处理类:删除副本单位(副本id,610,617) -- 道童
				任务处理类:删除副本单位(副本id,610,618) -- 雷公电母雨师
				任务处理类:删除副本单位(副本id,610,619) -- 三大仙
				任务处理类:删除副本单位(副本id,610,620) -- 宝箱
				任务处理类:删除副本单位(副本id,610,621) -- 小宝箱

				-- 处理队伍成员
				for i=1,#任务数据[任务id].队伍组 do
					if 玩家数据[任务数据[任务id].队伍组[i]] ~= nil then
						-- 记录完成次数
						副本数据.车迟斗法.完成[任务数据[任务id].队伍组[i]]=true
						玩家数据[任务数据[任务id].队伍组[i]].角色:取消任务(玩家数据[任务数据[任务id].队伍组[i]].角色:取任务(610))
						常规提示(任务数据[任务id].队伍组[i],"#Y/副本时间已到,任务失败了。")
						-- 清除玩家地图单位引用
						玩家数据[任务数据[任务id].队伍组[i]].地图单位 = nil
					end
				end

				-- 清除副本进行数据
				副本数据.车迟斗法.进行[副本id] = nil

				-- 最后删除任务数据
				任务数据[任务id]=nil
			else
				地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].单位编号)
				任务数据[任务id]=nil
			end
		end
	end
end
function 任务说明610(玩家id,任务id)
	local 说明 = {}
	local 副本id=任务数据[任务id].副本id
	if 副本数据.车迟斗法.进行[副本id]==nil then
		说明={"车迟斗法","您的副本已经完成"}
	else
		local 进程=副本数据.车迟斗法.进行[副本id].进程
		if 进程==1 then
			说明={"车迟斗法",format("寻找#R/有个和尚#W/，看看发生了什么事情。(剩余%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==2 then
			local 当前进度 = 副本数据.车迟斗法.进行[副本id].进度 or 0
			说明={"车迟斗法","回答#R/有个道士#W/的提问，获得木材，向#R/道观#W/递交木材建设道观（进度"..当前进度.."/100）。(剩余"..取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))).."分钟)"}
		elseif 进程==3 then
			说明={"车迟斗法",format("吃光场地内的所有供品后告诉#R/有个和尚#W/（完成度%s丨15）。(剩余%s分钟)",副本数据.车迟斗法.进行[副本id].数量,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==4 then
			说明={"车迟斗法",format("从三座道士逼出妖道，(剩余%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==5 then
			说明={"车迟斗法",format("火速阻止，#R/雷公#W/、#R/电母#W/、#R/雨师#W/施法（完成度%s丨3）。(剩余%s分钟)",副本数据.车迟斗法.进行[副本id].数量,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==6 then
			说明={"车迟斗法",format("三个妖道终于现身，众人齐心协力找到他们，赐妖道原型。(剩余%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==7 then
			说明={"车迟斗法",format("拾取属于自己的宝箱，和小宝箱。(剩余%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		end
	end
	return 说明
end
function 胜利MOB_100101(胜利id,战斗数据,id组)
	地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号,任务数据[战斗数据.任务id].单位编号)
	local 副本id=任务数据[战斗数据.任务id].副本id
	任务数据[战斗数据.任务id]=nil
	副本数据.车迟斗法.进行[副本id].数量=副本数据.车迟斗法.进行[副本id].数量+1
	刷新队伍任务追踪(战斗数据.进入战斗玩家id)
	local 链接
	for n=1,#id组 do
		local cyid=id组[n]
		local 等级=玩家数据[cyid].角色.等级
		-- 修改基础经验和银子公式
		local 经验=等级*取随机数(55,60)
		local 银子=等级*取随机数(20,22)
		玩家数据[cyid].角色:添加经验(经验,"车迟斗法副本")
		玩家数据[cyid].角色:添加储备(银子,"车迟斗法副本",1)
		if 取随机数()<=50 then
			local 奖励参数=取随机数(1,60)
			链接={提示=format("#S(车迟斗法)#G/%s#Y打理三清道观的贡品时，发现了一个",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y。"}
			if 奖励参数<=10 then
				local 名称="超级金柳露"
				玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
			elseif 奖励参数<=20 then
				local 名称="金柳露"
				玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
			elseif 奖励参数<=30 then
				local 名称="魔兽要诀"
				玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
			elseif 奖励参数<=40 then
				local 名称="净瓶玉露"
				玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
			elseif 奖励参数<=60 then
				local 名称=取宝石()
				玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
			end
		end
	end
end
function 失败MOB100101(id组,失败id,是否逃跑,战斗数据)
	for i=1,#id组 do
		if id组[i]~=失败id then
			战斗数据:扣除经验(id组[i],0.085)
			战斗数据:扣除银子(id组[i],0.075)
		else
			if 失败id==战斗数据.进入战斗玩家id and 玩家数据[战斗数据.进入战斗玩家id]~=nil then
				if 是否逃跑==nil then
					战斗数据:扣除经验(失败id,0.085)
					战斗数据:扣除银子(失败id,0.075)
				end
			end
		end
	end
end
function 胜利MOB_100102(胜利id,战斗数据,id组)
	地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号,任务数据[战斗数据.任务id].单位编号)
	local 副本id=任务数据[战斗数据.任务id].副本id
	任务数据[战斗数据.任务id]=nil
	副本数据.车迟斗法.进行[副本id].数量=副本数据.车迟斗法.进行[副本id].数量+1
	刷新队伍任务追踪(战斗数据.进入战斗玩家id)
	for n=1,#队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据 do
		玩家数据[队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据[n]].角色:添加积分(50,"副本积分")
	end
end
function 失败MOB100102(id组,失败id,是否逃跑,战斗数据)
	for i=1,#id组 do
		if id组[i]~=失败id then
			战斗数据:扣除经验(id组[i],0.085)
			战斗数据:扣除银子(id组[i],0.075)
		else
			if 失败id==战斗数据.进入战斗玩家id and 玩家数据[战斗数据.进入战斗玩家id]~=nil  then
				if 是否逃跑==nil then
					战斗数据:扣除经验(失败id,0.085)
					战斗数据:扣除银子(失败id,0.075)
				end
			end
		end
	end
end
function 胜利MOB_100103(胜利id,战斗数据,id组)
	地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号,任务数据[战斗数据.任务id].单位编号)
	local 副本id=任务数据[战斗数据.任务id].副本id
	任务数据[战斗数据.任务id]=nil
	副本数据.车迟斗法.进行[副本id].数量=副本数据.车迟斗法.进行[副本id].数量+1
	if  副本数据.车迟斗法.进行[副本id].数量 == 3 then
		副本数据.车迟斗法.进行[副本id]={进程=6,数量=0,进度=0}
		GetUpMOB610(副本id)
	end
	刷新队伍任务追踪(战斗数据.进入战斗玩家id)
	for n=1,#队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据 do
		玩家数据[队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据[n]].角色:添加积分(100,"副本积分")
	end
end
function 失败MOB100103(id组,失败id,是否逃跑,战斗数据)
	for i=1,#id组 do
		if id组[i]~=失败id then
			战斗数据:扣除经验(id组[i],0.085)
			战斗数据:扣除银子(id组[i],0.075)
		else
			if 失败id==战斗数据.进入战斗玩家id and 玩家数据[战斗数据.进入战斗玩家id]~=nil then
				if 是否逃跑==nil then
					战斗数据:扣除经验(失败id,0.085)
					战斗数据:扣除银子(失败id,0.075)
				end
			end
		end
	end
end
function 胜利MOB_100104(胜利id,战斗数据,id组)
	地图处理类:删除单位(任务数据[战斗数据.任务id].地图编号,任务数据[战斗数据.任务id].单位编号)
	local 副本id=任务数据[战斗数据.任务id].副本id
	任务数据[战斗数据.任务id]=nil
	副本数据.车迟斗法.进行[副本id].数量=副本数据.车迟斗法.进行[副本id].数量+1
	副本数据.车迟斗法.进行[副本id]={进程=7,数量=0,进度=0}
	GetUpMOB610(副本id)
	刷新队伍任务追踪(战斗数据.进入战斗玩家id)
	for n=1,#队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据 do
		玩家数据[队伍数据[玩家数据[战斗数据.进入战斗玩家id].队伍].成员数据[n]].角色:添加积分(200,"副本积分")
	end
end
function 失败MOB100104(id组,失败id,是否逃跑,战斗数据)
	for i=1,#id组 do
		if id组[i]~=失败id then
			战斗数据:扣除经验(id组[i],0.085)
			战斗数据:扣除银子(id组[i],0.075)
		else
			if 失败id==战斗数据.进入战斗玩家id and 玩家数据[战斗数据.进入战斗玩家id]~=nil  then
				if 是否逃跑==nil then
					战斗数据:扣除经验(失败id,0.085)
					战斗数据:扣除银子(失败id,0.075)
				end
			end
		end
	end
end
function 怪物属性:取车迟斗法贡品信息(任务id,玩家id)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)-20
	if 等级<=10 then
		等级=10
	end
	战斗单位[1]={
	名称=任务数据[任务id].名称,
	模型=任务数据[任务id].模型,
	显示饰品=任务数据[任务id].显示饰品,
	伤害=等级*10+200,
	气血=等级*75,
	法伤=等级*7+140,
	速度=等级*2.3,
	防御=等级*6,
	法防=等级*4.2+84,
	躲闪=等级*4,
	魔法=200000,
	等级=等级,
	技能={"魔之心","法术连击"},
	主动技能=取随机法术(5)
	}
	local 模型范围={"泡泡"}
	local 模型名称范围={"贡品"}
	local 模型=模型范围[取随机数(1,#模型范围)]
	local 技能范围 = {{"烟雨剑法","善恶有报"},{"三昧真火","地狱烈火","飞砂走石"},{"龙卷雨击","龙腾","龙吟"},{"落叶萧萧","雷击","奔雷咒"},{"失心符","定身符","反间之计"},{"推气过宫"},{"天雷斩"}}
	local 技能随机=技能范围[取随机数(1,#技能范围)]
	for i=2,5 do
		模型=模型范围[取随机数(1,#模型范围)]
		技能随机=技能范围[取随机数(1,#技能范围)]
		战斗单位[i]={
		名称=任务数据[任务id].名称,
		模型=模型,显示饰品=true,
		伤害=等级*10+200,
		气血=等级*75,
		法伤=等级*7+140,
		速度=等级*2.3,
		防御=等级*6,
		法防=等级*4.2+84,
		躲闪=等级*4,
		魔法=200000,
		等级=等级,
		技能={"高级感知"},
		主动技能=技能随机
		}
	end
	return 战斗单位
end
function 怪物属性:取车迟斗法三清信息(任务id,玩家id)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	战斗单位[1]={
	名称=任务数据[任务id].名称
	,模型="大力金刚"
	,变异=true
	,伤害=等级*17
	,气血=等级*等级
	,法伤=等级*7
	,速度=等级*3.5
	,防御=等级*6
	,法防=等级*4.2
	,躲闪=100
	,魔法=200000
	,等级=等级
	,技能={"高级感知","高级强力","高级偷袭","高级必杀"}
	,主动技能=取随机法术(3)
	}
	等级=等级-5
	for i=2,10 do
		战斗单位[i]={
		名称="护法"
		,模型="天兵"
		,伤害=等级*16
		,气血=等级*等级
		,法伤=等级*7
		,速度=等级*2.5
		,防御=等级*6
		,法防=等级*4.2
		,躲闪=等级*4
		,魔法=200000
		,等级=等级
		,技能={"高级连击","高级感知","高级魔之心","高级法术波动","高级强力"}
		,主动技能=取随机法术(2)
		}
	end
	return 战斗单位
end
function 怪物属性:取车迟斗法求雨信息(任务id,玩家id)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	战斗单位[1]={
	名称=任务数据[任务id].名称
	,模型=任务数据[任务id].模型
	,变异=true
	,伤害=等级*15
	,气血=等级*等级
	,法伤=等级*10.5
	,速度=等级*3.5
	,防御=等级*9
	,法防=等级*6.2
	,躲闪=100
	,魔法=200000
	,等级=等级
	,技能={"高级感知"}
	,主动技能=取随机法术(3)
	}
	for i=2,10 do
		战斗单位[i]={
		名称="天将"
		,模型="天将"
		,伤害=等级*15
		,气血=等级*等级
		,法伤=等级*10.5
		,速度=等级*2.5
		,防御=等级*9
		,法防=等级*6.2
		,躲闪=等级*4
		,魔法=200000
		,等级=等级
		,技能={"高级连击"}
		,主动技能=取随机法术(2)
		}
	end
	return 战斗单位
end
function 怪物属性:取车迟斗法不动信息(任务id,玩家id)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	if 任务数据[任务id].名称=="我不动" then
		战斗单位[1]={
		名称=任务数据[任务id].名称
		,模型="净瓶女娲"
		,变异=true
		,伤害=等级*12
		,气血=等级*等级
		,法伤=等级*8.4
		,速度=等级*3.5
		,防御=等级*7.2
		,法防=等级*5
		,躲闪=100
		,魔法=200000
		,等级=等级
		,技能={"高级感知"}
		,主动技能={"龙卷雨击","龙腾"}
		}
		for i=2,5 do
			战斗单位[i]={
			名称="你不动"
			,模型="净瓶女娲"
			,变异=true
			,伤害=等级*12
			,气血=等级*等级
			,法伤=等级*8.4
			,速度=等级*2.5
			,防御=等级*7.2
			,法防=等级*5
			,躲闪=100
			,魔法=200000
			,等级=等级
			,技能={"高级夜战"}
			,主动技能={"泰山压顶"}
			}
		end
		等级=等级-5
		for i=6,10 do
			战斗单位[i]={
			名称="护法"
			,模型="大力金刚"
			,伤害=等级*12
			,气血=等级*等级
			,法伤=等级*8.4
			,速度=等级*2.5
			,防御=等级*7.2
			,法防=等级*5
			,躲闪=等级*4
			,魔法=200000
			,等级=等级
			,技能={"高级连击"}
			,主动技能={}
			}
		end
	else
		战斗单位[1]={
		名称=任务数据[任务id].名称
		,模型="净瓶女娲"
		,变异=true
		,伤害=等级*12
		,气血=等级*等级
		,法伤=等级*8.4
		,速度=等级*2.5
		,防御=等级*7.2
		,法防=等级*5
		,躲闪=100
		,魔法=200000
		,等级=等级
		,技能={"高级感知"}
		,主动技能={"变身","鹰击","连环击"}
		}
		for i=2,5 do
			战斗单位[i]={
			名称="护法"
			,模型="大力金刚"
			,变异=true
			,伤害=等级*12
			,气血=等级*等级
			,法伤=等级*8.4
			,速度=等级*2.5
			,防御=等级*7.2
			,法防=等级*5
			,躲闪=100
			,魔法=200000
			,等级=等级
			,技能={"高级连击"}
			,主动技能=取随机法术(2)
			}
		end
		等级=等级-5
		for i=6,10 do
			战斗单位[i]={
			名称="护卫"
			,模型="夜罗刹"
			,伤害=等级*12
			,气血=等级*等级
			,法伤=等级*8.4
			,速度=等级*2.5
			,防御=等级*7.2
			,法防=等级*5
			,躲闪=等级*4
			,魔法=200000
			,等级=等级
			,技能={"高级连击"}
			,主动技能={}
			}
		end
	end
	return 战斗单位
end
return 副本_车迟斗法