[{"id": "preferences", "children": [{"caption": "Package Settings", "mnemonic": "P", "id": "package-settings", "children": [{"caption": "File Header", "children": [{"command": "open_file", "args": {"file": "${packages}/FileHeader/FileHeader.sublime-settings"}, "caption": "<PERSON>ting<PERSON> <PERSON> <PERSON><PERSON><PERSON>"}, {"command": "open_file", "args": {"file": "${packages}/User/FileHeader.sublime-settings"}, "caption": "Settings – User"}, {"caption": "-"}, {"command": "open_file", "args": {"file": "${packages}/FileHeader/Default (OSX).sublime-keymap", "platform": "OSX"}, "caption": "Key Bindings – <PERSON><PERSON><PERSON>"}, {"command": "open_file", "args": {"file": "${packages}/FileHeader/Default (Linux).sublime-keymap", "platform": "Linux"}, "caption": "Key Bindings – <PERSON><PERSON><PERSON>"}, {"command": "open_file", "args": {"file": "${packages}/FileHeader/Default (Windows).sublime-keymap", "platform": "Windows"}, "caption": "Key Bindings – <PERSON><PERSON><PERSON>"}, {"command": "open_file", "args": {"file": "${packages}/User/Default (OSX).sublime-keymap", "platform": "OSX"}, "caption": "Key Bindings – User"}, {"command": "open_file", "args": {"file": "${packages}/User/Default (Linux).sublime-keymap", "platform": "Linux"}, "caption": "Key Bindings – User"}, {"command": "open_file", "args": {"file": "${packages}/User/Default (Windows).sublime-keymap", "platform": "Windows"}, "caption": "Key Bindings – User"}, {"caption": "-"}]}]}]}]