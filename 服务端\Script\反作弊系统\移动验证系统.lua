-- @Author: 反作弊系统
-- @Date: 2025-01-14
-- @Description: 移动路径验证系统，防止瞬移和不合理的移动模式

local 移动验证系统 = {}

-- 引入行为检测系统
local 行为检测系统 = require("Script/反作弊系统/行为检测系统")

-- 移动验证配置
local 验证配置 = {
    最大移动距离 = 200,           -- 单次移动最大距离（像素）
    最大移动速度 = 300,           -- 最大移动速度（像素/秒）
    路径偏差阈值 = 100,           -- 路径偏差阈值
    瞬移检测阈值 = 500,           -- 瞬移检测阈值
    连续异常移动限制 = 3,         -- 连续异常移动次数限制
    移动频率限制 = 10,            -- 每秒最大移动次数
}

-- 玩家移动数据
local 玩家移动数据 = {}

-- 初始化玩家移动数据
function 移动验证系统:初始化玩家数据(玩家id)
    if not 玩家移动数据[玩家id] then
        玩家移动数据[玩家id] = {
            上次位置 = {x = 0, y = 0},
            上次移动时间 = 0,
            移动历史 = {},
            异常移动计数 = 0,
            移动频率计数 = {},
            预期路径 = {},
            最后验证时间 = os.time(),
        }
    end
end

-- 验证移动请求
function 移动验证系统:验证移动请求(玩家id, 目标坐标)
    if not 玩家数据[玩家id] or not 目标坐标 then
        return false, "无效的移动请求"
    end
    
    self:初始化玩家数据(玩家id)
    local 移动数据 = 玩家移动数据[玩家id]
    local 当前时间 = os.time()
    local 当前位置 = {
        x = 玩家数据[玩家id].角色.地图数据.x,
        y = 玩家数据[玩家id].角色.地图数据.y
    }
    
    -- 检查移动频率
    local 频率检查结果, 频率错误信息 = self:检查移动频率(玩家id, 当前时间)
    if not 频率检查结果 then
        return false, 频率错误信息
    end
    
    -- 检查移动距离
    local 距离检查结果, 距离错误信息 = self:检查移动距离(当前位置, 目标坐标)
    if not 距离检查结果 then
        移动数据.异常移动计数 = 移动数据.异常移动计数 + 1
        self:记录异常移动(玩家id, "移动距离异常", {
            当前位置 = 当前位置,
            目标位置 = 目标坐标,
            距离 = 距离错误信息.距离
        })
        return false, 距离错误信息.消息
    end
    
    -- 检查移动速度
    if 移动数据.上次移动时间 > 0 then
        local 速度检查结果, 速度错误信息 = self:检查移动速度(
            移动数据.上次位置, 目标坐标, 移动数据.上次移动时间, 当前时间
        )
        if not 速度检查结果 then
            移动数据.异常移动计数 = 移动数据.异常移动计数 + 1
            self:记录异常移动(玩家id, "移动速度异常", {
                速度 = 速度错误信息.速度,
                时间差 = 速度错误信息.时间差
            })
            return false, 速度错误信息.消息
        end
    end
    
    -- 检查路径合理性
    local 路径检查结果, 路径错误信息 = self:检查路径合理性(玩家id, 当前位置, 目标坐标)
    if not 路径检查结果 then
        移动数据.异常移动计数 = 移动数据.异常移动计数 + 1
        self:记录异常移动(玩家id, "路径不合理", 路径错误信息)
        return false, "移动路径不合理"
    end
    
    -- 检查连续异常移动
    if 移动数据.异常移动计数 >= 验证配置.连续异常移动限制 then
        self:处理异常移动(玩家id)
        return false, "检测到连续异常移动，已限制移动"
    end
    
    -- 更新移动数据
    self:更新移动数据(玩家id, 目标坐标, 当前时间)
    
    -- 调用行为检测系统
    行为检测系统:检测移动异常(玩家id, 目标坐标)
    
    return true, "移动验证通过"
end

-- 检查移动频率
function 移动验证系统:检查移动频率(玩家id, 当前时间)
    local 移动数据 = 玩家移动数据[玩家id]
    local 当前秒 = math.floor(当前时间)
    
    -- 初始化当前秒的计数
    if not 移动数据.移动频率计数[当前秒] then
        移动数据.移动频率计数[当前秒] = 0
    end
    
    移动数据.移动频率计数[当前秒] = 移动数据.移动频率计数[当前秒] + 1
    
    -- 清理过期数据
    for 秒, 计数 in pairs(移动数据.移动频率计数) do
        if 当前秒 - 秒 > 5 then
            移动数据.移动频率计数[秒] = nil
        end
    end
    
    -- 检查频率限制
    if 移动数据.移动频率计数[当前秒] > 验证配置.移动频率限制 then
        return false, "移动频率过高"
    end
    
    return true
end

-- 检查移动距离
function 移动验证系统:检查移动距离(当前位置, 目标位置)
    local 距离 = math.sqrt(
        (目标位置.x - 当前位置.x)^2 + (目标位置.y - 当前位置.y)^2
    )
    
    if 距离 > 验证配置.最大移动距离 then
        return false, {
            消息 = "单次移动距离过大",
            距离 = 距离
        }
    end
    
    -- 检查瞬移
    if 距离 > 验证配置.瞬移检测阈值 then
        return false, {
            消息 = "检测到瞬移行为",
            距离 = 距离
        }
    end
    
    return true
end

-- 检查移动速度
function 移动验证系统:检查移动速度(上次位置, 当前位置, 上次时间, 当前时间)
    local 时间差 = 当前时间 - 上次时间
    if 时间差 <= 0 then
        return false, {
            消息 = "时间异常",
            时间差 = 时间差
        }
    end
    
    local 距离 = math.sqrt(
        (当前位置.x - 上次位置.x)^2 + (当前位置.y - 上次位置.y)^2
    )
    local 速度 = 距离 / 时间差
    
    if 速度 > 验证配置.最大移动速度 then
        return false, {
            消息 = "移动速度过快",
            速度 = 速度,
            时间差 = 时间差
        }
    end
    
    return true
end

-- 检查路径合理性
function 移动验证系统:检查路径合理性(玩家id, 当前位置, 目标位置)
    local 移动数据 = 玩家移动数据[玩家id]
    local 地图编号 = 玩家数据[玩家id].角色.地图数据.编号
    
    -- 检查目标位置是否在地图范围内
    -- 这里需要根据你的地图系统来实现具体的边界检查
    
    -- 检查是否有障碍物阻挡
    local 障碍集成 = require("Script/工具/游戏障碍集成")
    if 障碍集成 and 障碍集成.检查障碍 then
        local 有障碍 = 障碍集成.检查障碍(地图编号, 目标位置.x, 目标位置.y)
        if 有障碍 then
            return false, {类型 = "障碍物阻挡", 位置 = 目标位置}
        end
    end
    
    -- 检查路径是否过于直线（可能是脚本行为）
    if #移动数据.移动历史 >= 3 then
        local 直线度 = self:计算路径直线度(移动数据.移动历史, 目标位置)
        if 直线度 > 0.95 then -- 95%以上的直线度认为可疑
            return false, {类型 = "路径过于直线", 直线度 = 直线度}
        end
    end
    
    return true
end

-- 计算路径直线度
function 移动验证系统:计算路径直线度(移动历史, 目标位置)
    if #移动历史 < 2 then
        return 0
    end
    
    local 起点 = 移动历史[1].位置
    local 终点 = 目标位置
    local 理论距离 = math.sqrt((终点.x - 起点.x)^2 + (终点.y - 起点.y)^2)
    
    if 理论距离 == 0 then
        return 1
    end
    
    local 实际距离 = 0
    for i = 1, #移动历史 - 1 do
        local 当前点 = 移动历史[i].位置
        local 下一点 = 移动历史[i + 1].位置
        实际距离 = 实际距离 + math.sqrt((下一点.x - 当前点.x)^2 + (下一点.y - 当前点.y)^2)
    end
    
    -- 加上最后一段到目标位置的距离
    local 最后点 = 移动历史[#移动历史].位置
    实际距离 = 实际距离 + math.sqrt((目标位置.x - 最后点.x)^2 + (目标位置.y - 最后点.y)^2)
    
    return 理论距离 / 实际距离
end

-- 更新移动数据
function 移动验证系统:更新移动数据(玩家id, 新位置, 当前时间)
    local 移动数据 = 玩家移动数据[玩家id]
    
    -- 记录移动历史
    table.insert(移动数据.移动历史, {
        位置 = {x = 新位置.x, y = 新位置.y},
        时间 = 当前时间
    })
    
    -- 限制历史记录长度
    if #移动数据.移动历史 > 20 then
        table.remove(移动数据.移动历史, 1)
    end
    
    -- 更新位置和时间
    移动数据.上次位置 = {x = 新位置.x, y = 新位置.y}
    移动数据.上次移动时间 = 当前时间
    移动数据.最后验证时间 = 当前时间
    
    -- 重置异常计数（正常移动）
    if 移动数据.异常移动计数 > 0 then
        移动数据.异常移动计数 = 移动数据.异常移动计数 - 1
    end
end

-- 记录异常移动
function 移动验证系统:记录异常移动(玩家id, 异常类型, 详细信息)
    if not 玩家数据[玩家id] then
        return
    end
    
    local 日志信息 = string.format(
        "[移动验证] 玩家%d(%s) %s: %s",
        玩家id,
        玩家数据[玩家id].角色.名称 or "未知",
        异常类型,
        table.tostring(详细信息 or {})
    )
    
    __S服务:输出(日志信息)
    
    -- 写入日志文件
    pcall(function()
        local 日志文件 = io.open("logs/movement_validation.log", "a")
        if 日志文件 then
            日志文件:write(os.date("%Y-%m-%d %H:%M:%S") .. " " .. 日志信息 .. "\n")
            日志文件:close()
        end
    end)
end

-- 处理异常移动
function 移动验证系统:处理异常移动(玩家id)
    if not 玩家数据[玩家id] then
        return
    end
    
    -- 限制移动一段时间
    玩家数据[玩家id].角色.移动限制 = os.time() + 300 -- 限制5分钟
    
    常规提示(玩家id, "#R/检测到异常移动行为，暂时限制移动5分钟")
    
    -- 通知管理员
    local 通知内容 = string.format(
        "[移动异常] 玩家 %s(ID:%d) 连续异常移动，已限制移动",
        玩家数据[玩家id].角色.名称 or "未知",
        玩家id
    )
    
    for id, data in pairs(玩家数据) do
        if data and data.角色 and data.角色.权限 and data.角色.权限 >= 5 then
            常规提示(id, "#R/" .. 通知内容)
        end
    end
end

-- 清理过期数据
function 移动验证系统:清理过期数据()
    local 当前时间 = os.time()
    local 清理阈值 = 24 * 60 * 60 -- 24小时
    
    for 玩家id, 数据 in pairs(玩家移动数据) do
        if 当前时间 - 数据.最后验证时间 > 清理阈值 then
            if not 玩家数据[玩家id] then
                玩家移动数据[玩家id] = nil
            end
        end
    end
end

return 移动验证系统
