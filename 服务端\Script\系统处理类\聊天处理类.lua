-- @Author: baidwwy
-- @Date:   2025-01-06 18:39:59
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-25 00:31:11
-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2024-05-12 22:27:45
-- @Last Modified by:   baidwwy
-- @Last Modified time: 2024-07-27 14:13:52
local 聊天处理类 = class()
function 聊天处理类:初始化() end
function 聊天处理类:数据处理(id,序号,数据)
	if 序号==6001 then
		self:频道发言处理(玩家数据[id].连接id,id,数据)
	end
end
不可结束战斗={}
不可结束战斗[200001]=1
不可结束战斗[200002]=1
不可结束战斗[200003]=1
不可结束战斗[200004]=1
不可结束战斗[200005]=1
不可结束战斗[200006]=1
不可结束战斗[100035]=1


function 聊天处理类:频道发言处理(连接id,id,数据)
	if 体验状态开关 and 玩家数据[id].角色.体验状态 then
		常规提示(id,"体验状态下无法进行此操作。")
		return
	end
	local 频道=数据.频道
	local 文本=数据.文本

	-- 处理验证命令（优先处理）
	local 验证命令处理 = require("Script/反作弊系统/验证命令处理")
	if 验证命令处理:处理验证命令(id, 文本) then
		return -- 验证命令已处理，不继续执行其他聊天逻辑
	end

	-- 处理管理员命令
	if 验证命令处理:处理管理员命令(id, 文本) then
		return -- 管理员命令已处理
	end

	if 频道==1 then
		if 数据.文本=="管理模式" then
			if f函数.读配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","管理") == "5268416" then
				玩家数据[id].管理模式 = true
				常规提示(id,"#Y/开启管理模式！")
				return
			end
		elseif 数据.文本=="退出战斗" or 数据.文本=="tczd" then
			-- 检查队长权限
			if not 取队长权限(id) then
				常规提示(id,"#Y/只有队长才能使用退出战斗功能")
				玩家数据[id].当前频道=os.time()+30  -- 设置冷却时间防止频繁尝试
				return
			end
			if 玩家数据[id].战斗~=0 and 玩家数据[id].观战 == 0 and not 不可结束战斗[战斗准备类.战斗盒子[玩家数据[id].战斗].战斗类型] then
				-- 检查是否为游泳活动战斗
				local 战斗类型 = 战斗准备类.战斗盒子[玩家数据[id].战斗].战斗类型
				if 战斗类型 == 100012 then
					-- 游泳活动战斗，记录退出行为
					if 游泳活动 and type(游泳活动) == "table" and 游泳活动.记录退出战斗 then
						游泳活动:记录退出战斗(id)
					else
						print("警告：游泳活动对象未正确初始化，无法记录退出战斗行为")
					end
				end
				战斗准备类.战斗盒子[玩家数据[id].战斗]:强制结束战斗()
				玩家数据[id].当前频道=os.time()+30  -- 成功退出战斗后设置冷却时间
			end
			return
		elseif 数据.文本=="@清空" then 
			玩家数据[id].道具:清空包裹(连接id,id)
		end
		if 玩家数据[id].管理模式 then
			if 数据.文本=="@1" then 
				玩家数据[id].道具:给予超链接道具(id,"未激活的符石",1)
			elseif 数据.文本=="@2" then 
				玩家数据[id].道具:给予超链接道具(id,"未激活的符石",2)
			elseif 数据.文本=="@3" then 
				玩家数据[id].道具:给予超链接道具(id,"未激活的符石",3)
			elseif string.find(数据.文本,"@发送")~=nil then
				local 临时数组=分割文本(数据.文本,"=")
				local 名称=临时数组[2] or "未知"
				local 数量=临时数组[3] or nil
				玩家数据[id].道具:给予道具(id,名称,数量)
			elseif string.find(数据.文本,"@清空银子")~=nil then
				local 临时数组=分割文本(数据.文本,"=")
				玩家数据[临时数组[2]].角色:清空银子()
			elseif 数据.文本=="@1" then
				local text1={{"半生潦倒","唯余荒唐一梦","梦里恍惚","怎记今夕何夕"},{"唯有四方城中","灯火阑珊"}}
				发送数据(连接id,6557,{文本=text1,类型="一斛珠序",字体=nil,音乐=nil,背景=nil,横排显示=nil,动画调用=nil})
			elseif 数据.文本=="@2" then
				local text1={"砰!  砰!砰!来人呐!","叶夫人和鸾儿姑娘晕倒啦!"}
				发送数据(连接id,6557,{文本=text1,类型="一斛珠1",字体=nil,音乐=nil,背景=nil,横排显示=true,动画调用=nil})
			elseif 数据.文本=="@渡劫" then
				玩家数据[id].角色.历劫.飞升=true
				玩家数据[id].角色.历劫.渡劫=true
			elseif 数据.文本=="@化圣" then
				玩家数据[id].角色.历劫.女娲=true
				玩家数据[id].角色.历劫.化圣=true
			elseif 数据.文本=="管理工具" then
				发送数据(连接id,145)
			elseif 数据.文本=="现代化管理工具" then
				发送数据(连接id,146)  -- 使用新的序号146
			elseif 数据.文本=="拍卖系统管理" then
				发送数据(玩家数据[id].连接id,221.3,玩家数据[id].道具:索要道具2(id))
			elseif 数据.文本=="@天宫" then
				地图处理类:跳转地图(id,1111,100,100)
				return
			elseif 数据.文本=="@鬼城" then
				地图处理类:跳转地图(id,1202,100,100)
				return				
			elseif 数据.文本=="@古董清理" then
				if GM指令处理类 and type(GM指令处理类.古董商人状态清理) == "function" then
					GM指令处理类:古董商人状态清理(id)
				else
					常规提示(id, "#Y/古董商人管理功能不可用")
				end
			elseif 数据.文本=="@古董状态" then
				if GM指令处理类 and type(GM指令处理类.古董商人状态检查) == "function" then
					GM指令处理类:古董商人状态检查(id)
				else
					常规提示(id, "#Y/古董商人管理功能不可用")
				end
			elseif 数据.文本=="@古董测试" then
				if GM指令处理类 and type(GM指令处理类.古董商人测试) == "function" then
					GM指令处理类:古董商人测试(id)
				else
					常规提示(id, "#Y/古董商人管理功能不可用")
				end
			elseif 数据.文本=="@状态重置" then
				if 场景类_对话商业栏 and type(场景类_对话商业栏.强制清理古董商人状态) == "function" then
					场景类_对话商业栏:强制清理古董商人状态(id)
					常规提示(id, "#G/您的商店状态已强制重置")
				else
					常规提示(id, "#Y/状态重置功能不可用")
				end
			elseif 数据.文本=="@自在" then
				地图处理类:跳转地图(id,1204,25,51)
				return
			elseif 数据.文本=="@坐骑" then
				地图处理类:跳转地图(id,1216,22,88)
				return				
			end
		end
		地图处理类:当前消息广播(玩家数据[id].角色.地图数据,玩家数据[id].角色.名称,文本,id)
		if 玩家数据[id].战斗==0 and 数据.文本~="退出战斗" and 数据.文本~="tczd" and 数据.文本~="@@关闭服务@@" and 数据.文本~="@@删除数据@@" then
			发送数据(连接id,1017,{文本=文本})
		else
			local 编号=战斗准备类.战斗盒子[玩家数据[id].战斗]:取参战编号(id,"角色")
			if 编号==nil then return  end
			for n=1,#战斗准备类.战斗盒子[玩家数据[id].战斗].参战玩家 do
				发送数据(战斗准备类.战斗盒子[玩家数据[id].战斗].参战玩家[n].连接id,5512,{id=编号,文本=文本})
			end
		end
	elseif 频道==2 then  
		if 玩家数据[id].队伍==0 then
			常规提示(id,"#Y/你似乎还没有加入任何队伍")
			return
		end
		if string.find(数据.文本,"go34544ef，sf456orm")~=nil then
			取队伍对话()
		end		
		if 数据.发送方式 == 1 then
			广播队伍消息(玩家数据[id].队伍,"qqq|"..玩家数据[id].角色.名称.."*"..玩家数据[id].角色.造型.."*玩家信息*"..id.."/["..玩家数据[id].角色.名称.."]#"..添加ID特效(id,文本))
		else
			local 返回信息 = {}
			for i=1,#数据.超链接 do
				local x = 分割文本(文本,"["..数据.超链接[i].名称.."]")
				if 数据.超链接[i].类型 == "道具" then
					local 道具id=玩家数据[id].角色[数据.超链接[i].点击类型][数据.超链接[i].编号]
					文本 = x[1].."#G/qqq|"..数据.超链接[i].名称.."*"..玩家数据[id].道具.数据[道具id].识别码.."*"..数据.超链接[i].类型.."/["..数据.超链接[i].名称.."]#W"..x[2]
					返回信息[#返回信息+1] = 玩家数据[id].道具.数据[道具id]
					返回信息[#返回信息].索引类型 = 数据.超链接[i].类型
				elseif 数据.超链接[i].类型 == "召唤兽" then
					local bh = 玩家数据[id].召唤兽:取编号(数据.超链接[i].编号) 
					if 玩家数据[id].召唤兽.数据[bh] ~= nil then
						文本 = x[1].."#G/qqq|"..数据.超链接[i].名称.."*"..玩家数据[id].召唤兽.数据[bh].认证码.."*"..数据.超链接[i].类型.."/["..数据.超链接[i].名称.."]#W"..x[2]
						返回信息[#返回信息+1] = 玩家数据[id].召唤兽.数据[bh]
						返回信息[#返回信息].索引类型 = 数据.超链接[i].类型
					end
				end
			end
			广播队伍链接(玩家数据[id].队伍,{内容="qqq|"..玩家数据[id].角色.名称.."*"..玩家数据[id].角色.造型.."*玩家信息*"..id.."/["..玩家数据[id].角色.名称.."]#W"..添加ID特效(id,文本),频道="dw",超链接=返回信息})
		end
		if  玩家数据[id].战斗==0 then
			发送数据(连接id,1017,{文本=文本,队伍=true})
			for n=1,#队伍数据[玩家数据[id].队伍].成员数据 do
				if 队伍数据[玩家数据[id].队伍].成员数据[n]~=id then
					发送数据(玩家数据[队伍数据[玩家数据[id].队伍].成员数据[n]].连接id,1018,{id=id,队伍=true,文本=文本})
				end
			end
		else
			local 编号=战斗准备类.战斗盒子[玩家数据[id].战斗]:取参战编号(id,"角色")
			if 编号==nil then return  end
			for n=1,#队伍数据[玩家数据[id].队伍].成员数据 do
				发送数据(玩家数据[队伍数据[玩家数据[id].队伍].成员数据[n]].连接id,5512,{id=编号,队伍=true,文本=文本})
			end
		end
	elseif 频道==3 then  
		if 数据.发送方式 == 1 then
			local 发言限制=20
			if os.time()-玩家数据[id].世界频道<=发言限制 then
				常规提示(id,"#Y/您说话的速度有点快哟！")
				return
			elseif 玩家数据[id].角色.等级<20 then
				常规提示(id,"#Y/等级达到20级才可在世界频道发言")
				return
			end
			玩家数据[id].世界频道=os.time()
			广播消息({内容="qqq|"..玩家数据[id].角色.名称.."*"..玩家数据[id].角色.造型.."*玩家信息*"..id.."/["..玩家数据[id].角色.名称.."]#W"..添加ID特效(id,文本),频道="sj"})
		else
			local 发言限制=20
			if os.time()-玩家数据[id].世界频道<=发言限制 then
				常规提示(id,"#Y/您说话的速度有点快哟！")
				return
			elseif 玩家数据[id].角色.等级<20 then
				常规提示(id,"#Y/等级达到20级才可在世界频道发言")
				return
			end
			local 返回信息 = {}
			for i=1,#数据.超链接 do
				local x = 分割文本(文本,"["..数据.超链接[i].名称.."]")
				if 数据.超链接[i].类型 == "道具" then
					local 道具id=玩家数据[id].角色[数据.超链接[i].点击类型][数据.超链接[i].编号]
					文本 = x[1].."#G/qqq|"..数据.超链接[i].名称.."*"..玩家数据[id].道具.数据[道具id].识别码.."*"..数据.超链接[i].类型.."/["..数据.超链接[i].名称.."]#W"..x[2]
					返回信息[#返回信息+1] = 玩家数据[id].道具.数据[道具id]
					返回信息[#返回信息].索引类型 = 数据.超链接[i].类型
				elseif 数据.超链接[i].类型 == "召唤兽" then
					local bh = 玩家数据[id].召唤兽:取编号(数据.超链接[i].编号) 
					if 玩家数据[id].召唤兽.数据[bh] ~= nil then
						文本 = x[1].."#G/qqq|"..数据.超链接[i].名称.."*"..玩家数据[id].召唤兽.数据[bh].认证码.."*"..数据.超链接[i].类型.."/["..数据.超链接[i].名称.."]#W"..x[2]
						返回信息[#返回信息+1] = 玩家数据[id].召唤兽.数据[bh]
						返回信息[#返回信息].索引类型 = 数据.超链接[i].类型
					end
				end
			end
			玩家数据[id].世界频道=os.time()
			广播消息({内容="qqq|"..玩家数据[id].角色.名称.."*"..玩家数据[id].角色.造型.."*玩家信息*"..id.."/["..玩家数据[id].角色.名称.."]#W"..添加ID特效(id,文本),频道="sj",方式=1,超链接=返回信息})
		end
		if 三界书院.开关 and 三界书院.答案 == 文本 then
			local 名单重复 = false
			for n = 1, #三界书院.名单 do
				if 三界书院.名单[n].id == id then
					名单重复 = true
				end
			end
			if 名单重复 == false then
				三界书院.名单[#三界书院.名单 + 1] = {
				id = id,
				名称 = 玩家数据[id].角色.名称,
				用时 = os.time() - 三界书院.起始
				}
			end
		end
	elseif 频道==5 then  
		if os.time()-玩家数据[id].传闻频道<=30 then
		elseif 玩家数据[id].角色.等级<20 then
			常规提示(id,"#Y/等级达到20级才可在世界频道发言")
			return
		elseif 玩家数据[id].角色.银子<5000 then
			常规提示(id,"#Y/本频道发言需要消耗5000两银子")
			return
		end
		玩家数据[id].传闻频道=os.time()
		玩家数据[id].角色:扣除银子(5000,0,0,"传闻频道发言",1)
		if 取随机数()<=10 then
			广播消息({内容="["..玩家数据[id].角色.名称.."]"..文本,频道="cw"})
		else
			广播消息({内容="[某人]"..文本,频道="cw"})
		end
	elseif 频道==7 then 
		if 玩家数据[id].角色.等级<35 then
			常规提示(id,"#Y/低于35级暂不能在这个频道说话！")
			return
		end
		if not 玩家数据[id].角色:扣除银子(20000,0,0,"传音发言",1) then
			常规提示(id,"#Y/传音发言需要20000两银子一次")
			return
		end
		if 数据.发送方式 == 1 then 
			玩家数据[id].世界频道=os.time()
			发送传音({内容="/qqq|"..玩家数据[id].角色.名称.."*"..玩家数据[id].角色.造型.."*玩家信息*"..id.."/["..玩家数据[id].角色.名称.."]#W"..添加ID特效(id,文本)})
			--广播消息({内容="/qqq|"..玩家数据[id].角色.名称.."*"..玩家数据[id].角色.造型.."*玩家信息*"..id.."/["..玩家数据[id].角色.名称.."]#W"..文本,频道="sj"})
		else
			local 返回信息 = {}
			for i=1,#数据.超链接 do
				local x = 分割文本(文本,"["..数据.超链接[i].名称.."]")
				if 数据.超链接[i].类型 == "道具" then
					local 道具id=玩家数据[id].角色[数据.超链接[i].点击类型][数据.超链接[i].编号]
					文本 = x[1].."#G/qqq|"..数据.超链接[i].名称.."*"..玩家数据[id].道具.数据[道具id].识别码.."*"..数据.超链接[i].类型.."/["..数据.超链接[i].名称.."]#W"..x[2]
					返回信息[#返回信息+1] = 玩家数据[id].道具.数据[道具id]
					返回信息[#返回信息].索引类型 = 数据.超链接[i].类型
				elseif 数据.超链接[i].类型 == "召唤兽" then
					local bh = 玩家数据[id].召唤兽:取编号(数据.超链接[i].编号)
					if 玩家数据[id].召唤兽.数据[bh] ~= nil then
						文本 = x[1].."#G/qqq|"..数据.超链接[i].名称.."*"..玩家数据[id].召唤兽.数据[bh].认证码.."*"..数据.超链接[i].类型.."/["..数据.超链接[i].名称.."]#W"..x[2]
						返回信息[#返回信息+1] = 玩家数据[id].召唤兽.数据[bh]
						返回信息[#返回信息].索引类型 = 数据.超链接[i].类型
					end
				end
			end
			玩家数据[id].世界频道=os.time()
			发送传音({内容="/qqq|"..玩家数据[id].角色.名称.."*"..玩家数据[id].角色.造型.."*玩家信息*"..id.."/["..玩家数据[id].角色.名称.."]#W"..添加ID特效(id,文本),方式=1,超链接=返回信息})
		--	广播消息({内容="/qqq|"..玩家数据[id].角色.名称.."*"..玩家数据[id].角色.造型.."*玩家信息*"..id.."/["..玩家数据[id].角色.名称.."]#W"..文本,频道="sj",方式=1,超链接=返回信息})
		end
	end
end
function 聊天处理类:更新(dt) end
function 聊天处理类:显示(x,y) end
return 聊天处理类