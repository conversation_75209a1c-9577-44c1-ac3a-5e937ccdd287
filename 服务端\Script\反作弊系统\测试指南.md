# 跑商验证系统测试指南

## 🧪 测试命令

### 管理员命令（需要权限≥5）

#### 1. 服务器控制台命令
```
@测试验证
```
- 功能：自动找到第一个在线玩家并启动验证
- 输出：显示验证启动状态和验证ID

#### 2. 游戏内聊天命令
```
@测试我的验证
```
- 功能：对当前管理员玩家启动验证测试
- 输出：在游戏内显示验证启动状态

#### 3. 查看验证状态
```
@验证状态
```
- 功能：查看当前玩家的验证状态
- 显示：是否在验证中、是否已通过验证

#### 4. 清除验证状态
```
@清除验证状态
```
- 功能：清除当前玩家的验证状态
- 用途：重置测试环境

#### 5. 重置玩家可疑度
```
@重置可疑度 玩家名称
```
- 功能：重置指定玩家的可疑度评分
- 示例：`@重置可疑度 张三`

#### 6. 查看玩家统计
```
@查看统计 玩家名称
```
- 功能：查看指定玩家的行为统计数据
- 示例：`@查看统计 张三`

## 🎯 测试流程

### 基础功能测试

1. **启动验证测试**
   ```
   步骤1：在控制台输入 @测试验证
   步骤2：观察控制台输出，确认验证启动
   步骤3：检查目标玩家是否收到验证提示
   ```

2. **验证回答测试**
   ```
   步骤1：玩家在聊天框输入 验证 答案
   步骤2：观察验证结果（通过/失败）
   步骤3：检查验证状态是否正确更新
   ```

3. **验证超时测试**
   ```
   步骤1：启动验证但不回答
   步骤2：等待120秒
   步骤3：观察是否自动失败并取消任务
   ```

### 完整跑商测试

1. **正常跑商流程**
   ```
   步骤1：玩家接取跑商任务
   步骤2：完成跑商（银票金额达标）
   步骤3：与NPC对话完成任务
   步骤4：观察是否触发验证（30%概率）
   ```

2. **高频跑商测试**
   ```
   步骤1：让玩家连续完成5次跑商
   步骤2：第6次跑商时应该有80%概率触发验证
   步骤3：测试验证流程是否正常
   ```

## 📋 测试检查清单

### ✅ 验证触发检查
- [ ] 新玩家跑商：30%概率触发验证
- [ ] 连续跑商5次：80%概率触发验证
- [ ] 1小时内跑商3次：触发验证
- [ ] 手动测试命令：100%触发验证

### ✅ 验证类型检查
- [ ] 数字验证码：4位随机数字
- [ ] 选择题：跑商相关问题
- [ ] 计算题：简单数学运算
- [ ] 随机选择：每次验证类型不同

### ✅ 验证回答检查
- [ ] 正确答案：验证通过，继续任务
- [ ] 错误答案：验证失败，重新验证
- [ ] 连续失败3次：取消任务，限制30分钟
- [ ] 验证超时：自动失败处理

### ✅ 系统安全检查
- [ ] 验证期间：玩家操作被锁定
- [ ] 文件缺失：系统不会崩溃
- [ ] 错误处理：所有调用都有pcall保护
- [ ] 日志记录：验证过程有完整日志

### ✅ 管理员功能检查
- [ ] 重置可疑度：功能正常
- [ ] 查看统计：数据显示正确
- [ ] 实时通知：可疑行为及时通知
- [ ] 测试命令：所有测试命令正常工作

## 🐛 常见问题排查

### 问题1：验证不触发
**可能原因：**
- 验证系统未正确加载
- 概率设置过低
- 玩家跑商次数不足

**排查方法：**
```
1. 使用 @测试验证 命令手动触发
2. 检查控制台是否有错误信息
3. 使用 @验证状态 查看当前状态
```

### 问题2：验证界面不显示
**可能原因：**
- 客户端UI系统问题
- 发送数据函数异常

**排查方法：**
```
1. 检查玩家是否收到聊天提示
2. 查看服务器日志是否有发送记录
3. 测试其他NPC对话是否正常
```

### 问题3：验证回答无效
**可能原因：**
- 聊天命令处理异常
- 验证状态不正确

**排查方法：**
```
1. 使用 @验证状态 检查状态
2. 使用 @清除验证状态 重置
3. 重新启动验证测试
```

### 问题4：系统报错
**可能原因：**
- 文件路径错误
- 语法错误
- 依赖缺失

**排查方法：**
```
1. 检查所有文件是否存在
2. 查看控制台错误信息
3. 使用pcall包装的调用应该不会崩溃
```

## 📊 测试报告模板

```
测试时间：____年__月__日
测试人员：________
服务器版本：________

基础功能测试：
□ 验证触发 - 通过/失败
□ 验证显示 - 通过/失败  
□ 验证回答 - 通过/失败
□ 验证超时 - 通过/失败

高级功能测试：
□ 概率触发 - 通过/失败
□ 高频检测 - 通过/失败
□ 管理员命令 - 通过/失败
□ 错误处理 - 通过/失败

发现问题：
1. ________________
2. ________________
3. ________________

建议改进：
1. ________________
2. ________________
3. ________________
```

## 🔧 调试技巧

1. **开启详细日志**
   - 检查 `logs/trade_verification.log`
   - 检查 `logs/admin_alerts.log`

2. **实时监控**
   - 使用控制台命令实时测试
   - 观察玩家反馈和系统响应

3. **分步测试**
   - 先测试单个功能
   - 再测试完整流程
   - 最后测试异常情况

4. **数据验证**
   - 使用统计命令查看数据
   - 确认计数和状态正确
   - 验证时间戳和概率计算
