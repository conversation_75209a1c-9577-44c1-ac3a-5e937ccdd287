-- @Author: 小九呀丶 - QQ：5268416
-- @Date:   2024-05-12 22:27:43
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-06-03 00:02:53
local 副本_通天河 = class()
local sj = 取随机数
local format = string.format
local insert = table.insert
local ceil = math.ceil
local floor = math.floor
local wps = 取物品数据
local typ = type
local random = 取随机数
local npc={}
function 副本_通天河:初始化()
	地图处理类.地图数据[7017]={npc={},单位={},传送圈={}} 
	地图处理类.地图玩家[7017]={}
	地图处理类.地图坐标[7017]=地图处理类.地图坐标[1070]
	地图处理类.地图单位[7017]={}
	地图处理类.单位编号[7017]=1000
	地图处理类.地图数据[7018]={npc={},单位={},传送圈={}} 
	地图处理类.地图玩家[7018]={}
	地图处理类.地图坐标[7018]=地图处理类.地图坐标[1140]
	地图处理类.地图单位[7018]={}
	地图处理类.单位编号[7018]=1000
	地图处理类.地图数据[7019]={npc={},单位={},传送圈={}} 
	地图处理类.地图玩家[7019]={}
	地图处理类.地图坐标[7019]=地图处理类.地图坐标[1116]
	地图处理类.地图单位[7019]={}
	地图处理类.单位编号[7019]=1000
	地图处理类.地图数据[7020]={npc={},单位={},传送圈={}} 
	地图处理类.地图玩家[7020]={}
	地图处理类.地图坐标[7020]=地图处理类.地图坐标[1202]
	地图处理类.地图单位[7020]={}
	地图处理类.单位编号[7020]=1000
end
function 副本_通天河:更改模型(id,模型)
	玩家数据[id].角色.变身数据=模型
	发送数据(玩家数据[id].连接id,37,玩家数据[id].角色.变身数据)
	设置任务1(id,1,玩家数据[id].角色.变身数据,3600)
	地图处理类:更改模型(id,玩家数据[id].角色.变身数据,1)
end
function 副本_通天河:开启副本(id)
	if 玩家数据[id].队伍==0 or 玩家数据[id].队长==false  then
		常规提示(id,"#Y/该任务必须组队完成且由队长领取")
		return
	elseif 取队伍人数(id)<5 and 调试模式==false then
		常规提示(id,"#Y此副本要求队伍人数不低于5人")
		return
	elseif  取等级要求(id,69)==false then
		常规提示(id,"#Y此副本要求角色等级不能低于69级")
		return
	end
	local 队伍id=玩家数据[id].队伍
	for n=1,#队伍数据[队伍id].成员数据 do
		local 临时id=队伍数据[队伍id].成员数据[n]
		if 副本数据.通天河.完成[临时id]~=nil then
			常规提示(id,"#Y"..玩家数据[临时id].角色.名称.."本日已经完成过此副本了")
			return
		elseif 玩家数据[临时id].角色:取任务(710)~=0 then
			常规提示(id,"#Y"..玩家数据[临时id].角色.名称.."正在进行副本任务，无法领取新的副本")
			return
		end
	end
	副本数据.通天河.进行[id]={进程=1,答题={}}
	local 任务id,ZU=取唯一任务(710,id)
	任务数据[任务id]={
	id=任务id,
	起始=os.time(),
	结束=7200,
	玩家id=0,
	DWZ=ZU,
	销毁=true,
	副本重置=true,
	队伍组=ZU,
	副本id=id,
	类型=710
	}
	任务处理类:添加队伍任务(id,任务id,"#Y你开启了通天河副本")
	self:刷新任务710(id)
end
function 副本_通天河:刷新任务710(id) 
	local id = 任务处理类:取副本id(id,710)
	if 副本数据.通天河.进行[id]==nil then
		return
	end
	玩家数据[id].战斗=0
	local 队伍id=玩家数据[id].队伍
	if 副本数据.通天河.进行[id].进程==1 then
		local 地图=7017
		local 任务id=id.."_711_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		销毁=true,
		副本重置=true,
		玩家id=0,
		队伍组={},
		名称="陈关保",
		模型="小毛头",
		行走开关=true,
		x=94,
		y=192,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=711
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_711_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		销毁=true,
		副本重置=true,
		玩家id=0,
		队伍组={},
		名称="一秤金",
		模型="小丫丫",
		行走开关=true,
		x=138,
		y=130,
		方向=1,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=711
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_711_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		销毁=true,
		副本重置=true,
		玩家id=0,
		队伍组={},
		名称="陈铁柱",
		模型="小毛头",
		行走开关=true,
		x=67,
		y=72,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=711
		}
		地图处理类:添加单位(任务id)
		地图处理类:跳转地图(id,7017,143,124) 
	elseif 副本数据.通天河.进行[id].进程==2 then
		local 地图=7017
		local 任务id=id.."_712_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		销毁=true,
		副本重置=true,
		玩家id=0,
		队伍组={},
		名称="灵感大王分身",
		模型="神天兵",
		x=19,
		y=172,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=712
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_712_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		销毁=true,
		副本重置=true,
		玩家id=0,
		队伍组={},
		名称="灵灯",
		模型="灵灯",
		x=22,
		y=176,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=712
		}
		地图处理类:添加单位(任务id)
		local 小孩模型 = {"小毛头","小丫丫"}
		for n=1,#队伍数据[队伍id].成员数据 do
			local 临时id=队伍数据[队伍id].成员数据[n]
			self:更改模型(临时id,小孩模型[math.random(#小孩模型)])
		end
	elseif 副本数据.通天河.进行[id].进程==3 then
		任务处理类:删除副本单位(id,710,711)
		任务处理类:删除副本单位(id,710,712)
		local 任务id=id.."_713_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=7017 
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="唐僧",
		模型="唐僧",
		销毁=true,
		副本重置=true,
		x=100,
		y=92,
		方向=3,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=713
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_713_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=7017 
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="陈家村村长",
		模型="村长",
		销毁=true,
		副本重置=true,
		x=87,
		y=91,
		方向=1,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=713
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_713_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=7017 
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="村民",
		模型="书生",
		销毁=true,
		副本重置=true,
		x=110,
		y=96,
		方向=1,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=713
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_713_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		local 地图=7017 
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="村民",
		模型="樵夫",
		销毁=true,
		副本重置=true,
		x=104,
		y=108,
		方向=2,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=713
		}
		地图处理类:添加单位(任务id)
	elseif 副本数据.通天河.进行[id].进程==4 then
		local 地图=7018 
		local 任务id=id.."_714_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="善财童子",
		模型="小魔头",
		销毁=true,
		副本重置=true,
		x=19,
		y=24,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=714
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_714_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="黑熊精",
		模型="黑熊精",
		销毁=true,
		副本重置=true,
		x=26,
		y=20,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=714
		}
		地图处理类:添加单位(任务id)
		地图处理类:跳转地图(id,7018,25,48)
		local text1 = {"河妖太多，师傅被抓走了","通天河太危险，只好去南海求菩萨了"}
		for n=1,#队伍数据[队伍id].成员数据 do
			local 临时id=队伍数据[队伍id].成员数据[n]
			发送数据(玩家数据[临时id].连接id,6557,{文本=text1,类型="通天河1",字体=nil,音乐=nil,背景=nil,横排显示=true,动画调用=nil})
		end
	elseif 副本数据.通天河.进行[id].进程==5 then
		local 地图=7018
		local 任务id=id.."_715_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="木材",
		模型="普通木材",
		销毁=true,
		副本重置=true,
		x=55,
		y=15,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=715
		}
		地图处理类:添加单位(任务id)
	elseif 副本数据.通天河.进行[id].进程==6 then
		local 地图=7019
		local 任务id=id.."_716_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="金鱼左将军",
		模型="蚌精",
		销毁=true,
		副本重置=true,
		x=99,
		y=58,
		方向=1,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=716
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_716_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="金鱼右将军",
		模型="蚌精",
		销毁=true,
		副本重置=true,
		x=107,
		y=61,
		方向=1,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=716
		}
		地图处理类:添加单位(任务id)
		地图处理类:跳转地图(id,7019,86,70)
		local text1 = {"将竹材交给菩萨后得到了避水珠","你同众人来到了通天河底"}
		for n=1,#队伍数据[队伍id].成员数据 do
			local 临时id=队伍数据[队伍id].成员数据[n]
			发送数据(玩家数据[临时id].连接id,6557,{文本=text1,类型="通天河2",字体=nil,音乐=nil,背景=nil,横排显示=true,动画调用=nil})
		end
	elseif 副本数据.通天河.进行[id].进程==7 then
		local 地图=7020
		local 任务id=id.."_717_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="婴灵",
		模型="幽灵",
		销毁=true,
		副本重置=true,
		x=41,
		y=37,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=717
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_717_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="冤魂",
		模型="野鬼",
		销毁=true,
		副本重置=true,
		x=78,
		y=67,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=717
		}
		地图处理类:添加单位(任务id)
		地图处理类:跳转地图(id,7020,48,112)
		local text1 = {"得益于菩萨避水珠的帮助","你打败了强敌，进入了灵感之腹"}
		for n=1,#队伍数据[队伍id].成员数据 do
			local 临时id=队伍数据[队伍id].成员数据[n]
			发送数据(玩家数据[临时id].连接id,6557,{文本=text1,类型="通天河3",字体=nil,音乐=nil,背景=nil,横排显示=true,动画调用=nil})
		end
	elseif 副本数据.通天河.进行[id].进程==8 then
		任务处理类:删除副本单位(id,710,717)
		local 地图=7020
		local 任务id=id.."_718_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="心",
		模型="钻石",
		销毁=true,
		副本重置=true,
		x=114,
		y=27,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=718
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_718_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="肝",
		模型="钻石",
		销毁=true,
		副本重置=true,
		x=107,
		y=27,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=718
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_718_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="脾",
		模型="钻石",
		销毁=true,
		副本重置=true,
		x=118,
		y=32,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=718
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_718_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="肾",
		模型="钻石",
		销毁=true,
		副本重置=true,
		x=123,
		y=27,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=718
		}
		地图处理类:添加单位(任务id)
		local 任务id=id.."_718_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="肺",
		模型="钻石",
		销毁=true,
		副本重置=true,
		x=114,
		y=22,
		方向=0,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=718
		}
		地图处理类:添加单位(任务id)
	elseif 副本数据.通天河.进行[id].进程==9 then
		任务处理类:删除副本单位(id,710,718)
		local 地图=7020
		local 任务id=id.."_719_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999).."_1_"
		任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		销毁=true,
		副本重置=true,
		结束=3600,
		玩家id=0,
		队伍组={},
		名称="灵感元神",
		模型="帮派妖兽",
		x=171,
		y=121,
		方向=1,
		副本id=id,
		地图编号=地图,
		地图名称=取地图名称(地图),
		小地图名称颜色=3,
		类型=719
		}
		地图处理类:添加单位(任务id)
		local text1 = {"你解救了灵感腹中的孩子","惊动了灵感大王"}
		for n=1,#队伍数据[队伍id].成员数据 do
			local 临时id=队伍数据[队伍id].成员数据[n]
			发送数据(玩家数据[临时id].连接id,6557,{文本=text1,类型="通天河4",字体=nil,音乐=nil,背景=nil,横排显示=true,动画=nil})
		end
	elseif 副本数据.通天河.进行[id].进程==10 then
		地图处理类:跳转地图(id,1070,127,178)
		local 任务id=玩家数据[id].角色:取任务(710)
		local text1={{"祸害一方的灵感大王","倒下了"},{"你将活着的孩子","送回了陈家庄","唐僧师徒也继续西行"},{"通天河  完"}}
		for n=1,#队伍数据[队伍id].成员数据 do
			local 临时id=队伍数据[队伍id].成员数据[n]
			玩家数据[临时id].角色:取消任务(任务id)
			副本数据.通天河.完成[临时id]=true
			成就数据[临时id]:判断进度(临时id,"副本首通","通天河")
			发送数据(玩家数据[临时id].连接id,6557,{文本=text1,类型="通天河结束",字体=nil,音乐=nil,背景=nil,横排显示=nil,动画=nil})
		end
	end
	刷新队伍任务追踪(id)
end
function 副本_通天河:怪物对话内容(id,类型,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	local 副本id = 任务处理类:取副本id(id,710)
	if 副本id == 0 or 副本id ~= id  then
		对话数据.对话="只有创建副本的队长才能和我对话"
	end
	if 副本数据.通天河.进行[副本id].进程==1 then
		if 对话数据.名称=="陈关保" or 对话数据.名称=="一秤金" or 对话数据.名称=="陈铁柱" then
			if 副本数据.通天河.进行[副本id].答题[标识] then
				对话数据.对话 = "你已经在我这里答过题了，去找别的小朋友吧！"
				对话数据.选项 = {"好的好的"}
			else
				local 临时题目 = {
				{"旧事王谢堂前( )，飞入寻常百姓家。",{"燕","雁","鸡","鲲"}},
				{"白日依山( )，黄河入海流。",{"傍","尽","近","逝"}},
				{"蔡徐坤做了多久练习生？",{"一年","一年半","两年","两年半"}},
				{"2+2*2-2=( )",{"4","6","2","8"}},
				{"May the force be with you的中文意思是",{"别太放肆没什么用","愿力量与你同在","五月的力量是你的","我看不懂"}},
				{"西游记中孙悟空在菩提老祖处所习得的技能是？",{"天罡三十六变","地煞七十二变","如来神掌","九阴真经"}},
				}
				local lstk = 临时题目[math.random(#临时题目)]
				对话数据.对话 = lstk[1]
				对话数据.选项 = lstk[2]
			end
		else
			对话数据.对话="发生肾么事了"
		end
	elseif 副本数据.通天河.进行[副本id].进程==2 then
		if 对话数据.名称=="灵感大王分身" then
			对话数据.对话 = "该死的村民终于把童男童女送来了#24"
			对话数据.选项 = {}
		elseif 对话数据.名称=="灵灯" then
			对话数据.对话 = "灵灯发出了不详的光照"
			对话数据.选项 = {"伸手触摸一下"}
		end
	elseif 副本数据.通天河.进行[副本id].进程==3 then
		if 对话数据.名称=="唐僧" then
			对话数据.对话 = "少侠，村子周围出现了很多河妖，你能帮助村民们击退水妖吗？"
			对话数据.选项 = {"没问题","我自身难保先溜了"}
		end
	elseif 副本数据.通天河.进行[副本id].进程==4 then
		if 对话数据.名称=="善财童子" then
			对话数据.对话 = "好啊，又是孙猴子的朋友，想要菩萨先打败我再说吧！"
			对话数据.选项 = {"那就得罪了","我先溜了..."}
		elseif 对话数据.名称=="黑熊精" then
			对话数据.对话 = "这善财童子阻拦你们我也不好多说话.."
			对话数据.选项 = {"。。。。"}
		end
	elseif 副本数据.通天河.进行[副本id].进程==5 then
		if 对话数据.名称=="木材" then
			对话数据.对话 = "一块被不知道谁处理好的木材在地上发着光"
			对话数据.选项 = {"捡起来"}
		end
	elseif 副本数据.通天河.进行[副本id].进程==6 then
		if 对话数据.名称=="金鱼左将军" then
			对话数据.对话 = "你是何人，尽然能安然来到这通天河底！#24"
			对话数据.选项 = {}
		elseif 对话数据.名称=="金鱼右将军" then
			对话数据.对话 = "你是何人，尽然能安然来到这通天河底！#24"
			对话数据.选项 = {"我是来要你命的人","小弟匆忙中不慎闯入"}
		end
	elseif 副本数据.通天河.进行[副本id].进程==7 then
		if 副本数据.通天河.进行[副本id].怪物[对话数据.名称] then
			对话数据.对话 = "我死的好惨#115"
			对话数据.选项 = {"...吓死个人"}
		else
			if 对话数据.名称=="婴灵" then
				对话数据.对话 = "我死的好惨#115"
				对话数据.选项 = {"帮助婴灵解脱","...吓死个人"}
			elseif 对话数据.名称=="冤魂" then
				对话数据.对话 = "我死的好惨#115"
				对话数据.选项 = {"帮助冤魂解脱","...吓死个人"}
			end
		end
	elseif 副本数据.通天河.进行[副本id].进程==8 then
		if 副本数据.通天河.进行[副本id].怪物[对话数据.名称] then
			对话数据.对话 = "我死的好惨#115"
			对话数据.选项 = {"...吓死个人"}
		else
			if 对话数据.名称=="心" then
				对话数据.对话 = "解救灵感大王腹中的童男童女的心"
				对话数据.选项 = {"杀","不知道打不打得过,我先溜了"}
			elseif 对话数据.名称=="肝" then
				对话数据.对话 = "解救灵感大王腹中的童男童女的肝"
				对话数据.选项 = {"杀","不知道打不打得过,我先溜了"}
			elseif 对话数据.名称=="脾" then
				对话数据.对话 = "解救灵感大王腹中的童男童女的脾"
				对话数据.选项 = {"杀","不知道打不打得过,我先溜了"}
			elseif 对话数据.名称=="肾" then
				对话数据.对话 = "解救灵感大王腹中的童男童女的肾"
				对话数据.选项 = {"杀","不知道打不打得过,我先溜了"}
			elseif 对话数据.名称=="肺" then
				对话数据.对话 = "解救灵感大王腹中的童男童女的肺"
				对话数据.选项 = {"杀","不知道打不打得过,我先溜了"}
			end
		end
	elseif 副本数据.通天河.进行[副本id].进程==9 then
		if 对话数据.名称=="灵感元神" then
			对话数据.对话 = "原来是你小子在我腹中搅得我无法安宁，受死吧！"
			对话数据.选项 = {"战就战谁怕谁","大王我错了，我给你跪下了。。"}
		end
	end
	return 对话数据
end
function 副本_通天河:对话事件处理(id,名称,事件,类型)
	local 副本id = 任务处理类:取副本id(id,710)
	if 类型 == 711 and (名称=="陈关保" or 名称 =="一秤金" or 名称=="陈铁柱")  then
		if 事件 == "燕" or 事件 == "尽" or  事件 == "两年半" or  事件 == "4" or  事件 == "愿力量与你同在" or  事件 == "地煞七十二变" then
			副本数据.通天河.进行[副本id].答题[玩家数据[id].地图单位.标识] = 1
			if table_leng(副本数据.通天河.进行[副本id].答题) >= 3 then
				副本数据.通天河.进行[副本id] = {进程=2}
				self:刷新任务710(副本id)
			end
			常规提示(id,"#Y/回答正确！")
		elseif 事件 == "好的好的" then
			return
		else
			常规提示(id,"#Y/太笨啦，不对不对")
		end
	elseif 类型 == 712 and 名称=="灵灯" and  事件=="伸手触摸一下" then
		if 副本数据.通天河.进行[副本id].进程==2 then
			if 任务数据[玩家数据[id].地图单位.标识].战斗~=nil then 常规提示(id,"#Y/对方正在战斗中") return  end
			if 取队伍人数(id)<1  and 调试模式==false then 常规提示(id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[玩家数据[id].地图单位.标识].战斗=true
			战斗准备类:创建战斗(id+0,190201,玩家数据[id].地图单位.标识)
		end
	elseif 类型 == 713 and 名称=="唐僧" and  事件=="没问题" then
		if 副本数据.通天河.进行[副本id].进程==3 then
			副本数据.通天河.进行[副本id] = {进程=4}
			self:刷新任务710(副本id)
		end
	elseif 类型 == 714 and 名称=="善财童子" and  事件=="那就得罪了" then
		if 副本数据.通天河.进行[副本id].进程==4 then
			if 任务数据[玩家数据[id].地图单位.标识].战斗~=nil then 常规提示(id,"#Y/对方正在战斗中") return  end
			if 取队伍人数(id)<1  and 调试模式==false then 常规提示(id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[玩家数据[id].地图单位.标识].战斗=true
			战斗准备类:创建战斗(id+0,190202,玩家数据[id].地图单位.标识)
		end
	elseif 类型 == 715 and 名称=="木材" and  事件=="捡起来" then
		if 副本数据.通天河.进行[副本id].进程==5 then
			if 任务数据[玩家数据[id].地图单位.标识].战斗~=nil then 常规提示(id,"#Y/对方正在战斗中") return  end
			if 取队伍人数(id)<1  and 调试模式==false then 常规提示(id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[玩家数据[id].地图单位.标识].战斗=true
			战斗准备类:创建战斗(id+0,190203,玩家数据[id].地图单位.标识)
		end
	elseif 类型 == 716 and 名称=="金鱼右将军" and  事件=="我是来要你命的人" then
		if 副本数据.通天河.进行[副本id].进程==6 then
			if 任务数据[玩家数据[id].地图单位.标识].战斗~=nil then 常规提示(id,"#Y/对方正在战斗中") return  end
			if 取队伍人数(id)<1  and 调试模式==false then 常规提示(id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[玩家数据[id].地图单位.标识].战斗=true
			战斗准备类:创建战斗(id+0,190204,玩家数据[id].地图单位.标识) 
		end
	elseif 类型 == 717 and 副本数据.通天河.进行[副本id].进程 == 7 then
		if 名称=="婴灵" and 事件=="帮助婴灵解脱" then
			if 副本数据.通天河.进行[副本id].怪物[名称] then 常规提示(id,"#Y/该怪物已被超度！") return  end
			if 任务数据[玩家数据[id].地图单位.标识].战斗~=nil then 常规提示(id,"#Y/对方正在战斗中") return  end
			if 取队伍人数(id)<1  and 调试模式==false then 常规提示(id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[玩家数据[id].地图单位.标识].战斗=true
			战斗准备类:创建战斗(id+0,190209,玩家数据[id].地图单位.标识)
		elseif 名称=="冤魂" and 事件=="帮助冤魂解脱" then
			if 副本数据.通天河.进行[副本id].怪物[名称] then 常规提示(id,"#Y/该怪物已被超度！") return  end
			if 任务数据[玩家数据[id].地图单位.标识].战斗~=nil then 常规提示(id,"#Y/对方正在战斗中") return  end
			if 取队伍人数(id)<1  and 调试模式==false then 常规提示(id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[玩家数据[id].地图单位.标识].战斗=true
			战斗准备类:创建战斗(id+0,190210,玩家数据[id].地图单位.标识)
		end
	elseif 类型 == 718 and 副本数据.通天河.进行[副本id].进程 == 8 then
		if 副本数据.通天河.进行[副本id].怪物[名称] then 常规提示(id,"#Y/该部位已被超度！") return  end
		if 事件=="杀" then
			local ls = {心=190211,肝=190212,脾=190213,肾=190214,肺=190215}
			if ls[名称] ~= nil then
				if 任务数据[玩家数据[id].地图单位.标识].战斗~=nil then 常规提示(id,"#Y/对方正在战斗中") return  end
				if 取队伍人数(id)<1  and 调试模式==false then 常规提示(id,"#Y队伍人数低于3人，无法进入战斗") return  end
				任务数据[玩家数据[id].地图单位.标识].战斗=true
				战斗准备类:创建战斗(id+0,ls[名称],玩家数据[id].地图单位.标识)
			end
		end
	elseif 类型 == 719 and 名称=="灵感元神" and  事件=="战就战谁怕谁" then
		if 副本数据.通天河.进行[副本id].进程==9 then
			if 任务数据[玩家数据[id].地图单位.标识].战斗~=nil then 常规提示(id,"#Y/对方正在战斗中") return  end
			if 取队伍人数(id)<1  and 调试模式==false then 常规提示(id,"#Y队伍人数低于3人，无法进入战斗") return  end
			任务数据[玩家数据[id].地图单位.标识].战斗=true
			战斗准备类:创建战斗(id+0,190216,玩家数据[id].地图单位.标识)
		end
	end
end
function 副本_通天河:战斗胜利处理(id组,战斗类型,任务id)
	local id = id组[1]
	local 副本id = 任务处理类:取副本id(id,710)
	if 副本id == 0 then return end
	if 战斗类型 == 190201 then
		副本数据.通天河.进行[副本id]={进程=3}
		任务数据[任务id].战斗 = nil
		self:刷新任务710(副本id)
		self:完成奖励(id组,战斗类型)
	elseif 战斗类型 == 190202 then
		副本数据.通天河.进行[副本id]={进程=5}
		任务数据[任务id].战斗 = nil
		self:刷新任务710(副本id)
		self:完成奖励(id组,战斗类型)
	elseif 战斗类型 == 190203 then
		副本数据.通天河.进行[副本id]={进程=6}
		任务数据[任务id].战斗 = nil
		self:刷新任务710(副本id)
		self:完成奖励(id组,战斗类型)
	elseif 战斗类型 == 190208 then
		副本数据.通天河.进行[副本id]={进程=7,怪物={}}
		任务数据[任务id].战斗 = nil
		self:刷新任务710(副本id)
		self:完成奖励(id组,战斗类型)
	elseif 战斗类型 == 190209 then 
		任务数据[任务id].战斗 = nil
		if  副本数据.通天河.进行[副本id].进程==7 and 副本数据.通天河.进行[副本id].怪物 then
			副本数据.通天河.进行[副本id].怪物["婴灵"] = 1
		end
		if table_leng(副本数据.通天河.进行[副本id].怪物) >= 2 then
			副本数据.通天河.进行[副本id]={进程=8,怪物={}}
			self:刷新任务710(副本id)
			self:完成奖励(id组,战斗类型)
		end
	elseif 战斗类型 == 190210 then
		任务数据[任务id].战斗 = nil
		if 副本数据.通天河.进行[副本id].进程==7 and 副本数据.通天河.进行[副本id].怪物 then
			副本数据.通天河.进行[副本id].怪物["冤魂"] = 1
		end
		if table_leng(副本数据.通天河.进行[副本id].怪物) >= 2 then
			副本数据.通天河.进行[副本id]={进程=8,怪物={}}
			self:刷新任务710(副本id)
			self:完成奖励(id组,战斗类型)
		end
	elseif 战斗类型 >= 190211 and 战斗类型 <= 190215 then
		任务数据[任务id].战斗 = nil
		if 副本数据.通天河.进行[副本id].进程== 8 and 副本数据.通天河.进行[副本id].怪物 then
			副本数据.通天河.进行[副本id].怪物[任务数据[任务id].名称] = 1
		end
		if table_leng(副本数据.通天河.进行[副本id].怪物) >= 5 then
			副本数据.通天河.进行[副本id]={进程=9}
			self:刷新任务710(副本id)
			self:完成奖励(id组,战斗类型)
		end
	elseif 战斗类型 == 190216 then
		副本数据.通天河.进行[副本id]={进程=10}
		任务数据[任务id].战斗 = nil
		self:刷新任务710(副本id)
		self:完成奖励(id组,战斗类型)
	end
end
function 副本_通天河:完成奖励(id组,战斗类型)
	if 战斗类型 == 190201 then
		local 链接
		for n=1,#id组 do
			local cyid=id组[n]
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(950,1000)
			local 银子=等级*取随机数(90,120)+4000
			玩家数据[cyid].角色:添加经验(经验,"灵感分身")
			玩家数据[cyid].角色:添加储备(银子,"灵感分身",1)
			local 链接
			if 取随机数()<=30 then
				local 奖励参数=取随机数(1,60)
				链接={提示=format("#S(通天河)#G/%s#Y击败了灵感分身，获得了",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y！"}
				if 奖励参数<=2 then
					local 名称="召唤兽内丹"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=25 then
					local 名称="超级金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接)
				elseif 奖励参数<=35 then
					local 名称="魔兽要诀"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=45 then
					local 名称="未激活的符石"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,2),nil,链接)

				elseif 奖励参数<=55 then
					local 名称="金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				else
					local 名称=取宝石()
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				end
			end
		end
	elseif 战斗类型 == 190202 then
		for n=1,#id组 do
			local cyid=id组[n]
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(950,1000)
			local 银子=等级*取随机数(90,120)+4000
			玩家数据[cyid].角色:添加经验(经验,"通天河红孩儿")
			玩家数据[cyid].角色:添加储备(银子,"通天河红孩儿",1)
			local 链接
			if 取随机数()<=30 then
				local 奖励参数=取随机数(1,60)
				链接={提示=format("#S(通天河)#G/%s#Y险胜善财童子，获得了",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y！"}
				if 奖励参数<=2 then
					local 名称="未激活的符石"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,2),nil,链接)
				elseif 奖励参数<=15 then
					local 名称="超级金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=25 then
					local 名称="召唤兽内丹"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接)
				elseif 奖励参数<=35 then
					local 名称="魔兽要诀"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=50 then
					local 名称="金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				else
					local 名称=取宝石()
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				end
			end
		end
	elseif 战斗类型 == 190203 then
		for n=1,#id组 do
			local cyid=id组[n]
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(950,1000)
			local 银子=等级*取随机数(90,120)+4000
			玩家数据[cyid].角色:添加经验(经验,"通天河木材")
			玩家数据[cyid].角色:添加储备(银子,"通天河木材",1)
		end
	elseif 战斗类型 == 190208 then
		for n=1,#id组 do
			local cyid=id组[n]
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(950,1000)
			local 银子=等级*取随机数(90,120)+4000
			玩家数据[cyid].角色:添加经验(经验,"通天河金鱼")
			玩家数据[cyid].角色:添加储备(银子,"通天河金鱼",1)
			local 链接
			if 取随机数()<=5 then
				local 奖励参数=取随机数(1,100)
				链接={提示=format("#S(通天河)#G/%s#Y击败了灵感手下的金鱼将军，获得了",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y一个！"}
				if 奖励参数<=1 then
					local 名称="神兜兜"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=15 then
					local 名称="超级金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=25 then
					local 名称="召唤兽内丹"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接)
				elseif 奖励参数<=35 then
					local 名称="未激活的符石"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,2),nil,链接)
				elseif 奖励参数<=45 then
					local 名称="魔兽要诀"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				else
					local 名称=取宝石()
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,3),nil,链接)
				end
			end
		end
	elseif 战斗类型 == 190209 then
		for n=1,#id组 do
			local cyid=id组[n]
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(950,1000)
			local 银子=等级*取随机数(90,120)+4000
			玩家数据[cyid].角色:添加经验(经验,"通天河婴灵")
			玩家数据[cyid].角色:添加储备(银子,"通天河婴灵",1)
			local 链接
			if 取随机数()<=30 then
				local 奖励参数=取随机数(1,60)
				链接={提示=format("#S(通天河)#G/%s#Y超度了婴灵，获得了",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y一个！"}
				if 奖励参数<=10 then
					local 名称="召唤兽内丹"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接)
				elseif 奖励参数<=15 then
					local 名称="超级金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=35 then
					local 名称="魔兽要诀"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=45 then
					local 名称="未激活的符石"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,2),nil,链接)
				else
					local 名称=取宝石()
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				end
			end
		end
	elseif 战斗类型 == 190210 then
		for n=1,#id组 do
			local cyid=id组[n]
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(1000,1200)
			local 银子=等级*取随机数(100,120)+5000
			玩家数据[cyid].角色:添加经验(经验,"通天河冤魂")
			玩家数据[cyid].角色:添加储备(银子,"通天河冤魂",1)
			if 取随机数()<=30 then
				local 奖励参数=取随机数(1,60)
				链接={提示=format("#S(通天河)#G/%s#Y超度了冤魂，获得了",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y一个！"}
				if 奖励参数<=10 then
					玩家数据[cyid].道具:给予超链接书铁(cyid,{6,8},nil,链接)
				elseif 奖励参数<=15 then
					local 名称="超级金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=25 then
					local 名称="高级召唤兽内丹"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接)
				elseif 奖励参数<=35 then
					local 名称="高级魔兽要诀"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=45 then
					local 名称="未激活的符石"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,3),nil,链接)
				else
					local 名称=取宝石()
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,3),nil,链接)
				end
			end
		end
	elseif 战斗类型 == 190211 then
		for n=1,#id组 do
			local cyid=id组[n]
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(950,1000)
			local 银子=等级*取随机数(90,120)+4000
			玩家数据[cyid].角色:添加经验(经验,"通天河心")
			玩家数据[cyid].角色:添加储备(银子,"通天河心",1)
			local 链接
			if 取随机数()<=30 then
				local 奖励参数=取随机数(1,60)
				链接={提示=format("#S(通天河)#G/%s#Y解放了孩童之心，获得了",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y一个！"}
				if 奖励参数<=10 then
					local 名称="召唤兽内丹"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接)
				elseif 奖励参数<=15 then
					local 名称="超级金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)	
				elseif 奖励参数<=35 then
					local 名称="魔兽要诀"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=45 then
					local 名称="未激活的符石"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,2),nil,链接)

				else
					local 名称=取宝石()
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				end
			end
		end
	elseif 战斗类型 == 190212 then
		for n=1,#id组 do
			local cyid=id组[n]
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(950,1000)
			local 银子=等级*取随机数(90,120)+4000
			玩家数据[cyid].角色:添加经验(经验,"通天河肝")
			玩家数据[cyid].角色:添加储备(银子,"通天河肝",1)
			local 链接
			if 取随机数()<=30 then
				local 奖励参数=取随机数(1,60)
				链接={提示=format("#S(通天河)#G/%s#Y超度了孩童之肝，获得了",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y一个！"}
				if 奖励参数<=10 then
					local 名称="召唤兽内丹"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接)
				elseif 奖励参数<=15 then
					local 名称="超级金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)	
				elseif 奖励参数<=35 then
					local 名称="魔兽要诀"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=45 then
					local 名称="未激活的符石"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,2),nil,链接)

				else
					local 名称=取宝石()
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				end
			end
		end
	elseif 战斗类型 == 190213 then
		for n=1,#id组 do
			local cyid=id组[n]
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(950,1000)
			local 银子=等级*取随机数(90,120)+4000
			玩家数据[cyid].角色:添加经验(经验,"通天河肾")
			玩家数据[cyid].角色:添加储备(银子,"通天河肾",1)
			local 链接
			if 取随机数()<=30 then
				local 奖励参数=取随机数(1,60)
				链接={提示=format("#S(通天河)#G/%s#Y超度了孩童之肾，获得了",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y一个！"}
				if 奖励参数<=10 then
					local 名称="召唤兽内丹"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接)
				elseif 奖励参数<=15 then
					local 名称="超级金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)	
				elseif 奖励参数<=35 then
					local 名称="魔兽要诀"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=45 then
					local 名称="未激活的符石"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,2),nil,链接)

				else
					local 名称=取宝石()
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				end
			end
		end
	elseif 战斗类型 == 190214 then
		for n=1,#id组 do
			local cyid=id组[n]
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(950,1000)
			local 银子=等级*取随机数(90,120)+4000
			玩家数据[cyid].角色:添加经验(经验,"通天河脾")
			玩家数据[cyid].角色:添加储备(银子,"通天河脾",1)
			local 链接
			if 取随机数()<=30 then
				local 奖励参数=取随机数(1,60)
				链接={提示=format("#S(通天河)#G/%s#Y超度了孩童之脾，获得了",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y一个！"}
				if 奖励参数<=10 then
					local 名称="召唤兽内丹"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接)
				elseif 奖励参数<=15 then
					local 名称="超级金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)	
				elseif 奖励参数<=35 then
					local 名称="魔兽要诀"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=45 then
					local 名称="未激活的符石"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,2),nil,链接)

				else
					local 名称=取宝石()
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				end
			end
		end
	elseif 战斗类型 == 190215 then
		for n=1,#id组 do
			local cyid=id组[n]
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(950,1000)
			local 银子=等级*取随机数(90,120)+4000
			玩家数据[cyid].角色:添加经验(经验,"通天河肺")
			玩家数据[cyid].角色:添加储备(银子,"通天河肺",1)
			local 链接
			if 取随机数()<=30 then
				local 奖励参数=取随机数(1,60)
				链接={提示=format("#S(通天河)#G/%s#Y超度了孩童之肺，获得了",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y一个！"}
				if 奖励参数<=10 then
					local 名称="召唤兽内丹"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接)
				elseif 奖励参数<=15 then
					local 名称="超级金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)	
				elseif 奖励参数<=35 then
					local 名称="魔兽要诀"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=45 then
					local 名称="未激活的符石"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,2),nil,链接)
				else
					local 名称=取宝石()
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				end
			end
		end
	elseif 战斗类型 == 190216 then
		local stid=取随机数(1,#id组)
		stid=id组[stid]
		local 链接={提示=format("#S(通天河)#G/%s#R击败了灵感大王，孩童们被解救，获得了村民送出的",玩家数据[stid].角色.名称),频道="xt",结尾="#R！#80"}
		玩家数据[stid].道具:给予超链接书铁(stid,{6,8},nil,链接)
		for n=1,#id组 do
			local cyid=id组[n]
			玩家数据[cyid].角色:添加积分(100,"副本积分")
			local 等级=玩家数据[cyid].角色.等级
			local 经验=等级*取随机数(2900,3000)
			local 银子=等级*取随机数(695,700)+4000
			玩家数据[cyid].角色:添加经验(经验,"通天河灵感大王")
			玩家数据[cyid].角色:添加储备(银子,"通天河灵感大王",1)
			local 链接
			if 取随机数()<=100 then
				local 奖励参数=取随机数(1,60)
				链接={提示=format("#S(通天河)#G/%s#Y击败了灵感大王，获得了",玩家数据[cyid].角色.名称),频道="xt",结尾="#Y一个！"}
				if 奖励参数<=1 then
					local 名称="高级召唤兽内丹"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,nil,nil,链接)
				elseif 奖励参数<=15 then
					local 名称="超级金柳露"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=25 then
					local 名称="高级魔兽要诀"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				elseif 奖励参数<=45 then
					local 名称="未激活的符石"
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,取随机数(1,3),nil,链接)
				else
					local 名称=取宝石()
					玩家数据[cyid].道具:给予超链接道具(cyid,名称,1,nil,链接)
				end
			end
		end
	else 
		return
	end
end
function 副本_通天河:任务说明(玩家id,任务id)
	local 说明 = {}
	local 副本id=任务数据[任务id].副本id
	if 副本数据.通天河.进行[副本id]==nil then
		说明={"通天河","#L您的副本已经完成"}
	else
		local 进程=副本数据.通天河.进行[副本id].进程
		if 进程==1 then
			说明={"通天河",format("#L先在庄里逛逛，和".."#Y/qqq|陈关保*7017*临时npc*94*192/陈关保".."#L、".."#Y/qqq|一秤金*7017*临时npc*138*130/一秤金".."#L以及".."#Y/qqq|陈铁柱*7017*临时npc*67*72/陈铁柱".."#L打声招呼吧。(剩余时间%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==2 then
			说明={"通天河",format("#L此时灵感大王已经进入庄内了,前去触摸他面前的".."#Y/qqq|灵灯*7017*临时npc*19*172/灵灯".."#L打探一番吧。触摸(剩余时间%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==3 then
			说明={"通天河",format("#L庄内似乎冲出了很多河妖，快去找".."#Y/qqq|唐僧*7017*临时npc*100*92/唐僧".."#L他似乎有话对你说。(剩余时间%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==4 then
			说明={"通天河",format("#L到了南海".."#Y/qqq|善财童子*7018*临时npc*19*24/善财童子".."#L挡住了你的去路。(剩余时间%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==5 then
			说明={"通天河",format("#L击败了善财童子，从他口中得知菩萨需要，".."#Y/qqq|木材*7018*临时npc*55*15/木材".."#L,快去帮菩萨找一些来吧。(剩余时间%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==6 then
			说明={"通天河",format("#L得到了菩萨的宝物，来到了通天河底。向面前的".."#Y/qqq|金鱼右将军*7019*临时npc*107*61/金鱼右将军".."#L打探一番情报(此处将进行5场连续战斗)。(剩余时间%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==7 then
			说明={"通天河",format("#L击败了金鱼将军，却被吸入灵感之腹。远处有不少".."#Y/qqq|冤魂*7020*临时npc*78*67/冤魂".."#L和".."#Y/qqq|婴灵*7020*临时npc*41*37/婴灵".."#L,只好先超度他们了。(剩余时间%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==8 then
			说明={"通天河",format("#L在不远处好像有灵感吃下孩童的".."#Y/qqq|心*7020*临时npc*114*27/心、肝、脾、肾、肺".."#L,快去解救未来得及消化的孩童。(剩余时间%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==9 then
			说明={"通天河",format("#L你惊动了".."#Y/qqq|灵感元神*7020*临时npc*171*121/灵感元神".."#L,看来只能同他决一死战了。(剩余时间%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		elseif 进程==10 then
			说明={"通天河",format("#L通天河完(剩余时间%s分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
		end
	end
	return 说明
end
function 怪物属性:通天河灵灯(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级,"魔王寨")
	local 武器属性=取150角色武器("神天兵")
	战斗单位[1]={
	名称="灵感分身"
	,模型="神天兵"
	,主怪=true
	,不可封印=true
	,角色=true
	,开场发言="你们以为我只有一个人吗#24"
	,武器={名称=武器属性.武器,级别限制=武器属性.级别,子类=武器属性.子类}
	,染色方案=Qu角色属性["神天兵"].染色方案
	,染色组={[1]=取随机数(1,6),[2]=取随机数(1,6),[3]=取随机数(1,6)}
	,等级=等级
	,伤害=等级*12+500
	,气血=等级*等级
	,法伤=等级*8
	,速度=等级*4.5
	,防御=等级*4
	,法防=等级*3.8
	,躲闪=等级*4
	,技能={"高级必杀"}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	return 战斗单位
end
function 怪物属性:通天河善财童子(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)-2
	战斗单位[1]={名称=任务数据[任务id].名称,模型=任务数据[任务id].模型,显示饰品=任务数据[任务id].显示饰品,伤害=等级*7+500,气血=等级*30,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
	防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"高级感知"},主动技能=取随机法术(5)}
	等级=等级-10
	local 模型范围={"净瓶女娲"}
	local 名称范围={"炎上","润下","稼穑"}
	local 模型=模型范围[取随机数(1,#模型范围)]
	local 名称=名称范围[取随机数(1,#名称范围)]
	local 技能范围 = {{"烟雨剑法","善恶有报"},{"三昧真火","地狱烈火","飞砂走石"},{"龙卷雨击","龙腾","龙吟"},{"落叶萧萧","雷击","奔雷咒"},{"失心符","定身符","反间之计"},{"推气过宫"},{"天雷斩"}}
	local 技能随机=技能范围[取随机数(1,#技能范围)]
	for i=2,8 do
		模型=模型范围[取随机数(1,#模型范围)]
		名称=名称范围[取随机数(1,#名称范围)]
		技能随机=技能范围[取随机数(1,#技能范围)]
		战斗单位[i]={名称=名称,模型=模型,显示饰品=true,伤害=等级*5+500,气血=等级*30,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
		防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"魔之心","法术连击"},主动技能=取随机法术(5)}
	end
	return 战斗单位
end
function 怪物属性:通天河木材(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)-2
	战斗单位[1]={名称="竹精",模型="泡泡",显示饰品=任务数据[任务id].显示饰品,伤害=等级*7+500,气血=等级*30,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
	防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"高级感知"},主动技能=取随机法术(5)}
	等级=等级-10
	local 模型范围={"泡泡"}
	local 名称范围={"竹精"}
	local 模型=模型范围[取随机数(1,#模型范围)]
	local 名称=名称范围[取随机数(1,#名称范围)]
	local 技能范围 = {{"烟雨剑法","善恶有报"},{"三昧真火","地狱烈火","飞砂走石"},{"龙卷雨击","龙腾","龙吟"},{"落叶萧萧","雷击","奔雷咒"},{"失心符","定身符","反间之计"},{"推气过宫"},{"天雷斩"}}
	local 技能随机=技能范围[取随机数(1,#技能范围)]
	for i=2,5 do
		模型=模型范围[取随机数(1,#模型范围)]
		名称=名称范围[取随机数(1,#名称范围)]
		技能随机=技能范围[取随机数(1,#技能范围)]
		战斗单位[i]={名称=名称,模型=模型,显示饰品=true,伤害=等级*5+500,气血=等级*30,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
		防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"魔之心","法术连击"},主动技能=取随机法术(5)}
	end
	return 战斗单位
end
function 怪物属性:取通天金鱼将军1信息(任务id,玩家id)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)-2
	战斗单位[1]={名称="红色鱼精",模型="野鬼",显示饰品=任务数据[任务id].显示饰品,变异=true,伤害=等级*10+500,气血=等级*等级,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
	防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"高级感知"},主动技能=取随机法术(5)}
	等级=等级
	local 模型范围={"蚌精","龟丞相"}
	local 名称范围={"通天妖将","通天师爷"}
	local 模型=模型范围[取随机数(1,#模型范围)]
	local 名称=名称范围[取随机数(1,#名称范围)]
	local 技能范围 = {{"烟雨剑法","善恶有报"},{"三昧真火","地狱烈火","飞砂走石"},{"龙卷雨击","龙腾","龙吟"},{"落叶萧萧","雷击","奔雷咒"},{"失心符","定身符","反间之计"},{"推气过宫"},{"天雷斩"}}
	local 技能随机=技能范围[取随机数(1,#技能范围)]
	for i=2,5 do
		模型=模型范围[取随机数(1,#模型范围)]
		名称=名称范围[取随机数(1,#名称范围)]
		技能随机=技能范围[取随机数(1,#技能范围)]
		战斗单位[i]={名称=名称,模型=模型,显示饰品=true,伤害=等级*8+500,气血=等级*等级*0.8,法伤=等级*4+300,速度=等级*3.5,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
		防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"魔之心","法术连击"},主动技能=取随机法术(5)}
	end
	return 战斗单位
end
function 怪物属性:取通天金鱼将军2信息(任务id,玩家id)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)-2
	战斗单位[1]={名称="黄色鱼精",模型="地狱战神",显示饰品=任务数据[任务id].显示饰品,变异=true,伤害=等级*10+500,气血=等级*等级,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
	防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"高级感知"},主动技能=取随机法术(5)}
	等级=等级
	local 模型范围={"虾兵","龟丞相"}
	local 名称范围={"通天妖将","通天师爷"}
	local 模型=模型范围[取随机数(1,#模型范围)]
	local 名称=名称范围[取随机数(1,#名称范围)]
	local 技能范围 = {{"烟雨剑法","善恶有报"},{"三昧真火","地狱烈火","飞砂走石"},{"龙卷雨击","龙腾","龙吟"},{"落叶萧萧","雷击","奔雷咒"},{"失心符","定身符","反间之计"},{"推气过宫"},{"天雷斩"}}
	local 技能随机=技能范围[取随机数(1,#技能范围)]
	for i=2,5 do
		模型=模型范围[取随机数(1,#模型范围)]
		名称=名称范围[取随机数(1,#名称范围)]
		技能随机=技能范围[取随机数(1,#技能范围)]
		战斗单位[i]={名称=名称,模型=模型,显示饰品=true,伤害=等级*8+500,气血=等级*等级*0.8,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
		防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"魔之心","法术连击"},主动技能=取随机法术(5)}
	end
	return 战斗单位
end
function 怪物属性:取通天金鱼将军3信息(任务id,玩家id)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)-2
	战斗单位[1]={名称="黑色鱼精",模型="鬼将",显示饰品=任务数据[任务id].显示饰品,变异=true,伤害=等级*8+500,气血=等级*等级,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
	防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"高级感知"},主动技能=取随机法术(5)}
	等级=等级
	local 模型范围={"虾兵","龟丞相"}
	local 名称范围={"通天妖将","通天师爷"}
	local 模型=模型范围[取随机数(1,#模型范围)]
	local 名称=名称范围[取随机数(1,#名称范围)]
	local 技能范围 = {{"烟雨剑法","善恶有报"},{"三昧真火","地狱烈火","飞砂走石"},{"龙卷雨击","龙腾","龙吟"},{"落叶萧萧","雷击","奔雷咒"},{"失心符","定身符","反间之计"},{"推气过宫"},{"天雷斩"}}
	local 技能随机=技能范围[取随机数(1,#技能范围)]
	for i=2,5 do
		模型=模型范围[取随机数(1,#模型范围)]
		名称=名称范围[取随机数(1,#名称范围)]
		技能随机=技能范围[取随机数(1,#技能范围)]
		战斗单位[i]={名称=名称,模型=模型,显示饰品=true,伤害=等级*5+500,气血=等级*等级*0.7,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
		防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"魔之心","法术连击"},主动技能=取随机法术(5)}
	end
	return 战斗单位
end
function 怪物属性:取通天金鱼将军4信息(任务id,玩家id)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)-2
	战斗单位[1]={名称="白色鱼精",模型="鲛人",显示饰品=任务数据[任务id].显示饰品,变异=true,伤害=等级*11+500,气血=等级*等级,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
	防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"高级感知"},主动技能=取随机法术(5)}
	等级=等级
	local 模型范围={"蟹将","龟丞相"}
	local 名称范围={"通天妖将","通天师爷"}
	local 模型=模型范围[取随机数(1,#模型范围)]
	local 名称=名称范围[取随机数(1,#名称范围)]
	local 技能范围 = {{"烟雨剑法","善恶有报"},{"三昧真火","地狱烈火","飞砂走石"},{"龙卷雨击","龙腾","龙吟"},{"落叶萧萧","雷击","奔雷咒"},{"失心符","定身符","反间之计"},{"推气过宫"},{"天雷斩"}}
	local 技能随机=技能范围[取随机数(1,#技能范围)]
	for i=2,5 do
		模型=模型范围[取随机数(1,#模型范围)]
		名称=名称范围[取随机数(1,#名称范围)]
		技能随机=技能范围[取随机数(1,#技能范围)]
		战斗单位[i]={名称=名称,模型=模型,显示饰品=true,伤害=等级*8+500,气血=等级*等级*0.9,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
		防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"魔之心","法术连击"},主动技能=取随机法术(5)}
	end
	return 战斗单位
end
function 怪物属性:取通天金鱼将军5信息(任务id,玩家id)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)-2
	战斗单位[1]={名称="蓝色鱼精",模型="蚌精",显示饰品=任务数据[任务id].显示饰品,变异=true,伤害=等级*10+500,气血=等级*等级,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
	防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"高级感知"},主动技能=取随机法术(5)}
	等级=等级
	local 模型范围={"蟹将","龟丞相"}
	local 名称范围={"通天妖将","通天师爷"}
	local 模型=模型范围[取随机数(1,#模型范围)]
	local 名称=名称范围[取随机数(1,#名称范围)]
	local 技能范围 = {{"烟雨剑法","善恶有报"},{"三昧真火","地狱烈火","飞砂走石"},{"龙卷雨击","龙腾","龙吟"},{"落叶萧萧","雷击","奔雷咒"},{"失心符","定身符","反间之计"},{"推气过宫"},{"天雷斩"}}
	local 技能随机=技能范围[取随机数(1,#技能范围)]
	for i=2,5 do
		模型=模型范围[取随机数(1,#模型范围)]
		名称=名称范围[取随机数(1,#名称范围)]
		技能随机=技能范围[取随机数(1,#技能范围)]
		战斗单位[i]={名称=名称,模型=模型,显示饰品=true,伤害=等级*8+500,气血=等级*等级*0.9,法伤=等级*4+300,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,攻击修炼=math.floor(取人物修炼等级上限(等级)),
		防御修炼=math.floor(取人物修炼等级上限(等级)),法术修炼=math.floor(取人物修炼等级上限(等级)),抗法修炼=math.floor(取人物修炼等级上限(等级)),技能={"魔之心","法术连击"},主动技能=取随机法术(5)}
	end
	return 战斗单位
end
function 怪物属性:通天河冤魂(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级,"地府","固伤")
	战斗单位[1]={
	名称="冤魂"
	,模型="野鬼"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 17)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"高级防御"}
	,修炼 = {物抗=9,法抗=9,攻修=9}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"地府","固伤")
	战斗单位[2]={
	名称="见欲"
	,模型="蜃气妖"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 14)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"地府","固伤")
	战斗单位[3]={
	名称="听欲"
	,模型="蜃气妖"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 14)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"神木林")
	战斗单位[4]={
	名称="香欲"
	,模型="藤蔓妖花"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 14)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"龙宫")
	战斗单位[5]={
	名称="味欲"
	,模型="藤蔓妖花"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 14)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"高级魔之心"}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"大唐官府")
	战斗单位[6]={
	名称="触欲"
	,模型="夜罗刹"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 14)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"高级夜战","高级敏捷","高级偷袭"}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"魔王寨")
	战斗单位[7]={
	名称="意欲"
	,模型="野鬼"
	,变异=true
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 14)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"高级法术暴击"}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	return 战斗单位
end
function 怪物属性:通天河婴灵(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级,"阴曹地府","固伤")
	战斗单位[1]={
	名称="婴灵"
	,模型="幽灵"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 10)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"高级防御"}
	,修炼 = {物抗=9,法抗=9,攻修=9}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"五庄观")
	战斗单位[2]={
	名称="喜"
	,模型="葫芦宝贝"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"高级夜战","高级敏捷","高级偷袭"}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"五庄观")
	战斗单位[3]={
	名称="乐"
	,模型="葫芦宝贝"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"五庄观")
	战斗单位[4]={
	名称="爱"
	,模型="葫芦宝贝"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"大唐官府")
	战斗单位[5]={
	名称="怒"
	,模型="炎魔神"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"高级夜战","高级敏捷","高级偷袭"}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"大唐官府")
	战斗单位[6]={
	名称="恶"
	,模型="炎魔神"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"高级夜战","高级敏捷","高级偷袭"}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"化生寺")
	战斗单位[7]={
	名称="哀"
	,模型="野鬼"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*4)
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"阴曹地府")
	战斗单位[8]={
	名称="欲"
	,模型="野鬼"
	,变异=true
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	return 战斗单位
end
function 怪物属性:通天河肺(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级,"狮驼岭")
	战斗单位[1]={
	名称="肺之守卫"
	,模型="金身罗汉"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 17)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,修炼 = {物抗=3,法抗=3,攻修=0}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级)
	战斗单位[2]={
	名称="肺液"
	,模型="虾兵"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 14)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级)
	战斗单位[3]={
	名称="肺液"
	,模型="虾兵"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 14)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级)
	战斗单位[4]={
	名称="肺液"
	,模型="虾兵"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 14)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,修炼 = {物抗=3,法抗=3,攻修=0}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"阴曹地府")
	战斗单位[5]={
	名称="肺液"
	,模型="虾兵"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 14)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	return 战斗单位
end
function 怪物属性:通天河肝(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级,"魔王寨")
	战斗单位[1]={
	名称="肝之守卫"
	,模型="野鬼"
	,等级=等级
	,变异=true
	,气血 = math.floor(sx.属性.气血 * 10)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,修炼 = {物抗=3,法抗=3,攻修=0}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级)
	战斗单位[2]={
	名称="肝火"
	,模型="凤凰"
	,炫彩=取染色id("凤凰")
	,炫彩组=取炫彩染色("番茄红")
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级)
	战斗单位[3]={
	名称="肝火"
	,模型="凤凰"
	,炫彩=取染色id("凤凰")
	,炫彩组=取炫彩染色("番茄红")
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级)
	战斗单位[4]={
	名称="肝火"
	,模型="凤凰"
	,炫彩=取染色id("凤凰")
	,炫彩组=取炫彩染色("番茄红")
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,修炼 = {物抗=3,法抗=3,攻修=0}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"狮驼岭")
	战斗单位[5]={
	名称="肝火"
	,模型="凤凰"
	,炫彩=取染色id("凤凰")
	,炫彩组=取炫彩染色("番茄红")
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	return 战斗单位
end
function 怪物属性:通天河肾(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级,"龙宫")
	战斗单位[1]={
	名称="肾之守卫"
	,模型="幽灵"
	,等级=等级
	,炫彩=取染色id("幽灵")
	,炫彩组=取炫彩染色("冷灰")
	,气血 = math.floor(sx.属性.气血 * 10)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,修炼 = {物抗=3,法抗=3,攻修=0}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级)
	战斗单位[2]={
	名称="肾水"
	,模型="蛟龙"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级)
	战斗单位[3]={
	名称="肾水"
	,模型="蛟龙"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级)
	战斗单位[4]={
	名称="肾水"
	,模型="蛟龙"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,修炼 = {物抗=3,法抗=3,攻修=0}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"龙宫")
	战斗单位[5]={
	名称="肾水"
	,模型="蛟龙"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	return 战斗单位
end
function 怪物属性:通天河脾(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级,"大唐官府")
	战斗单位[1]={
	名称="脾之守卫"
	,模型="鬼将"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 10)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,修炼 = {物抗=3,法抗=3,攻修=0}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级)
	战斗单位[2]={
	名称="脾气"
	,模型="夜罗刹"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级)
	战斗单位[3]={
	名称="脾气"
	,模型="夜罗刹"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级)
	战斗单位[4]={
	名称="脾气"
	,模型="夜罗刹"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,修炼 = {物抗=3,法抗=3,攻修=0}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"神木林")
	战斗单位[5]={
	名称="脾气"
	,模型="夜罗刹"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 8)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	return 战斗单位
end
function 怪物属性:通天河心(任务id,玩家id,序号)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local sx = self:取属性(等级,"五庄观")
	战斗单位[1]={
	名称="心之守卫"
	,模型="曼珠沙华"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 18)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,修炼 = {物抗=3,法抗=3,攻修=0}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级)
	战斗单位[2]={
	名称="心血"
	,模型="龙龟"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 15)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级)
	战斗单位[3]={
	名称="心血"
	,模型="龙龟"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 15)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	sx = self:取属性(等级)
	战斗单位[4]={
	名称="心血"
	,模型="龙龟"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 15)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,技能={"感知"}
	,修炼 = {物抗=3,法抗=3,攻修=0}
	,门派=sx.门派
	,主动技能=Q_门派法术[sx.门派]
	}
	sx = self:取属性(等级,"方寸山")
	战斗单位[5]={
	名称="心血"
	,模型="龙龟"
	,等级=等级
	,气血 = math.floor(sx.属性.气血 * 15)
	,伤害 = math.floor(sx.属性.伤害)
	,法伤 = math.floor(sx.属性.法伤)
	,速度 = math.floor(sx.属性.速度)
	,固定伤害 = math.floor(等级*4)
	,治疗能力 = math.floor(等级*3)
	,主动技能=sx.技能组
	}
	return 战斗单位
end
function 怪物属性:取通天灵感大王信息(任务id,玩家id)
	local 战斗单位={}
	local 等级=取队伍平均等级(玩家数据[玩家id].队伍,玩家id)
	local 模型范围={"神天兵"}
	local 名称范围={"灵感大王"}
	local 模型=模型范围[取随机数(1,#模型范围)]
	local 名称=名称范围[取随机数(1,#名称范围)]
	战斗单位[1]={名称=名称,模型=模型,角色=true,武器=取武器数据("五虎断魂",140),饰品=true,变异=true,伤害=等级*10+800,气血=等级*100,法伤=等级*7+500,速度=等级*4,防御=等级*3,法防=等级*3,躲闪=等级*4,魔法=200000,等级=等级,技能={"高级感知"},主动技能=取随机法术(5)}
	for i=2,5 do
		战斗单位[i]={
		名称="灵感分身"
		,模型="神天兵"
		,角色=true
		,武器=取武器数据("五虎断魂",100)
		,伤害=等级*15+200
		,气血=等级*300
		,法伤=等级*4+300
		,速度=等级*4.5
		,防御=等级*4
		,法防=等级*3
		,躲闪=等级*4
		,魔法=200000
		,等级=等级
		,技能={"高级感知"}
		,主动技能=取随机法术(5)
		}
	end
	等级=等级-10
	local 模型范围={"蟹将","龟丞相"}
	local 名称范围={"通天妖将","通天师爷"}
	local 模型=模型范围[取随机数(1,#模型范围)]
	local 名称=名称范围[取随机数(1,#名称范围)]
	local 技能范围 = {{"烟雨剑法","善恶有报"},{"三昧真火","地狱烈火","飞砂走石"},{"龙卷雨击","龙腾","龙吟"},{"落叶萧萧","雷击","奔雷咒"},{"失心符","定身符","反间之计"},{"推气过宫"},{"天雷斩"}}
	local 技能随机=技能范围[取随机数(1,#技能范围)]
	for i=6,10 do
		模型=模型范围[取随机数(1,#模型范围)]
		名称=名称范围[取随机数(1,#名称范围)]
		技能随机=技能范围[取随机数(1,#技能范围)]
		战斗单位[i]={名称=名称,模型=模型,饰品=true,伤害=等级*8+500,气血=等级*50,法伤=等级*4+500,速度=等级*4,防御=等级*6,法防=等级*4,躲闪=等级*4,魔法=200000,等级=等级,技能={"魔之心","法术连击"},主动技能=取随机法术新(5)}
	end
	return 战斗单位
end
return 副本_通天河