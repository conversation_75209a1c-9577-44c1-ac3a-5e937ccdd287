-- @Author: baidwwy
-- @Date:   2025-01-20 19:07:57
-- @Last Modified by:   交流QQ群：834248191
-- @Last Modified time: 2025-02-16 12:06:07

function 怪物属性:沙僧剧情一天兵飞剑(任务id, 玩家id)
	local 战斗单位 = {}
	local 等级 = 55 -- 统一使用官服55级标准

	-- 主怪（天兵飞剑）
	战斗单位[1] = {
		名称 = "天兵飞剑",
		模型 = "天兵",
		伤害 = 等级 * 10, -- 550
		气血 = 等级 * 150, -- 8250
		法伤 = 等级 * 5, -- 275
		速度 = 等级 * 1.1, -- 60.5
		防御 = 等级 * 3, -- 165
		法防 = 等级 * 1.8, -- 99
		等级 = 等级,
		技能 = { "高级防御" },
		主动技能 = { "后发制人", "破血狂攻" }
	}

	-- 蛟龙喽啰*2
	for i = 2, 3 do
		战斗单位[i] = {
			名称 = "蛟龙喽啰",
			模型 = "蛟龙",
			伤害 = 等级 * 9, -- 495
			气血 = 等级 * 100, -- 5500
			法伤 = 等级 * 4,
			速度 = 等级 * 0.9, -- 49.5
			防御 = 等级 * 2.5, -- 137.5
			法防 = 等级 * 1.2, -- 66
			等级 = 等级,
			主动技能 = { "龙卷雨击" }
		}
	end

	-- 天兵喽啰*2
	for i = 4, 5 do
		战斗单位[i] = {
			名称 = "天兵喽啰",
			模型 = "天兵",
			伤害 = 等级 * 8, -- 440
			气血 = 等级 * 90, -- 4950
			法伤 = 等级 * 3,
			速度 = 等级 * 0.8, -- 44
			防御 = 等级 * 2, -- 110
			法防 = 等级 * 1, -- 55
			等级 = 等级,
			主动技能 = { "龙卷雨击" }
		}
	end
	return 战斗单位
end

function 胜利MOB_110018(胜利id, 战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组 = { 数字id }
	if 玩家数据[数字id].队伍 ~= 0 and 玩家数据[数字id].队长 then
		for k, v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v ~= 数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线 == 玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度 == 玩家数据[数字id].角色.剧情.进度 then
					id组[#id组 + 1] = v
				end
			end
		end
	end
	for k, v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(180000), "主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情 = { 主线 = 4, 编号 = 28, 地图 = 1173, 进度 = 2, 附加 = { 战斗 = 1 } }
		local wb = { "天兵", "天兵飞剑", "算你狠……今天就饶了这小子……" }
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, { 模型 = wb[1], 名称 = wb[2], 对话 = wb[3], 选项 = xx, 下一页 = wb2, 剧情 = 玩家数据[v].角色.剧情 })
	end
end

function 怪物属性:沙僧剧情一卷帘大将(任务id, 玩家id)
	local 战斗单位 = {}
	local 等级 = 55 -- 统一使用官服55级标准

	-- 主怪（卷帘大将）
	战斗单位[1] = {
		名称 = "卷帘大将",
		模型 = "沙和尚",
		伤害 = 等级 * 12, -- 55*12=660
		气血 = 等级 * 180, -- 55*180=9900
		法伤 = 等级 * 6, -- 55*6=330
		速度 = 等级 * 1.1, -- 60.5
		防御 = 等级 * 3.5, -- 192.5
		法防 = 等级 * 2.2, -- 121
		等级 = 等级,
		技能 = { "高级招架", "吸血" },
		主动技能 = { "狮搏" }
	}

	-- 天兵喽啰*4
	for i = 2, 5 do
		战斗单位[i] = {
			名称 = "天兵喽啰",
			模型 = "天兵",
			伤害 = 等级 * 9, -- 495
			气血 = 等级 * 90, -- 4950
			法伤 = 等级 * 4,
			速度 = 等级 * 0.8, -- 44
			防御 = 等级 * 2.5, -- 137.5
			法防 = 等级 * 1.2, -- 66
			等级 = 等级,
			主动技能 = { "龙卷雨击" }
		}
	end
	return 战斗单位
end

function 胜利MOB_110019(胜利id, 战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组 = { 数字id }
	if 玩家数据[数字id].队伍 ~= 0 and 玩家数据[数字id].队长 then
		for k, v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v ~= 数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线 == 玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度 == 玩家数据[数字id].角色.剧情.进度 then
					id组[#id组 + 1] = v
				end
			end
		end
	end
	for k, v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(222500), "主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情 = { 主线 = 4, 编号 = 28, 地图 = 1173, 进度 = 3, 附加 = { 战斗 = 1 } }
		local wb = { "沙和尚", "卷帘大将", "我的念珠！" }
		local xx = {}
		local wb2 = { 玩家数据[v].角色.模型, 玩家数据[v].角色.名称, "丢了串珠子算什么，我们再来打过！", {},
			{ "沙和尚", "卷帘大将", "啊，我的头好痛……", {},
				nil } }
		发送数据(玩家数据[v].连接id, 1501, { 模型 = wb[1], 名称 = wb[2], 对话 = wb[3], 选项 = xx, 下一页 = wb2, 剧情 = 玩家数据[v].角色.剧情 })
	end
end

function 怪物属性:沙僧剧情一卷帘大将心魔(任务id, 玩家id)
	local 战斗单位 = {}
	local 等级 = 60
	local 剧情名称 = 取假人表(玩家数据[玩家id].角色.剧情.地图, 玩家数据[玩家id].角色.剧情.编号)
	战斗单位[1] = { 名称 = 剧情名称.名称 .. "(心魔)", 模型 = "沙和尚", 伤害 = 9999999, 气血 = 9999999, 灵力 = 9999999, 速度 = 9999999, 防御 = 9999999, 躲闪 = 9999999, 等级 = 9999999, 技能 = { "吸血" }, 主动技能 = { "龙卷雨击" } }
	战斗单位[2] = { 名称 = "天兵喽啰", 模型 = "天兵", 伤害 = 等级 * 13, 气血 = 等级 * 125, 法伤 = 等级 * 8, 速度 = 等级 * 4, 防御 = 等级 * 4, 法防 = 等级 * 2, 躲闪 =
	等级 * 2, 等级 = 等级, 技能 = {}, 主动技能 = 取随机法术(5) }
	战斗单位[3] = { 名称 = "天兵喽啰", 模型 = "天兵", 伤害 = 等级 * 13, 气血 = 等级 * 125, 法伤 = 等级 * 8, 速度 = 等级 * 4, 防御 = 等级 * 4, 法防 = 等级 * 2, 躲闪 =
	等级 * 2, 等级 = 等级, 技能 = {}, 主动技能 = 取随机法术(5) }
	战斗单位[4] = { 名称 = "天兵喽啰", 模型 = "天兵", 伤害 = 等级 * 13, 气血 = 等级 * 125, 法伤 = 等级 * 8, 速度 = 等级 * 4, 防御 = 等级 * 4, 法防 = 等级 * 2, 躲闪 =
	等级 * 2, 等级 = 等级, 技能 = {}, 主动技能 = 取随机法术(5) }
	战斗单位[5] = { 名称 = "天兵喽啰", 模型 = "天兵", 伤害 = 等级 * 13, 气血 = 等级 * 125, 法伤 = 等级 * 8, 速度 = 等级 * 4, 防御 = 等级 * 4, 法防 = 等级 * 2, 躲闪 =
	等级 * 2, 等级 = 等级, 技能 = {}, 主动技能 = 取随机法术(5) }
	战斗单位[6] = { 名称 = "天兵喽啰", 模型 = "天兵", 伤害 = 等级 * 13, 气血 = 等级 * 125, 法伤 = 等级 * 8, 速度 = 等级 * 4, 防御 = 等级 * 4, 法防 = 等级 * 2, 躲闪 =
	等级 * 2, 等级 = 等级, 技能 = {}, 主动技能 = 取随机法术(5) }
	return 战斗单位
end

function 胜利MOB_110020(胜利id, 战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组 = { 数字id }
	if 玩家数据[数字id].队伍 ~= 0 and 玩家数据[数字id].队长 then
		for k, v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v ~= 数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线 == 玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度 == 玩家数据[数字id].角色.剧情.进度 then
					id组[#id组 + 1] = v
				end
			end
		end
	end
	for k, v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(185000), "主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].角色.剧情 = { 主线 = 4, 编号 = 21, 地图 = 1001, 进度 = 4, 附加 = {} }
		local wb = { "沙和尚", "卷帘大将", "知道我厉害了吧，哈哈哈哈！" }
		local xx = {}
		local wb2 = { 玩家数据[v].角色.模型, 玩家数据[v].角色.名称, "没想到解除了九宫阵的封印，他的力量竟然会如此强大……", {},
			nil }
		发送数据(玩家数据[v].连接id, 1501, { 模型 = wb[1], 名称 = wb[2], 对话 = wb[3], 选项 = xx, 下一页 = wb2, 剧情 = 玩家数据[v].角色.剧情 })
	end
end

function 失败MOB110020(id组, 失败id, 是否逃跑, 战斗数据)
	for i = 1, #id组 do
		if id组[i] == 失败id then
			if 失败id == 战斗数据.进入战斗玩家id and 玩家数据[战斗数据.进入战斗玩家id] ~= nil then
				if 是否逃跑 == nil then
					local 数字id = 战斗数据.进入战斗玩家id
					local id组 = { 数字id }
					if 玩家数据[数字id].队伍 ~= 0 and 玩家数据[数字id].队长 then
						for k, v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
							if v ~= 数字id then
								if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线 == 玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度 == 玩家数据[数字id].角色.剧情.进度 then
									id组[#id组 + 1] = v
								end
							end
						end
					end
					for k, v in pairs(id组) do
						玩家数据[v].角色.剧情 = { 主线 = 4, 编号 = 21, 地图 = 1001, 进度 = 4, 附加 = {} }
						local wb = { "沙和尚", "卷帘大将", "知道我厉害了吧，哈哈哈哈！" }
						local xx = {}
						local wb2 = { 玩家数据[v].角色.模型, 玩家数据[v].角色.名称, "没想到解除了九宫阵的封印，他的力量竟然会如此强大……", {},
							nil }
						发送数据(玩家数据[v].连接id, 1501, { 模型 = wb[1], 名称 = wb[2], 对话 = wb[3], 选项 = xx, 下一页 = wb2, 剧情 = 玩家数据[v]
						.角色.剧情 })
					end
				end
			end
		end
	end
end

function 怪物属性:沙僧剧情一龙孙(任务id, 玩家id)
	local 战斗单位 = {}
	local 等级 = 55 -- 修正等级

	-- 主怪（龙孙）
	战斗单位[1] = {
		名称 = "龙孙",
		模型 = "小龙女",
		伤害 = 等级 * 10, -- 55*10=550
		气血 = 等级 * 150, -- 8250
		法伤 = 等级 * 5,
		速度 = 等级 * 1.0, -- 55
		防御 = 等级 * 3, -- 165
		法防 = 等级 * 1.5, -- 82.5
		等级 = 等级,
		技能 = { "高级水属性吸收" },
		主动技能 = { "龙卷雨击" }
	}

	-- 喽啰配置（2虾兵+2蟹将）
	local 喽啰数据 = {
		{ 模型 = "虾兵", 数量 = 2, 伤害系数 = 8, 气血系数 = 90 },
		{ 模型 = "蟹将", 数量 = 2, 伤害系数 = 9, 气血系数 = 95 }
	}

	local 位置 = 2
	for _, 配置 in ipairs(喽啰数据) do
		for i = 1, 配置.数量 do
			战斗单位[位置] = {
				名称 = 配置.模型 .. "喽啰",
				模型 = 配置.模型,
				伤害 = 等级 * 配置.伤害系数,
				气血 = 等级 * 配置.气血系数,
				法伤 = 等级 * 4,
				速度 = 等级 * 0.8,
				防御 = 等级 * 2.5,
				法防 = 等级 * 1.2,
				等级 = 等级,
				主动技能 = { "水攻" }
			}
			位置 = 位置 + 1
		end
	end
	return 战斗单位
end

function 胜利MOB_110021(胜利id, 战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组 = { 数字id }
	if 玩家数据[数字id].队伍 ~= 0 and 玩家数据[数字id].队长 then
		for k, v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v ~= 数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线 == 玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度 == 玩家数据[数字id].角色.剧情.进度 then
					id组[#id组 + 1] = v
				end
			end
		end
	end
	for k, v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(255000), "主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v, "天龙水", 10)
		玩家数据[v].角色.剧情 = { 主线 = 4, 编号 = 131, 地图 = 1001, 进度 = 7, 附加 = {} }
		local wb = { 玩家数据[v].角色.模型, 玩家数据[v].角色.名称, "都告诉你不要随便打人啦……喂，你，你怎么晕了，我都没有用力啊……" }
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, { 模型 = wb[1], 名称 = wb[2], 对话 = wb[3], 选项 = xx, 下一页 = wb2, 剧情 = 玩家数据[v].角色.剧情 })
	end
end

function 怪物属性:沙僧剧情一小龙女(任务id, 玩家id)
	local 战斗单位 = {}
	local 等级 = 55 -- 修正等级

	-- 主怪（小龙女）
	战斗单位[1] = {
		名称 = "小龙女",
		模型 = "小龙女",
		伤害 = 等级 * 11, -- 55*11=605
		气血 = 等级 * 160, -- 8800
		法伤 = 等级 * 5,
		速度 = 等级 * 1.1, -- 60.5
		防御 = 等级 * 3.2, -- 176
		法防 = 等级 * 1.8, -- 99
		等级 = 等级,
		技能 = { "高级水属性吸收" },
		主动技能 = { "龙腾", "龙卷雨击" }
	}

	-- 喽啰配置（2龟丞相+2蟹将）
	local 喽啰数据 = {
		{ 模型 = "龟丞相", 数量 = 2, 伤害系数 = 8, 气血系数 = 90 },
		{ 模型 = "蟹将", 数量 = 2, 伤害系数 = 9, 气血系数 = 95 }
	}

	local 位置 = 2
	for _, 配置 in ipairs(喽啰数据) do
		for i = 1, 配置.数量 do
			战斗单位[位置] = {
				名称 = 配置.模型 .. "喽啰",
				模型 = 配置.模型,
				伤害 = 等级 * 配置.伤害系数,
				气血 = 等级 * 配置.气血系数,
				法伤 = 等级 * 4,
				速度 = 等级 * 0.8,
				防御 = 等级 * 2.5,
				法防 = 等级 * 1.2,
				等级 = 等级,
				主动技能 = { "水攻" }
			}
			位置 = 位置 + 1
		end
	end
	return 战斗单位
end

function 胜利MOB_110022(胜利id, 战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组 = { 数字id }
	if 玩家数据[数字id].队伍 ~= 0 and 玩家数据[数字id].队长 then
		for k, v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v ~= 数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线 == 玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度 == 玩家数据[数字id].角色.剧情.进度 then
					id组[#id组 + 1] = v
				end
			end
		end
	end
	for k, v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(255000), "主线剧情")
		玩家数据[v].道具:给予道具(v, "龙之心屑", 10)
		玩家数据[v].角色.剧情 = { 主线 = 4, 编号 = 131, 地图 = 1001, 进度 = 9, 附加 = {} }
		local wb = { "小龙女", "小龙女", "算你狠，我保证以后不去找袁先生的麻烦就是了" }
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, { 模型 = wb[1], 名称 = wb[2], 对话 = wb[3], 选项 = xx, 下一页 = wb2, 剧情 = 玩家数据[v].角色.剧情 })
	end
end

function 怪物属性:沙僧剧情一李靖(任务id, 玩家id)
	local 战斗单位 = {}
	local 等级 = 55 -- 修正等级

	-- 主怪（李靖）
	战斗单位[1] = {
		名称 = "李靖",
		模型 = "李靖",
		伤害 = 等级 * 13, -- 715
		气血 = 等级 * 200, -- 11000
		法伤 = 等级 * 6,
		速度 = 等级 * 1.2, -- 66
		防御 = 等级 * 3.8, -- 209
		法防 = 等级 * 2.5, -- 137.5
		等级 = 等级,
		技能 = { "高级必杀" },
		主动技能 = { "横扫千军" }
	}

	-- 天兵喽啰*4
	for i = 2, 5 do
		战斗单位[i] = {
			名称 = "天兵喽啰",
			模型 = "天兵",
			伤害 = 等级 * 9, -- 495
			气血 = 等级 * 90, -- 4950
			法伤 = 等级 * 4,
			速度 = 等级 * 0.8, -- 44
			防御 = 等级 * 2.5, -- 137.5
			法防 = 等级 * 1.2, -- 66
			等级 = 等级,
			主动技能 = { "龙卷雨击" }
		}
	end
	return 战斗单位
end

function 胜利MOB_110023(胜利id, 战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组 = { 数字id }
	if 玩家数据[数字id].队伍 ~= 0 and 玩家数据[数字id].队长 then
		for k, v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v ~= 数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线 == 玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度 == 玩家数据[数字id].角色.剧情.进度 then
					id组[#id组 + 1] = v
				end
			end
		end
	end
	for k, v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(255000), "主线剧情")
		玩家数据[v].道具:给予道具(v, "钢甲")
		玩家数据[v].角色.剧情 = { 主线 = 4, 编号 = 3, 地图 = 1111, 进度 = 13, 附加 = { 物品 = "醉生梦死" } }
		local wb = { "李靖", "李靖", "哈哈哈，侠士果真武功过人，看来小儿还需多多历练啊！本王今天兴致好，来人啊，颁赏！侠士还有什么要求尽管提。" }
		local xx = {}
		local wb2 = { 玩家数据[v].角色.模型, 玩家数据[v].角色.名称, "天王，在下别无所求，只盼能拿回天英仙身？", {},
			{ "李靖", "李靖", "天英帮助天蓬给嫦娥仙子递情书，怎能如此轻易饶恕，玉帝已经吩咐下来让将他仙身封入锁仙瓶，永不得出世呢。", {},
				{ 玩家数据[v].角色.模型, 玩家数据[v].角色.名称, "但是，天英作为九宫阵中一星实在是至关重要啊！", {},
					{ "李靖", "李靖", "这个啊……我儿哪吒日前和守门天将赌胜，竟然把火尖枪输去了，你若能要回来，挽回我儿面子，我就许你一个方便。", {},
						nil } } } }
		发送数据(玩家数据[v].连接id, 1501, { 模型 = wb[1], 名称 = wb[2], 对话 = wb[3], 选项 = xx, 下一页 = wb2, 剧情 = 玩家数据[v].角色.剧情 })
	end
end

function 怪物属性:沙僧剧情一守门天将(任务id, 玩家id)
	local 战斗单位 = {}
	local 等级 = 55 -- 修正等级

	-- 主怪（守门天将）
	战斗单位[1] = {
		名称 = "守门天将",
		模型 = "天将",
		伤害 = 等级 * 14, -- 770
		气血 = 等级 * 220, -- 12100
		法伤 = 等级 * 5,
		速度 = 等级 * 1.3, -- 71.5
		防御 = 等级 * 4, -- 220
		法防 = 等级 * 2.8, -- 154
		等级 = 等级,
		技能 = { "高级反击" },
		主动技能 = { "破血狂攻" }
	}

	-- 风伯喽啰*4
	for i = 2, 5 do
		战斗单位[i] = {
			名称 = "风伯喽啰",
			模型 = "风伯",
			伤害 = 等级 * 10, -- 550
			气血 = 等级 * 100, -- 5500
			法伤 = 等级 * 5,
			速度 = 等级 * 0.9, -- 49.5
			防御 = 等级 * 3, -- 165
			法防 = 等级 * 1.5, -- 82.5
			等级 = 等级,
			主动技能 = { "风卷残云" }
		}
	end
	return 战斗单位
end

function 胜利MOB_110024(胜利id, 战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组 = { 数字id }
	if 玩家数据[数字id].队伍 ~= 0 and 玩家数据[数字id].队长 then
		for k, v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v ~= 数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线 == 玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度 == 玩家数据[数字id].角色.剧情.进度 then
					id组[#id组 + 1] = v
				end
			end
		end
	end
	for k, v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(255000), "主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予任务道具(v, "火尖枪")
		玩家数据[v].角色.剧情 = { 主线 = 4, 编号 = 1, 地图 = 1112, 进度 = 14, 附加 = { 物品 = "火尖枪" } }
		local wb = { "李靖", "李靖", "骗子，把火尖枪还给我！" }
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, { 模型 = wb[1], 名称 = wb[2], 对话 = wb[3], 选项 = xx, 下一页 = wb2, 剧情 = 玩家数据[v].角色.剧情 })
	end
end

function 怪物属性:沙僧剧情一路人甲(任务id, 玩家id)
	local 战斗单位 = {}
	local 等级 = 55 -- 修正等级

	-- 主怪（赌徒）
	战斗单位[1] = {
		名称 = "神秘赌徒",
		模型 = "赌徒",
		伤害 = 等级 * 12, -- 660
		气血 = 等级 * 150, -- 8250
		法伤 = 等级 * 4,
		速度 = 等级 * 1.0, -- 55
		防御 = 等级 * 3, -- 165
		法防 = 等级 * 1.8, -- 99
		等级 = 等级,
		技能 = { "高级偷袭" },
		主动技能 = { "连环击" }
	}

	-- 喽啰配置（2雷鸟人+2花妖）
	local 喽啰数据 = {
		{ 模型 = "雷鸟人", 数量 = 2, 伤害系数 = 9, 气血系数 = 100 },
		{ 模型 = "花妖", 数量 = 2, 伤害系数 = 8, 气血系数 = 90 }
	}

	local 位置 = 2
	for _, 配置 in ipairs(喽啰数据) do
		for i = 1, 配置.数量 do
			战斗单位[位置] = {
				名称 = 配置.模型 .. "喽啰",
				模型 = 配置.模型,
				伤害 = 等级 * 配置.伤害系数,
				气血 = 等级 * 配置.气血系数,
				法伤 = 等级 * 4,
				速度 = 等级 * 0.8,
				防御 = 等级 * 2.5,
				法防 = 等级 * 1.2,
				等级 = 等级,
				主动技能 = { "雷击", "落岩" }
			}
			位置 = 位置 + 1
		end
	end
	return 战斗单位
end

function 胜利MOB_110025(胜利id, 战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组 = { 数字id }
	if 玩家数据[数字id].队伍 ~= 0 and 玩家数据[数字id].队长 then
		for k, v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v ~= 数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线 == 玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度 == 玩家数据[数字id].角色.剧情.进度 then
					id组[#id组 + 1] = v
				end
			end
		end
	end
	for k, v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(255000), "主线剧情")
		玩家数据[v].道具:给予任务道具(v, "金击子")
		玩家数据[v].角色.剧情 = { 主线 = 4, 编号 = 3, 地图 = 1146, 进度 = 22, 附加 = { 物品 = "金击子" } }
		local wb = { 玩家数据[v].角色.模型, 玩家数据[v].角色.名称, "诸位朋友，这个故事教育我们:拣到东西要交公！" }
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, { 模型 = wb[1], 名称 = wb[2], 对话 = wb[3], 选项 = xx, 下一页 = wb2, 剧情 = 玩家数据[v].角色.剧情 })
	end
end

function 怪物属性:沙僧剧情一大战心魔(任务id, 玩家id)
	local 战斗单位 = {}
	local 等级 = 55 -- 严格对齐官服55级

	-- 主怪（幽灵造型心魔）
	战斗单位[1] = {
		名称 = "心魔",
		模型 = "幽灵",
		伤害 = 等级 * 12, -- 660
		气血 = 等级 * 200, -- 11000
		法伤 = 等级 * 6,
		速度 = 等级 * 1.2, -- 66
		防御 = 等级 * 4,
		法防 = 等级 * 2,
		躲闪 = 等级 * 1.5,
		等级 = 等级,
		技能 = { "高级连击", "吸血" },
		主动技能 = { "横扫千军" }
	}

	-- 天兵喽啰*4
	for i = 2, 5 do
		战斗单位[i] = {
			名称 = "天兵喽啰",
			模型 = "天兵",
			伤害 = 等级 * 9, -- 495
			气血 = 等级 * 80, -- 4400
			法伤 = 等级 * 4,
			速度 = 等级 * 0.8, -- 44
			防御 = 等级 * 3,
			法防 = 等级 * 1.5,
			等级 = 等级,
			主动技能 = { "龙卷雨击" }
		}
	end
	return 战斗单位
end

function 胜利MOB_110026(胜利id, 战斗数据)
	local 数字id = 战斗数据.进入战斗玩家id
	local id组 = { 数字id }
	if 玩家数据[数字id].队伍 ~= 0 and 玩家数据[数字id].队长 then
		for k, v in pairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
			if v ~= 数字id then
				if 玩家数据[v].角色.剧情 and 玩家数据[v].角色.剧情.主线 == 玩家数据[数字id].角色.剧情.主线 and 玩家数据[v].角色.剧情.进度 == 玩家数据[数字id].角色.剧情.进度 then
					id组[#id组 + 1] = v
				end
			end
		end
	end
	for k, v in pairs(id组) do
		玩家数据[v].角色:添加经验(math.floor(287500), "主线剧情")
		玩家数据[v].角色:增加剧情点(1)
		玩家数据[v].道具:给予道具(v, "六道轮回", 10)
		玩家数据[v].角色.剧情 = { 主线 = 4, 编号 = 28, 地图 = 1173, 进度 = 24, 附加 = {} }
		local wb = { "沙和尚", "卷帘大将", "怎么可能……怎么可能会有人打败我……" }
		local xx = {}
		local wb2 = {}
		发送数据(玩家数据[v].连接id, 1501, { 模型 = wb[1], 名称 = wb[2], 对话 = wb[3], 选项 = xx, 下一页 = wb2, 剧情 = 玩家数据[v].角色.剧情 })
	end
end
